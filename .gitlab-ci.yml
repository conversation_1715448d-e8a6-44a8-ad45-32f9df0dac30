image: node:20-alpine

stages:
  - dockerize
  - deploy

variables:
  #        gamma deployment variables
  ECR_REPOSITORY_NAME_GAMMA: thena-platform
  ECS_CLUSTER_NAME_GAMMA: thena-backend-gamma
  SERVICE_NAME_GAMMA: thena-platform-slack-app
  AWS_REGION: us-east-1
  TASK_DEFINITION_NAME_GAMMA: thena-platform-slack-app

dockerize:
  stage: dockerize
  image: docker:26.1.3-alpine3.19
  services:
    - docker:dind
  script:
    - echo "Building Docker image and pushing to ECR..."
    - apk update
    - apk add python3 py3-pip
    - apk add --no-cache aws-cli
    - aws configure set aws_access_key_id $AWS_ACCESS_KEY_GAMMA
    - aws configure set aws_secret_access_key $AWS_SECRET_KEY_GAMMA
    - aws configure set region $AWS_REGION
    - aws ecr get-login-password --region $AWS_REGION | docker login --username AWS --password-stdin $AWS_ACCOUNT_ID_GAMMA.dkr.ecr.$AWS_REGION.amazonaws.com
    - docker build -t $AWS_ACCOUNT_ID_GAMMA.dkr.ecr.$AWS_REGION.amazonaws.com/$ECR_REPOSITORY_NAME_GAMMA:thena-platform-slack-app-$CI_COMMIT_SHORT_SHA .
    - docker push $AWS_ACCOUNT_ID_GAMMA.dkr.ecr.$AWS_REGION.amazonaws.com/$ECR_REPOSITORY_NAME_GAMMA:thena-platform-slack-app-$CI_COMMIT_SHORT_SHA

  only:
    - master

deploy_staging:
  stage: deploy
  image: docker:26.1.3-alpine3.19
  services:
    - docker:dind
  script:
    - echo "Deploying the app to staging..."
    - apk update
    - apk add python3 py3-pip jq
    - apk add --no-cache aws-cli
    - aws configure set aws_access_key_id $AWS_ACCESS_KEY_GAMMA
    - aws configure set aws_secret_access_key $AWS_SECRET_KEY_GAMMA
    - aws configure set region $AWS_REGION
    - FULL_IMAGE="${AWS_ACCOUNT_ID_GAMMA}.dkr.ecr.${AWS_REGION}.amazonaws.com/${ECR_REPOSITORY_NAME_GAMMA}:thena-platform-slack-app-${CI_COMMIT_SHORT_SHA}"
    - TASK_DEFINITION=$(aws ecs describe-task-definition --task-definition "$TASK_DEFINITION_NAME_GAMMA" --include TAGS)
    - NEW_TASK_DEFINITION=$(echo $TASK_DEFINITION | jq --arg IMAGE "$FULL_IMAGE" '.taskDefinition | .containerDefinitions[0].image = $IMAGE | del(.taskDefinitionArn) | del(.revision) | del(.status) | del(.requiresAttributes) | del(.compatibilities) |  del(.registeredAt)  | del(.registeredBy)')
    - NEW_TASK_INFO=$(aws ecs register-task-definition --cli-input-json "$NEW_TASK_DEFINITION")
    - NEW_REVISION=$(echo $NEW_TASK_INFO | jq '.taskDefinition.revision')
    - aws ecs update-service --cluster $ECS_CLUSTER_NAME_GAMMA --service $SERVICE_NAME_GAMMA --task-definition ${TASK_DEFINITION_NAME_GAMMA}:${NEW_REVISION} --force-new-deployment

  only:
    - master
