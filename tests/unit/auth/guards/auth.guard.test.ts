import { describe, it, expect, beforeEach, vi, Mock } from 'vitest';
import { AuthGuard } from '../../../../src/auth/guards/auth.guard';
import { Reflector } from '@nestjs/core';
import { ForbiddenException } from '@nestjs/common';
import { NO_SLACK_TEAM } from '../../../../src/auth/decorators/no-slack-team.decorator';
import { Installations } from '../../../../src/database/entities';

describe('AuthGuard', () => {
  let guard: AuthGuard;
  let mockLogger: any;
  let mockReflector: any;
  let mockInstallationRepository: any;
  let mockExecutionContext: any;
  let mockRequest: any;

  beforeEach(() => {
    vi.resetAllMocks();

    mockLogger = {
      log: vi.fn(),
      error: vi.fn(),
      warn: vi.fn(),
      debug: vi.fn(),
    };

    mockReflector = {
      get: vi.fn(),
    };

    mockInstallationRepository = {
      findOne: vi.fn(),
      find: vi.fn(),
    };

    mockRequest = {
      headers: {},
      path: '/test-path',
    };

    mockExecutionContext = {
      switchToHttp: vi.fn().mockReturnValue({
        getRequest: vi.fn().mockReturnValue(mockRequest),
      }),
      getHandler: vi.fn(),
    };

    guard = new AuthGuard(
      mockLogger,
      mockReflector as unknown as Reflector,
      mockInstallationRepository,
    );
  });

  describe('canActivate', () => {
    it('should return false if no auth token is provided', async () => {
      mockRequest.headers = {};

      const result = await guard.canActivate(mockExecutionContext);

      expect(result).toBe(false);
      expect(mockLogger.debug).toHaveBeenCalledWith('No auth token found in the request');
    });

    it('should throw ForbiddenException if no slack id is provided and noSlackTeam is false', async () => {
      mockRequest.headers = {
        'x-auth-token': 'test-token',
      };
      mockReflector.get.mockReturnValue(false);

      await expect(guard.canActivate(mockExecutionContext)).rejects.toThrow(ForbiddenException);
      expect(mockLogger.debug).toHaveBeenCalledWith('No slack id found in the request');
    });

    it('should not throw if noSlackTeam is true even without slack id', async () => {
      mockRequest.headers = {
        'x-auth-token': 'test-token',
      };
      mockReflector.get.mockReturnValue(true);
      mockInstallationRepository.find.mockResolvedValue([
        {
          id: 'installation-id',
          organization: { uid: 'org-id', apiKey: 'test-token' },
        },
      ]);

      const result = await guard.canActivate(mockExecutionContext);

      expect(result).toBe(true);
      expect(mockInstallationRepository.find).toHaveBeenCalledWith({
        where: { organization: { apiKey: 'test-token' } },
        relations: { organization: true },
      });
    });

    it('should return false if no installation is found', async () => {
      mockRequest.headers = {
        'x-auth-token': 'test-token',
        'x-slack-id': 'slack-id',
      };
      mockReflector.get.mockReturnValue(false);
      mockInstallationRepository.findOne.mockResolvedValue(null);

      const result = await guard.canActivate(mockExecutionContext);

      expect(result).toBe(false);
      expect(mockLogger.debug).toHaveBeenCalledWith('No installation found in the request');
    });

    it('should set installation and organization in request and return true if installation is found', async () => {
      mockRequest.headers = {
        'x-auth-token': 'test-token',
        'x-slack-id': 'slack-id',
      };
      mockReflector.get.mockReturnValue(false);
      const mockInstallation = {
        id: 'installation-id',
        organization: { uid: 'org-id', apiKey: 'test-token' },
      };
      mockInstallationRepository.findOne.mockResolvedValue(mockInstallation);

      const result = await guard.canActivate(mockExecutionContext);

      expect(result).toBe(true);
      expect(mockRequest.installation).toEqual(mockInstallation);
      expect(mockRequest.organization).toEqual(mockInstallation.organization);
    });

    it('should set installations array in request if multiple installations are found', async () => {
      mockRequest.headers = {
        'x-auth-token': 'test-token',
      };
      mockReflector.get.mockReturnValue(true);
      const mockInstallations = [
        {
          id: 'installation-id-1',
          organization: { uid: 'org-id', apiKey: 'test-token' },
        },
        {
          id: 'installation-id-2',
          organization: { uid: 'org-id', apiKey: 'test-token' },
        },
      ];
      mockInstallationRepository.find.mockResolvedValue(mockInstallations);

      const result = await guard.canActivate(mockExecutionContext);

      expect(result).toBe(true);
      expect(mockRequest.installations).toEqual(mockInstallations);
      expect(mockRequest.organization).toEqual(mockInstallations[0].organization);
    });
  });

  describe('getSlackId', () => {
    it('should return the slack id from the request headers', () => {
      mockRequest.headers = {
        'x-slack-id': 'slack-id',
      };

      const result = guard['getSlackId'](mockRequest);

      expect(result).toBe('slack-id');
    });

    it('should return null if no slack id is found in the request headers', () => {
      mockRequest.headers = {};

      const result = guard['getSlackId'](mockRequest);

      expect(result).toBe(null);
    });
  });

  describe('getAuthToken', () => {
    it('should return the auth token from the request headers', () => {
      mockRequest.headers = {
        'x-auth-token': 'test-token',
      };

      const result = guard['getAuthToken'](mockRequest);

      expect(result).toBe('test-token');
    });

    it('should return undefined if no auth token is found in the request headers', () => {
      mockRequest.headers = {};

      const result = guard['getAuthToken'](mockRequest);

      expect(result).toBeUndefined();
    });
  });
});
