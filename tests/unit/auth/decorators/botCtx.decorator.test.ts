import { describe, it, expect, vi } from 'vitest';
import * as NestCommon from '@nestjs/common';
import { GetBotCtx } from '../../../../src/auth/decorators/botCtx.decorator';

// Mock createParamDecorator to return factory result directly
vi.mock('@nestjs/common', async () => {
  const actual = await vi.importActual('@nestjs/common');
  return {
    ...actual,
    createParamDecorator: vi.fn().mockImplementation((factory) => {
      return (data: unknown, ctx: any) => factory(data, ctx);
    })
  };
});

describe('GetBotCtx Decorator', () => {
  it('should extract installation, installations, and organization from request', () => {
    const mockInstallation = { id: 'installation-id' };
    const mockInstallations = [{ id: 'installation-id-1' }, { id: 'installation-id-2' }];
    const mockOrganization = { id: 'org-id' };
    
    const mockRequest = {
      installation: mockInstallation,
      installations: mockInstallations,
      organization: mockOrganization,
    };
    
    const mockExecutionContext = {
      switchToHttp: vi.fn().mockReturnValue({
        getRequest: vi.fn().mockReturnValue(mockRequest),
      }),
    };
    
    const factory = GetBotCtx(null, mockExecutionContext as any);
    
    expect(factory).toEqual({
      installation: mockInstallation,
      installations: mockInstallations,
      organization: mockOrganization,
    });
    expect(mockExecutionContext.switchToHttp).toHaveBeenCalled();
    expect(mockExecutionContext.switchToHttp().getRequest).toHaveBeenCalled();
  });
  
  it('should handle missing properties in request', () => {
    const mockRequest = {};
    
    const mockExecutionContext = {
      switchToHttp: vi.fn().mockReturnValue({
        getRequest: vi.fn().mockReturnValue(mockRequest),
      }),
    };
    
    const factory = GetBotCtx(null, mockExecutionContext as any);
    
    expect(factory).toEqual({
      installation: undefined,
      installations: undefined,
      organization: undefined,
    });
  });
});
