import { describe, it, expect, vi } from 'vitest';
import { NO_SLACK_TEAM } from '../../../../src/auth/decorators/no-slack-team.decorator';

// Mock SetMetadata before importing NoSlackTeam
vi.mock('@nestjs/common', () => {
  const originalModule = vi.importActual('@nestjs/common');
  return {
    ...originalModule,
    SetMetadata: vi.fn().mockImplementation((key, value) => {
      return { key, value };
    })
  };
});

// Import after the mock is set up
import { NoSlackTeam } from '../../../../src/auth/decorators/no-slack-team.decorator';
import { SetMetadata } from '@nestjs/common';

describe('NoSlackTeam Decorator', () => {
  it('should set metadata with NO_SLACK_TEAM key and true value', () => {
    const result = NoSlackTeam();
    
    expect(SetMetadata).toHaveBeenCalledWith(NO_SLACK_TEAM, true);
  });
  
  it('should have the correct constant value for NO_SLACK_TEAM', () => {
    expect(NO_SLACK_TEAM).toBe('no-slack-team');
  });
});
