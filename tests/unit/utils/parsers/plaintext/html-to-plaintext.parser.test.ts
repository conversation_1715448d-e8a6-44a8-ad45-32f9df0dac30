import { Test, TestingModule } from '@nestjs/testing';
import { Repository } from 'typeorm';
import { Users } from '../../../../../src/database/entities/users/users.entity';
import { HtmlToPlainText } from '../../../../../src/utils/parsers/plaintext/html-to-plaintext.parser';
import { describe, beforeEach, it, expect, vi } from 'vitest';

describe('HtmlToPlainText', () => {
  let mockUserRepository: Repository<Users>;

  beforeEach(() => {
    mockUserRepository = {
      findOne: vi.fn(),
    } as unknown as Repository<Users>;
  });

  describe('constructor', () => {
    it('should initialize with HTML content', () => {
      const html = '<p>Test content</p>';
      const parser = new HtmlToPlainText(html, mockUserRepository);
      expect(parser).toBeDefined();
    });
  });

  describe('convert', () => {
    it('should convert simple paragraph to plain text', async () => {
      const html = '<p>Test content</p>';
      const parser = new HtmlToPlainText(html, mockUserRepository);
      const result = await parser.convert();
      expect(result).toBe('Test content');
    });

    it('should handle multiple paragraphs', async () => {
      const html = '<p>First paragraph</p><p>Second paragraph</p>';
      const parser = new HtmlToPlainText(html, mockUserRepository);
      const result = await parser.convert();
      expect(result).toBe('First paragraph\n\nSecond paragraph');
    });

    it('should handle empty HTML', async () => {
      const html = '';
      const parser = new HtmlToPlainText(html, mockUserRepository);
      const result = await parser.convert();
      expect(result).toBe('');
    });

    it('should handle HTML with no content', async () => {
      const html = '<div></div>';
      const parser = new HtmlToPlainText(html, mockUserRepository);
      const result = await parser.convert();
      expect(result).toBe('');
    });
  });

  describe('processInlineElements', () => {
    it('should process strong text', async () => {
      const html = '<p>This is <strong>bold</strong> text</p>';
      const parser = new HtmlToPlainText(html, mockUserRepository);
      const result = await parser.convert();
      expect(result).toBe('This is **bold** text');
    });

    it('should process emphasized text', async () => {
      const html = '<p>This is <em>italic</em> text</p>';
      const parser = new HtmlToPlainText(html, mockUserRepository);
      const result = await parser.convert();
      expect(result).toBe('This is _italic_ text');
    });

    it('should process strikethrough text', async () => {
      const html = '<p>This is <s>strikethrough</s> text</p>';
      const parser = new HtmlToPlainText(html, mockUserRepository);
      const result = await parser.convert();
      expect(result).toBe('This is ~strikethrough~ text');
    });

    it('should process code text', async () => {
      const html = '<p>This is <code>code</code> text</p>';
      const parser = new HtmlToPlainText(html, mockUserRepository);
      const result = await parser.convert();
      expect(result).toBe('This is `code` text');
    });

    it('should process links', async () => {
      const html = '<p>This is a <a href="https://example.com">link</a></p>';
      const parser = new HtmlToPlainText(html, mockUserRepository);
      const result = await parser.convert();
      expect(result).toBe('This is a [link](https://example.com)');
    });

    it('should process user mentions with existing user', async () => {
      const html = '<p>Hello <span class="mention" data-id="U12345" data-email="<EMAIL>" data-label="User">@User</span></p>';
      (mockUserRepository.findOne as any).mockResolvedValue({ slackId: 'U12345' });
      
      const parser = new HtmlToPlainText(html, mockUserRepository);
      const result = await parser.convert();
      expect(result).toBe('Hello <@U12345>');
      expect(mockUserRepository.findOne).toHaveBeenCalledWith({
        where: { slackProfileEmail: '<EMAIL>' },
      });
    });

    it('should process user mentions with non-existing user', async () => {
      const html = '<p>Hello <span class="mention" data-id="U12345" data-email="<EMAIL>" data-label="User">@User</span></p>';
      (mockUserRepository.findOne as any).mockResolvedValue(null);
      
      const parser = new HtmlToPlainText(html, mockUserRepository);
      const result = await parser.convert();
      expect(result).toBe('Hello @User');
      expect(mockUserRepository.findOne).toHaveBeenCalledWith({
        where: { slackProfileEmail: '<EMAIL>' },
      });
    });

    it('should process subteam mentions', async () => {
      const html = '<p>Hello <span class="mention" data-id="S12345" data-label="Subteam">@Subteam</span></p>';
      
      const parser = new HtmlToPlainText(html, mockUserRepository);
      const result = await parser.convert();
      expect(result).toBe('Hello <!subteam^S12345>');
    });

    it('should process complex nested elements', async () => {
      const html = '<p>This is <strong>bold <em>and italic</em></strong> text</p>';
      const parser = new HtmlToPlainText(html, mockUserRepository);
      const result = await parser.convert();
      expect(result).toBe('This is **bold _and italic_** text');
    });
  });
});
