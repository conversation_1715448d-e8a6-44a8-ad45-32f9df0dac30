import { describe, it, expect, vi, beforeEach } from 'vitest';
import { BaseSlackBlocksToHtml } from '../../../../../src/utils/parsers/slack/slack-blocks-to-html/base-slack-to-html.parser';
import { SlackToHTMLRichTextParser } from '../../../../../src/utils/parsers/slack/slack-blocks-to-html/slack-to-html-rich-text.parser';
import { Repository } from 'typeorm';
import { Users, Installations } from '../../../../../src/database/entities';

describe('BaseSlackBlocksToHtml', () => {
  let baseSlackBlocksToHtml: BaseSlackBlocksToHtml;
  let mockUsersRepository: Repository<Users>;
  let mockSlackToHTMLRichTextParser: SlackToHTMLRichTextParser;
  let mockInstallation: Installations;

  beforeEach(() => {
    mockUsersRepository = {
      findOne: vi.fn(),
    } as unknown as Repository<Users>;

    mockSlackToHTMLRichTextParser = {
      slackToHTMLParser: vi.fn().mockResolvedValue('<div><p>Rich text content</p></div>'),
    } as unknown as SlackToHTMLRichTextParser;

    mockInstallation = {
      id: 'installation-id',
      teamId: 'T12345',
      botToken: 'xoxb-token',
      organization: { id: 'org-id' },
    } as Installations;

    baseSlackBlocksToHtml = new BaseSlackBlocksToHtml(
      mockUsersRepository,
      mockSlackToHTMLRichTextParser
    );
  });

  describe('initialize', () => {
    it('should initialize with blocks and installation', () => {
      const blocks = [{ type: 'section', text: { text: 'Hello world' } }];
      baseSlackBlocksToHtml.initialize(blocks, mockInstallation);
      expect(baseSlackBlocksToHtml).toBeDefined();
    });

    it('should set raw option correctly', () => {
      const blocks = [{ type: 'section', text: { text: 'Hello world' } }];
      baseSlackBlocksToHtml.initialize(blocks, mockInstallation, { raw: true });
      expect(baseSlackBlocksToHtml).toBeDefined();
    });
  });

  describe('convert', () => {
    it('should convert simple section block to HTML', async () => {
      const blocks = [{ type: 'section', text: { type: 'mrkdwn', text: 'Hello world' } }];
      baseSlackBlocksToHtml.initialize(blocks, mockInstallation);
      
      // Mock the processMarkdown method
      vi.spyOn(baseSlackBlocksToHtml as any, 'processMarkdown').mockResolvedValue('Hello world');
      
      const result = await baseSlackBlocksToHtml.convert();
      
      expect(result).toContain('Hello world');
    });

    it('should convert header block to HTML', async () => {
      const blocks = [{ type: 'header', text: { type: 'plain_text', text: 'Header Text' } }];
      baseSlackBlocksToHtml.initialize(blocks, mockInstallation);
      
      // Mock the processMarkdown method
      vi.spyOn(baseSlackBlocksToHtml as any, 'processMarkdown').mockResolvedValue('Header Text');
      
      const result = await baseSlackBlocksToHtml.convert();
      
      expect(result).toContain('<h1>');
      expect(result).toContain('Header Text');
    });

    it('should convert rich_text block to HTML', async () => {
      const blocks = [{ type: 'rich_text', elements: [{ type: 'rich_text_section', elements: [] }] }];
      baseSlackBlocksToHtml.initialize(blocks, mockInstallation);
      
      const result = await baseSlackBlocksToHtml.convert();
      
      expect(mockSlackToHTMLRichTextParser.slackToHTMLParser).toHaveBeenCalledWith({
        blocks: [blocks[0]],
        installation: mockInstallation,
        raw: false,
      });
      expect(result).toContain('Rich text content');
    });

    it('should convert divider block to HTML', async () => {
      const blocks = [{ type: 'divider' }];
      baseSlackBlocksToHtml.initialize(blocks, mockInstallation);
      
      const result = await baseSlackBlocksToHtml.convert();
      
      expect(result).toContain('<hr>');
    });

    it('should convert image block to HTML', async () => {
      const blocks = [{ 
        type: 'image', 
        image_url: 'https://example.com/image.jpg',
        alt_text: 'Example image' 
      }];
      baseSlackBlocksToHtml.initialize(blocks, mockInstallation);
      
      const result = await baseSlackBlocksToHtml.convert();
      
      expect(result).toContain('<img');
      expect(result).toContain('src=\'https://example.com/image.jpg\'');
      expect(result).toContain('alt=\'Example image\'');
    });

    it('should convert video block to HTML', async () => {
      const blocks = [{ 
        type: 'video', 
        video_url: 'https://example.com/video.mp4',
        thumbnail_url: 'https://example.com/thumbnail.jpg' 
      }];
      baseSlackBlocksToHtml.initialize(blocks, mockInstallation);
      
      const result = await baseSlackBlocksToHtml.convert();
      
      expect(result).toContain('<video');
      expect(result).toContain('src=\'https://example.com/video.mp4\'');
      expect(result).toContain('poster=\'https://example.com/thumbnail.jpg\'');
    });

    it.todo('should handle errors gracefully', async () => {
      const blocks = [{ type: 'section', text: { text: 'Hello world' } }];
      baseSlackBlocksToHtml.initialize(blocks, mockInstallation);
      
      // Force an error
      vi.spyOn(baseSlackBlocksToHtml as any, 'processMarkdown').mockRejectedValue(new Error('Test error'));
      
      const result = await baseSlackBlocksToHtml.convert();
      
      expect(result).toBe('');
    });

    it('should handle channel_not_found errors by using a placeholder', async () => {
      const blocks = [{ 
        type: 'rich_text',
        elements: [{ 
          type: 'rich_text_section', 
          elements: [
            { type: 'channel', channel_id: 'C12345' },
            { type: 'text', text: ' channel message' }
          ] 
        }] 
      }];
      baseSlackBlocksToHtml.initialize(blocks, mockInstallation);
      
      // Reset the mock and set it to throw an error for this test
      mockSlackToHTMLRichTextParser.slackToHTMLParser = vi.fn().mockRejectedValue(
        new Error('An error occurred: channel_not_found')
      );
      
      // Mock the reconstructWithChannelPlaceholder method
      vi.spyOn(baseSlackBlocksToHtml as any, 'reconstructWithChannelPlaceholder').mockReturnValue('private_channel channel message');
      
      const result = await baseSlackBlocksToHtml.convert();
      
      expect(result).toContain('<div><p>private_channel channel message</p></div>');
    });
  });

  describe('processMarkdown', () => {
    it('should convert bold markdown to HTML', async () => {
      const markdown = 'Hello *bold* world';
      
      baseSlackBlocksToHtml.initialize([], mockInstallation);
      const result = await (baseSlackBlocksToHtml as any).processMarkdown(markdown);
      
      expect(result).toContain('<strong>bold</strong>');
    });

    it('should convert italic markdown to HTML', async () => {
      const markdown = 'Hello _italic_ world';
      
      baseSlackBlocksToHtml.initialize([], mockInstallation);
      const result = await (baseSlackBlocksToHtml as any).processMarkdown(markdown);
      
      expect(result).toContain('<em>italic</em>');
    });

    it('should convert strikethrough markdown to HTML', async () => {
      const markdown = 'Hello ~strike~ world';
      
      baseSlackBlocksToHtml.initialize([], mockInstallation);
      const result = await (baseSlackBlocksToHtml as any).processMarkdown(markdown);
      
      expect(result).toContain('<s>strike</s>');
    });

    it('should convert code markdown to HTML', async () => {
      const markdown = 'Hello `code` world';
      
      baseSlackBlocksToHtml.initialize([], mockInstallation);
      const result = await (baseSlackBlocksToHtml as any).processMarkdown(markdown);
      
      expect(result).toContain('<code>code</code>');
    });

    it('should convert code blocks to HTML', async () => {
      const markdown = 'Hello\n```\ncode block\n```\nworld';
      
      baseSlackBlocksToHtml.initialize([], mockInstallation);
      const result = await (baseSlackBlocksToHtml as any).processMarkdown(markdown);
      
      expect(result).toContain('<pre><code>code block<br>');
    });

    it('should convert blockquotes to HTML', async () => {
      const markdown = '>This is a quote';
      
      baseSlackBlocksToHtml.initialize([], mockInstallation);
      const result = await (baseSlackBlocksToHtml as any).processMarkdown(markdown);
      
      expect(result).toContain('<blockquote><p>This is a quote</p></blockquote>');
    });

    it('should convert URLs to HTML links', async () => {
      const markdown = 'Visit <https://example.com|Example>';
      
      baseSlackBlocksToHtml.initialize([], mockInstallation);
      const result = await (baseSlackBlocksToHtml as any).processMarkdown(markdown);
      
      expect(result).toContain('<a href="https://example.com" target="_blank">Example</a>');
    });

    it('should convert user mentions to HTML', async () => {
      const markdown = 'Hello <@U12345>';
      const user = { 
        slackId: 'U12345', 
        slackProfileDisplayName: 'TestUser',
        slackProfileRealName: 'Test User',
        name: 'User' 
      };
      
      mockUsersRepository.findOne = vi.fn().mockResolvedValue(user);
      
      baseSlackBlocksToHtml.initialize([], mockInstallation);
      const result = await (baseSlackBlocksToHtml as any).processMarkdown(markdown);
      
      expect(result).toContain('@TestUser');
    });

    it('should handle line breaks correctly', async () => {
      const markdown = 'Line 1\nLine 2';
      
      baseSlackBlocksToHtml.initialize([], mockInstallation);
      const result = await (baseSlackBlocksToHtml as any).processMarkdown(markdown);
      
      expect(result).toContain('Line 1<br>Line 2');
    });

    it('should preserve leading spaces', async () => {
      const markdown = '    Indented text';
      
      baseSlackBlocksToHtml.initialize([], mockInstallation);
      const result = await (baseSlackBlocksToHtml as any).processMarkdown(markdown);
      
      expect(result).toContain('&nbsp;&nbsp;&nbsp;&nbsp;Indented text');
    });
  });

  describe('reconstructWithChannelPlaceholder', () => {
    it('should replace channel mentions with placeholder', () => {
      const blocks = [{ 
        type: 'rich_text',
        elements: [{ 
          type: 'rich_text_section', 
          elements: [
            { type: 'channel', channel_id: 'C12345' },
            { type: 'text', text: ' channel message' }
          ] 
        }] 
      }];
      
      baseSlackBlocksToHtml.initialize(blocks, mockInstallation);
      const result = (baseSlackBlocksToHtml as any).reconstructWithChannelPlaceholder(blocks, 'private_channel');
      
      expect(result).toContain('private_channel');
      expect(result).toContain('channel message');
    });

    it('should handle multiple channel mentions', () => {
      const blocks = [{ 
        type: 'rich_text',
        elements: [{ 
          type: 'rich_text_section', 
          elements: [
            { type: 'text', text: 'Message in ' },
            { type: 'channel', channel_id: 'C12345' },
            { type: 'text', text: ' and ' },
            { type: 'channel', channel_id: 'C67890' }
          ] 
        }] 
      }];
      
      baseSlackBlocksToHtml.initialize(blocks, mockInstallation);
      const result = (baseSlackBlocksToHtml as any).reconstructWithChannelPlaceholder(blocks, 'private_channel');
      
      expect(result).toContain('Message in');
      expect(result).toContain('private_channel');
      expect(result).toContain('and');
    });

    it('should handle non-rich_text blocks', () => {
      const blocks = [{ 
        type: 'section',
        text: { type: 'mrkdwn', text: 'Regular section' }
      }];
      
      baseSlackBlocksToHtml.initialize(blocks, mockInstallation);
      const result = (baseSlackBlocksToHtml as any).reconstructWithChannelPlaceholder(blocks, 'private_channel');
      
      expect(result).toBe('');
    });

    it('should handle empty blocks array', () => {
      baseSlackBlocksToHtml.initialize([], mockInstallation);
      const result = (baseSlackBlocksToHtml as any).reconstructWithChannelPlaceholder([], 'private_channel');
      
      expect(result).toBe('');
    });

    it('should handle null blocks', () => {
      baseSlackBlocksToHtml.initialize(null, mockInstallation);
      const result = (baseSlackBlocksToHtml as any).reconstructWithChannelPlaceholder(null, 'private_channel');
      
      expect(result).toBe('');
    });
  });
}); 