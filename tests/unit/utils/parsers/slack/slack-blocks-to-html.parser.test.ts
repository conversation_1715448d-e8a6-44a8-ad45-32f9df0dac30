import { describe, expect, it } from 'vitest';
import { slackBlocksToHtml } from '../../../../../src/utils/parsers/slack/slack-blocks-to-html.parser';

describe('slackBlocksToHtml', () => {
  describe('main function', () => {
    it('should return empty string for null or empty blocks', () => {
      expect(slackBlocksToHtml(null as any)).toBe('');
      expect(slackBlocksToHtml([] as any)).toBe('');
      expect(slackBlocksToHtml(undefined as any)).toBe('');
    });

    it('should parse a simple text block', () => {
      const blocks = [
        {
          type: 'section',
          text: {
            type: 'plain_text',
            text: 'Hello world',
          },
        },
      ];
      const html = slackBlocksToHtml(blocks);
      expect(html).toContain('Hello world');
      expect(html).toContain('<div class="slack-section">');
    });

    it('should parse multiple blocks', () => {
      const blocks = [
        {
          type: 'section',
          text: {
            type: 'plain_text',
            text: 'Section 1',
          },
        },
        {
          type: 'divider',
        },
        {
          type: 'section',
          text: {
            type: 'plain_text',
            text: 'Section 2',
          },
        },
      ];
      const html = slackBlocksToHtml(blocks);
      expect(html).toContain('Section 1');
      expect(html).toContain('<hr />');
      expect(html).toContain('Section 2');
    });

    it('should handle blocks with no type', () => {
      const blocks = [
        {
          text: 'No type block',
        } as any,
      ];
      const html = slackBlocksToHtml(blocks);
      expect(html).toBe('');
    });
  });

  describe('section blocks', () => {
    it('should parse a section block with plain text', () => {
      const blocks = [
        {
          type: 'section',
          text: {
            type: 'plain_text',
            text: 'Plain text section',
          },
        },
      ];
      const html = slackBlocksToHtml(blocks);
      expect(html).toContain('Plain text section');
      expect(html).toContain('<div class="slack-section">');
    });

    it('should parse a section block with markdown text', () => {
      const blocks = [
        {
          type: 'section',
          text: {
            type: 'mrkdwn',
            text: '*Bold* and _italic_',
          },
        },
      ];
      const html = slackBlocksToHtml(blocks);
      expect(html).toContain('<strong>Bold</strong>');
      expect(html).toContain('<em>italic</em>');
    });

    it('should parse a section block with fields', () => {
      const blocks = [
        {
          type: 'section',
          fields: [
            {
              type: 'plain_text',
              text: 'Field 1',
            },
            {
              type: 'plain_text',
              text: 'Field 2',
            },
          ],
        },
      ];
      const html = slackBlocksToHtml(blocks);
      expect(html).toContain('Field 1');
      expect(html).toContain('Field 2');
      expect(html).toContain('<div class="slack-fields">');
      expect(html).toContain('<div class="slack-field">');
    });

    it('should parse a section block with an image accessory', () => {
      const blocks = [
        {
          type: 'section',
          text: {
            type: 'plain_text',
            text: 'Section with image',
          },
          accessory: {
            type: 'image',
            image_url: 'https://example.com/image.jpg',
            alt_text: 'Example image',
          },
        },
      ];
      const html = slackBlocksToHtml(blocks);
      expect(html).toContain('Section with image');
      expect(html).toContain('<div class="slack-accessory">');
      expect(html).toContain('<img src="https://example.com/image.jpg"');
      expect(html).toContain('alt="Example image"');
    });

    it('should parse a section block with a button accessory', () => {
      const blocks = [
        {
          type: 'section',
          text: {
            type: 'plain_text',
            text: 'Section with button',
          },
          accessory: {
            type: 'button',
            text: {
              type: 'plain_text',
              text: 'Click me',
            },
          },
        },
      ];
      const html = slackBlocksToHtml(blocks);
      expect(html).toContain('Section with button');
      expect(html).toContain('<div class="slack-accessory">');
      expect(html).toContain('<button>');
      expect(html).toContain('Click me');
    });
  });

  describe('rich text blocks', () => {
    it.todo('should parse a rich text block');

    it.todo('should parse a rich text block with formatted text');

    it.todo('should parse a rich text block with links');

    it.todo('should parse a rich text block with mentions');

    it.todo('should parse a rich text block with emoji');

    it.todo('should parse a rich text list');

    it.todo('should parse an ordered rich text list');
  });

  describe('header blocks', () => {
    it('should parse a header block', () => {
      const blocks = [
        {
          type: 'header',
          text: {
            type: 'plain_text',
            text: 'Header text',
          },
        },
      ];
      const html = slackBlocksToHtml(blocks);
      expect(html).toContain('<h3 class="slack-header">');
      expect(html).toContain('Header text');
    });
  });

  describe('image blocks', () => {
    it('should parse an image block', () => {
      const blocks = [
        {
          type: 'image',
          image_url: 'https://example.com/image.jpg',
          alt_text: 'Example image',
        },
      ];
      const html = slackBlocksToHtml(blocks);
      expect(html).toContain('<div class="slack-image">');
      expect(html).toContain('<img src="https://example.com/image.jpg"');
      expect(html).toContain('alt="Example image"');
    });

    it('should parse an image block with title', () => {
      const blocks = [
        {
          type: 'image',
          image_url: 'https://example.com/image.jpg',
          alt_text: 'Example image',
          title: {
            type: 'plain_text',
            text: 'Image title',
          },
        },
      ];
      const html = slackBlocksToHtml(blocks);
      expect(html).toContain('<div class="slack-image">');
      expect(html).toContain('<img src="https://example.com/image.jpg"');
      expect(html).toContain('alt="Example image"');
      expect(html).toContain('<div class="slack-image-title">');
      expect(html).toContain('Image title');
    });
  });

  describe('context blocks', () => {
    it('should parse a context block with text', () => {
      const blocks = [
        {
          type: 'context',
          elements: [
            {
              type: 'plain_text',
              text: 'Context text',
            },
          ],
        },
      ];
      const html = slackBlocksToHtml(blocks);
      expect(html).toContain('<div class="slack-context">');
      expect(html).toContain('<div class="slack-context-text">');
      expect(html).toContain('Context text');
    });

    it('should parse a context block with images', () => {
      const blocks = [
        {
          type: 'context',
          elements: [
            {
              type: 'image',
              image_url: 'https://example.com/image.jpg',
              alt_text: 'Example image',
            },
          ],
        },
      ];
      const html = slackBlocksToHtml(blocks);
      expect(html).toContain('<div class="slack-context">');
      expect(html).toContain('<img src="https://example.com/image.jpg"');
      expect(html).toContain('alt="Example image"');
      expect(html).toContain('class="slack-context-image"');
    });

    it('should parse a context block with mixed elements', () => {
      const blocks = [
        {
          type: 'context',
          elements: [
            {
              type: 'plain_text',
              text: 'Context text',
            },
            {
              type: 'image',
              image_url: 'https://example.com/image.jpg',
              alt_text: 'Example image',
            },
          ],
        },
      ];
      const html = slackBlocksToHtml(blocks);
      expect(html).toContain('<div class="slack-context">');
      expect(html).toContain('<div class="slack-context-text">');
      expect(html).toContain('Context text');
      expect(html).toContain('<img src="https://example.com/image.jpg"');
      expect(html).toContain('alt="Example image"');
      expect(html).toContain('class="slack-context-image"');
    });
  });

  describe('actions blocks', () => {
    it('should parse an actions block with buttons', () => {
      const blocks = [
        {
          type: 'actions',
          elements: [
            {
              type: 'button',
              text: {
                type: 'plain_text',
                text: 'Button 1',
              },
            },
            {
              type: 'button',
              text: {
                type: 'plain_text',
                text: 'Button 2',
              },
            },
          ],
        },
      ];
      const html = slackBlocksToHtml(blocks);
      expect(html).toContain('<div class="slack-actions">');
      expect(html).toContain('<button class="slack-button">');
      expect(html).toContain('Button 1');
      expect(html).toContain('Button 2');
    });

    it('should parse an actions block with select menus', () => {
      const blocks = [
        {
          type: 'actions',
          elements: [
            {
              type: 'select',
              placeholder: {
                type: 'plain_text',
                text: 'Select an option',
              },
              options: [
                {
                  text: {
                    type: 'plain_text',
                    text: 'Option 1',
                  },
                  value: 'option1',
                },
                {
                  text: {
                    type: 'plain_text',
                    text: 'Option 2',
                  },
                  value: 'option2',
                },
              ],
            },
          ],
        },
      ];
      const html = slackBlocksToHtml(blocks);
      expect(html).toContain('<div class="slack-actions">');
      expect(html).toContain('<select class="slack-select">');
      expect(html).toContain('<option disabled selected>');
      expect(html).toContain('Select an option');
      expect(html).toContain('<option value="option1">');
      expect(html).toContain('Option 1');
      expect(html).toContain('<option value="option2">');
      expect(html).toContain('Option 2');
    });
  });

  describe('input blocks', () => {
    it('should parse an input block with text input', () => {
      const blocks = [
        {
          type: 'input',
          label: {
            type: 'plain_text',
            text: 'Input label',
          },
          element: {
            type: 'plain_text_input',
            placeholder: {
              type: 'plain_text',
              text: 'Enter text',
            },
          },
        },
      ];
      const html = slackBlocksToHtml(blocks);
      expect(html).toContain('<div class="slack-input">');
      expect(html).toContain('<label class="slack-input-label">');
      expect(html).toContain('Input label');
      expect(html).toContain('<input type="text"');
      expect(html).toContain('placeholder="Enter text"');
      expect(html).toContain('class="slack-text-input"');
    });

    it('should parse an input block with select menu', () => {
      const blocks = [
        {
          type: 'input',
          label: {
            type: 'plain_text',
            text: 'Select input',
          },
          element: {
            type: 'select',
            placeholder: {
              type: 'plain_text',
              text: 'Select an option',
            },
            options: [
              {
                text: {
                  type: 'plain_text',
                  text: 'Option 1',
                },
                value: 'option1',
              },
              {
                text: {
                  type: 'plain_text',
                  text: 'Option 2',
                },
                value: 'option2',
              },
            ],
          },
        },
      ];
      const html = slackBlocksToHtml(blocks);
      expect(html).toContain('<div class="slack-input">');
      expect(html).toContain('<label class="slack-input-label">');
      expect(html).toContain('Select input');
      expect(html).toContain('<select class="slack-select">');
      expect(html).toContain('<option disabled selected>');
      expect(html).toContain('Select an option');
      expect(html).toContain('<option value="option1">');
      expect(html).toContain('Option 1');
      expect(html).toContain('<option value="option2">');
      expect(html).toContain('Option 2');
    });
  });

  describe('markdown conversion', () => {
    it('should convert bold markdown to HTML', () => {
      const blocks = [
        {
          type: 'section',
          text: {
            type: 'mrkdwn',
            text: 'This is *bold* text',
          },
        },
      ];
      const html = slackBlocksToHtml(blocks);
      expect(html).toContain('This is <strong>bold</strong> text');
    });

    it('should convert italic markdown to HTML', () => {
      const blocks = [
        {
          type: 'section',
          text: {
            type: 'mrkdwn',
            text: 'This is _italic_ text',
          },
        },
      ];
      const html = slackBlocksToHtml(blocks);
      expect(html).toContain('This is <em>italic</em> text');
    });

    it('should convert strikethrough markdown to HTML', () => {
      const blocks = [
        {
          type: 'section',
          text: {
            type: 'mrkdwn',
            text: 'This is ~strikethrough~ text',
          },
        },
      ];
      const html = slackBlocksToHtml(blocks);
      expect(html).toContain('This is <del>strikethrough</del> text');
    });

    it('should convert code markdown to HTML', () => {
      const blocks = [
        {
          type: 'section',
          text: {
            type: 'mrkdwn',
            text: 'This is `code` text',
          },
        },
      ];
      const html = slackBlocksToHtml(blocks);
      expect(html).toContain('This is <code>code</code> text');
    });

    it.todo('should convert preformatted markdown to HTML');

    it('should convert links with text to HTML', () => {
      const blocks = [
        {
          type: 'section',
          text: {
            type: 'mrkdwn',
            text: 'This is a <https://example.com|link>',
          },
        },
      ];
      const html = slackBlocksToHtml(blocks);
      expect(html).toContain('This is a <a href="https://example.com">link</a>');
    });

    it('should convert plain links to HTML', () => {
      const blocks = [
        {
          type: 'section',
          text: {
            type: 'mrkdwn',
            text: 'This is a <https://example.com>',
          },
        },
      ];
      const html = slackBlocksToHtml(blocks);
      expect(html).toContain('This is a <a href="https://example.com">https://example.com</a>');
    });

    it('should convert user mentions to HTML', () => {
      const blocks = [
        {
          type: 'section',
          text: {
            type: 'mrkdwn',
            text: 'This mentions <@U12345>',
          },
        },
      ];
      const html = slackBlocksToHtml(blocks);
      expect(html).toContain('This mentions <span class="slack-mention slack-user-mention">@U12345</span>');
    });

    it('should convert channel mentions to HTML', () => {
      const blocks = [
        {
          type: 'section',
          text: {
            type: 'mrkdwn',
            text: 'This mentions <#C12345>',
          },
        },
      ];
      const html = slackBlocksToHtml(blocks);
      expect(html).toContain('This mentions <span class="slack-mention slack-channel-mention">#C12345</span>');
    });

    it('should convert newlines to <br> tags', () => {
      const blocks = [
        {
          type: 'section',
          text: {
            type: 'mrkdwn',
            text: 'Line 1\nLine 2',
          },
        },
      ];
      const html = slackBlocksToHtml(blocks);
      expect(html).toContain('Line 1<br>Line 2');
    });
  });

  describe('HTML escaping', () => {
    it('should escape HTML special characters', () => {
      const blocks = [
        {
          type: 'section',
          text: {
            type: 'plain_text',
            text: '<div>This & that</div>',
          },
        },
      ];
      const html = slackBlocksToHtml(blocks);
      expect(html).toContain('&lt;div&gt;This &amp; that&lt;/div&gt;');
    });

    it('should escape quotes in HTML attributes', () => {
      const blocks = [
        {
          type: 'input',
          element: {
            type: 'plain_text_input',
            placeholder: {
              type: 'plain_text',
              text: 'Enter "quoted" text',
            },
          },
        },
      ];
      const html = slackBlocksToHtml(blocks);
      expect(html).toContain('placeholder="Enter &quot;quoted&quot; text"');
    });
  });
});
