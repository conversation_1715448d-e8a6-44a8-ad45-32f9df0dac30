import { describe, it, expect, beforeEach } from 'vitest';
import { HtmlToSlackBlocks } from '../../../../../src/utils/parsers/slack/html-to-slack-blocks.parser';

describe('HtmlToSlackBlocks', () => {
  describe('constructor', () => {
    it('should initialize with HTML content', () => {
      const html = '<p>Test content</p>';
      const parser = new HtmlToSlackBlocks(html);
      expect(parser).toBeDefined();
    });
  });

  describe('convert', () => {
    it('should convert simple paragraph to Slack blocks', async () => {
      const html = '<p>Test content</p>';
      const parser = new HtmlToSlackBlocks(html);
      const result = await parser.convert();

      expect(result).toBeDefined();
      expect(result.blocks).toBeInstanceOf(Array);
      expect(result.blocks.length).toBeGreaterThan(0);
      expect(result.blocks[0]).toHaveProperty('type');
      expect(result.usersMentioned).toBeInstanceOf(Array);
    });

    it('should handle empty HTML', async () => {
      const html = '';
      const parser = new HtmlToSlackBlocks(html);
      const result = await parser.convert();

      expect(result).toBeDefined();
      expect(result.blocks).toBeInstanceOf(Array);
      expect(result.usersMentioned).toBeInstanceOf(Array);
    });

    it.todo('should extract pre code content');
  });

  describe('processNode', () => {
    let parser: HtmlToSlackBlocks;

    beforeEach(() => {
      parser = new HtmlToSlackBlocks('');
    });

    it.todo('should process paragraphs');

    it.todo('should process headings');

    it.todo('should process unordered lists');

    it.todo('should process ordered lists');

    it.todo('should process blockquotes');

    it.todo('should process code blocks');

    it.todo('should process images');

    it.todo('should process links');

    it.todo('should process user mentions');

    it.todo('should process formatted text');
  });
});
