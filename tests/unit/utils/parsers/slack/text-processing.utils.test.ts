import { describe, it, expect, vi } from 'vitest';
import { processSlackMessageText, DEFAULT_MESSAGE_CONTENT } from '../../../../../src/utils/parsers/slack/text-processing.utils';
import { ILogger } from '../../../../../src/utils/logger';

describe('text-processing.utils', () => {
  describe('processSlackMessageText', () => {
    it('should return default message content when text is empty', () => {
      // Setup
      const mockLogger = {
        debug: vi.fn(),
        log: vi.fn(),
        error: vi.fn(),
        warn: vi.fn(),
      } as unknown as <PERSON>ogger;
      
      // Execute
      const result = processSlackMessageText('', DEFAULT_MESSAGE_CONTENT, mockLogger);
      
      // Verify
      expect(result).toBe(DEFAULT_MESSAGE_CONTENT);
      expect(mockLogger.debug).toHaveBeenCalledWith(
        expect.stringContaining('No text provided, returning default message content'),
      );
    });

    it('should return custom default content when text is empty', () => {
      // Setup
      const customDefault = 'Custom default message';
      
      // Execute
      const result = processSlackMessageText('', customDefault);
      
      // Verify
      expect(result).toBe(customDefault);
    });

    it('should handle mailto links correctly', () => {
      // Setup
      const text = 'Contact <mailto:<EMAIL>|<EMAIL>> for more info';
      const expected = 'Contact <EMAIL> for more info';
      
      // Execute
      const result = processSlackMessageText(text);
      
      // Verify
      expect(result).toBe(expected);
    });

    it('should handle links with email addresses correctly', () => {
      // Setup
      const text = 'Contact <https://example.com|<EMAIL>> for more info';
      const expected = 'Contact <EMAIL> for more info';
      
      // Execute
      const result = processSlackMessageText(text);
      
      // Verify
      expect(result).toBe(expected);
    });

    it('should handle plain email links correctly', () => {
      // Setup
      const text = 'Contact <<EMAIL>> for more info';
      const expected = 'Contact <EMAIL> for more info';
      
      // Execute
      const result = processSlackMessageText(text);
      
      // Verify
      expect(result).toBe(expected);
    });

    it('should handle multiple email formats in the same text', () => {
      // Setup
      const text = 'Contact <mailto:<EMAIL>|<EMAIL>> or <https://example.com|<EMAIL>> or <<EMAIL>>';
      const expected = 'Contact test1@example.<NAME_EMAIL> or <EMAIL>';
      
      // Execute
      const result = processSlackMessageText(text);
      
      // Verify
      expect(result).toBe(expected);
    });

    it('should not modify text without special formatting', () => {
      // Setup
      const text = 'This is a regular text message without any special formatting';
      
      // Execute
      const result = processSlackMessageText(text);
      
      // Verify
      expect(result).toBe(text);
    });
  });
});
