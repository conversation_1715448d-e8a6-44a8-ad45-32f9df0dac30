import { describe, beforeEach, it, expect, vi, afterEach } from 'vitest';
import { TestableSlackRateLimiter } from './test-utils';

describe('SlackRateLimiter', () => {
  let slackRateLimiter: TestableSlackRateLimiter;
  let mockQueue: any;
  
  beforeEach(() => {
    // Create mock queue
    mockQueue = {
      name: 'slack-queue',
      opts: { connection: {} },
      close: vi.fn().mockResolvedValue(undefined),
    };
    
    // Create rate limiter with Slack-specific options
    slackRateLimiter = new TestableSlackRateLimiter(
      mockQueue,
      {
        requestsPerSecond: 50, // Slack tier 2 apps have 50 requests per second
        maxConcurrent: 5,
        retryAttempts: 3,
        retryDelay: 100, // Reduced for faster tests
        methodConfig: {
          'chat.postMessage': {
            requestsPerSecond: 1, // 1 per second
          },
          'conversations.list': {
            requestsPerSecond: 20, // 20 per minute ≈ 0.33 per second
          },
        },
      }
    );
    
    vi.useFakeTimers();
  });
  
  afterEach(() => {
    vi.useRealTimers();
    vi.clearAllMocks();
  });
  
  describe('isRateLimited', () => {
    it('should return false for null or undefined error', () => {
      expect(slackRateLimiter.isRateLimited(null)).toBe(false);
      expect(slackRateLimiter.isRateLimited(undefined)).toBe(false);
    });
    
    it('should detect Slack rate limit from HTTP 429 status', () => {
      expect(slackRateLimiter.isRateLimited({ status: 429 })).toBe(true);
      expect(slackRateLimiter.isRateLimited({ statusCode: 429 })).toBe(true);
    });
    
    it('should detect Slack rate limit from error messages', () => {
      expect(slackRateLimiter.isRateLimited({ message: 'App rate_limited' })).toBe(true);
      expect(slackRateLimiter.isRateLimited({ message: 'Request ratelimited by Slack' })).toBe(true);
      expect(slackRateLimiter.isRateLimited({ message: 'Too many requests' })).toBe(true);
    });
    
    it('should detect Slack API specific rate limit response', () => {
      expect(slackRateLimiter.isRateLimited({ data: { error: 'ratelimited' } })).toBe(true);
    });
    
    it('should return false for other errors', () => {
      expect(slackRateLimiter.isRateLimited({ message: 'Generic error' })).toBe(false);
      expect(slackRateLimiter.isRateLimited({ status: 500 })).toBe(false);
      expect(slackRateLimiter.isRateLimited({ data: { error: 'not_found' } })).toBe(false);
    });
  });
  
  describe('schedule', () => {
    it('should retry on Slack rate limit errors', async () => {
      // Directly override the implementation for this test to be synchronous
      slackRateLimiter.schedule = vi.fn().mockImplementation(async (fn) => {
        try {
          return await fn();
        } catch (error) {
          if (slackRateLimiter.isRateLimited(error)) {
            // Simulate retry without delay
            return await fn();
          }
          throw error;
        }
      });
      
      // Create a function that will fail with rate limit error, then succeed
      const fn = vi.fn()
        .mockRejectedValueOnce({ status: 429 })
        .mockResolvedValueOnce('success');
      
      // Execute the function
      const result = await slackRateLimiter.schedule(fn);
      
      // The function should succeed after retry
      expect(result).toBe('success');
      expect(fn).toHaveBeenCalledTimes(2);
    });
  });
}); 