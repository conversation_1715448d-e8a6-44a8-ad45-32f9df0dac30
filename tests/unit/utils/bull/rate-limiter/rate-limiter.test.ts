import { Test, TestingModule } from '@nestjs/testing';
import { getQueueToken } from '@nestjs/bullmq';
import { vi, describe, beforeEach, it, expect, afterEach } from 'vitest';
import { Queue, Worker, QueueEvents } from 'bullmq';
import { TestableRateLimiter } from './test-utils';
import { RateLimiterOptions } from '../../../../../src/utils/bull/rate-limiter/rate-limiter.types';

// Mock BullMQ dependencies
vi.mock('bullmq', () => {
  return {
    Queue: vi.fn(),
    Worker: vi.fn().mockImplementation(() => ({
      on: vi.fn(),
      close: vi.fn().mockResolvedValue(undefined),
    })),
    QueueEvents: vi.fn().mockImplementation(() => ({
      close: vi.fn().mockResolvedValue(undefined),
    })),
  };
});

// Mock queue for testing
const createMockQueue = () => ({
  name: 'test-queue',
  opts: { connection: {} },
  close: vi.fn().mockResolvedValue(undefined),
}) as unknown as Queue;

class TestRateLimiter extends TestableRateLimiter {
  isRateLimited(error: any): boolean {
    return error?.isRateLimited === true;
  }
  
  // Expose the protected worker event handler for testing
  setupWorkerEventHandlers() {
    return super.setupWorkerEventHandlers();
  }
  
  // Get worker for testing purposes
  getWorker() {
    return this.workerMock;
  }
  
  // Expose executeFn for testing
  async executeFn<T>(fn: () => Promise<T>): Promise<T> {
    try {
      return await fn();
    } catch (error) {
      if (this.isRateLimited(error) && this.options.retryAttempts && this.options.retryAttempts > 0) {
        return fn(); // Retry immediately for testing
      }
      throw error;
    }
  }
}

describe('RateLimiter', () => {
  let rateLimiter: TestRateLimiter;
  let mockQueue: Queue;
  let workerMock: any;
  let queueEventsMock: any;
  
  beforeEach(() => {
    // Create mock queue
    mockQueue = createMockQueue();
    
    // Create mock Worker
    workerMock = {
      on: vi.fn(),
      close: vi.fn().mockResolvedValue(undefined),
    };
    
    // Create mock QueueEvents
    queueEventsMock = {
      close: vi.fn().mockResolvedValue(undefined),
    };
    
    // Set up mocks
    vi.mocked(Worker).mockImplementation(() => workerMock);
    vi.mocked(QueueEvents).mockImplementation(() => queueEventsMock);
    
    // Mock console methods for testing
    vi.spyOn(console, 'log').mockImplementation(() => {});
    vi.spyOn(console, 'warn').mockImplementation(() => {});
    vi.spyOn(console, 'error').mockImplementation(() => {});
    
    // Create rate limiter with reasonable test options
    rateLimiter = new TestRateLimiter(
      mockQueue,
      {
        requestsPerSecond: 100, // Higher value for faster tests
        maxConcurrent: 2,
        retryAttempts: 1,
        retryDelay: 10, // Very short delay for tests
      }
    );
    
    vi.useFakeTimers();
  });
  
  afterEach(() => {
    vi.useRealTimers();
    vi.clearAllMocks();
  });
  
  describe('initialization', () => {
    it('should initialize properly', () => {
      expect(rateLimiter).toBeInstanceOf(TestableRateLimiter);
    });
    
    it('should set up worker event handlers', () => {
      rateLimiter.setupWorkerEventHandlers();
      
      // Verify event handlers were set up
      const worker = rateLimiter.getWorker();
      expect(worker.on).toHaveBeenCalledTimes(3);
      expect(worker.on).toHaveBeenCalledWith('completed', expect.any(Function));
      expect(worker.on).toHaveBeenCalledWith('failed', expect.any(Function));
      expect(worker.on).toHaveBeenCalledWith('error', expect.any(Function));
    });
    
    it('should handle worker events', () => {
      rateLimiter.setupWorkerEventHandlers();
      
      const worker = rateLimiter.getWorker();
      
      // Extract the event handlers
      const completedHandler = vi.mocked(worker.on).mock.calls.find(
        call => call[0] === 'completed'
      )?.[1] as Function;
      
      const failedHandler = vi.mocked(worker.on).mock.calls.find(
        call => call[0] === 'failed'
      )?.[1] as Function;
      
      const errorHandler = vi.mocked(worker.on).mock.calls.find(
        call => call[0] === 'error'
      )?.[1] as Function;
      
      // Call the handlers
      completedHandler?.({ id: '123' });
      failedHandler?.({ id: '456' }, new Error('Test error'));
      errorHandler?.(new Error('Worker error'));
      
      // Verify console logs
      expect(console.log).toHaveBeenCalledWith('Job 123 completed');
      expect(console.warn).toHaveBeenCalledWith('Job 456 failed with error:', expect.any(Error));
      expect(console.error).toHaveBeenCalledWith('Worker error:', expect.any(Error));
    });
  });
  
  describe('isRateLimited', () => {
    it('should correctly identify rate-limited errors', () => {
      expect(rateLimiter.isRateLimited({ isRateLimited: true })).toBe(true);
      expect(rateLimiter.isRateLimited({ isRateLimited: false })).toBe(false);
      expect(rateLimiter.isRateLimited({})).toBe(false);
      expect(rateLimiter.isRateLimited(null)).toBe(false);
    });
  });
  
  describe('enforceRateLimit', () => {
    it('should enforce rate limits (basic case)', async () => {
      // Directly measure function call time
      const start = Date.now();
      await rateLimiter.enforceRateLimit();
      const end = Date.now();
      
      // First call should complete quickly
      expect(end - start).toBeLessThan(5);
    });
    
    it('should apply method-specific rate limits', async () => {
      // Use real timers for this test
      vi.useRealTimers();
      
      // Mock the delay method to immediately resolve
      const mockDelay = vi.fn().mockImplementation(() => Promise.resolve());
      
      // Create a test implementation with the overridden delay
      const TestRateLimiterWithMockDelay = class extends TestRateLimiter {
        // @ts-ignore - override protected method for testing
        delay(ms: number): Promise<void> {
          return mockDelay(ms);
        }
      };
      
      const rateLimiterWithMethod = new TestRateLimiterWithMockDelay(
        createMockQueue(),
        {
          requestsPerSecond: 100,
          maxConcurrent: 2,
          retryAttempts: 1,
          retryDelay: 10,
          methodConfig: {
            'slowMethod': {
              requestsPerSecond: 2, // 500ms between requests
            }
          }
        }
      );
      
      // Call for the first time - should not delay
      await rateLimiterWithMethod.enforceRateLimit('slowMethod');
      expect(mockDelay).not.toHaveBeenCalled();
      
      // Call again immediately - should calculate a delay, but our mock immediately resolves
      await rateLimiterWithMethod.enforceRateLimit('slowMethod');
      expect(mockDelay).toHaveBeenCalledTimes(1);
      
      // The delay should be approximately 500ms (1000ms / 2 req/sec)
      const delayArg = mockDelay.mock.calls[0][0];
      expect(delayArg).toBeGreaterThan(450); // Allow some margin
      expect(delayArg).toBeLessThan(550);
    });
  });
  
  describe('schedule', () => {
    it('should execute function with rate limiting', async () => {
      const fn = vi.fn().mockResolvedValue('result');
      
      const result = await rateLimiter.schedule(fn);
      
      expect(fn).toHaveBeenCalledTimes(1);
      expect(result).toBe('result');
    });
    
    it('should retry on rate limit errors', async () => {
      const fn = vi.fn()
        .mockRejectedValueOnce({ isRateLimited: true })
        .mockResolvedValueOnce('success');
      
      const result = await rateLimiter.executeFn(fn);
      
      expect(result).toBe('success');
      expect(fn).toHaveBeenCalledTimes(2);
    });
    
    it('should pass method to enforceRateLimit when specified', async () => {
      const enforceRateLimit = vi.spyOn(rateLimiter, 'enforceRateLimit');
      const fn = vi.fn().mockResolvedValue('method result');
      
      await rateLimiter.schedule(fn, 'testMethod');
      
      expect(enforceRateLimit).toHaveBeenCalledWith('testMethod');
      expect(fn).toHaveBeenCalledTimes(1);
    });
  });
  
  describe('cleanup', () => {
    it('should close connections on module destroy', async () => {
      await rateLimiter.onModuleDestroy();
      
      expect(mockQueue.close).toHaveBeenCalled();
    });
  });
}); 