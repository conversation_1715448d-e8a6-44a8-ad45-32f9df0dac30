import { vi } from 'vitest';
import { Queue } from 'bullmq';
import { RateLimiterOptions } from '../../../../../src/utils/bull/rate-limiter/rate-limiter.types';

/**
 * RateLimiter for testing purposes
 * This extracts only the essential parts we need to test without using the actual AbstractRateLimiter
 */
export class TestableRateLimiter {
  private defaultLastRequestTime = 0;
  private methodLastRequestTimes: Map<string, number> = new Map();
  protected workerMock = {
    on: vi.fn(),
    close: vi.fn().mockResolvedValue(undefined),
  };
  protected queueEventsMock = {
    close: vi.fn().mockResolvedValue(undefined),
  };

  constructor(
    protected readonly queue: Queue,
    protected readonly options: RateLimiterOptions,
  ) {}

  /**
   * Setup worker event handlers for testing purposes.
   */
  setupWorkerEventHandlers(): void {
    this.workerMock.on('completed', (job) => {
      console.log(`Job ${job.id} completed`);
    });

    this.workerMock.on('failed', (job, error) => {
      console.warn(`Job ${job.id} failed with error:`, error);
    });

    this.workerMock.on('error', (error) => {
      console.error('Worker error:', error);
    });
  }

  /**
   * Get worker mock for testing.
   */
  getWorker() {
    return this.workerMock;
  }

  /**
   * Get queue events mock for testing.
   */
  getQueueEvents() {
    return this.queueEventsMock;
  }

  // Method to check if an error is a rate limit error
  isRateLimited(error: any): boolean {
    return false; // Default implementation, meant to be overridden
  }

  // Expose a testable version of enforceRateLimit
  async enforceRateLimit(method?: string): Promise<void> {
    const now = Date.now();
    const config = method && this.options.methodConfig?.[method];

    if (config) {
      // Use method-specific rate limit
      const lastRequestTime = this.methodLastRequestTimes.get(method) ?? 0;
      const timeSinceLastRequest = now - lastRequestTime;
      const minTimeBetweenRequests = 1000 / config.requestsPerSecond;

      if (timeSinceLastRequest < minTimeBetweenRequests) {
        const delay = minTimeBetweenRequests - timeSinceLastRequest;
        await this.delay(delay);
      }

      this.methodLastRequestTimes.set(method, Date.now());
    } else {
      // Use default rate limit
      const lastRequestTime = this.defaultLastRequestTime;
      const timeSinceLastRequest = now - lastRequestTime;
      const minTimeBetweenRequests = 1000 / this.options.requestsPerSecond;

      if (timeSinceLastRequest < minTimeBetweenRequests) {
        const delay = minTimeBetweenRequests - timeSinceLastRequest;
        await this.delay(delay);
      }

      this.defaultLastRequestTime = Date.now();
    }
  }

  // Simplified schedule method for testing
  async schedule<T>(fn: () => Promise<T>, method?: string): Promise<T> {
    await this.enforceRateLimit(method);
    try {
      return await fn();
    } catch (error) {
      if (this.isRateLimited(error) && this.options.retryAttempts && this.options.retryAttempts > 0) {
        await this.delay(this.options.retryDelay || 100);
        return this.schedule(fn, method);
      }
      throw error;
    }
  }

  // Execute function with retry logic for testing
  async executeFn<T>(fn: () => Promise<T>): Promise<T> {
    try {
      return await fn();
    } catch (error) {
      if (this.isRateLimited(error) && this.options.retryAttempts && this.options.retryAttempts > 0) {
        return fn(); // Retry immediately for testing
      }
      throw error;
    }
  }

  // Utility delay function
  protected delay(ms: number): Promise<void> {
    return new Promise((resolve) => setTimeout(resolve, ms));
  }

  // Mock cleanup method
  async onModuleDestroy() {
    if (this.queue.close) {
      await this.queue.close();
    }
    await this.workerMock.close();
    await this.queueEventsMock.close();
  }
}

/**
 * Test implementation of Slack rate limiter
 */
export class TestableSlackRateLimiter extends TestableRateLimiter {
  isRateLimited(error: any): boolean {
    // Check for Slack rate limiting error patterns
    if (!error) return false;
    
    // Check for HTTP 429 status
    if (error.status === 429 || error.statusCode === 429) return true;
    
    // Check for Slack-specific rate limit error messages
    if (error.message && (
      error.message.includes('rate_limited') ||
      error.message.includes('ratelimited') ||
      error.message.toLowerCase().includes('too many requests')
    )) {
      return true;
    }
    
    // Slack API specific response
    if (error.data && error.data.error === 'ratelimited') return true;
    
    return false;
  }
} 