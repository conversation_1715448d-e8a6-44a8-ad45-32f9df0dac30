import { describe, it, expect, beforeEach, vi, Mock } from 'vitest';
import { SentryService } from '../../../../src/utils/filters/sentry-alerts.filter';
import * as Sentry from '@sentry/node';
import * as rTracer from 'cls-rtracer';

vi.mock('@sentry/node', () => ({
  init: vi.fn(),
  withScope: vi.fn(),
  captureException: vi.fn(),
}));

vi.mock('cls-rtracer', () => ({
  id: vi.fn(),
}));

describe('SentryService', () => {
  let service: SentryService;
  let mockWithScope: Mock;
  let mockScope: any;

  const sentryDsn = 'https://<EMAIL>/123456';
  const appEnv = 'test';
  const appTag = 'test-app';
  const serviceTag = 'test-service';

  beforeEach(() => {
    vi.clearAllMocks();

    mockScope = {
      setLevel: vi.fn(),
      setTag: vi.fn(),
      setTransactionName: vi.fn(),
      setExtra: vi.fn(),
    };

    mockWithScope = Sentry.withScope as Mock;
    mockWithScope.mockImplementation((callback) => callback(mockScope));

    service = new SentryService(sentryDsn, appEnv, appTag, serviceTag);

    vi.spyOn(console, 'error').mockImplementation(() => {});
  });

  it('should initialize Sentry with correct configuration', () => {
    expect(Sentry.init).toHaveBeenCalledWith({
      dsn: sentryDsn,
      environment: appEnv,
    });
  });

  it('should use default environment if not provided', () => {
    const serviceWithDefaultEnv = new SentryService(sentryDsn, '', appTag, serviceTag);

    expect(Sentry.init).toHaveBeenCalledWith({
      dsn: sentryDsn,
      environment: 'development',
    });
  });

  describe('captureException', () => {
    it('should capture exception with basic tags', () => {
      const error = new Error('Test error');

      service.captureException(error);

      expect(mockWithScope).toHaveBeenCalled();
      expect(mockScope.setLevel).toHaveBeenCalledWith('error');
      expect(mockScope.setTag).toHaveBeenCalledWith('app', appTag);
      expect(mockScope.setTag).toHaveBeenCalledWith('service', serviceTag);
      expect(mockScope.setTag).toHaveBeenCalledWith('deployment', appEnv);
      expect(Sentry.captureException).toHaveBeenCalledWith(error);
    });

    it('should include metadata in scope', () => {
      const error = new Error('Test error');
      const meta = {
        tag: 'error-tag',
        errorSubType: 'validation',
        userId: '123',
        context: { key: 'value' },
      };

      service.captureException(error, meta);

      expect(mockScope.setTransactionName).toHaveBeenCalledWith('error-tag');
      expect(mockScope.setTag).toHaveBeenCalledWith('error-tag', true);
      expect(mockScope.setTag).toHaveBeenCalledWith('errorSubType', 'validation');
      expect(mockScope.setExtra).toHaveBeenCalledWith('tag', 'error-tag');
      expect(mockScope.setExtra).toHaveBeenCalledWith('errorSubType', 'validation');
      expect(mockScope.setExtra).toHaveBeenCalledWith('userId', '123');
      expect(mockScope.setExtra).toHaveBeenCalledWith('context', { key: 'value' });
    });

    it('should include request ID if available', () => {
      const error = new Error('Test error');
      const requestId = '12345-request-id';
      (rTracer.id as Mock).mockReturnValue(requestId);

      service.captureException(error);

      expect(mockScope.setTag).toHaveBeenCalledWith('requestId', requestId);
    });

    it('should handle errors during capture and rethrow', () => {
      const error = new Error('Test error');
      const captureError = new Error('Capture failed');
      mockWithScope.mockImplementation(() => {
        throw captureError;
      });

      expect(() => service.captureException(error)).toThrow(captureError);
      expect(console.error).toHaveBeenCalledWith(
        '[Sentry] Failed to capture exception: Capture failed'
      );
    });

    it('should handle non-Error exceptions during capture', () => {
      const error = new Error('Test error');
      mockWithScope.mockImplementation(() => {
        throw 'String error';
      });

      expect(() => service.captureException(error)).toThrow('String error');
    });
  });
});
