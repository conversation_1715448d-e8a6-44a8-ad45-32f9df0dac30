import { describe, it, expect, beforeEach, vi } from 'vitest';
import { HttpExceptionFilter } from '../../../../src/utils/filters/catch-all.filters';
import { HttpException, HttpStatus } from '@nestjs/common';
import { ArgumentsHost } from '@nestjs/common';

describe('HttpExceptionFilter', () => {
  let filter: HttpExceptionFilter<HttpException>;
  let mockResponse: any;
  let mockJson: any;
  let mockStatus: any;
  let mockArgumentsHost: ArgumentsHost;

  beforeEach(() => {
    mockJson = vi.fn().mockReturnThis();
    mockStatus = vi.fn().mockReturnValue({ json: mockJson });
    mockResponse = { status: mockStatus };

    mockArgumentsHost = {
      switchToHttp: vi.fn().mockReturnValue({
        getResponse: vi.fn().mockReturnValue(mockResponse),
        getRequest: vi.fn().mockReturnValue({}),
      }),
      switchToRpc: vi.fn(),
      switchToWs: vi.fn(),
      getArgByIndex: vi.fn(),
      getArgs: vi.fn(),
      getType: vi.fn(),
    };

    filter = new HttpExceptionFilter();

    const mockDate = new Date('2023-01-01T00:00:00.000Z');
    vi.spyOn(global, 'Date').mockImplementation(() => mockDate as any);
  });

  it('should catch HttpException and format response with string message', () => {
    const exception = new HttpException('Test error message', HttpStatus.BAD_REQUEST);

    filter.catch(exception, mockArgumentsHost);

    expect(mockArgumentsHost.switchToHttp).toHaveBeenCalled();
    expect(mockStatus).toHaveBeenCalledWith(HttpStatus.BAD_REQUEST);
    expect(mockJson).toHaveBeenCalledWith({
      message: 'Test error message',
      timestamp: '2023-01-01T00:00:00.000Z',
    });
  });

  it('should catch HttpException and format response with object message', () => {
    const errorResponse = {
      message: 'Test error message',
      error: 'Bad Request',
      statusCode: HttpStatus.BAD_REQUEST,
    };
    const exception = new HttpException(errorResponse, HttpStatus.BAD_REQUEST);

    filter.catch(exception, mockArgumentsHost);

    expect(mockArgumentsHost.switchToHttp).toHaveBeenCalled();
    expect(mockStatus).toHaveBeenCalledWith(HttpStatus.BAD_REQUEST);
    expect(mockJson).toHaveBeenCalledWith({
      ...errorResponse,
      timestamp: '2023-01-01T00:00:00.000Z',
    });
  });
});
