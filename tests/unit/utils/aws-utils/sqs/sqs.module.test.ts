import { describe, it, expect } from 'vitest';
import { SQSModule } from '../../../../../src/utils/aws-utils/sqs/sqs.module';
import { SQSProducerService, SQS_PRODUCER_CONFIG_TOKEN } from '../../../../../src/utils/aws-utils/sqs/sqs-producer.service';
import { SQSConsumerService, SQS_CONSUMER_CONFIG_TOKEN } from '../../../../../src/utils/aws-utils/sqs/sqs-consumer.service';

describe('SQSModule', () => {
  describe('Producer', () => {
    it.todo('should create a dynamic module with SQSProducerService');
  });

  describe('Consumer', () => {
    it.todo('should create a dynamic module with SQSConsumerService');
  });
});
