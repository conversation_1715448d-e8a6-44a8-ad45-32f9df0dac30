import { describe, it, expect, vi, beforeEach, afterEach } from 'vitest';
import { SQSConsumerService } from '../../../../../src/utils/aws-utils/sqs/sqs-consumer.service';
import { SQSClient, ReceiveMessageCommand, DeleteMessageCommand } from '@aws-sdk/client-sqs';
import { SQSMessage } from '../../../../../src/utils/aws-utils/sqs/sqs.interfaces';

// Mock AWS SDK
vi.mock('@aws-sdk/client-sqs', () => {
  return {
    SQSClient: vi.fn().mockImplementation(() => ({
      send: vi.fn(),
    })),
    ReceiveMessageCommand: vi.fn(),
    DeleteMessageCommand: vi.fn(),
  };
});

describe('SQSConsumerService', () => {
  let service: SQSConsumerService;
  let mockConfig: any;
  let mockHandler: any;
  let mockSend: any;

  beforeEach(() => {
    // Reset mocks
    vi.clearAllMocks();

    // Mock console.error to prevent test output pollution
    vi.spyOn(console, 'error').mockImplementation(() => {});

    // Setup mock config
    mockConfig = {
      queueUrl: 'https://sqs.example.com/queue',
      region: 'us-east-1',
      credentials: {
        accessKeyId: 'test-access-key',
        secretAccessKey: 'test-secret-key',
      },
    };

    // Create service instance
    service = new SQSConsumerService(mockConfig);
    
    // Create and assign the mock send function directly to the service
    mockSend = vi.fn();
    (service as any).sqsClient.send = mockSend;
    
    // Setup mock message handler
    mockHandler = vi.fn().mockResolvedValue(undefined);
  });

  afterEach(() => {
    // Stop any running consumer
    service.stopConsumer();
  });

  describe('initialization', () => {
    it('should initialize SQS client with correct config', () => {
      expect(SQSClient).toHaveBeenCalledWith({
        region: 'us-east-1',
        credentials: {
          accessKeyId: 'test-access-key',
          secretAccessKey: 'test-secret-key',
        },
      });
    });

    it('should initialize SQS client in onModuleInit if not already initialized', () => {
      // Reset the SQS client mock
      vi.clearAllMocks();

      // Create a new service with a null SQS client
      const newService = new SQSConsumerService(mockConfig);
      (newService as any).sqsClient = null;

      // Call onModuleInit
      newService.onModuleInit();

      // Verify SQS client was initialized
      expect(SQSClient).toHaveBeenCalledWith({
        region: 'us-east-1',
        credentials: {
          accessKeyId: 'test-access-key',
          secretAccessKey: 'test-secret-key',
        },
      });
    });
  });

  describe('receiveMessages', () => {
    it('should send ReceiveMessageCommand with correct parameters', async () => {
      // Setup mock response
      mockSend.mockResolvedValueOnce({
        Messages: [],
      });

      // Call the method
      await service.receiveMessages();

      // Verify the command was created with correct parameters
      expect(ReceiveMessageCommand).toHaveBeenCalledWith({
        QueueUrl: 'https://sqs.example.com/queue',
        MaxNumberOfMessages: 10,
        WaitTimeSeconds: 20,
        MessageAttributeNames: ['All'],
      });

      // Verify the command was sent
      expect(mockSend).toHaveBeenCalledWith(expect.any(ReceiveMessageCommand));
    });

    it('should transform received messages', async () => {
      // Setup mock response with messages
      mockSend.mockResolvedValueOnce({
        Messages: [
          {
            ReceiptHandle: 'receipt-handle-1',
            Body: JSON.stringify({ data: 'message-1' }),
            MessageAttributes: {
              event_name: { DataType: 'String', StringValue: 'event-1' },
              event_id: { DataType: 'String', StringValue: 'id-1' },
            },
          },
          {
            ReceiptHandle: 'receipt-handle-2',
            Body: JSON.stringify({ data: 'message-2' }),
            MessageAttributes: {
              event_name: { DataType: 'String', StringValue: 'event-2' },
              event_id: { DataType: 'String', StringValue: 'id-2' },
            },
          },
        ],
      });

      // Call the method
      const result = await service.receiveMessages();

      // Verify the result contains transformed messages
      expect(result).toEqual([
        {
          id: 'receipt-handle-1',
          message: { data: 'message-1' },
          messageAttributes: {
            event_name: 'event-1',
            event_id: 'id-1',
          },
        },
        {
          id: 'receipt-handle-2',
          message: { data: 'message-2' },
          messageAttributes: {
            event_name: 'event-2',
            event_id: 'id-2',
          },
        },
      ]);
    });

    it('should return empty array when no messages are received', async () => {
      // Setup mock response with no messages
      mockSend.mockResolvedValueOnce({
        Messages: [],
      });

      // Call the method
      const result = await service.receiveMessages();

      // Verify the result is an empty array
      expect(result).toEqual([]);
    });

    it('should handle errors when receiving messages', async () => {
      // Setup mock to throw an error
      mockSend.mockRejectedValueOnce(new Error('Receive error'));

      // Call the method
      const result = await service.receiveMessages();

      // Verify error was logged
      expect(console.error).toHaveBeenCalledWith(expect.any(Error));

      // Verify the result is undefined
      expect(result).toBeUndefined();
    });
  });

  describe('deleteMessage', () => {
    it('should send DeleteMessageCommand with correct parameters', async () => {
      // Setup mock response
      mockSend.mockResolvedValueOnce({});
      
      // Call the method
      await service.deleteMessage('receipt-handle-123');

      // Verify the command was created with correct parameters
      expect(DeleteMessageCommand).toHaveBeenCalledWith({
        QueueUrl: 'https://sqs.example.com/queue',
        ReceiptHandle: 'receipt-handle-123',
      });

      // Verify the command was sent
      expect(mockSend).toHaveBeenCalledWith(expect.any(DeleteMessageCommand));
    });

    it('should handle errors when deleting messages', async () => {
      // Setup mock to throw an error
      mockSend.mockRejectedValueOnce(new Error('Delete error'));

      // Call the method
      await service.deleteMessage('receipt-handle-123');

      // Verify error was logged
      expect(console.error).toHaveBeenCalledWith(expect.any(Error));
    });
  });

  describe('startConsumer and stopConsumer', () => {
    it('should start and stop polling', async () => {
      // Mock the poll method
      const pollSpy = vi.spyOn(service as any, 'poll').mockResolvedValue(undefined);

      // Start the consumer
      service.startConsumer(mockHandler);

      // Verify polling was started
      expect(pollSpy).toHaveBeenCalled();
      expect((service as any).isPolling).toBe(true);
      expect((service as any).handler).toBe(mockHandler);

      // Stop the consumer
      service.stopConsumer();

      // Verify polling was stopped
      expect((service as any).isPolling).toBe(false);
    });
  });

  describe('processMessage', () => {
    it('should process message and delete it on success', async () => {
      // Setup mock message
      const mockMessage: SQSMessage = {
        id: 'receipt-handle-123',
        message: { data: 'test' },
        messageAttributes: {
          event_name: 'test-event',
          event_id: 'test-id',
        },
      };

      // Setup service with mock handler
      (service as any).handler = mockHandler;

      // Call the method
      await (service as any).processMessage(mockMessage);

      // Verify handler was called with the message
      expect(mockHandler).toHaveBeenCalledWith(mockMessage);

      // Verify message was deleted
      expect(DeleteMessageCommand).toHaveBeenCalledWith({
        QueueUrl: 'https://sqs.example.com/queue',
        ReceiptHandle: 'receipt-handle-123',
      });
    });

    it('should handle errors during message processing', async () => {
      // Setup mock message
      const mockMessage: SQSMessage = {
        id: 'receipt-handle-123',
        message: { data: 'test' },
        messageAttributes: {
          event_name: 'test-event',
          event_id: 'test-id',
        },
      };

      // Setup handler to throw an error
      const errorHandler = vi.fn().mockRejectedValue(new Error('Processing error'));
      (service as any).handler = errorHandler;

      // Call the method
      await (service as any).processMessage(mockMessage);

      // Verify handler was called
      expect(errorHandler).toHaveBeenCalledWith(mockMessage);

      // Verify error was logged
      expect(console.error).toHaveBeenCalledWith(expect.any(Error));

      // Verify message was not deleted
      expect(DeleteMessageCommand).not.toHaveBeenCalled();
    });
  });

  describe('poll', () => {
    it('should continuously poll for messages while isPolling is true', async () => {
      // Setup mock for receiveMessages to return messages once then empty array
      const mockMessages: SQSMessage[] = [
        {
          id: 'receipt-handle-1',
          message: { data: 'test-1' },
          messageAttributes: {
            event_name: 'test-event',
            event_id: 'test-id-1',
          },
        },
        {
          id: 'receipt-handle-2',
          message: { data: 'test-2' },
          messageAttributes: {
            event_name: 'test-event',
            event_id: 'test-id-2',
          },
        },
      ];

      // Mock receiveMessages to return messages once then stop polling
      let callCount = 0;
      vi.spyOn(service, 'receiveMessages').mockImplementation(async () => {
        callCount++;
        if (callCount === 1) {
          return mockMessages;
        }
        // Stop polling after first call
        (service as any).isPolling = false;
        return [];
      });

      // Mock processMessage
      const processMessageSpy = vi.spyOn(service as any, 'processMessage').mockResolvedValue(undefined);

      // Start polling
      (service as any).isPolling = true;
      await (service as any).poll();

      // Verify processMessage was called for each message
      expect(processMessageSpy).toHaveBeenCalledTimes(2);
      expect(processMessageSpy).toHaveBeenCalledWith(mockMessages[0]);
      expect(processMessageSpy).toHaveBeenCalledWith(mockMessages[1]);
    });
  });

  describe('onModuleDestroy', () => {
    it('should stop the consumer', () => {
      // Setup spy on stopConsumer
      const stopConsumerSpy = vi.spyOn(service, 'stopConsumer');

      // Call onModuleDestroy
      service.onModuleDestroy();

      // Verify stopConsumer was called
      expect(stopConsumerSpy).toHaveBeenCalled();
    });
  });
});
