import { describe, it, expect, vi, beforeEach } from 'vitest';
import { SQSProducerService } from '../../../../../src/utils/aws-utils/sqs/sqs-producer.service';
import { SQSClient, SendMessageCommand } from '@aws-sdk/client-sqs';
import { EnforcedMessageAttributes } from '../../../../../src/utils/aws-utils/sqs/sqs.interfaces';

// Mock AWS SDK
vi.mock('@aws-sdk/client-sqs', () => {
  const mockSend = vi.fn().mockResolvedValue({});
  return {
    SQSClient: vi.fn().mockImplementation(() => ({
      send: mockSend,
    })),
    SendMessageCommand: vi.fn(),
  };
});

describe('SQSProducerService', () => {
  let service: SQSProducerService;
  let mockConfig: any;
  let mockSQSClient: any;

  beforeEach(() => {
    // Reset mocks
    vi.clearAllMocks();

    // Setup mock config
    mockConfig = {
      queueUrl: 'https://sqs.example.com/queue',
      region: 'us-east-1',
      credentials: {
        accessKeyId: 'test-access-key',
        secretAccessKey: 'test-secret-key',
      },
    };

    // Create service instance
    service = new SQSProducerService(mockConfig);

    // Initialize the service
    service.onModuleInit();

    // Get reference to the mocked SQS client
    mockSQSClient = (SQSClient as any).mock.instances[0];
    mockSQSClient.send = vi.fn().mockResolvedValue({});
  });

  describe('initialization', () => {
    it('should initialize SQS client with correct config', () => {
      expect(SQSClient).toHaveBeenCalledWith({
        region: 'us-east-1',
        credentials: {
          accessKeyId: 'test-access-key',
          secretAccessKey: 'test-secret-key',
        },
      });
    });
  });

  describe('sendMessage', () => {
    it('should send message with required attributes', async () => {
      // Setup mock message and attributes
      const message = JSON.stringify({ data: 'test-message' });
      const attributes: EnforcedMessageAttributes = {
        event_name: {
          DataType: 'String',
          StringValue: 'test-event',
        },
        event_id: {
          DataType: 'String',
          StringValue: 'test-id',
        },
        custom_attribute: {
          DataType: 'String',
          StringValue: 'custom-value',
        },
      };

      // Call the method
      await service.sendMessage(message, attributes);

      // Verify the command was created with correct parameters
      expect(SendMessageCommand).toHaveBeenCalledWith({
        QueueUrl: 'https://sqs.example.com/queue',
        MessageBody: message,
        MessageAttributes: attributes,
        MessageGroupId: undefined,
      });

      // Verify the command was sent
      expect(mockSQSClient.send).toHaveBeenCalledWith(expect.any(SendMessageCommand));
    });

    it('should send message with message group ID for FIFO queues', async () => {
      // Setup mock message and attributes
      const message = JSON.stringify({ data: 'test-message' });
      const attributes: EnforcedMessageAttributes = {
        event_name: {
          DataType: 'String',
          StringValue: 'test-event',
        },
        event_id: {
          DataType: 'String',
          StringValue: 'test-id',
        },
      };
      const messageGroupId = 'group-123';

      // Call the method
      await service.sendMessage(message, attributes, messageGroupId);

      // Verify the command was created with correct parameters
      expect(SendMessageCommand).toHaveBeenCalledWith({
        QueueUrl: 'https://sqs.example.com/queue',
        MessageBody: message,
        MessageAttributes: attributes,
        MessageGroupId: messageGroupId,
      });
    });

    it('should throw error if event_name is missing', async () => {
      // Setup mock message and attributes without event_name
      const message = JSON.stringify({ data: 'test-message' });
      const attributes = {
        event_id: {
          DataType: 'String',
          StringValue: 'test-id',
        },
      } as unknown as EnforcedMessageAttributes;

      // Call the method and expect it to throw
      await expect(service.sendMessage(message, attributes)).rejects.toThrow(
        "'event_name' and 'event_id' are required in MessageAttributes"
      );

      // Verify the command was not sent
      expect(mockSQSClient.send).not.toHaveBeenCalled();
    });

    it('should throw error if event_id is missing', async () => {
      // Setup mock message and attributes without event_id
      const message = JSON.stringify({ data: 'test-message' });
      const attributes = {
        event_name: {
          DataType: 'String',
          StringValue: 'test-event',
        },
      } as unknown as EnforcedMessageAttributes;

      // Call the method and expect it to throw
      await expect(service.sendMessage(message, attributes)).rejects.toThrow(
        "'event_name' and 'event_id' are required in MessageAttributes"
      );

      // Verify the command was not sent
      expect(mockSQSClient.send).not.toHaveBeenCalled();
    });

    it('should throw error if event_name value is empty', async () => {
      // Setup mock message and attributes with empty event_name
      const message = JSON.stringify({ data: 'test-message' });
      const attributes: EnforcedMessageAttributes = {
        event_name: {
          DataType: 'String',
          StringValue: '',
        },
        event_id: {
          DataType: 'String',
          StringValue: 'test-id',
        },
      };

      // Call the method and expect it to throw
      await expect(service.sendMessage(message, attributes)).rejects.toThrow(
        "'event_name' and 'event_id' are required in MessageAttributes"
      );
    });

    it('should throw error if event_id value is empty', async () => {
      // Setup mock message and attributes with empty event_id
      const message = JSON.stringify({ data: 'test-message' });
      const attributes: EnforcedMessageAttributes = {
        event_name: {
          DataType: 'String',
          StringValue: 'test-event',
        },
        event_id: {
          DataType: 'String',
          StringValue: '',
        },
      };

      // Call the method and expect it to throw
      await expect(service.sendMessage(message, attributes)).rejects.toThrow(
        "'event_name' and 'event_id' are required in MessageAttributes"
      );
    });

    it('should propagate errors from SQS client', async () => {
      // Setup mock message and attributes
      const message = JSON.stringify({ data: 'test-message' });
      const attributes: EnforcedMessageAttributes = {
        event_name: {
          DataType: 'String',
          StringValue: 'test-event',
        },
        event_id: {
          DataType: 'String',
          StringValue: 'test-id',
        },
      };

      // Setup mock to throw an error
      mockSQSClient.send.mockRejectedValueOnce(new Error('SQS error'));

      // Call the method and expect it to throw
      await expect(service.sendMessage(message, attributes)).rejects.toThrow('SQS error');
    });
  });
});
