import { describe, it, expect } from 'vitest';
import { transformSQSMessage } from '../../../../../src/utils/aws-utils/sqs/helper';
import { Message } from '@aws-sdk/client-sqs';

describe('SQS Helper Functions', () => {
  describe('transformSQSMessage', () => {
    it('should transform a valid SQS message with JSON body', () => {
      // Arrange
      const mockMessage: Message = {
        ReceiptHandle: 'receipt-handle-123',
        Body: JSON.stringify({ key: 'value', nested: { prop: 'test' } }),
        MessageAttributes: {
          event_name: {
            DataType: 'String',
            StringValue: 'test_event',
          },
          event_id: {
            DataType: 'String',
            StringValue: 'event-123',
          },
          custom_attribute: {
            DataType: 'String',
            StringValue: 'custom-value',
          },
        },
      };

      // Act
      const result = transformSQSMessage(mockMessage);

      // Assert
      expect(result).toEqual({
        id: 'receipt-handle-123',
        message: { key: 'value', nested: { prop: 'test' } },
        messageAttributes: {
          event_name: 'test_event',
          event_id: 'event-123',
          custom_attribute: 'custom-value',
        },
      });
    });

    it('should transform a valid SQS message with string body', () => {
      // Arrange
      const mockMessage: Message = {
        ReceiptHandle: 'receipt-handle-123',
        Body: 'plain text message',
        MessageAttributes: {
          event_name: {
            DataType: 'String',
            StringValue: 'test_event',
          },
          event_id: {
            DataType: 'String',
            StringValue: 'event-123',
          },
        },
      };

      // Act
      const result = transformSQSMessage(mockMessage);

      // Assert
      expect(result).toEqual({
        id: 'receipt-handle-123',
        message: 'plain text message',
        messageAttributes: {
          event_name: 'test_event',
          event_id: 'event-123',
        },
      });
    });

    it.todo('should handle message with no attributes');

    it('should handle message with invalid JSON body', () => {
      // Arrange
      const mockMessage: Message = {
        ReceiptHandle: 'receipt-handle-123',
        Body: '{invalid json',
        MessageAttributes: {
          event_name: {
            DataType: 'String',
            StringValue: 'test_event',
          },
          event_id: {
            DataType: 'String',
            StringValue: 'event-123',
          },
        },
      };

      // Act
      const result = transformSQSMessage(mockMessage);

      // Assert
      expect(result).toEqual({
        id: 'receipt-handle-123',
        message: '{invalid json',
        messageAttributes: {
          event_name: 'test_event',
          event_id: 'event-123',
        },
      });
    });

    it.todo('should handle message with null or undefined body');

    it.todo('should handle message with non-string attributes');
  });
});
