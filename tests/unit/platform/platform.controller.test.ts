import { HttpException, InternalServerErrorException } from '@nestjs/common';
import { describe, expect, it, vi, beforeEach } from 'vitest';
import { PlatformController } from '../../../src/platform/platform.controller';
import { PlatformService } from '../../../src/platform/services/platform.service';
import { AppInstallation } from '../../../src/platform/interfaces';
import { PlatformWebhookEvent } from '../../../src/platform/type-system';

describe('PlatformController', () => {
  // Create mock functions
  const installAppMock = vi.fn().mockResolvedValue({ ok: true });
  const handlePlatformEventMock = vi.fn().mockResolvedValue({ processed: true });

  // Create the service mock
  const platformServiceMock = {
    installApp: installAppMock,
    handlePlatformEvent: handlePlatformEventMock
  } as unknown as PlatformService;

  // Create controller instance directly with the mocked service
  const controller = new PlatformController(platformServiceMock);

  // Reset mocks before each test
  beforeEach(() => {
    installAppMock.mockClear();
    handlePlatformEventMock.mockClear();
  });

  describe('checkInstallationsHTTPResource', () => {
    it('should return { ok: true }', () => {
      const result = controller.checkInstallationsHTTPResource();
      expect(result).toEqual({ ok: true });
    });
  });

  describe('handleInstallation', () => {
    it('should call platformService.installApp with the correct parameters', async () => {
      // Arrange
      const installationData: AppInstallation = {
        organization_id: 'org123',
        bot_token: 'xoxb-token',
        application_id: 'app123',
        installation_id: 'install123',
        created_by: 'user123',
        created_at: '2023-01-01T00:00:00Z',
        team_ids: ['team123'],
        configuration: {
          optional_settings: {},
          required_settings: {}
        },
        metadata: {
          slack_team_id: 'T12345'
        }
      };

      // Act
      const result = await controller.handleInstallation(installationData);

      // Assert
      expect(installAppMock).toHaveBeenCalledWith(installationData);
      expect(result).toEqual({ ok: true });
    });
  });

  describe('checkEventsHTTPResource', () => {
    it('should return { ok: true }', () => {
      const result = controller.checkEventsHTTPResource();
      expect(result).toEqual({ ok: true });
    });
  });

  describe('handleEvents', () => {
    it('should call platformService.handlePlatformEvent with the correct parameters', async () => {
      // Arrange
      const eventData: PlatformWebhookEvent = {
        xWebhookEvent: true,
        message: {
          actor: {
            email: '<EMAIL>',
            id: 'user123',
            type: 'user'
          },
          eventId: 'evt123',
          eventType: 'test:event',
          orgId: 'org123',
          payload: { data: 'test' },
          timestamp: '2023-01-01T00:00:00Z'
        }
      };

      // Act
      const result = await controller.handleEvents(eventData);

      // Assert
      expect(handlePlatformEventMock).toHaveBeenCalledWith(eventData);
      expect(result).toEqual({ ok: true, data: handlePlatformEventMock.mock.results[0].value });
    });

    it('should pass through HttpException errors from the service', async () => {
      // Arrange
      const eventData: PlatformWebhookEvent = {
        xWebhookEvent: true,
        message: {
          actor: {
            email: '<EMAIL>',
            id: 'user123',
            type: 'user'
          },
          eventId: 'evt123',
          eventType: 'test:event',
          orgId: 'org123',
          payload: { data: 'test' },
          timestamp: '2023-01-01T00:00:00Z'
        }
      };

      const httpError = new HttpException('Bad request', 400);
      handlePlatformEventMock.mockRejectedValueOnce(httpError);

      // Act & Assert
      await expect(controller.handleEvents(eventData)).rejects.toThrow(HttpException);
      expect(handlePlatformEventMock).toHaveBeenCalledWith(eventData);
    });

    it('should wrap non-HttpException errors in InternalServerErrorException', async () => {
      // Arrange
      const eventData: PlatformWebhookEvent = {
        xWebhookEvent: true,
        message: {
          actor: {
            email: '<EMAIL>',
            id: 'user123',
            type: 'user'
          },
          eventId: 'evt123',
          eventType: 'test:event',
          orgId: 'org123',
          payload: { data: 'test' },
          timestamp: '2023-01-01T00:00:00Z'
        }
      };

      const error = new Error('Something went wrong');
      handlePlatformEventMock.mockRejectedValueOnce(error);

      // Act & Assert
      await expect(controller.handleEvents(eventData)).rejects.toThrow(InternalServerErrorException);
      expect(handlePlatformEventMock).toHaveBeenCalledWith(eventData);
    });
  });
}); 