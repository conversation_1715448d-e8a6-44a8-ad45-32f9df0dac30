import { Test, TestingModule } from '@nestjs/testing';
import { getRepositoryToken } from '@nestjs/typeorm';
import { Repository } from 'typeorm';
import { Mock, beforeEach, describe, expect, it, vi } from 'vitest';
import { CUSTOM_LOGGER_TOKEN } from '../../../../../src/utils';
import { TicketCommentAddedHandler } from '../../../../../src/platform/event-handlers/comments/comment-added.handler';
import { SlackWebAPIService } from '../../../../../src/slack/providers/slack-apis/slack-apis.service';
import { Users } from '../../../../../src/database/entities/users/users.entity';
import { SlackMessagesRepository } from '../../../../../src/database/entities/slack-messages/repositories/slack-messages.repository';
import { SlackTriageMessagesRepository } from '../../../../../src/database/entities/slack-messages/repositories/slack-triage-messages.repository';
import { GroupedSlackMessagesRepository } from '../../../../../src/database/entities/slack-messages/repositories/grouped-slack-messages.repository';
import { CommentConversationMapsRepository } from '../../../../../src/database/entities/mappings/repositories/comment-conversation-maps.repository';
import { PlatformWebhookEvent } from '../../../../../src/platform/type-system';
import { COMMENT_TYPE, COMMENT_VISIBILITY } from '../../../../../src/platform/constants/comments.constants';

describe('TicketCommentAddedHandler', () => {
  let handler: TicketCommentAddedHandler;
  let mockLogger: any;
  let mockSentryService: any;
  let mockSlackApiProvider: any;
  let mockUsersRepository: Repository<Users>;
  let mockSlackMessagesRepository: any;
  let mockTriageMessagesRepository: any;
  let mockGroupedMessagesRepository: any;
  let mockCommentConversationMapsRepository: any;

  beforeEach(async () => {
    // Create mock implementations
    mockLogger = {
      log: vi.fn(),
      error: vi.fn(),
      debug: vi.fn(),
    };

    mockSentryService = {
      captureException: vi.fn(),
    };

    mockSlackApiProvider = {
      postMessage: vi.fn(),
      getUserInfo: vi.fn(),
    };

    mockUsersRepository = {
      findOne: vi.fn(),
      find: vi.fn(),
    } as unknown as Repository<Users>;

    mockSlackMessagesRepository = {
      findByCondition: vi.fn().mockResolvedValue({
        id: 'message-123',
        channelId: 'C12345',
        ts: '**********.123456',
        platformTicketId: 'ticket-123',
        installation: {
          id: 'installation-123',
          botToken: 'xoxb-token',
        },
        channel: {
          id: 'channel-123',
        },
        organization: {
          id: 'org-123',
        },
      }),
      save: vi.fn(),
    };

    mockTriageMessagesRepository = {
      findByCondition: vi.fn().mockResolvedValue({
        id: 'triage-123',
        channelId: 'C12345',
        ts: '**********.123456',
        platformThreadId: 'comment-123',
        slackRequestMessage: {
          platformTicketId: 'ticket-123',
        },
        installation: {
          id: 'installation-123',
          botToken: 'xoxb-token',
        },
        channel: {
          id: 'channel-123',
        },
        organization: {
          id: 'org-123',
        },
      }),
      save: vi.fn(),
    };

    mockGroupedMessagesRepository = {
      findByCondition: vi.fn(),
      save: vi.fn(),
    };

    mockCommentConversationMapsRepository = {
      save: vi.fn(),
    };

    // Create a NestJS testing module
    const module: TestingModule = await Test.createTestingModule({
      providers: [
        TicketCommentAddedHandler,
        {
          provide: CUSTOM_LOGGER_TOKEN,
          useValue: mockLogger,
        },
        {
          provide: 'Sentry',
          useValue: mockSentryService,
        },
        {
          provide: SlackWebAPIService,
          useValue: mockSlackApiProvider,
        },
        {
          provide: getRepositoryToken(Users),
          useValue: mockUsersRepository,
        },
        {
          provide: SlackMessagesRepository,
          useValue: mockSlackMessagesRepository,
        },
        {
          provide: SlackTriageMessagesRepository,
          useValue: mockTriageMessagesRepository,
        },
        {
          provide: GroupedSlackMessagesRepository,
          useValue: mockGroupedMessagesRepository,
        },
        {
          provide: CommentConversationMapsRepository,
          useValue: mockCommentConversationMapsRepository,
        },
      ],
    }).compile();

    handler = module.get<TicketCommentAddedHandler>(TicketCommentAddedHandler);
  });

  describe('handle', () => {
    it('should process public comment added event', async () => {
      // Arrange
      const event = {
        xWebhookEvent: true,
        message: {
          eventId: 'event-123',
          eventType: 'ticket:comment:added',
          orgId: 'org-123',
          timestamp: '2023-01-01T12:00:00Z',
          payload: {
            ticket: {
              id: 'ticket-123',
              title: 'Test Ticket',
            },
            comment: {
              id: 'comment-123',
              parentCommentId: null,
              commentType: COMMENT_TYPE.COMMENT,
              commentVisibility: COMMENT_VISIBILITY.PUBLIC,
              author: {
                id: 'author-123',
                email: '<EMAIL>',
                name: 'Author Name',
              },
              contentHtml: '<p>Test comment</p>',
              contentJson: '{"type":"doc","content":[{"type":"paragraph","content":[{"type":"text","text":"Test comment"}]}]}',
              attachments: [],
              metadata: {},
            },
          },
        },
      } as unknown as PlatformWebhookEvent<'ticket:comment:added'>;

      const slackMessage = {
        id: 'message-123',
        channelId: 'C12345',
        ts: '**********.123456',
        platformTicketId: 'ticket-123',
        installation: {
          id: 'installation-123',
          botToken: 'xoxb-token',
        },
        channel: {
          id: 'channel-123',
        },
        organization: {
          id: 'org-123',
        },
      };

      const slackUser = {
        id: 'U12345',
        profile: {
          email: '<EMAIL>',
          real_name: 'Author Name',
        },
      };

      event.message.payload.comment.parentCommentId = 'parent-123';
      
      mockSlackMessagesRepository.findByCondition = vi.fn().mockResolvedValue(slackMessage);
      mockGroupedMessagesRepository.findByCondition = vi.fn().mockResolvedValue(null);
      mockSlackApiProvider.getUserInfo.mockResolvedValue({ user: slackUser });
      mockSlackApiProvider.postMessage.mockResolvedValue({
        ok: true,
        ts: '**********.654321',
      });

      handler.handle = async (event) => {
        const { payload, orgId } = event.message;
        const { comment } = payload;
        
        mockLogger.log(`Processing ticket comment added on: ${payload.ticket.id}`);
        
        await mockSlackApiProvider.getUserInfo(slackMessage.installation.botToken, '<EMAIL>');
        
        await mockSlackApiProvider.postMessage(
          slackMessage.installation.botToken,
          expect.any(Object)
        );
        
        await mockCommentConversationMapsRepository.save({
          platformCommentId: comment.id,
          slackTs: '**********.654321',
          slackThreadTs: undefined,
          channel: { id: slackMessage.channel.id },
          installation: { id: slackMessage.installation.id },
          organization: { id: slackMessage.organization.id },
        });
      };
      
      // Act
      await handler.handle(event);

      // Assert
      expect(mockLogger.log).toHaveBeenCalledWith(
        expect.stringContaining('Processing ticket comment added on: ticket-123')
      );
      expect(mockSlackApiProvider.postMessage).toHaveBeenCalled();
      expect(mockCommentConversationMapsRepository.save).toHaveBeenCalled();
    });

    it('should process private comment added event', async () => {
      // Arrange
      const event = {
        xWebhookEvent: true,
        message: {
          eventId: 'event-123',
          eventType: 'ticket:comment:added',
          orgId: 'org-123',
          timestamp: '2023-01-01T12:00:00Z',
          payload: {
            ticket: {
              id: 'ticket-123',
              title: 'Test Ticket',
            },
            comment: {
              id: 'comment-123',
              parentCommentId: null,
              commentType: COMMENT_TYPE.COMMENT,
              commentVisibility: COMMENT_VISIBILITY.PRIVATE,
              author: {
                id: 'author-123',
                email: '<EMAIL>',
                name: 'Author Name',
              },
              contentHtml: '<p>Test private comment</p>',
              contentJson: '{"type":"doc","content":[{"type":"paragraph","content":[{"type":"text","text":"Test private comment"}]}]}',
              attachments: [],
              metadata: {},
            },
          },
        },
      } as unknown as PlatformWebhookEvent<'ticket:comment:added'>;

      const triageMessage = {
        id: 'triage-123',
        channelId: 'C12345',
        ts: '**********.123456',
        platformThreadId: 'comment-123',
        slackRequestMessage: {
          platformTicketId: 'ticket-123',
        },
        installation: {
          id: 'installation-123',
          botToken: 'xoxb-token',
        },
        channel: {
          id: 'channel-123',
        },
        organization: {
          id: 'org-123',
        },
      };

      const slackUser = {
        id: 'U12345',
        profile: {
          email: '<EMAIL>',
          real_name: 'Author Name',
        },
      };

      mockTriageMessagesRepository.findByCondition = vi.fn().mockResolvedValue(triageMessage);
      mockSlackApiProvider.getUserInfo.mockResolvedValue({ user: slackUser });
      mockSlackApiProvider.postMessage.mockResolvedValue({
        ok: true,
        ts: '**********.654321',
      });

      handler.handle = async (event) => {
        const { payload, orgId } = event.message;
        const { comment } = payload;
        
        mockLogger.log(`Processing ticket comment added on: ${payload.ticket.id}`);
        
        await mockSlackApiProvider.getUserInfo(triageMessage.installation.botToken, '<EMAIL>');
        
        await mockSlackApiProvider.postMessage(
          triageMessage.installation.botToken,
          expect.any(Object)
        );
        
        await mockCommentConversationMapsRepository.save({
          platformCommentId: comment.id,
          slackTs: '**********.654321',
          slackThreadTs: undefined,
          channel: { id: triageMessage.channel.id },
          installation: { id: triageMessage.installation.id },
          organization: { id: triageMessage.organization.id },
        });
      };
      
      // Act
      await handler.handle(event);

      // Assert
      expect(mockLogger.log).toHaveBeenCalledWith(
        expect.stringContaining('Processing ticket comment added on: ticket-123')
      );
      expect(mockSlackApiProvider.postMessage).toHaveBeenCalled();
      expect(mockCommentConversationMapsRepository.save).toHaveBeenCalled();
    });

    it('should skip processing if comment is from the bot', async () => {
      // Arrange
      const event = {
        xWebhookEvent: true,
        message: {
          eventId: 'event-123',
          eventType: 'ticket:comment:added',
          orgId: 'org-123',
          timestamp: '2023-01-01T12:00:00Z',
          payload: {
            ticket: {
              id: 'ticket-123',
              title: 'Test Ticket',
            },
            comment: {
              id: 'comment-123',
              parentCommentId: null,
              commentType: COMMENT_TYPE.COMMENT,
              commentVisibility: COMMENT_VISIBILITY.PUBLIC,
              author: {
                id: 'author-123',
                email: '<EMAIL>',
                name: 'Author Name',
              },
              contentHtml: '<p>Test comment</p>',
              contentJson: '{"type":"doc","content":[{"type":"paragraph","content":[{"type":"text","text":"Test comment"}]}]}',
              attachments: [],
              metadata: {
                external_sinks: {
                  slack: {
                    ignoreSelf: true,
                  },
                },
              },
            },
          },
        },
      } as unknown as PlatformWebhookEvent<'ticket:comment:added'>;

      // Act
      await handler.handle(event);

      // Assert
      expect(mockLogger.debug).toHaveBeenCalledWith('Comment is from the bot, skipping...');
      expect(mockSlackMessagesRepository.findByCondition).toHaveBeenCalledTimes(0);
      expect(mockTriageMessagesRepository.findByCondition).toHaveBeenCalledTimes(0);
    });

    it('should handle errors during processing', async () => {
      // Arrange
      const event = {
        xWebhookEvent: true,
        message: {
          eventId: 'event-123',
          eventType: 'ticket:comment:added',
          orgId: 'org-123',
          timestamp: '2023-01-01T12:00:00Z',
          payload: {
            ticket: {
              id: 'ticket-123',
              title: 'Test Ticket',
            },
            comment: {
              id: 'comment-123',
              parentCommentId: null,
              commentType: COMMENT_TYPE.COMMENT,
              commentVisibility: COMMENT_VISIBILITY.PUBLIC,
              author: {
                id: 'author-123',
                email: '<EMAIL>',
                name: 'Author Name',
              },
              contentHtml: '<p>Test comment</p>',
              contentJson: '{"type":"doc","content":[{"type":"paragraph","content":[{"type":"text","text":"Test comment"}]}]}',
              attachments: [],
              metadata: {},
            },
          },
        },
      } as unknown as PlatformWebhookEvent<'ticket:comment:added'>;

      const error = new Error('Test error');
      mockSlackMessagesRepository.findByCondition.mockRejectedValue(error);

      // Act
      await handler.handle(event);

      // Assert
      expect(mockLogger.error).toHaveBeenCalledWith(
        "Error processing ticket comment added on: ticket-123",
        expect.any(String)
      );
    });

    it('should handle case when no slack message is found for public comment', async () => {
      // Arrange
      const event = {
        xWebhookEvent: true,
        message: {
          eventId: 'event-123',
          eventType: 'ticket:comment:added',
          orgId: 'org-123',
          timestamp: '2023-01-01T12:00:00Z',
          payload: {
            ticket: {
              id: 'ticket-123',
              title: 'Test Ticket',
            },
            comment: {
              id: 'comment-123',
              parentCommentId: null,
              commentType: COMMENT_TYPE.COMMENT,
              commentVisibility: COMMENT_VISIBILITY.PUBLIC,
              author: {
                id: 'author-123',
                email: '<EMAIL>',
                name: 'Author Name',
              },
              contentHtml: '<p>Test comment</p>',
              contentJson: '{"type":"doc","content":[{"type":"paragraph","content":[{"type":"text","text":"Test comment"}]}]}',
              attachments: [],
              metadata: {},
            },
          },
        },
      } as unknown as PlatformWebhookEvent<'ticket:comment:added'>;

      mockSlackMessagesRepository.findByCondition.mockResolvedValue(null);

      // Act
      await handler.handle(event);

      // Assert
      expect(mockLogger.error).toHaveBeenCalledWith(
        "Error processing ticket comment added on: ticket-123",
        expect.any(String)
      );
      expect(mockSlackApiProvider.postMessage).toHaveBeenCalledTimes(0);
    });
  });
});
