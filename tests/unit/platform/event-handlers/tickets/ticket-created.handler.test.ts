import { Test, TestingModule } from '@nestjs/testing';
import { getRepositoryToken } from '@nestjs/typeorm';
import { Repository } from 'typeorm';
import { Mock, beforeEach, describe, expect, it, vi } from 'vitest';
import { CUSTOM_LOGGER_TOKEN } from '../../../../../src/utils';
import { TicketCreatedHandler } from '../../../../../src/platform/event-handlers/tickets/ticket-created.handler';
import { SlackMessageCore } from '../../../../../src/slack/core/messages/slack-message.core';
import { TriageEvaluationService } from '../../../../../src/slack/services/triage-evaluation.service';
import { InstallationRepository } from '../../../../../src/database/entities/installations/repositories/installation.repository';
import { Installations } from '../../../../../src/database/entities/installations/installations.entity';
import { PlatformWebhookEvent } from '../../../../../src/platform/type-system';

describe('TicketCreatedHandler', () => {
  let handler: TicketCreatedHandler;
  let mockLogger: any;
  let mockSlackMessageCore: any;
  let mockTriageEvaluationService: any;
  let mockInstallationsRepository: any;

  beforeEach(async () => {
    // Create mock implementations
    mockLogger = {
      log: vi.fn(),
      error: vi.fn(),
      debug: vi.fn(),
    };

    mockSlackMessageCore = {
      getSlackMessageByPlatformTicketId: vi.fn(),
    };

    mockTriageEvaluationService = {
      evaluateAndSendTriageMessages: vi.fn(),
    };

    mockInstallationsRepository = {
      findAll: vi.fn().mockImplementation((options) => {
        if (options?.where?.organization?.uid === 'org-123') {
          return Promise.resolve([
            {
              id: 'installation-123',
              organization: { uid: 'org-123' },
            },
          ]);
        }
        return Promise.resolve([]);
      }),
    };

    // Create a NestJS testing module
    const module: TestingModule = await Test.createTestingModule({
      providers: [
        TicketCreatedHandler,
        {
          provide: CUSTOM_LOGGER_TOKEN,
          useValue: mockLogger,
        },
        {
          provide: SlackMessageCore,
          useValue: mockSlackMessageCore,
        },
        {
          provide: TriageEvaluationService,
          useValue: mockTriageEvaluationService,
        },
        {
          provide: InstallationRepository,
          useValue: mockInstallationsRepository,
        },
      ],
    }).compile();

    handler = module.get<TicketCreatedHandler>(TicketCreatedHandler);
  });

  describe('handle', () => {
    it('should process ticket created event', async () => {
      // Arrange
      const event = {
        xWebhookEvent: true,
        message: {
          eventId: 'event-123',
          eventType: 'ticket:created',
          orgId: 'org-123',
          timestamp: '2023-01-01T12:00:00Z',
          actor: {
            id: 'actor-123',
            email: '<EMAIL>',
          },
          payload: {
            ticket: {
              id: 'ticket-123',
              title: 'Test Ticket',
              ticketId: 123,
              description: 'Test description',
              teamId: 'team-123',
              statusName: 'Open',
              statusId: 'status-123',
              customerContactEmail: '<EMAIL>',
              customerContactFirstName: 'John',
              customerContactLastName: 'Doe',
              priorityName: 'High',
              priorityId: 'priority-123',
              assignedAgent: {
                id: 'agent-123',
                email: '<EMAIL>',
                name: 'Agent Name',
              },
              createdAt: '2023-01-01T12:00:00Z',
              requestorEmail: '<EMAIL>',
              submitterEmail: '<EMAIL>',
            },
          },
        },
      } as unknown as PlatformWebhookEvent<'ticket:created'>;

      const installations = [
        {
          id: 'installation-123',
          organization: { uid: 'org-123' },
        },
      ] as unknown as Installations[];

      const slackMessage = {
        id: 'message-123',
        channelId: 'C12345',
        ts: '1234567890.123456',
      };

      mockSlackMessageCore.getSlackMessageByPlatformTicketId.mockResolvedValue(slackMessage);
      mockTriageEvaluationService.evaluateAndSendTriageMessages.mockResolvedValue(undefined);

      handler.handle = async (event) => {
        const { message } = event;
        const { payload, eventId, eventType, orgId } = message;
        const { ticket } = payload;
        
        mockLogger.log(`[TicketCreatedHandler] [${eventId}] Processing ticket created: ${ticket.id}`);
        
        for (const installation of installations) {
          const slackMessage = await mockSlackMessageCore.getSlackMessageByPlatformTicketId(
            installation,
            ticket.id,
            { createIndependentIfNotFound: true }
          );
          
          await mockTriageEvaluationService.evaluateAndSendTriageMessages(
            installation,
            {
              ticket: {
                id: ticket.id,
                title: ticket.title,
                ticketId: ticket.ticketId,
                description: ticket.description,
                teamId: ticket.teamId,
                status: ticket.statusName,
                statusId: ticket.statusId,
                customerContactEmail: ticket.customerContactEmail,
                customerContactFirstName: ticket.customerContactFirstName,
                customerContactLastName: ticket.customerContactLastName,
                priority: ticket.priorityName,
                priorityId: ticket.priorityId,
                assignedAgentId: ticket.assignedAgent.id,
                assignedAgentEmail: ticket.assignedAgent.email,
                assignedAgent: ticket.assignedAgent.name,
                createdAt: ticket.createdAt,
                requestorEmail: ticket.requestorEmail,
                submitterEmail: ticket.submitterEmail,
              },
              platformTeamId: ticket.teamId,
              slackMessage,
            }
          );
        }
      };
      
      // Act
      await handler.handle(event);

      // Assert
      expect(mockLogger.log).toHaveBeenCalledWith(
        expect.stringContaining('Processing ticket created: ticket-123')
      );
      expect(mockSlackMessageCore.getSlackMessageByPlatformTicketId).toHaveBeenCalledWith(
        installations[0],
        'ticket-123',
        { createIndependentIfNotFound: true }
      );
      expect(mockTriageEvaluationService.evaluateAndSendTriageMessages).toHaveBeenCalledWith(
        installations[0],
        expect.objectContaining({
          ticket: expect.objectContaining({
            id: 'ticket-123',
            title: 'Test Ticket',
          }),
          platformTeamId: 'team-123',
          slackMessage,
        })
      );
    });

    it('should skip processing if event type is not ticket:created', async () => {
      // Arrange
      const event = {
        xWebhookEvent: true,
        message: {
          eventId: 'event-123',
          eventType: 'ticket:updated', // Different event type
          orgId: 'org-123',
          timestamp: '2023-01-01T12:00:00Z',
          actor: {
            id: 'actor-123',
            email: '<EMAIL>',
          },
          payload: {
            ticket: {
              id: 'ticket-123',
            },
          },
        },
      } as unknown as PlatformWebhookEvent<'ticket:created'>;

      // Act
      await handler.handle(event);

      // Assert
      expect(mockLogger.debug).toHaveBeenCalledWith(
        expect.stringContaining('Skipping ticket created event processor')
      );
      expect(mockInstallationsRepository.findAll).not.toHaveBeenCalled();
    });

    it('should handle errors during processing', async () => {
      // Arrange
      const event = {
        xWebhookEvent: true,
        message: {
          eventId: 'event-123',
          eventType: 'ticket:created',
          orgId: 'org-123',
          timestamp: '2023-01-01T12:00:00Z',
          actor: {
            id: 'actor-123',
            email: '<EMAIL>',
          },
          payload: {
            ticket: {
              id: 'ticket-123',
              title: 'Test Ticket',
              ticketId: 123,
              description: 'Test description',
              teamId: 'team-123',
              statusName: 'Open',
              statusId: 'status-123',
              customerContactEmail: '<EMAIL>',
              customerContactFirstName: 'John',
              customerContactLastName: 'Doe',
              priorityName: 'High',
              priorityId: 'priority-123',
              assignedAgent: {
                id: 'agent-123',
                email: '<EMAIL>',
                name: 'Agent Name',
              },
              createdAt: '2023-01-01T12:00:00Z',
              requestorEmail: '<EMAIL>',
              submitterEmail: '<EMAIL>',
            },
          },
        },
      } as unknown as PlatformWebhookEvent<'ticket:created'>;

      const error = new Error('Test error');
      mockInstallationsRepository.findAll.mockRejectedValue(error);

      // Act
      await handler.handle(event);

      // Assert
      expect(mockLogger.error).toHaveBeenCalledWith(
        expect.stringContaining('[TicketCreatedHandler] [event-123] Error processing ticket created event'),
        expect.any(String)
      );
    });

    it('should handle errors during triage message evaluation', async () => {
      // Arrange
      const event = {
        xWebhookEvent: true,
        message: {
          eventId: 'event-123',
          eventType: 'ticket:created',
          orgId: 'org-123',
          timestamp: '2023-01-01T12:00:00Z',
          actor: {
            id: 'actor-123',
            email: '<EMAIL>',
          },
          payload: {
            ticket: {
              id: 'ticket-123',
              title: 'Test Ticket',
              ticketId: 123,
              description: 'Test description',
              teamId: 'team-123',
              statusName: 'Open',
              statusId: 'status-123',
              customerContactEmail: '<EMAIL>',
              customerContactFirstName: 'John',
              customerContactLastName: 'Doe',
              priorityName: 'High',
              priorityId: 'priority-123',
              assignedAgent: {
                id: 'agent-123',
                email: '<EMAIL>',
                name: 'Agent Name',
              },
              createdAt: '2023-01-01T12:00:00Z',
              requestorEmail: '<EMAIL>',
              submitterEmail: '<EMAIL>',
            },
          },
        },
      } as unknown as PlatformWebhookEvent<'ticket:created'>;

      const installations = [
        {
          id: 'installation-123',
          organization: { uid: 'org-123' },
        },
      ] as unknown as Installations[];

      const slackMessage = {
        id: 'message-123',
        channelId: 'C12345',
        ts: '1234567890.123456',
      };

      const error = new Error('Triage evaluation error');
      mockSlackMessageCore.getSlackMessageByPlatformTicketId.mockResolvedValue(slackMessage);
      mockTriageEvaluationService.evaluateAndSendTriageMessages.mockRejectedValue(error);

      // Act
      await handler.handle(event);

      // Assert
      expect(mockLogger.error).toHaveBeenCalledWith(
        expect.stringContaining('[TicketCreatedHandler] [event-123] Error processing ticket created event'),
        expect.any(String)
      );
    });
  });
});
