import { Repository } from 'typeorm';
import { Test, TestingModule } from '@nestjs/testing';
import { beforeEach, describe, expect, it, vi } from 'vitest';
import { PlatformService } from '../../../../src/platform/services/platform.service';
import { AppInstallation } from '../../../../src/platform/interfaces';
import { Organizations } from '../../../../src/database/entities';
import { ILogger } from '../../../../src/utils/logger';
import { BadRequestException } from '@nestjs/common';
import { PlatformWebhookEvent } from '../../../../src/platform/type-system';
import { CUSTOM_LOGGER_TOKEN } from '../../../../src/utils/logger';

describe('PlatformService', () => {
  let service: PlatformService;
  let mockLogger: ILogger;
  let mockOrganizationsRepository: Repository<Organizations>;
  let mockEventHandlers: Map<string, any>;

  beforeEach(async () => {
    // Reset mocks
    vi.resetAllMocks();

    // Create mock logger
    mockLogger = {
      log: vi.fn(),
      error: vi.fn(),
      warn: vi.fn(),
      debug: vi.fn(),
      verbose: vi.fn(),
    } as unknown as ILogger;

    // Create mock organizations repository
    mockOrganizationsRepository = {
      findOne: vi.fn(),
      save: vi.fn(),
      update: vi.fn(),
    } as unknown as Repository<Organizations>;

    // Create mock event handlers
    mockEventHandlers = new Map<string, any>();

    // Create a test module
    const module: TestingModule = await Test.createTestingModule({
      providers: [
        PlatformService,
        {
          provide: CUSTOM_LOGGER_TOKEN,
          useValue: mockLogger,
        },
        {
          provide: getRepositoryToken(Organizations),
          useValue: mockOrganizationsRepository,
        },
      ],
    }).compile();

    // Get service instance
    service = module.get<PlatformService>(PlatformService);
  });

  describe('installApp', () => {
    it('should update an existing organization with new metadata', async () => {
      // Arrange
      const installation: AppInstallation = {
        organization_id: 'org123',
        bot_token: 'xoxb-token',
        application_id: 'app123',
        installation_id: 'install123',
        created_by: 'user123',
        created_at: '2023-01-01T00:00:00Z',
        team_ids: ['team123'],
        configuration: {
          optional_settings: {},
          required_settings: {},
        },
        metadata: { 
          slack_team_id: 'T12345'
        }
      };

      const existingOrg = {
        uid: 'org123',
        installingUserId: 'oldUser',
        metadata: {
          oldKey: 'oldValue',
        },
      };

      (mockOrganizationsRepository.findOne as any).mockResolvedValue(existingOrg);

      // Act
      const result = await service.installApp(installation);

      // Assert
      expect(mockOrganizationsRepository.findOne).toHaveBeenCalledWith({
        where: { uid: installation.organization_id },
      });
      
      expect(mockOrganizationsRepository.update).toHaveBeenCalledWith(
        { uid: installation.organization_id },
        {
          apiKey: installation.bot_token,
          metadata: {
            oldKey: 'oldValue',
            applicationId: installation.application_id,
            createdBy: installation.created_by,
            installationId: installation.installation_id,
          },
        },
      );
      
      expect(result).toEqual({ ok: true });
    });

    it('should create a new organization if it does not exist', async () => {
      // Arrange
      const installation: AppInstallation = {
        organization_id: 'newOrg123',
        bot_token: 'xoxb-token',
        application_id: 'app123',
        installation_id: 'install123',
        created_by: 'user123',
        created_at: '2023-01-01T00:00:00Z',
        team_ids: ['team123'],
        configuration: {
          optional_settings: {},
          required_settings: {},
        },
        metadata: { 
          slack_team_id: 'T12345'
        }
      };

      const newOrg = {
        uid: 'newOrg123',
        installingUserId: 'user123',
        metadata: {},
      };

      (mockOrganizationsRepository.findOne as any).mockResolvedValue(null);
      (mockOrganizationsRepository.save as any).mockResolvedValue(newOrg);

      // Act
      const result = await service.installApp(installation);

      // Assert
      expect(mockOrganizationsRepository.findOne).toHaveBeenCalledWith({
        where: { uid: installation.organization_id },
      });
      
      expect(mockOrganizationsRepository.save).toHaveBeenCalledWith({
        uid: installation.organization_id,
        installingUserId: installation.created_by,
      });
      
      expect(mockOrganizationsRepository.update).toHaveBeenCalledWith(
        { uid: installation.organization_id },
        {
          apiKey: installation.bot_token,
          metadata: {
            applicationId: installation.application_id,
            createdBy: installation.created_by,
            installationId: installation.installation_id,
          },
        },
      );
      
      expect(result).toEqual({ ok: true });
    });

    it('should handle errors during installation', async () => {
      // Arrange
      const installation: AppInstallation = {
        organization_id: 'org123',
        bot_token: 'xoxb-token',
        application_id: 'app123',
        installation_id: 'install123',
        created_by: 'user123',
        created_at: '2023-01-01T00:00:00Z',
        team_ids: ['team123'],
        configuration: {
          optional_settings: {},
          required_settings: {},
        },
        metadata: { 
          slack_team_id: 'T12345'
        }
      };

      const error = new Error('Database connection error');
      (mockOrganizationsRepository.findOne as any).mockRejectedValue(error);
      
      // Mock console.error to prevent actual console logs during test
      const consoleErrorSpy = vi.spyOn(console, 'error').mockImplementation(() => {});

      // Act & Assert
      await expect(service.installApp(installation)).rejects.toThrow(error);
      expect(consoleErrorSpy).toHaveBeenCalledWith(error);
    });
  });

  describe('setEventHandlers', () => {
    it('should set event handlers', () => {
      // Arrange
      const handlers = new Map<string, any>();
      handlers.set('event1', { instance: { handle: vi.fn() } });
      
      // Act
      service.setEventHandlers(handlers);
      
      // Assert - we'll verify this works in the handlePlatformEvent test
    });
  });

  describe('handlePlatformEvent', () => {
    it('should handle valid platform events', async () => {
      // Arrange
      const event: PlatformWebhookEvent = {
        xWebhookEvent: true,
        message: {
          actor: {
            email: '<EMAIL>',
            id: 'user123',
            type: 'user'
          },
          eventId: 'evt123',
          eventType: 'testEvent',
          orgId: 'org123',
          payload: { data: 'test' },
          timestamp: '2023-01-01T00:00:00Z'
        }
      };
      
      const expectedResult = { processed: true };
      const mockHandler = {
        instance: {
          handle: vi.fn().mockResolvedValue(expectedResult),
        },
      };
      
      const handlers = new Map<string, any>();
      handlers.set('testEvent', mockHandler);
      service.setEventHandlers(handlers);
      
      // Act
      const result = await service.handlePlatformEvent(event);
      
      // Assert
      expect(mockHandler.instance.handle).toHaveBeenCalledWith(event);
      expect(result).toEqual(expectedResult);
    });

    it('should return null for unknown event types', async () => {
      // Arrange
      const event: PlatformWebhookEvent = {
        xWebhookEvent: true,
        message: {
          actor: {
            email: '<EMAIL>',
            id: 'user123',
            type: 'user'
          },
          eventId: 'evt123',
          eventType: 'unknownEvent',
          orgId: 'org123',
          payload: { data: 'test' },
          timestamp: '2023-01-01T00:00:00Z'
        }
      };
      
      const handlers = new Map<string, any>();
      service.setEventHandlers(handlers);
      
      // Act
      const result = await service.handlePlatformEvent(event);
      
      // Assert
      expect(result).toBeNull();
    });

    it('should throw BadRequestException for invalid webhook events', async () => {
      // Arrange
      const invalidEvent = {
        xWebhookEvent: false,
        message: {
          actor: {
            email: '<EMAIL>',
            id: 'user123',
            type: 'user'
          },
          eventId: 'evt123',
          eventType: 'testEvent',
          orgId: 'org123',
          payload: { data: 'test' },
          timestamp: '2023-01-01T00:00:00Z'
        }
      };
      
      // Act & Assert
      await expect(service.handlePlatformEvent(invalidEvent as any)).rejects.toThrow(
        BadRequestException
      );
    });
  });
});

function getRepositoryToken(entity: any) {
  return `${entity.name}Repository`;
} 