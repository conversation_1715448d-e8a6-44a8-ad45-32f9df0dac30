import { Mock, beforeEach, describe, expect, it, vi } from 'vitest';
import { Config<PERSON>eys, ConfigService } from '../../../../src/config/config.service';
import { Organizations } from '../../../../src/database/entities/organizations/organizations.entity';
import { ThenaAppsPlatformApiProvider } from '../../../../src/external/provider/thena-apps-platform-api.provider';
import { EmittableSlackEventData } from '../../../../src/external/provider/constants/platform-events.constants';
import { ILogger } from '../../../../src/utils';

// Mock global fetch
global.fetch = vi.fn();

describe('ThenaAppsPlatformApiProvider', () => {
  let provider: ThenaAppsPlatformApiProvider;
  let mockLogger: ILogger;
  let mockConfigService: ConfigService;
  let mockOrganization: Organizations;

  beforeEach(() => {
    vi.resetAllMocks();

    // Mock the logger
    mockLogger = {
      log: vi.fn(),
      error: vi.fn(),
      warn: vi.fn(),
      debug: vi.fn(),
      verbose: vi.fn(),
    } as unknown as ILogger;

    // Mock the config service
    mockConfigService = {
      get: vi.fn(),
    } as unknown as ConfigService;

    // Mock the organization
    mockOrganization = {
      id: 'org-1',
      uid: 'org-uid-1',
      apiKey: 'api-key-1',
      name: 'Test Organization',
      externalPk: 'ext-1',
      installingUserId: 'user-1',
      metadata: {},
      bots: [],
      users: [],
      channels: [],
      installations: [],
      teams: [],
      subgroups: [],
    };

    // Configure the config service mock
    (mockConfigService.get as Mock).mockImplementation((key: string) => {
      if (key === ConfigKeys.APPS_PLATFORM_API_URL) {
        return 'https://api.apps-platform.test';
      }
      return '';
    });

    // Create the provider
    provider = new ThenaAppsPlatformApiProvider(mockLogger, mockConfigService);
  });

  describe('proxy', () => {
    it('should throw an error if the organization has no API key', async () => {
      // Setup
      const orgWithoutApiKey = { ...mockOrganization, apiKey: null };

      // Execute and verify
      try {
        await provider.proxy(orgWithoutApiKey as Organizations, 'GET', '/test');
        // If we reach here, the test should fail because no error was thrown
        expect(true).toBe(false); // This line should not be reached
      } catch (error) {
        // Verify the error message
        expect(error.message).toBe('Organization has no API key');
      }
    });

    it('should make a GET request with the correct headers', async () => {
      // Setup
      const mockResponse = new Response(JSON.stringify({ data: 'test' }), {
        status: 200,
      });
      (fetch as Mock).mockResolvedValue(mockResponse);

      // Execute
      await provider.proxy(mockOrganization, 'GET', '/test');

      // Verify
      expect(fetch).toHaveBeenCalledWith(
        'https://api.apps-platform.test/test',
        expect.objectContaining({
          method: 'GET',
          headers: {
            'Content-Type': 'application/json',
            'x-org-id': mockOrganization.uid,
            'x-bot-key': mockOrganization.apiKey,
          },
        })
      );
    });

    it('should make a POST request with the correct body', async () => {
      // Setup
      const mockResponse = new Response(JSON.stringify({ data: 'test' }), {
        status: 200,
      });
      (fetch as Mock).mockResolvedValue(mockResponse);
      const body = { test: 'data' };

      // Execute
      await provider.proxy(mockOrganization, 'POST', '/test', body);

      // Verify
      expect(fetch).toHaveBeenCalledWith(
        'https://api.apps-platform.test/test',
        expect.objectContaining({
          method: 'POST',
          headers: expect.any(Object),
          body: JSON.stringify(body),
        })
      );
    });

    it('should handle empty body for non-GET requests', async () => {
      // Setup
      const mockResponse = new Response(JSON.stringify({ data: 'test' }), {
        status: 200,
      });
      (fetch as Mock).mockResolvedValue(mockResponse);

      // Execute
      await provider.proxy(mockOrganization, 'POST', '/test');

      // Verify
      expect(fetch).toHaveBeenCalledWith(
        'https://api.apps-platform.test/test',
        expect.objectContaining({
          method: 'POST',
          headers: expect.any(Object),
          body: '{}', // Empty JSON object
        })
      );
    });

    it('should handle URL path normalization', async () => {
      // Setup
      const mockResponse = new Response(JSON.stringify({ data: 'test' }), {
        status: 200,
      });
      (fetch as Mock).mockResolvedValue(mockResponse);

      // Execute - with leading slash in path
      await provider.proxy(mockOrganization, 'GET', '/test/path');

      // Verify
      expect(fetch).toHaveBeenCalledWith(
        'https://api.apps-platform.test/test/path',
        expect.any(Object)
      );

      // Reset mock
      (fetch as Mock).mockReset();
      (fetch as Mock).mockResolvedValue(mockResponse);

      // Execute - with trailing slash in base URL
      (mockConfigService.get as Mock).mockReturnValue('https://api.apps-platform.test/');
      await provider.proxy(mockOrganization, 'GET', 'test/path');

      // Verify
      expect(fetch).toHaveBeenCalledWith(
        'https://api.apps-platform.test/test/path',
        expect.any(Object)
      );
    });
  });

  describe('postEventsToPlatform', () => {
    it('should throw an error if the organization has no API key', async () => {
      // Setup
      const orgWithoutApiKey = { ...mockOrganization, apiKey: null };
      const mockEvent = { type: 'message' } as EmittableSlackEventData;

      // Execute and verify
      try {
        await provider.postEventsToPlatform(orgWithoutApiKey as Organizations, mockEvent);
        // If we reach here, the test should fail because no error was thrown
        expect(true).toBe(false); // This line should not be reached
      } catch (error) {
        // Verify the error message
        expect(error.message).toBe('Organization has no API key');
      }
    });

    it('should return undefined (as the method is currently disabled)', async () => {
      // Setup
      const mockEvent = { type: 'message' } as EmittableSlackEventData;

      // Execute
      const result = await provider.postEventsToPlatform(mockOrganization, mockEvent);

      // Verify
      expect(result).toBeUndefined();
      expect(fetch).not.toHaveBeenCalled(); // Method is disabled, so fetch should not be called
    });
  });
});
