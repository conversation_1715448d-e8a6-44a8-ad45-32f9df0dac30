import { HttpException, HttpStatus } from '@nestjs/common';
import { Mock, beforeEach, describe, expect, it, vi } from 'vitest';
import { ConfigKeys, ConfigService } from '../../../../src/config/config.service';
import { Installations } from '../../../../src/database/entities';
import { Organizations } from '../../../../src/database/entities/organizations/organizations.entity';
import { AnnotatorApiProvider } from '../../../../src/external/provider/annotator-api.provider';
import { ILogger } from '../../../../src/utils';

// Mock global fetch
global.fetch = vi.fn();

describe('AnnotatorApiProvider', () => {
  let provider: AnnotatorApiProvider;
  let mockLogger: ILogger;
  let mockConfigService: ConfigService;
  let mockInstallation: Installations;
  let mockOrganization: Organizations;

  beforeEach(() => {
    vi.resetAllMocks();

    // Mock the logger
    mockLogger = {
      log: vi.fn(),
      error: vi.fn(),
      warn: vi.fn(),
      debug: vi.fn(),
      verbose: vi.fn(),
    } as unknown as ILogger;

    // Mock the config service
    mockConfigService = {
      get: vi.fn(),
    } as unknown as ConfigService;

    // Mock the organization
    mockOrganization = {
      id: 'org-1',
      uid: 'org-uid-1',
      apiKey: 'api-key-1',
      name: 'Test Organization',
      externalPk: 'ext-1',
      installingUserId: 'user-1',
      metadata: {},
      bots: [],
      users: [],
      channels: [],
      installations: [],
      teams: [],
      subgroups: [],
    };

    // Mock the installation
    mockInstallation = {
      id: 'inst-1',
      teamId: 'team-1',
      teamName: 'Team 1',
      botToken: 'xoxb-token-1',
      organization: mockOrganization,
    } as unknown as Installations;

    // Configure the config service mock
    (mockConfigService.get as Mock).mockImplementation((key: string) => {
      if (key === ConfigKeys.ANNOTATOR_API_URL) {
        return 'https://api.annotator.test';
      }
      return '';
    });

    // Create the provider
    provider = new AnnotatorApiProvider(mockLogger, mockConfigService);
  });

  describe('getEntityMetadata', () => {
    it('should fetch entity metadata successfully', async () => {
      // Setup
      const entityType = 'ticket';
      const relations = ['team', 'status'];
      const mockMetadata = {
        fields: {
          id: {
            type: 'string',
            label: 'ID',
            expression: 'id',
            standard: true,
            supportedOperators: ['eq', 'in'],
          },
          title: {
            type: 'string',
            label: 'Title',
            expression: 'title',
            standard: true,
            supportedOperators: ['eq', 'contains'],
          },
        },
      };

      const mockResponse = new Response(
        JSON.stringify({
          data: mockMetadata,
        }),
        { status: 200 }
      );
      (fetch as Mock).mockResolvedValue(mockResponse);

      // Execute
      const result = await provider.getEntityMetadata(
        mockInstallation,
        entityType,
        relations
      );

      // Verify
      expect(result).toEqual(mockMetadata);
      expect(fetch).toHaveBeenCalledWith(
        'https://api.annotator.test/v1/annotator/metadata',
        expect.objectContaining({
          method: 'POST',
          headers: {
            'X-Org-Id': mockOrganization.uid,
            'X-Api-Key': mockOrganization.apiKey,
            'Content-Type': 'application/json',
          },
          body: JSON.stringify({
            entityType,
            relations,
          }),
        })
      );
    });

    it('should handle API errors when fetching metadata', async () => {
      // Setup
      const entityType = 'ticket';
      const mockResponse = new Response(
        JSON.stringify({
          message: 'Invalid entity type',
        }),
        { status: 400 }
      );
      (fetch as Mock).mockResolvedValue(mockResponse);

      // Execute and verify
      await expect(
        provider.getEntityMetadata(mockInstallation, entityType)
      ).rejects.toThrow(HttpException);
      expect(mockLogger.error).toHaveBeenCalled();
    });

    it('should handle network errors', async () => {
      // Setup
      const entityType = 'ticket';
      (fetch as Mock).mockRejectedValue(new Error('Network error'));

      // Execute and verify
      await expect(
        provider.getEntityMetadata(mockInstallation, entityType)
      ).rejects.toThrow(HttpException);
      expect(mockLogger.error).toHaveBeenCalled();
    });
  });

  describe('getFieldTypeMappings', () => {
    it('should return field type mappings correctly', async () => {
      // Setup
      const entityType = 'ticket';
      const baseField = 'team';
      const mockMetadata = {
        fields: {
          id: {
            type: 'string',
            label: 'ID',
            expression: 'id',
            standard: true,
            supportedOperators: ['eq', 'in'],
          },
          title: {
            type: 'string',
            label: 'Title',
            expression: 'title',
            standard: true,
            supportedOperators: ['eq', 'contains'],
          },
          priority: {
            type: 'enum',
            label: 'Priority',
            expression: 'priority',
            standard: true,
            supportedOperators: ['eq'],
          },
          dueDate: {
            type: 'date',
            label: 'Due Date',
            expression: 'dueDate',
            standard: true,
            supportedOperators: ['eq', 'gt', 'lt'],
          },
          isUrgent: {
            type: 'boolean',
            label: 'Is Urgent',
            expression: 'isUrgent',
            standard: true,
            supportedOperators: ['eq'],
          },
          score: {
            type: 'number',
            label: 'Score',
            expression: 'score',
            standard: true,
            supportedOperators: ['eq', 'gt', 'lt'],
          },
          tags: {
            type: 'array',
            label: 'Tags',
            expression: 'tags',
            standard: true,
            supportedOperators: ['contains'],
          },
        },
      };

      // Mock the getEntityMetadata method
      vi.spyOn(provider, 'getEntityMetadata').mockResolvedValue(mockMetadata);

      // Execute
      const result = await provider.getFieldTypeMappings(
        mockInstallation,
        entityType,
        baseField
      );

      // Verify
      expect(result).toEqual({
        id: 'string',
        title: 'string',
        priority: 'string',
        dueDate: 'string',
        isUrgent: 'boolean',
        score: 'number',
        tags: 'array',
      });
      expect(provider.getEntityMetadata).toHaveBeenCalledWith(
        mockInstallation,
        entityType,
        [baseField]
      );
    });

    it('should handle unknown field types by defaulting to string', async () => {
      // Setup
      const entityType = 'ticket';
      const baseField = 'team';
      const mockMetadata = {
        fields: {
          id: {
            type: 'string',
            label: 'ID',
            expression: 'id',
            standard: true,
            supportedOperators: ['eq', 'in'],
          },
          customField: {
            type: 'unknown_type',
            label: 'Custom Field',
            expression: 'customField',
            standard: false,
            supportedOperators: ['eq'],
          },
        },
      };

      // Mock the getEntityMetadata method
      vi.spyOn(provider, 'getEntityMetadata').mockResolvedValue(mockMetadata);

      // Execute
      const result = await provider.getFieldTypeMappings(
        mockInstallation,
        entityType,
        baseField
      );

      // Verify
      expect(result).toEqual({
        id: 'string',
        customField: 'string', // Default to string for unknown type
      });
    });
  });
});
