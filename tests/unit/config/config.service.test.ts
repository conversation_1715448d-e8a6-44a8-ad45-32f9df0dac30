import * as fs from 'node:fs';
import * as dotenv from 'dotenv';
import { Mock, beforeEach, describe, expect, it, vi } from 'vitest';
import { ConfigKeys, ConfigService } from '../../../src/config/config.service';

vi.mock('node:fs', () => ({
  existsSync: vi.fn(),
  readFileSync: vi.fn(),
}));

vi.mock('dotenv', () => ({
  parse: vi.fn(),
}));

describe('ConfigService', () => {
  let configService: ConfigService;
  let mockEnvConfig: Record<string, string>;

  beforeEach(() => {
    vi.resetAllMocks();

    mockEnvConfig = {
      [ConfigKeys.APP_TAG]: 'test-app',
      [ConfigKeys.SERVICE_TAG]: 'test-service',
      [ConfigKeys.NODE_ENV]: 'test',
      [ConfigKeys.MODE]: 'app',
      [ConfigKeys.SLACK_APP_ID]: 'A12345',
      [ConfigKeys.SLACK_CLIENT_ID]: 'client-id',
      [ConfigKeys.SLACK_CLIENT_SECRET]: 'client-secret',
      [ConfigKeys.SLACK_SIGNING_SECRET]: 'signing-secret',
      [ConfigKeys.SLACK_STATE_SECRET]: 'state-secret',
      [ConfigKeys.SLACK_APP_BASE_URL]: 'https://example.com',
      [ConfigKeys.PLATFORM_API_URL]: 'https://api.example.com',
      [ConfigKeys.PLATFORM_SALT]: 'salt',
      [ConfigKeys.ANNOTATOR_API_URL]: 'https://annotator.example.com',
      [ConfigKeys.THENA_WEB_URL]: 'https://web.example.com',
      [ConfigKeys.AWS_ACCESS_KEY]: 'aws-access-key',
      [ConfigKeys.AWS_SECRET_KEY]: 'aws-secret-key',
      [ConfigKeys.AWS_REGION]: 'us-west-2',
      [ConfigKeys.AWS_SLACK_EVENTS_FIFO_QUEUE_URL]: 'https://sqs.example.com',
      [ConfigKeys.DB_HOST]: 'localhost',
      [ConfigKeys.DB_PORT]: '5432',
      [ConfigKeys.DB_USERNAME]: 'postgres',
      [ConfigKeys.DB_PASSWORD]: 'password',
      [ConfigKeys.DB_NAME]: 'thena',
      [ConfigKeys.REDIS_HOST]: 'localhost',
      [ConfigKeys.REDIS_PORT]: '6379',
      [ConfigKeys.REDIS_PASSWORD]: 'password',
      [ConfigKeys.REDIS_USERNAME]: 'default',
      [ConfigKeys.DATABASE_AUTO_RUN_SEEDS]: 'true',
      [ConfigKeys.GROK_API_KEY]: 'grok-api-key',
      [ConfigKeys.CLAUDE_API_KEY]: 'claude-api-key',
      [ConfigKeys.OPENAI_API_KEY]: 'openai-api-key',
      [ConfigKeys.VAULT_URL]: 'https://vault.example.com',
      [ConfigKeys.VAULT_TOKEN]: 'vault-token',
      [ConfigKeys.CERT_PATH]: '/path/to/cert',
      [ConfigKeys.SENTRY_DSN]: 'https://sentry.example.com',
    };

    (fs.existsSync as Mock).mockReturnValue(true);

    (fs.readFileSync as Mock).mockReturnValue('mock file content');

    (dotenv.parse as Mock).mockReturnValue(mockEnvConfig);

    configService = new ConfigService('/path/to/.env');
  });

  describe('get', () => {
    it('should return the correct value for a valid key', () => {
      const result = configService.get(ConfigKeys.SLACK_APP_ID);

      expect(result).toBe('A12345');
    });

    it('should throw an error for an invalid key', () => {
      expect(() => configService.get('INVALID_KEY' as ConfigKeys)).toThrow();
    });
  });

  describe('validateEnv', () => {
    it('should validate the environment variables successfully', () => {
      expect(configService).toBeDefined();
    });

    it('should throw an error if a required environment variable is missing', () => {
      const incompleteConfig = { ...mockEnvConfig };
      delete incompleteConfig[ConfigKeys.SLACK_APP_ID];
      (dotenv.parse as Mock).mockReturnValue(incompleteConfig);

      expect(() => new ConfigService('/path/to/.env')).toThrow();
    });

    it('should throw an error if NODE_ENV has an invalid value', () => {
      const invalidConfig = { ...mockEnvConfig };
      invalidConfig[ConfigKeys.NODE_ENV] = 'invalid';
      (dotenv.parse as Mock).mockReturnValue(invalidConfig);

      expect(() => new ConfigService('/path/to/.env')).toThrow();
    });

    it('should use process.env if the .env file does not exist', () => {
      vi.resetAllMocks();

      (fs.existsSync as Mock).mockReturnValue(false);

      const originalEnv = process.env;
      process.env = { ...mockEnvConfig };

      const service = new ConfigService('/path/to/.env');

      expect(service).toBeDefined();

      expect(fs.existsSync).toHaveBeenCalledWith('/path/to/.env');
      expect(fs.readFileSync).not.toHaveBeenCalled();
      expect(dotenv.parse).not.toHaveBeenCalled();

      process.env = originalEnv;
    });
  });
});
