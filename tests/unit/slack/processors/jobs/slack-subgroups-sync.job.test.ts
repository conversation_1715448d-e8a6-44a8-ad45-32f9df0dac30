import { beforeEach, describe, expect, it, vi, afterEach } from 'vitest';
import { SlackSubgroupsSyncJob } from '../../../../../src/slack/processors/jobs/slack-subgroups-sync.job';
import { SlackWebAPIService } from '../../../../../src/slack/providers/slack-apis/slack-apis.service';
import { TransactionService } from '../../../../../src/database/common';
import { SlackSubgroupsRepository } from '../../../../../src/database/entities/subgroups/repositories/subgroups.repository';
import { Installations } from '../../../../../src/database/entities';

describe('SlackSubgroupsSyncJob', () => {
  let job: SlackSubgroupsSyncJob;
  let mockSlackWebAPIService: any;
  let mockSlackSubgroupsRepository: any;
  let mockTransactionService: any;
  let mockLogger: any;

  beforeEach(() => {
    mockLogger = {
      log: vi.fn(),
      debug: vi.fn(),
      error: vi.fn(),
    };

    mockSlackWebAPIService = {
      listUserGroups: vi.fn(),
    };

    mockSlackSubgroupsRepository = {
      upsertWithTxn: vi.fn(),
    };

    mockTransactionService = {
      runInTransaction: vi.fn((callback) => callback('txn-context')),
    };

    job = new SlackSubgroupsSyncJob(
      mockLogger,
      mockSlackWebAPIService,
      mockTransactionService,
      mockSlackSubgroupsRepository
    );
  });

  afterEach(() => {
    vi.clearAllMocks();
  });

  describe('execute', () => {
    it('should sync user groups for an installation', async () => {
      // Arrange
      const installation = {
        id: 'installation1',
        botToken: 'xoxb-123456',
        teamId: 'team1',
        organization: { id: 'org1' },
      } as Installations;

      // Mock Slack API response
      const usergroups = [
        {
          id: 'S12345',
          team_id: 'team1',
          name: 'Engineering',
          handle: 'eng',
          description: 'Engineering team',
          is_external: false,
          user_count: 10,
          users: ['U12345', 'U67890'],
          created_by: 'U12345',
          updated_by: 'U12345',
          date_create: 123456789,
          date_update: 123456789,
          date_delete: 0,
        },
        {
          id: 'S67890',
          team_id: 'team1',
          name: 'Marketing',
          handle: 'mktg',
          description: 'Marketing team',
          is_external: false,
          user_count: 5,
          users: ['U54321'],
          created_by: 'U54321',
          updated_by: 'U54321',
          date_create: 123456789,
          date_update: 123456789,
          date_delete: 0,
        },
      ];

      mockSlackWebAPIService.listUserGroups.mockResolvedValue({
        ok: true,
        usergroups,
      });

      // Act
      await job.execute(installation);

      // Assert
      expect(mockSlackWebAPIService.listUserGroups).toHaveBeenCalledWith(
        'xoxb-123456',
        expect.objectContaining({
          include_count: true,
          include_users: true,
        })
      );

      // Check if transaction service was called
      expect(mockTransactionService.runInTransaction).toHaveBeenCalledTimes(1);

      // Check if subgroups were upserted correctly
      expect(mockSlackSubgroupsRepository.upsertWithTxn).toHaveBeenCalledTimes(1);
      expect(mockSlackSubgroupsRepository.upsertWithTxn).toHaveBeenCalledWith(
        'txn-context',
        expect.arrayContaining([
          expect.objectContaining({
            slackGroupId: 'S12345',
            slackHandle: 'eng',
            description: 'Engineering team',
            isExternal: false,
            usersCount: 10,
            users: ['U12345', 'U67890'],
            createdBy: { slackId: 'U12345' },
            updatedBy: { slackId: 'U12345' },
            installation: { id: 'installation1' },
            organization: { id: 'org1' },
          }),
          expect.objectContaining({
            slackGroupId: 'S67890',
            slackHandle: 'mktg',
            description: 'Marketing team',
            isExternal: false,
            usersCount: 5,
            users: ['U54321'],
            createdBy: { slackId: 'U54321' },
            updatedBy: { slackId: 'U54321' },
            installation: { id: 'installation1' },
            organization: { id: 'org1' },
          }),
        ]),
        { conflictPaths: ['slackGroupId', 'installation'] }
      );
    });

    it('should handle Slack API error response', async () => {
      // Arrange
      const installation = {
        id: 'installation1',
        botToken: 'xoxb-123456',
        teamId: 'team1',
        organization: { id: 'org1' },
      } as Installations;

      // Mock Slack API error response
      mockSlackWebAPIService.listUserGroups.mockResolvedValueOnce({
        ok: false,
        error: 'token_expired',
      });

      // Act
      await job.execute(installation);

      // Assert
      expect(mockLogger.error).toHaveBeenCalledWith(
        expect.stringContaining('Slack subgroups sync failed, error: token_expired')
      );
      expect(mockTransactionService.runInTransaction).not.toHaveBeenCalled();
      expect(mockSlackSubgroupsRepository.upsertWithTxn).not.toHaveBeenCalled();
    });

    it('should handle API errors gracefully', async () => {
      // Arrange
      const installation = {
        id: 'installation1',
        botToken: 'xoxb-123456',
        teamId: 'team1',
        organization: { id: 'org1' },
      } as Installations;

      // Mock API error
      mockSlackWebAPIService.listUserGroups.mockRejectedValueOnce(
        new Error('API rate limit exceeded')
      );

      // Act
      await job.execute(installation);

      // Assert
      expect(mockLogger.error).toHaveBeenCalled();
      expect(mockTransactionService.runInTransaction).not.toHaveBeenCalled();
      expect(mockSlackSubgroupsRepository.upsertWithTxn).not.toHaveBeenCalled();
    });

    it('should handle empty subgroups list gracefully', async () => {
      // Arrange
      const installation = {
        id: 'installation1',
        botToken: 'xoxb-123456',
        teamId: 'team1',
        organization: { id: 'org1' },
      } as Installations;

      // Mock Slack API response with empty subgroups array
      mockSlackWebAPIService.listUserGroups.mockResolvedValue({
        ok: true,
        usergroups: [],
      });

      // Act
      await job.execute(installation);

      // Assert
      expect(mockTransactionService.runInTransaction).toHaveBeenCalledTimes(1);
      expect(mockSlackSubgroupsRepository.upsertWithTxn).toHaveBeenCalledWith(
        'txn-context',
        [],
        { conflictPaths: ['slackGroupId', 'installation'] }
      );
    });

    it.todo('should handle transaction failures gracefully');

    it('should handle invalid subgroup data gracefully', async () => {
      // Arrange
      const installation = {
        id: 'installation1',
        botToken: 'xoxb-123456',
        teamId: 'team1',
        organization: { id: 'org1' },
      } as Installations;

      // Mock Slack API response with invalid subgroup data
      const usergroups = [
        {
          id: 'S12345',
          team_id: 'team1',
          name: 'Engineering',
          handle: 'eng',
          description: 'Engineering team',
          is_external: false,
          user_count: 10,
          users: ['U12345', 'U67890'],
          created_by: undefined, // Invalid created_by
          updated_by: undefined, // Invalid updated_by
          date_create: 123456789,
          date_update: 123456789,
          date_delete: 0,
        },
        {
          id: 'S67890',
          team_id: 'team1',
          name: 'Marketing',
          handle: 'mktg',
          description: 'Marketing team',
          is_external: false,
          user_count: 5,
          users: undefined, // Invalid users
          created_by: 'U54321',
          updated_by: 'U54321',
          date_create: 123456789,
          date_update: 123456789,
          date_delete: 0,
        },
      ];

      mockSlackWebAPIService.listUserGroups.mockResolvedValue({
        ok: true,
        usergroups,
      });

      // Act
      await job.execute(installation);

      // Assert
      expect(mockTransactionService.runInTransaction).toHaveBeenCalledTimes(1);
      expect(mockSlackSubgroupsRepository.upsertWithTxn).toHaveBeenCalledWith(
        'txn-context',
        expect.arrayContaining([
          expect.objectContaining({
            slackGroupId: 'S12345',
            createdBy: { slackId: undefined }, // Should handle undefined created_by
            updatedBy: { slackId: undefined }, // Should handle undefined updated_by
          }),
          expect.objectContaining({
            slackGroupId: 'S67890',
            users: undefined, // Should handle undefined users
          }),
        ]),
        { conflictPaths: ['slackGroupId', 'installation'] }
      );
    });

    it.todo('should handle writeSubgroups method errors gracefully');
  });
});