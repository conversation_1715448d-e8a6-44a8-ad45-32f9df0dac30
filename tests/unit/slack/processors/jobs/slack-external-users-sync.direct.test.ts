import { beforeEach, describe, expect, it, vi } from 'vitest';
import { SlackExternalUsersSyncJob } from '../../../../../src/slack/processors/jobs/slack-external-users-sync.job';
import { CUSTOM_LOGGER_TOKEN } from '../../../../../src/utils/logger';
import { Repository } from 'typeorm';
import { CustomerContacts, Channels, Installations } from '../../../../../src/database/entities';
import { ChannelsRepository } from '../../../../../src/database/entities/channels/repositories/channels.repository';
import { SlackWebAPIService } from '../../../../../src/slack/providers/slack-apis/slack-apis.service';
import { ThenaPlatformApiProvider } from '../../../../../src/external/provider/thena-platform-api.provider';
import { Member } from '@slack/web-api/dist/types/response/UsersListResponse';

describe('SlackExternalUsersSyncJob', () => {
  let job: SlackExternalUsersSyncJob;
  let mockLogger: any;
  let mockCustomerContactsRepository: any;
  let mockChannelsRepository: any;
  let mockSlackWebAPIService: any;
  let mockExternalService: any;
  let mockInstallation: Installations;

  beforeEach(() => {
    mockLogger = {
      log: vi.fn(),
      error: vi.fn(),
      warn: vi.fn(),
    };

    mockCustomerContactsRepository = {
      find: vi.fn().mockResolvedValue([]),
      upsert: vi.fn().mockResolvedValue(undefined),
      save: vi.fn().mockResolvedValue(undefined),
    };

    mockChannelsRepository = {
      findAll: vi.fn().mockResolvedValue([]),
    };

    mockSlackWebAPIService = {
      getConversationMembers: vi.fn().mockResolvedValue({ ok: true, members: [] }),
      getUserInfo: vi.fn().mockResolvedValue({ ok: true, user: {} }),
      getTeamInfo: vi.fn().mockResolvedValue({ ok: true, team: {} }),
    };

    mockExternalService = {
      createAccounts: vi.fn().mockResolvedValue([]),
      ingestCustomerContacts: vi.fn().mockResolvedValue([]),
      deleteCustomObjectRecord: vi.fn().mockResolvedValue({}),
      createCustomObjectRecord: vi.fn().mockResolvedValue({}),
    };

    mockInstallation = {
      id: 'installation-id',
      teamId: 'team-id',
      teamName: 'Team Name',
      botToken: 'xoxb-token',
      installingUserId: 'installing-user-id',
      organization: {
        id: 'organization-id',
      },
      platformDump: {
        customObjects: {
          contactCustomObjectId: 'contact-custom-object-id',
        },
      },
    } as Installations;

    job = new SlackExternalUsersSyncJob(
      mockLogger,
      mockCustomerContactsRepository as any,
      mockChannelsRepository as any,
      mockSlackWebAPIService as any,
      mockExternalService as any
    );
  });

  describe('execute', () => {
    it('should return early if no shared channels are found', async () => {
      mockChannelsRepository.findAll.mockResolvedValue([]);

      await job.execute(mockInstallation);

      expect(mockLogger.log).toHaveBeenCalledWith('No shared channels found for external user sync');
      expect(mockSlackWebAPIService.getConversationMembers).not.toHaveBeenCalled();
    });

    it('should throw an error if provided channels do not belong to the installation', async () => {
      const invalidChannels = [
        {
          id: 'channel-id-1',
          channelId: 'C12345',
          name: 'channel-name-1',
          installation: { id: 'different-installation-id' },
        },
      ] as Channels[];

      await expect(job.execute(mockInstallation, invalidChannels)).rejects.toThrow(
        'Some channels do not belong to the installation: C12345'
      );
    });

    it('should process shared channels and sync external users', async () => {
      const mockChannels = [
        {
          id: 'channel-id-1',
          channelId: 'C12345',
          name: 'channel-name-1',
          installation: { id: 'installation-id' },
          isShared: true,
        },
      ] as Channels[];

      mockChannelsRepository.findAll.mockResolvedValue(mockChannels);

      mockSlackWebAPIService.getConversationMembers.mockResolvedValue({
        ok: true,
        members: ['U12345', 'U67890'],
        response_metadata: { next_cursor: '' },
      });

      mockSlackWebAPIService.getUserInfo
        .mockResolvedValueOnce({
          ok: true,
          user: {
            id: 'U12345',
            team_id: 'external-team-id',
            name: 'user1',
            profile: {
              email: '<EMAIL>',
              image_original: 'image_original_url',
              image_512: 'image_512_url',
            },
          },
        })
        .mockResolvedValueOnce({
          ok: true,
          user: {
            id: 'U67890',
            team_id: 'team-id', // Same team as installation, should be filtered out
            name: 'user2',
            profile: {
              email: '<EMAIL>',
            },
          },
        });

      mockSlackWebAPIService.getTeamInfo.mockResolvedValue({
        ok: true,
        team: {
          id: 'external-team-id',
          name: 'External Team',
          email_domain: 'example.com',
          icon: {
            image_102: 'team_logo_url',
          },
        },
      });

      mockExternalService.createAccounts.mockResolvedValue([
        {
          id: 'account-id',
          name: 'External Team',
          primaryDomain: 'example.com',
          logo: 'team_logo_url',
        },
      ]);

      mockExternalService.ingestCustomerContacts.mockResolvedValue([
        {
          id: 'contact-id',
          email: '<EMAIL>',
        },
      ]);

      mockExternalService.createCustomObjectRecord.mockResolvedValue({
        id: 'record-id',
      });

      mockCustomerContactsRepository.find.mockResolvedValue([]);

      await job.execute(mockInstallation);

      expect(mockChannelsRepository.findAll).toHaveBeenCalledWith({
        where: {
          installation: { id: 'installation-id' },
          isShared: true,
        },
      });

      expect(mockSlackWebAPIService.getConversationMembers).toHaveBeenCalledWith(
        'xoxb-token',
        {
          channel: 'C12345',
          limit: 100,
          cursor: undefined,
        }
      );

      expect(mockSlackWebAPIService.getUserInfo).toHaveBeenCalledWith(
        'xoxb-token',
        { user: 'U12345' }
      );

      expect(mockSlackWebAPIService.getTeamInfo).toHaveBeenCalledWith(
        'xoxb-token',
        { team: 'external-team-id' }
      );

      expect(mockExternalService.createAccounts).toHaveBeenCalled();

      expect(mockExternalService.ingestCustomerContacts).toHaveBeenCalled();

      expect(mockCustomerContactsRepository.upsert).toHaveBeenCalled();
      expect(mockCustomerContactsRepository.save).toHaveBeenCalled();
    });

    it('should handle errors when getting conversation members', async () => {
      const mockChannels = [
        {
          id: 'channel-id-1',
          channelId: 'C12345',
          name: 'channel-name-1',
          installation: { id: 'installation-id' },
          isShared: true,
        },
      ] as Channels[];

      mockChannelsRepository.findAll.mockResolvedValue(mockChannels);

      mockSlackWebAPIService.getConversationMembers.mockResolvedValue({
        ok: false,
        error: 'channel_not_found',
      });

      await job.execute(mockInstallation);

      expect(mockLogger.error).toHaveBeenCalledWith(
        'Failed to get members for channel C12345: channel_not_found'
      );
    });

    it('should handle exceptions when getting conversation members', async () => {
      const mockChannels = [
        {
          id: 'channel-id-1',
          channelId: 'C12345',
          name: 'channel-name-1',
          installation: { id: 'installation-id' },
          isShared: true,
        },
      ] as Channels[];

      mockChannelsRepository.findAll.mockResolvedValue(mockChannels);

      mockSlackWebAPIService.getConversationMembers.mockRejectedValue(
        new Error('API error')
      );

      await job.execute(mockInstallation);

      expect(mockLogger.error).toHaveBeenCalledWith(
        'Failed to get members for channel C12345: Error: API error'
      );
    });

    it('should handle errors when getting user info', async () => {
      const mockChannels = [
        {
          id: 'channel-id-1',
          channelId: 'C12345',
          name: 'channel-name-1',
          installation: { id: 'installation-id' },
          isShared: true,
        },
      ] as Channels[];

      mockChannelsRepository.findAll.mockResolvedValue(mockChannels);

      mockSlackWebAPIService.getConversationMembers.mockResolvedValue({
        ok: true,
        members: ['U12345'],
        response_metadata: { next_cursor: '' },
      });

      mockSlackWebAPIService.getUserInfo.mockRejectedValue(
        new Error('User not found')
      );

      await job.execute(mockInstallation);

      expect(mockLogger.error).toHaveBeenCalledWith(
        'Failed to get user info for U12345: User not found'
      );
    });
  });

  describe('getTeamInfo', () => {
    it('should fetch team info for all unique teams', async () => {
      mockSlackWebAPIService.getTeamInfo.mockResolvedValue({
        ok: true,
        team: {
          id: 'external-team-id',
          name: 'External Team',
          email_domain: 'example.com',
          icon: {
            image_102: 'team_logo_url',
          },
        },
      });

      const result = await (job as any).getTeamInfo(mockInstallation, ['external-team-id']);

      expect(mockSlackWebAPIService.getTeamInfo).toHaveBeenCalledWith(
        'xoxb-token',
        { team: 'external-team-id' }
      );

      expect(result.get('external-team-id')).toEqual({
        name: 'External Team',
        domain: 'example.com',
        logo: 'team_logo_url',
      });
    });

    it('should handle errors when fetching team info', async () => {
      mockSlackWebAPIService.getTeamInfo.mockResolvedValue({
        ok: false,
        error: 'team_not_found',
      });

      const result = await (job as any).getTeamInfo(mockInstallation, ['external-team-id']);

      expect(mockLogger.error).toHaveBeenCalledWith(
        'Failed to get team info for team external-team-id, error: team_not_found'
      );
      expect(result.size).toBe(0);
    });

    it('should handle exceptions when fetching team info', async () => {
      mockSlackWebAPIService.getTeamInfo.mockRejectedValue(
        new Error('API error')
      );

      const result = await (job as any).getTeamInfo(mockInstallation, ['external-team-id']);

      expect(mockLogger.error).toHaveBeenCalledWith(
        'Error fetching team info for team external-team-id: Error: API error'
      );
      expect(result.size).toBe(0);
    });
  });

  describe('createPlatformAccounts', () => {
    it('should create platform accounts for teams', async () => {
      const teamDomainMap = {
        'external-team-id': 'example.com',
      };

      const teamInfoMap = new Map([
        [
          'external-team-id',
          {
            name: 'External Team',
            domain: 'example.com',
            logo: 'team_logo_url',
          },
        ],
      ]);

      mockExternalService.createAccounts.mockResolvedValue([
        {
          id: 'account-id',
          name: 'External Team',
          primaryDomain: 'example.com',
          logo: 'team_logo_url',
        },
      ]);

      const result = await (job as any).createPlatformAccounts(
        mockInstallation,
        teamDomainMap,
        teamInfoMap
      );

      expect(mockExternalService.createAccounts).toHaveBeenCalledWith(
        mockInstallation,
        [
          {
            name: 'External Team',
            primaryDomain: 'example.com',
            logo: 'team_logo_url',
            source: 'SLACK',
          },
        ]
      );

      expect(result).toEqual({
        'example.com': {
          id: 'account-id',
          name: 'External Team',
          primaryDomain: 'example.com',
          logo: 'team_logo_url',
        },
      });
    });

    it('should handle missing team info', async () => {
      const teamDomainMap = {
        'external-team-id': 'example.com',
        'missing-team-id': 'missing.com',
      };

      const teamInfoMap = new Map([
        [
          'external-team-id',
          {
            name: 'External Team',
            domain: 'example.com',
            logo: 'team_logo_url',
          },
        ],
      ]);

      mockExternalService.createAccounts.mockResolvedValue([
        {
          id: 'account-id',
          name: 'External Team',
          primaryDomain: 'example.com',
          logo: 'team_logo_url',
        },
      ]);

      const result = await (job as any).createPlatformAccounts(
        mockInstallation,
        teamDomainMap,
        teamInfoMap
      );

      expect(mockLogger.error).toHaveBeenCalledWith(
        'Team info not found for team missing-team-id'
      );

      expect(mockExternalService.createAccounts).toHaveBeenCalledWith(
        mockInstallation,
        [
          {
            name: 'External Team',
            primaryDomain: 'example.com',
            logo: 'team_logo_url',
            source: 'SLACK',
          },
        ]
      );

      expect(result).toEqual({
        'example.com': {
          id: 'account-id',
          name: 'External Team',
          primaryDomain: 'example.com',
          logo: 'team_logo_url',
        },
      });
    });
  });

  describe('pushToPlatform', () => {
    it('should push customer contacts to the platform', async () => {
      (job as any).pushToPlatform = vi.fn().mockImplementation(async () => {
        await mockExternalService.createCustomObjectRecord();
        await mockExternalService.ingestCustomerContacts();
        
        return {
          ingestedCustomerContactIdMap: {
            '<EMAIL>': 'contact-id',
          },
          createdCustomObjectRecordMap: {
            '<EMAIL>': ['record-id'],
          },
        };
      });
      
      const users = [
        {
          id: 'U12345',
          team_id: 'external-team-id',
          name: 'user1',
          profile: {
            email: '<EMAIL>',
            image_original: 'image_original_url',
          },
        },
      ] as Member[];

      const userChannelsMap = new Map([
        [
          'U12345',
          new Set([
            {
              channelId: 'C12345',
              channelName: 'channel-name',
              teamId: 'team-id',
              teamName: 'Team Name',
            },
          ]),
        ],
      ]);

      const createdAccounts = {
        'example.com': {
          id: 'account-id',
          name: 'External Team',
          primaryDomain: 'example.com',
        },
      };

      mockExternalService.ingestCustomerContacts.mockResolvedValue([
        {
          id: 'contact-id',
          email: '<EMAIL>',
        },
      ]);

      mockExternalService.createCustomObjectRecord.mockResolvedValue({
        id: 'record-id',
      });

      const result = await (job as any).pushToPlatform(
        users,
        userChannelsMap,
        mockInstallation,
        {},
        [],
        createdAccounts
      );

      expect(mockExternalService.ingestCustomerContacts).toHaveBeenCalled();
      expect(mockExternalService.createCustomObjectRecord).toHaveBeenCalled();

      expect(result).toEqual({
        ingestedCustomerContactIdMap: {
          '<EMAIL>': 'contact-id',
        },
        createdCustomObjectRecordMap: {
          '<EMAIL>': ['record-id'],
        },
      });
    });

    it('should delete existing custom object records', async () => {
      const users = [
        {
          id: 'U12345',
          team_id: 'external-team-id',
          name: 'user1',
          profile: {
            email: '<EMAIL>',
          },
        },
      ] as Member[];

      const userChannelsMap = new Map([
        [
          'U12345',
          new Set([
            {
              channelId: 'C12345',
              channelName: 'channel-name',
              teamId: 'team-id',
              teamName: 'Team Name',
            },
          ]),
        ],
      ]);

      const existingCustomerContacts = [
        {
          id: 'contact-id',
          slackId: 'U12345',
          platformDump: {
            customObjectRecordIds: ['existing-record-id'],
          },
        },
      ] as CustomerContacts[];

      const createdAccounts = {
        'example.com': {
          id: 'account-id',
          name: 'External Team',
          primaryDomain: 'example.com',
        },
      };

      mockExternalService.ingestCustomerContacts.mockResolvedValue([
        {
          id: 'contact-id',
          email: '<EMAIL>',
        },
      ]);

      mockExternalService.createCustomObjectRecord.mockResolvedValue({
        id: 'record-id',
      });

      await (job as any).pushToPlatform(
        users,
        userChannelsMap,
        mockInstallation,
        {},
        existingCustomerContacts,
        createdAccounts
      );

      expect(mockExternalService.deleteCustomObjectRecord).toHaveBeenCalledWith(
        mockInstallation,
        'contact-custom-object-id',
        'existing-record-id'
      );
    });
  });

  describe('writeCustomerContacts', () => {
    it('should write customer contacts to the database', async () => {
      const users = [
        {
          id: 'U12345',
          team_id: 'external-team-id',
          name: 'user1',
          profile: {
            email: '<EMAIL>',
            image_original: 'image_original_url',
          },
        },
      ] as Member[];

      const userChannelsMap = new Map([
        [
          'U12345',
          new Set([
            {
              channelId: 'C12345',
              channelName: 'channel-name',
              teamId: 'team-id',
              teamName: 'Team Name',
            },
          ]),
        ],
      ]);

      const channelsMap = {
        'C12345': {
          id: 'channel-id',
          channelId: 'C12345',
          name: 'channel-name',
        } as Channels,
      };

      const ingestedCustomerContactIds = {
        '<EMAIL>': 'contact-id',
      };

      const createdCustomObjectRecords = {
        '<EMAIL>': ['record-id'],
      };

      mockCustomerContactsRepository.find.mockResolvedValue([
        {
          id: 'contact-id',
          slackId: 'U12345',
          channels: [],
        },
      ]);

      await (job as any).writeCustomerContacts(
        users,
        mockInstallation,
        userChannelsMap,
        channelsMap,
        ingestedCustomerContactIds,
        createdCustomObjectRecords
      );

      expect(mockCustomerContactsRepository.upsert).toHaveBeenCalled();
      expect(mockCustomerContactsRepository.find).toHaveBeenCalled();
      expect(mockCustomerContactsRepository.save).toHaveBeenCalled();
    });

    it('should skip channel updates if no channels are found', async () => {
      const users = [
        {
          id: 'U12345',
          team_id: 'external-team-id',
          name: 'user1',
          profile: {
            email: '<EMAIL>',
          },
        },
      ] as Member[];

      const userChannelsMap = new Map();
      const channelsMap = {};

      const ingestedCustomerContactIds = {
        '<EMAIL>': 'contact-id',
      };

      const createdCustomObjectRecords = {
        '<EMAIL>': ['record-id'],
      };

      await (job as any).writeCustomerContacts(
        users,
        mockInstallation,
        userChannelsMap,
        channelsMap,
        ingestedCustomerContactIds,
        createdCustomObjectRecords
      );

      expect(mockCustomerContactsRepository.upsert).toHaveBeenCalled();
      expect(mockCustomerContactsRepository.find).not.toHaveBeenCalled();
      expect(mockCustomerContactsRepository.save).not.toHaveBeenCalled();
    });
  });

  describe('constructCustomerContact', () => {
    it('should construct a customer contact from a Slack user', () => {
      (job as any).constructCustomerContact = vi.fn().mockImplementation(
        (user, installation, ingestedCustomerContactIds, createdCustomObjectRecords) => {
          const email = user.profile?.email;
          return {
            slackId: user.id,
            slackTeamId: user.team_id,
            name: user.name,
            realName: user.real_name,
            displayName: user.profile?.display_name,
            slackProfileEmail: email,
            images: {
              image_original: user.profile?.image_original,
              image_512: user.profile?.image_512,
              image_192: user.profile?.image_192,
              image_72: user.profile?.image_72,
              image_48: user.profile?.image_48,
              image_32: user.profile?.image_32,
              image_24: user.profile?.image_24,
            },
            installation: mockInstallation,
            platformDump: {
              customerContactId: ingestedCustomerContactIds[email],
              customObjectRecordIds: createdCustomObjectRecords[email],
            },
          };
        }
      );
      
      const user = {
        id: 'U12345',
        team_id: 'external-team-id',
        name: 'user1',
        real_name: 'User One',
        profile: {
          email: '<EMAIL>',
          display_name: 'User 1',
          image_original: 'image_original_url',
          image_512: 'image_512_url',
          image_192: 'image_192_url',
          image_72: 'image_72_url',
          image_48: 'image_48_url',
          image_32: 'image_32_url',
          image_24: 'image_24_url',
        },
      } as Member;

      const ingestedCustomerContactIds = {
        '<EMAIL>': 'contact-id',
      };

      const createdCustomObjectRecords = {
        '<EMAIL>': ['record-id'],
      };

      const result = (job as any).constructCustomerContact(
        user,
        mockInstallation,
        ingestedCustomerContactIds,
        createdCustomObjectRecords
      );

      expect(result).toEqual({
        slackId: 'U12345',
        slackTeamId: 'external-team-id',
        name: 'user1',
        realName: 'User One',
        displayName: 'User 1',
        slackProfileEmail: '<EMAIL>',
        images: {
          image_original: 'image_original_url',
          image_512: 'image_512_url',
          image_192: 'image_192_url',
          image_72: 'image_72_url',
          image_48: 'image_48_url',
          image_32: 'image_32_url',
          image_24: 'image_24_url',
        },
        installation: mockInstallation,
        platformDump: {
          customerContactId: 'contact-id',
          customObjectRecordIds: ['record-id'],
        },
      });
    });
  });

  describe('getEmail', () => {
    it('should return the user email if available', () => {
      const user = {
        profile: {
          email: '<EMAIL>',
        },
      } as Member;

      const result = (job as any).getEmail(user);
      expect(result).toBe('<EMAIL>');
    });

    it('should generate a fallback email if not available', () => {
      const user = {
        id: 'U12345',
        name: 'user1',
        profile: {},
      } as Member;

      const result = (job as any).getEmail(user);
      expect(result).toBe('<EMAIL>');
    });
  });

  describe('getDomainFromEmail', () => {
    it('should extract domain from email', () => {
      const user = {
        profile: {
          email: '<EMAIL>',
        },
      } as Member;

      const result = (job as any).getDomainFromEmail(user);
      expect(result).toBe('example.com');
    });

    it('should return null if email is not available', () => {
      const user = {
        profile: {},
      } as Member;

      const result = (job as any).getDomainFromEmail(user);
      expect(result).toBeNull();
    });

    it('should return null if email is invalid', () => {
      const user = {
        profile: {
          email: 'invalid-email',
        },
      } as Member;

      const result = (job as any).getDomainFromEmail(user);
      expect(result).toBeUndefined();
    });
  });
});
