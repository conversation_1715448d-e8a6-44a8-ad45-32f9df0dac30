import { beforeEach, describe, expect, it, vi, afterEach } from 'vitest';
import { SlackUsersSyncJob } from '../../../../../src/slack/processors/jobs/slack-users-sync.job';
import { ILogger } from '../../../../../src/utils/logger';
import { Installations, Users } from '../../../../../src/database/entities';
import { Repository } from 'typeorm';
import { BotsRepository } from '../../../../../src/database/entities/bots/repositories';
import { SlackWebAPIService } from '../../../../../src/slack/providers/slack-apis/slack-apis.service';
import { ThenaPlatformApiProvider } from '../../../../../src/external/provider/thena-platform-api.provider';
import { Member } from '@slack/web-api/dist/types/response/UsersListResponse';

describe('SlackUsersSyncJob', () => {
  let job: SlackUsersSyncJob;
  let mockLogger: ILogger;
  let mockUsersRepository: Repository<Users>;
  let mockBotsRepository: BotsRepository;
  let mockSlackWebAPIService: SlackWebAPIService;
  let mockThenaPlatformService: ThenaPlatformApiProvider;
  let defaultInstallation: Installations;

  beforeEach(() => {
    mockLogger = {
      log: vi.fn(),
      debug: vi.fn(),
      error: vi.fn(),
      warn: vi.fn(),
      verbose: vi.fn(),
    } as unknown as ILogger;

    mockUsersRepository = {
      upsert: vi.fn().mockResolvedValue(undefined),
    } as unknown as Repository<Users>;

    mockBotsRepository = {
      upsert: vi.fn().mockResolvedValue(undefined),
    } as unknown as BotsRepository;

    mockSlackWebAPIService = {
      listUsers: vi.fn(),
    } as unknown as SlackWebAPIService;

    mockThenaPlatformService = {
      linkUsersToPlatform: vi.fn().mockResolvedValue(undefined),
    } as unknown as ThenaPlatformApiProvider;

    defaultInstallation = {
      id: 'installation1',
      botToken: 'xoxb-123456',
      teamId: 'team1',
      organization: { id: 'org1' },
    } as Installations;

    job = new SlackUsersSyncJob(
      mockLogger,
      mockUsersRepository,
      mockBotsRepository,
      mockSlackWebAPIService,
      mockThenaPlatformService
    );
  });

  afterEach(() => {
    vi.clearAllMocks();
    vi.resetAllMocks();
  });

  describe('execute', () => {
    it('should sync users for an installation', async () => {
      // Mock Slack API response - keep dataset small
      const members = [
        {
          id: 'U12345',
          name: 'johndoe',
          real_name: 'John Doe',
          is_bot: false,
          profile: {
            real_name: 'John Doe',
            display_name: 'johndoe',
            email: '<EMAIL>',
            status_text: 'Working',
            status_emoji: ':computer:',
            image_48: 'http://example.com/image48.jpg',
          },
          tz: 'America/Los_Angeles',
          tz_label: 'Pacific Standard Time',
          is_admin: true,
          is_owner: false,
          is_restricted: false,
          is_ultra_restricted: false,
          deleted: false,
        },
        {
          id: 'B67890',
          name: 'botuser',
          real_name: 'Bot User',
          is_bot: true,
          profile: {
            real_name: 'Bot User',
            display_name: 'botuser',
            title: 'A Helpful Bot',
            status_text: 'Always online',
            status_emoji: ':robot_face:',
            image_48: 'http://example.com/bot48.jpg',
          },
          tz: 'UTC',
          tz_label: 'UTC',
          is_admin: false,
          is_owner: false,
          is_restricted: false,
          is_ultra_restricted: false,
          deleted: false,
        },
      ];

      vi.mocked(mockSlackWebAPIService.listUsers).mockResolvedValueOnce({
        ok: true,
        members,
        response_metadata: { next_cursor: '' },
      });

      // Act
      await job.execute(defaultInstallation);

      // Assert
      expect(mockSlackWebAPIService.listUsers).toHaveBeenCalledWith(
        'xoxb-123456',
        expect.objectContaining({ limit: 200 })
      );

      // Check if users were upserted correctly
      expect(mockUsersRepository.upsert).toHaveBeenCalledTimes(1);
      
      // Check if bots were upserted correctly
      expect(mockBotsRepository.upsert).toHaveBeenCalledTimes(1);
      
      // Check if users were linked to platform
      expect(mockThenaPlatformService.linkUsersToPlatform).toHaveBeenCalledTimes(1);
      expect(mockThenaPlatformService.linkUsersToPlatform).toHaveBeenCalledWith(
        defaultInstallation,
        expect.objectContaining({
          externalType: 'slack',
          details: expect.arrayContaining([
            expect.objectContaining({
              email: '<EMAIL>',
              slackSinkDetails: {
                id: 'U12345',
                teamId: 'team1',
              },
            }),
          ]),
        })
      );
    });

    it('should handle Slack API pagination correctly', async () => {
      // Mock first page of results - small dataset
      vi.mocked(mockSlackWebAPIService.listUsers).mockResolvedValueOnce({
        ok: true,
        members: [
          {
            id: 'U1',
            name: 'user1',
            is_bot: false,
            profile: { email: '<EMAIL>' },
            deleted: false,
          },
        ],
        response_metadata: { next_cursor: 'next_page_cursor' },
      });

      // Mock second page of results - small dataset
      vi.mocked(mockSlackWebAPIService.listUsers).mockResolvedValueOnce({
        ok: true,
        members: [
          {
            id: 'U2',
            name: 'user2',
            is_bot: false,
            profile: { email: '<EMAIL>' },
            deleted: false,
          },
        ],
        response_metadata: { next_cursor: '' },
      });

      // Act
      await job.execute(defaultInstallation);

      // Assert
      expect(mockSlackWebAPIService.listUsers).toHaveBeenCalledTimes(2);
      expect(mockSlackWebAPIService.listUsers).toHaveBeenCalledWith(
        'xoxb-123456',
        expect.objectContaining({ limit: 200, cursor: '' }) // First call should have empty string cursor
      );
      expect(mockSlackWebAPIService.listUsers).toHaveBeenCalledWith(
        'xoxb-123456',
        expect.objectContaining({ limit: 200, cursor: 'next_page_cursor' }) // Second call should use the cursor
      );

      // Should have called writeInternalUsers twice, once for each page
      expect(mockUsersRepository.upsert).toHaveBeenCalledTimes(2);
      expect(mockBotsRepository.upsert).toHaveBeenCalledTimes(2);
      expect(mockThenaPlatformService.linkUsersToPlatform).toHaveBeenCalledTimes(2);
    });

    it('should handle API errors gracefully', async () => {
      // Mock API error
      vi.mocked(mockSlackWebAPIService.listUsers).mockRejectedValueOnce(
        new Error('API rate limit exceeded')
      );

      // Act
      await job.execute(defaultInstallation);

      // Assert
      expect(mockLogger.error).toHaveBeenCalled();
      expect(mockUsersRepository.upsert).not.toHaveBeenCalled();
      expect(mockBotsRepository.upsert).not.toHaveBeenCalled();
      expect(mockThenaPlatformService.linkUsersToPlatform).not.toHaveBeenCalled();
    });

    it('should handle Slack API error response', async () => {
      // Mock Slack API error response
      vi.mocked(mockSlackWebAPIService.listUsers).mockResolvedValueOnce({
        ok: false,
        error: 'token_expired',
      });

      // Act
      await job.execute(defaultInstallation);

      // Assert
      expect(mockLogger.error).toHaveBeenCalledWith(
        expect.stringContaining('Slack users sync failed, error: token_expired')
      );
      expect(mockUsersRepository.upsert).not.toHaveBeenCalled();
      expect(mockBotsRepository.upsert).not.toHaveBeenCalled();
      expect(mockThenaPlatformService.linkUsersToPlatform).not.toHaveBeenCalled();
    });

    it('should handle empty user data gracefully', async () => {
      // Mock Slack API response with empty members array
      vi.mocked(mockSlackWebAPIService.listUsers).mockResolvedValueOnce({
        ok: true,
        members: [],
        response_metadata: { next_cursor: '' },
      });

      // Act
      await job.execute(defaultInstallation);

      // Assert
      expect(mockUsersRepository.upsert).toHaveBeenCalledWith([], expect.any(Object));
      expect(mockBotsRepository.upsert).toHaveBeenCalledWith([], expect.any(Object));
      expect(mockThenaPlatformService.linkUsersToPlatform).toHaveBeenCalledWith(
        defaultInstallation,
        expect.objectContaining({
          externalType: 'slack',
          details: [],
        })
      );
    });

    it('should handle invalid user data gracefully', async () => {
      // Minimal profile data to avoid memory issues
      const members = [
        {
          id: 'U12345',
          name: 'johndoe',
          is_bot: false,
          profile: undefined, // Invalid profile data
          deleted: false,
        },
        {
          id: 'B67890',
          name: 'botuser',
          is_bot: true,
          profile: undefined, // Invalid profile data
          deleted: false,
        },
      ] as Member[];

      // Mock Slack API response with invalid user data
      vi.mocked(mockSlackWebAPIService.listUsers).mockResolvedValueOnce({
        ok: true,
        members,
        response_metadata: { next_cursor: '' },
      });

      // Act
      await job.execute(defaultInstallation);

      // Assert
      expect(mockUsersRepository.upsert).toHaveBeenCalledWith(
        expect.arrayContaining([
          expect.objectContaining({
            slackId: 'U12345',
            slackProfileEmail: undefined, // Should handle missing email gracefully
          }),
        ]),
        expect.any(Object)
      );
      expect(mockBotsRepository.upsert).toHaveBeenCalledWith(
        expect.arrayContaining([
          expect.objectContaining({
            slackId: 'B67890',
            botTitle: undefined, // Should handle missing title gracefully
          }),
        ]),
        expect.any(Object)
      );
    });
  });
}); 