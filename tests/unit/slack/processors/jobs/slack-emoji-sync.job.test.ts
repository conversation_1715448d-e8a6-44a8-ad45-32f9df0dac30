import { beforeEach, describe, expect, it, vi, afterEach } from 'vitest';
import { SlackEmojiSyncJob } from '../../../../../src/slack/processors/jobs/slack-emoji-sync.job';
import { SlackWebAPIService } from '../../../../../src/slack/providers/slack-apis/slack-apis.service';
import { TransactionService } from '../../../../../src/database/common';
import { SlackEmojisRepository } from '../../../../../src/database/entities/emojis/repository/slack-emojis.repository';
import { Installations } from '../../../../../src/database/entities';

describe('SlackEmojiSyncJob', () => {
  let job: SlackEmojiSyncJob;
  let mockSlackWebAPIService: any;
  let mockSlackEmojisRepository: any;
  let mockTransactionService: any;
  let mockLogger: any;

  beforeEach(() => {
    mockLogger = {
      log: vi.fn(),
      debug: vi.fn(),
      error: vi.fn(),
    };

    mockSlackWebAPIService = {
      listWorkspaceEmojis: vi.fn(),
    };

    mockSlackEmojisRepository = {
      upsertWithTxn: vi.fn(),
    };

    mockTransactionService = {
      runInTransaction: vi.fn((callback) => callback('txn-context')),
    };

    job = new SlackEmojiSyncJob(
      mockLogger,
      mockSlackWebAPIService,
      mockTransactionService,
      mockSlackEmojisRepository
    );
  });

  afterEach(() => {
    vi.clearAllMocks();
  });

  describe('execute', () => {
    it('should sync emojis for an installation', async () => {
      // Arrange
      const installation = {
        id: 'installation1',
        botToken: 'xoxb-123456',
        teamId: 'team1',
        organization: { id: 'org1' },
      } as Installations;

      // Mock Slack API response
      const emoji = {
        'party': 'https://emoji.slack-edge.com/T123/party/123.gif',
        'smile': 'https://emoji.slack-edge.com/T123/smile/456.png',
        'thumbsup': 'https://emoji.slack-edge.com/T123/thumbsup/789.gif',
      };

      mockSlackWebAPIService.listWorkspaceEmojis.mockResolvedValue({
        ok: true,
        emoji,
      });

      // Act
      await job.execute(installation);

      // Assert
      expect(mockSlackWebAPIService.listWorkspaceEmojis).toHaveBeenCalledWith(
        'xoxb-123456'
      );

      // Check if transaction service was called
      expect(mockTransactionService.runInTransaction).toHaveBeenCalledTimes(1);
      
      // Check if emojis were upserted correctly
      expect(mockSlackEmojisRepository.upsertWithTxn).toHaveBeenCalledTimes(1);
      expect(mockSlackEmojisRepository.upsertWithTxn).toHaveBeenCalledWith(
        'txn-context',
        expect.arrayContaining([
          expect.objectContaining({
            name: 'party',
            url: 'https://emoji.slack-edge.com/T123/party/123.gif',
            installation: { id: 'installation1' },
            organization: { id: 'org1' },
          }),
          expect.objectContaining({
            name: 'smile',
            url: 'https://emoji.slack-edge.com/T123/smile/456.png',
            installation: { id: 'installation1' },
            organization: { id: 'org1' },
          }),
          expect.objectContaining({
            name: 'thumbsup',
            url: 'https://emoji.slack-edge.com/T123/thumbsup/789.gif',
            installation: { id: 'installation1' },
            organization: { id: 'org1' },
          }),
        ]),
        { conflictPaths: ['name', 'installation'] }
      );
    });

    it('should handle Slack API error response', async () => {
      // Arrange
      const installation = {
        id: 'installation1',
        botToken: 'xoxb-123456',
        teamId: 'team1',
        organization: { id: 'org1' },
      } as Installations;

      // Mock Slack API error response
      mockSlackWebAPIService.listWorkspaceEmojis.mockResolvedValueOnce({
        ok: false,
        error: 'token_expired',
      });

      // Act
      await job.execute(installation);

      // Assert
      expect(mockLogger.error).toHaveBeenCalledWith(
        expect.stringContaining('Slack emojis sync failed, error: token_expired')
      );
      expect(mockTransactionService.runInTransaction).not.toHaveBeenCalled();
      expect(mockSlackEmojisRepository.upsertWithTxn).not.toHaveBeenCalled();
    });

    it('should handle API errors gracefully', async () => {
      // Arrange
      const installation = {
        id: 'installation1',
        botToken: 'xoxb-123456',
        teamId: 'team1',
        organization: { id: 'org1' },
      } as Installations;

      // Mock API error
      mockSlackWebAPIService.listWorkspaceEmojis.mockRejectedValueOnce(
        new Error('API rate limit exceeded')
      );

      // Act
      await job.execute(installation);

      // Assert
      expect(mockLogger.error).toHaveBeenCalled();
      expect(mockTransactionService.runInTransaction).not.toHaveBeenCalled();
      expect(mockSlackEmojisRepository.upsertWithTxn).not.toHaveBeenCalled();
    });

    it('should handle empty emoji list gracefully', async () => {
      // Arrange
      const installation = {
        id: 'installation1',
        botToken: 'xoxb-123456',
        teamId: 'team1',
        organization: { id: 'org1' },
      } as Installations;

      // Mock Slack API response with empty emoji object
      mockSlackWebAPIService.listWorkspaceEmojis.mockResolvedValue({
        ok: true,
        emoji: {},
      });

      // Act
      await job.execute(installation);

      // Assert
      expect(mockTransactionService.runInTransaction).toHaveBeenCalledTimes(1);
      expect(mockSlackEmojisRepository.upsertWithTxn).toHaveBeenCalledWith(
        'txn-context',
        [],
        { conflictPaths: ['name', 'installation'] }
      );
    });

    it('should handle transaction failures gracefully', async () => {
      // Arrange
      const installation = {
        id: 'installation1',
        botToken: 'xoxb-123456',
        teamId: 'team1',
        organization: { id: 'org1' },
      } as Installations;

      const emoji = {
        'party': 'https://emoji.slack-edge.com/T123/party/123.gif',
        'smile': 'https://emoji.slack-edge.com/T123/smile/456.png',
      };

      // Mock successful Slack API response
      mockSlackWebAPIService.listWorkspaceEmojis.mockResolvedValue({
        ok: true,
        emoji,
      });

      // Mock transaction failure
      mockTransactionService.runInTransaction.mockRejectedValue(
        new Error('Transaction failed')
      );

      // Act
      await job.execute(installation);

      // Assert
      expect(mockLogger.error).toHaveBeenCalledWith(
        expect.stringContaining('Slack emojis sync failed'),
        expect.any(String)
      );
    });

    it('should handle invalid emoji data gracefully', async () => {
      // Arrange
      const installation = {
        id: 'installation1',
        botToken: 'xoxb-123456',
        teamId: 'team1',
        organization: { id: 'org1' },
      } as Installations;

      // Mock Slack API response with invalid emoji data
      const emoji = {
        'party': 'https://emoji.slack-edge.com/T123/party/123.gif',
        'invalid': null, // Invalid URL
        'empty': '', // Empty URL
      };

      mockSlackWebAPIService.listWorkspaceEmojis.mockResolvedValue({
        ok: true,
        emoji,
      });

      // Act
      await job.execute(installation);

      // Assert
      expect(mockTransactionService.runInTransaction).toHaveBeenCalledTimes(1);
      expect(mockSlackEmojisRepository.upsertWithTxn).toHaveBeenCalledWith(
        'txn-context',
        expect.arrayContaining([
          expect.objectContaining({
            name: 'party',
            url: 'https://emoji.slack-edge.com/T123/party/123.gif',
          }),
          expect.objectContaining({
            name: 'invalid',
            url: null,
          }),
          expect.objectContaining({
            name: 'empty',
            url: '',
          }),
        ]),
        { conflictPaths: ['name', 'installation'] }
      );
    });

    it('should handle writeEmojis method errors gracefully', async () => {
      // Arrange
      const installation = {
        id: 'installation1',
        botToken: 'xoxb-123456',
        teamId: 'team1',
        organization: { id: 'org1' },
      } as Installations;

      const emoji = {
        'party': 'https://emoji.slack-edge.com/T123/party/123.gif',
        'smile': 'https://emoji.slack-edge.com/T123/smile/456.png',
      };

      // Mock successful Slack API response
      mockSlackWebAPIService.listWorkspaceEmojis.mockResolvedValue({
        ok: true,
        emoji,
      });

      // Mock repository error
      mockSlackEmojisRepository.upsertWithTxn.mockRejectedValue(
        new Error('Repository error')
      );

      // Act
      await job.execute(installation);

      // Assert
      expect(mockLogger.error).toHaveBeenCalledWith(
        expect.stringContaining('Slack emojis sync failed'),
        expect.any(String)
      );
    });
  });
}); 