import { describe, it, expect, beforeEach, vi, <PERSON><PERSON> } from 'vitest';
import { WorkspaceSyncProcessor } from '../../../../src/slack/processors/workspace-sync.processor';
import { ILogger } from '../../../../src/utils/logger';
import { InstallationRepository } from '../../../../src/database/entities/installations/repositories';
import { InstallationStatus } from '../../../../src/database/entities/installations';
import { Job } from 'bullmq';
import {
  SlackAccountsSyncJob,
  SlackChannelsSyncJob,
  SlackEmojiSyncJob,
  SlackExternalUsersSyncJob,
  SlackUsersSyncJob,
} from '../../../../src/slack/processors/jobs';
import { SlackSubgroupsSyncJob } from '../../../../src/slack/processors/jobs/slack-subgroups-sync.job';

describe('WorkspaceSyncProcessor', () => {
  let processor: WorkspaceSyncProcessor;
  let mockLogger: ILogger;
  let mockInstallationsRepository: InstallationRepository;
  let mockSlackChannelsSyncJob: SlackChannelsSyncJob;
  let mockSlackUsersSyncJob: SlackUsersSyncJob;
  let mockSlackExternalUsersSyncJob: SlackExternalUsersSyncJob;
  let mockSlackSubgroupsSyncJob: SlackSubgroupsSyncJob;
  let mockSlackAccountsSyncJob: SlackAccountsSyncJob;
  let mockSlackEmojisSyncJob: SlackEmojiSyncJob;

  const mockInstallation = {
    id: 'installation-123',
    teamId: 'T12345',
    botToken: 'xoxb-token',
    organization: { id: 'org-123' }
  };

  beforeEach(() => {
    mockLogger = {
      debug: vi.fn(),
      log: vi.fn(),
      error: vi.fn(),
      warn: vi.fn(),
      verbose: vi.fn()
    } as unknown as ILogger;

    mockInstallationsRepository = {
      findByCondition: vi.fn(),
      update: vi.fn()
    } as unknown as InstallationRepository;

    mockSlackChannelsSyncJob = {
      execute: vi.fn()
    } as unknown as SlackChannelsSyncJob;

    mockSlackUsersSyncJob = {
      execute: vi.fn()
    } as unknown as SlackUsersSyncJob;

    mockSlackExternalUsersSyncJob = {
      execute: vi.fn()
    } as unknown as SlackExternalUsersSyncJob;

    mockSlackSubgroupsSyncJob = {
      execute: vi.fn()
    } as unknown as SlackSubgroupsSyncJob;

    mockSlackAccountsSyncJob = {
      execute: vi.fn()
    } as unknown as SlackAccountsSyncJob;

    mockSlackEmojisSyncJob = {
      execute: vi.fn()
    } as unknown as SlackEmojiSyncJob;

    processor = new WorkspaceSyncProcessor(
      mockLogger,
      mockInstallationsRepository,
      mockSlackChannelsSyncJob,
      mockSlackUsersSyncJob,
      mockSlackExternalUsersSyncJob,
      mockSlackSubgroupsSyncJob,
      mockSlackAccountsSyncJob,
      mockSlackEmojisSyncJob
    );

    vi.spyOn(console, 'time').mockImplementation(() => {});
    vi.spyOn(console, 'timeEnd').mockImplementation(() => {});
  });

  describe('process', () => {
    it('should process job and execute all sync jobs', async () => {
      const job = {
        data: {
          installationId: 'installation-123'
        }
      } as Job;

      (mockInstallationsRepository.findByCondition as Mock).mockResolvedValue(mockInstallation);
      (mockSlackAccountsSyncJob.execute as Mock).mockResolvedValue({ 'example.com': 'account-123' });

      await processor.process(job);

      expect(mockInstallationsRepository.findByCondition).toHaveBeenCalledWith({
        where: { id: 'installation-123' },
        relations: ['organization']
      });
      expect(mockLogger.log).toHaveBeenCalledWith(
        'Slack workspace sync started for installation installation-123'
      );
      expect(mockSlackChannelsSyncJob.execute).toHaveBeenCalledWith(mockInstallation);
      expect(mockSlackUsersSyncJob.execute).toHaveBeenCalledWith(mockInstallation);
      expect(mockSlackSubgroupsSyncJob.execute).toHaveBeenCalledWith(mockInstallation);
      expect(mockSlackEmojisSyncJob.execute).toHaveBeenCalledWith(mockInstallation);
      expect(mockSlackAccountsSyncJob.execute).toHaveBeenCalledWith(mockInstallation);
      expect(mockSlackExternalUsersSyncJob.execute).toHaveBeenCalledWith(
        mockInstallation,
        null,
        { 'example.com': 'account-123' }
      );
      expect(mockInstallationsRepository.update).toHaveBeenCalledWith(
        'installation-123',
        { status: InstallationStatus.SYNCED }
      );
    });

    it('should skip job if installation is not found', async () => {
      const job = {
        data: {
          installationId: 'non-existent-installation'
        }
      } as Job;

      (mockInstallationsRepository.findByCondition as Mock).mockResolvedValue(null);

      await processor.process(job);

      expect(mockInstallationsRepository.findByCondition).toHaveBeenCalledWith({
        where: { id: 'non-existent-installation' },
        relations: ['organization']
      });
      expect(mockLogger.error).toHaveBeenCalledWith(
        'Installation not found for id non-existent-installation'
      );
      expect(mockSlackChannelsSyncJob.execute).not.toHaveBeenCalled();
      expect(mockSlackUsersSyncJob.execute).not.toHaveBeenCalled();
      expect(mockSlackSubgroupsSyncJob.execute).not.toHaveBeenCalled();
      expect(mockSlackEmojisSyncJob.execute).not.toHaveBeenCalled();
      expect(mockSlackAccountsSyncJob.execute).not.toHaveBeenCalled();
      expect(mockSlackExternalUsersSyncJob.execute).not.toHaveBeenCalled();
      expect(mockInstallationsRepository.update).not.toHaveBeenCalled();
    });
  });
});
