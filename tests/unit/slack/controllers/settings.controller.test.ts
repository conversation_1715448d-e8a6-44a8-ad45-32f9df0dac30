import {
  BadRequestException,
  InternalServerErrorException,
} from '@nestjs/common';
import { Mock, beforeEach, describe, expect, it, vi } from 'vitest';
import { BotCtx } from '../../../../src/auth/interfaces';
import {
  InstallationStatus,
  Installations,
} from '../../../../src/database/entities/installations/installations.entity';
import { Organizations } from '../../../../src/database/entities/organizations/organizations.entity';
import { SettingsController } from '../../../../src/slack/controllers/settings.controller';
import { UpdateSettingsDTO } from '../../../../src/slack/dtos/settings.dto';
import { SettingsService } from '../../../../src/slack/services/settings.service';

describe('SettingsController', () => {
  let controller: SettingsController;
  let mockSettingsService: SettingsService;

  const createMockInstallation = (
    id: string,
    teamId: string,
  ): Installations => ({
    id,
    name: `Installation ${id}`,
    installationDump: {
      team: { id: teamId },
      enterprise: undefined,
      user: {
        id: `user-${id}`,
        token: `token-${id}`,
        scopes: ['chat:write'],
      },
    },
    teamId,
    domains: 'example.com',
    teamName: `Team ${id}`,
    enterpriseId: null,
    teamInfo: {},
    status: InstallationStatus.SYNCED,
    installingUserId: `user-${id}`,
    installingUserSlackId: `slack-user-${id}`,
    installingUserName: `User ${id}`,
    botToken: `xoxb-token-${id}`,
    botSlackId: `bot-${id}`,
    botSlackUserId: `bot-user-${id}`,
    slackAppAuthToken: `auth-token-${id}`,
    disconnected: false,
    disconnectedOn: undefined as unknown as Date,
    platformDump: {} as any,
    createdAt: new Date(),
    updatedAt: new Date(),
    deletedAt: undefined as unknown as Date,
    bots: [],
    channels: [],
    users: [],
    teams: [],
    subgroups: [],
    organization: null as any,
  });

  const createMockOrganization = (id: string): Organizations => ({
    id,
    externalPk: `ext-${id}`,
    name: `Organization ${id}`,
    uid: `org-uid-${id}`,
    apiKey: `api-key-${id}`,
    installingUserId: `user-${id}`,
    metadata: {
      applicationId: `app-${id}`,
      installationId: `inst-${id}`,
      createdBy: `user-${id}`,
    },
    bots: [],
    users: [],
    channels: [],
    installations: [],
    teams: [],
    subgroups: [],
  });

  const mockInstallation = createMockInstallation('1', 'team-1');
  const mockOrganization = createMockOrganization('1');

  const botCtx: BotCtx = {
    installations: [mockInstallation],
    installation: mockInstallation,
    organization: mockOrganization,
  };

  const mockSettings = {
    id: 'settings-1',
    teamId: 'team-1',
    settings: {
      aiEnabled: true,
      customField1: 'value1',
      customField2: 'value2',
    },
    createdAt: new Date(),
    updatedAt: new Date(),
  };

  beforeEach(() => {
    mockSettingsService = {
      getTeamSettings: vi.fn(),
      updateSettings: vi.fn(),
      deleteSetting: vi.fn(),
      resetSettings: vi.fn(),
    } as unknown as SettingsService;

    controller = new SettingsController(mockSettingsService);
  });

  describe('getTeamSettings', () => {
    it('should get team settings successfully', async () => {
      (mockSettingsService.getTeamSettings as Mock).mockResolvedValue(
        mockSettings,
      );

      const result = await controller.getTeamSettings('team-1', botCtx);

      expect(result).toEqual(mockSettings);
      expect(mockSettingsService.getTeamSettings).toHaveBeenCalledWith(
        'team-1',
        botCtx,
      );
    });

    it('should handle errors when getting team settings', async () => {
      const error = new BadRequestException('Team not found');
      (mockSettingsService.getTeamSettings as Mock).mockRejectedValue(error);

      await expect(
        controller.getTeamSettings('team-1', botCtx),
      ).rejects.toThrow(BadRequestException);
    });
  });

  describe('updateSettings', () => {
    const updateSettingsDTO = {
      automaticTickets: false,
      slashCommands: true,
      aiModel: 'gpt-4',
      aiMaxTokens: 2000,
      aiTemperature: 0.7,
    } as UpdateSettingsDTO;

    it('should update settings successfully', async () => {
      const updatedSettings = {
        ...mockSettings,
        settings: {
          ...mockSettings.settings,
          automaticTickets: updateSettingsDTO.automaticTickets,
          slashCommands: updateSettingsDTO.slashCommands,
          aiModel: updateSettingsDTO.aiModel,
          aiMaxTokens: updateSettingsDTO.aiMaxTokens,
          aiTemperature: updateSettingsDTO.aiTemperature,
        },
      };
      (mockSettingsService.updateSettings as Mock).mockResolvedValue(
        updatedSettings,
      );

      const result = await controller.updateSettings(
        'team-1',
        updateSettingsDTO,
        botCtx,
      );

      expect(result).toEqual(updatedSettings);
      expect(mockSettingsService.updateSettings).toHaveBeenCalledWith(
        'team-1',
        updateSettingsDTO,
        botCtx,
      );
    });

    it('should handle errors when updating settings', async () => {
      const error = new InternalServerErrorException(
        'Failed to update settings',
      );
      (mockSettingsService.updateSettings as Mock).mockRejectedValue(error);

      await expect(
        controller.updateSettings('team-1', updateSettingsDTO, botCtx),
      ).rejects.toThrow(InternalServerErrorException);
    });
  });

  describe('deleteSetting', () => {
    it('should delete setting successfully', async () => {
      const updatedSettings = {
        ...mockSettings,
        settings: {
          aiEnabled: true,
          customField2: 'value2',
        },
      };
      (mockSettingsService.deleteSetting as Mock).mockResolvedValue(
        updatedSettings,
      );

      const result = await controller.deleteSetting(
        'team-1',
        'customField1',
        botCtx,
      );

      expect(result).toEqual(updatedSettings);
      expect(mockSettingsService.deleteSetting).toHaveBeenCalledWith(
        'team-1',
        'customField1',
        botCtx,
      );
    });

    it('should handle errors when deleting setting', async () => {
      const error = new BadRequestException('Setting not found');
      (mockSettingsService.deleteSetting as Mock).mockRejectedValue(error);

      await expect(
        controller.deleteSetting('team-1', 'customField1', botCtx),
      ).rejects.toThrow(BadRequestException);
    });
  });

  describe('resetSettings', () => {
    it('should reset settings successfully', async () => {
      const defaultSettings = {
        id: 'settings-1',
        teamId: 'team-1',
        settings: {
          aiEnabled: true,
        },
        createdAt: new Date(),
        updatedAt: new Date(),
      };
      (mockSettingsService.resetSettings as Mock).mockResolvedValue(
        defaultSettings,
      );

      const result = await controller.resetSettings('team-1', botCtx);

      expect(result).toEqual(defaultSettings);
      expect(mockSettingsService.resetSettings).toHaveBeenCalledWith(
        'team-1',
        botCtx,
      );
    });

    it('should handle errors when resetting settings', async () => {
      const error = new InternalServerErrorException(
        'Failed to reset settings',
      );
      (mockSettingsService.resetSettings as Mock).mockRejectedValue(error);

      await expect(controller.resetSettings('team-1', botCtx)).rejects.toThrow(
        InternalServerErrorException,
      );
    });
  });
});
