import { BadRequestException, NotFoundException } from '@nestjs/common';
import { Mock, beforeEach, describe, expect, it, vi } from 'vitest';
import { BotCtx } from '../../../../src/auth/interfaces';
import {
  InstallationStatus,
  Installations,
} from '../../../../src/database/entities/installations/installations.entity';
import { Organizations } from '../../../../src/database/entities/organizations/organizations.entity';
import { TriageRulesController } from '../../../../src/slack/controllers/triage-rules.controller';
import {
  CreateTriageRuleDto,
  UpdateTriageRuleDto,
} from '../../../../src/slack/dtos/triage-rule.dto';
import { TriageRulesService } from '../../../../src/slack/services/triage-rules.service';
import { ILogger } from '../../../../src/utils/logger/logger.interface';

describe('TriageRulesController', () => {
  let controller: TriageRulesController;
  let mockLogger: ILogger;
  let mockTriageRulesService: TriageRulesService;

  const createMockInstallation = (
    id: string,
    teamId: string,
  ): Installations => ({
    id,
    name: `Installation ${id}`,
    installationDump: {
      team: { id: teamId },
      enterprise: undefined,
      user: {
        id: `user-${id}`,
        token: `token-${id}`,
        scopes: ['chat:write'],
      },
    },
    teamId,
    domains: 'example.com',
    teamName: `Team ${id}`,
    enterpriseId: null,
    teamInfo: {},
    status: InstallationStatus.SYNCED,
    installingUserId: `user-${id}`,
    installingUserSlackId: `slack-user-${id}`,
    installingUserName: `User ${id}`,
    botToken: `xoxb-token-${id}`,
    botSlackId: `bot-${id}`,
    botSlackUserId: `bot-user-${id}`,
    slackAppAuthToken: `auth-token-${id}`,
    disconnected: false,
    disconnectedOn: undefined as unknown as Date,
    platformDump: {} as any,
    createdAt: new Date(),
    updatedAt: new Date(),
    deletedAt: undefined as unknown as Date,
    bots: [],
    channels: [],
    users: [],
    teams: [],
    subgroups: [],
    organization: null as any,
  });

  const createMockOrganization = (id: string): Organizations => ({
    id,
    externalPk: `ext-${id}`,
    name: `Organization ${id}`,
    uid: `org-uid-${id}`,
    apiKey: `api-key-${id}`,
    installingUserId: `user-${id}`,
    metadata: {
      applicationId: `app-${id}`,
      installationId: `inst-${id}`,
      createdBy: `user-${id}`,
    },
    bots: [],
    users: [],
    channels: [],
    installations: [],
    teams: [],
    subgroups: [],
  });

  const mockInstallation = createMockInstallation('1', 'team-1');
  const mockOrganization = createMockOrganization('1');

  const botCtx: BotCtx = {
    installations: [mockInstallation],
    installation: mockInstallation,
    organization: mockOrganization,
  };

  beforeEach(() => {
    vi.resetAllMocks();

    mockLogger = {
      log: vi.fn(),
      error: vi.fn(),
      warn: vi.fn(),
      debug: vi.fn(),
      verbose: vi.fn(),
    } as unknown as ILogger;

    mockTriageRulesService = {
      createRule: vi.fn(),
      getTeamRules: vi.fn(),
      updateRule: vi.fn(),
      deleteRule: vi.fn(),
    } as unknown as TriageRulesService;

    controller = new TriageRulesController(mockLogger, mockTriageRulesService);
  });

  describe('createRule', () => {
    const createRuleDto: CreateTriageRuleDto = {
      isDefault: false,
      channelIds: ['C12345', 'C67890'],
      triageRules: {
        AND: [
          {
            category: 'message' as any,
            field: 'content',
            operator: 'contains' as any,
            value: 'test',
          },
        ],
      },
    };
    const teamId = 'team-1';

    it('should create a triage rule successfully', async () => {
      const expectedResponse = {
        id: 'rule-1',
        ...createRuleDto,
      };
      (mockTriageRulesService.createRule as Mock).mockResolvedValue(
        expectedResponse,
      );

      const result = await controller.createRule(teamId, createRuleDto, botCtx);

      expect(result).toEqual(expectedResponse);
      expect(mockTriageRulesService.createRule).toHaveBeenCalledWith(
        teamId,
        createRuleDto,
        botCtx,
      );
    });

    it('should handle HttpException from service', async () => {
      const httpError = new BadRequestException('Invalid rule data');
      (mockTriageRulesService.createRule as Mock).mockRejectedValue(httpError);

      await expect(
        controller.createRule(teamId, createRuleDto, botCtx),
      ).rejects.toThrow(httpError);
    });

    it('should handle general errors and log them', async () => {
      const error = new Error('Service error');
      (mockTriageRulesService.createRule as Mock).mockRejectedValue(error);

      await expect(
        controller.createRule(teamId, createRuleDto, botCtx),
      ).rejects.toThrow();
    });
  });

  describe('getRules', () => {
    const teamId = 'team-1';

    it('should get all triage rules for a team', async () => {
      const expectedRules = [
        {
          id: 'rule-1',
          isDefault: false,
          channelIds: ['C12345'],
          triageRules: {
            AND: [
              {
                category: 'message' as any,
                field: 'content',
                operator: 'contains' as any,
                value: 'test',
              },
            ],
          },
        },
        {
          id: 'rule-2',
          isDefault: true,
          channelIds: ['C67890'],
        },
      ];
      (mockTriageRulesService.getTeamRules as Mock).mockResolvedValue(
        expectedRules,
      );

      const result = await controller.getRules(teamId, botCtx);

      expect(result).toEqual(expectedRules);
      expect(mockTriageRulesService.getTeamRules).toHaveBeenCalledWith(
        teamId,
        botCtx,
      );
    });

    it('should handle HttpException from service', async () => {
      const httpError = new NotFoundException('Team not found');
      (mockTriageRulesService.getTeamRules as Mock).mockRejectedValue(
        httpError,
      );

      await expect(controller.getRules(teamId, botCtx)).rejects.toThrow(
        httpError,
      );
    });

    it('should handle general errors and log them', async () => {
      const error = new Error('Service error');
      (mockTriageRulesService.getTeamRules as Mock).mockRejectedValue(error);

      await expect(controller.getRules(teamId, botCtx)).rejects.toThrow();
    });
  });

  describe('updateRule', () => {
    const ruleId = 'rule-1';
    const updateRuleDto: UpdateTriageRuleDto = {
      isEnabled: false,
      channelIds: ['C12345', 'C67890'],
      triageRules: {
        OR: [
          {
            category: 'message' as any,
            field: 'content',
            operator: 'contains' as any,
            value: 'updated',
          },
        ],
      },
    };

    it('should update a triage rule successfully', async () => {
      const expectedResponse = {
        id: ruleId,
        ...updateRuleDto,
      };
      (mockTriageRulesService.updateRule as Mock).mockResolvedValue(
        expectedResponse,
      );

      const result = await controller.updateRule(ruleId, updateRuleDto, botCtx);

      expect(result).toEqual(expectedResponse);
      expect(mockTriageRulesService.updateRule).toHaveBeenCalledWith(
        ruleId,
        updateRuleDto,
        botCtx,
      );
    });

    it('should handle HttpException from service', async () => {
      const httpError = new NotFoundException('Rule not found');
      (mockTriageRulesService.updateRule as Mock).mockRejectedValue(httpError);

      await expect(
        controller.updateRule(ruleId, updateRuleDto, botCtx),
      ).rejects.toThrow(httpError);
    });

    it('should handle general errors and log them', async () => {
      const error = new Error('Service error');
      (mockTriageRulesService.updateRule as Mock).mockRejectedValue(error);

      await expect(
        controller.updateRule(ruleId, updateRuleDto, botCtx),
      ).rejects.toThrow();
    });
  });

  describe('deleteRule', () => {
    const ruleId = 'rule-1';

    it('should delete a triage rule successfully', async () => {
      const expectedResponse = { success: true };
      (mockTriageRulesService.deleteRule as Mock).mockResolvedValue(
        expectedResponse,
      );

      const result = await controller.deleteRule(ruleId, botCtx);

      expect(result).toEqual(expectedResponse);
      expect(mockTriageRulesService.deleteRule).toHaveBeenCalledWith(
        ruleId,
        botCtx,
      );
    });

    it('should handle HttpException from service', async () => {
      const httpError = new NotFoundException('Rule not found');
      (mockTriageRulesService.deleteRule as Mock).mockRejectedValue(httpError);

      await expect(controller.deleteRule(ruleId, botCtx)).rejects.toThrow(
        httpError,
      );
    });

    it('should handle general errors and log them', async () => {
      const error = new Error('Service error');
      (mockTriageRulesService.deleteRule as Mock).mockRejectedValue(error);

      await expect(controller.deleteRule(ruleId, botCtx)).rejects.toThrow();
    });
  });
});
