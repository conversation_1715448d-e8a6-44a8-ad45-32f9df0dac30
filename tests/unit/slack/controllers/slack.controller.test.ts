import { describe, it, expect, beforeEach, vi, Mock } from 'vitest';
import { SlackController } from '../../../../src/slack/controllers/slack.controller';
import { ILogger } from '../../../../src/utils/logger/logger.interface';
import { SlackService } from '../../../../src/slack/services/slack.service';
import { BadRequestException, InternalServerErrorException } from '@nestjs/common';
import { Response } from 'express';
import { BotCtx } from '../../../../src/auth/interfaces';
import { InstallationStatus } from '../../../../src/database/entities/installations/installations.entity';

describe('SlackController', () => {
  let controller: SlackController;
  let mockLogger: ILogger;
  let mockSlackService: SlackService;
  let mockResponse: Response;

  const createMockInstallation = (id: string, teamId: string) => ({
    id,
    name: `Installation ${id}`,
    installationDump: {},
    teamId,
    domains: 'example.com',
    teamName: `Team ${id}`,
    enterpriseId: null,
    teamInfo: {},
    status: InstallationStatus.SYNCED,
    installingUserId: `user-${id}`,
    installingUserSlackId: `slack-user-${id}`,
    installingUserName: `User ${id}`,
    botToken: `xoxb-token-${id}`,
    botSlackId: `bot-${id}`,
    botSlackUserId: `bot-user-${id}`,
    slackAppAuthToken: `auth-token-${id}`,
    disconnected: false,
    disconnectedOn: null,
    platformDump: null,
    createdAt: new Date(),
    updatedAt: new Date(),
    deletedAt: null,
    bots: [],
    channels: [],
    users: [],
    teams: [],
    subgroups: [],
  });

  const createMockOrganization = (id: string) => ({
    id,
    externalPk: `ext-${id}`,
    name: `Organization ${id}`,
    uid: `org-uid-${id}`,
    apiKey: `api-key-${id}`,
    installingUserId: `user-${id}`,
    metadata: {
      applicationId: `app-${id}`,
      installationId: `inst-${id}`,
      createdBy: `user-${id}`,
    },
    bots: [],
    users: [],
    channels: [],
    installations: [],
    teams: [],
    subgroups: [],
  });

  beforeEach(() => {
    vi.resetAllMocks();
    
    mockLogger = {
      log: vi.fn(),
      error: vi.fn(),
      warn: vi.fn(),
      debug: vi.fn(),
      verbose: vi.fn(),
    } as unknown as ILogger;

    mockSlackService = {
      installForOrganization: vi.fn(),
      getWorkspaces: vi.fn(),
      disconnectWorkspace: vi.fn(),
    } as unknown as SlackService;

    mockResponse = {
      redirect: vi.fn(),
    } as unknown as Response;

    controller = new SlackController(
      mockLogger,
      mockSlackService,
    );
  });

  describe('installForOrganization', () => {
    it('should redirect to the Slack app installation page', async () => {
      const orgId = 'org-123';
      const userId = 'user-123';
      const mockOrganization = { uid: 'org-123' };
      (mockSlackService.installForOrganization as Mock).mockResolvedValue(mockOrganization);

      await controller.installForOrganization(orgId, userId, mockResponse);

      expect(mockSlackService.installForOrganization).toHaveBeenCalledWith({
        orgId,
        userId,
      });
      expect(mockResponse.redirect).toHaveBeenCalledWith(
        `/slack/install?orgId=${mockOrganization.uid}&installingUser=${userId}`,
      );
    });

    it('should throw BadRequestException if orgId is not provided', async () => {
      const orgId = '';
      const userId = 'user-123';

      await expect(controller.installForOrganization(orgId, userId, mockResponse))
        .rejects.toThrow(BadRequestException);
      expect(mockSlackService.installForOrganization).not.toHaveBeenCalled();
    });

    it('should throw BadRequestException if userId is not provided', async () => {
      const orgId = 'org-123';
      const userId = '';

      await expect(controller.installForOrganization(orgId, userId, mockResponse))
        .rejects.toThrow(BadRequestException);
      expect(mockSlackService.installForOrganization).not.toHaveBeenCalled();
    });

    it('should propagate errors from the service', async () => {
      const orgId = 'org-123';
      const userId = 'user-123';
      const mockError = new Error('Service error');
      (mockSlackService.installForOrganization as Mock).mockRejectedValue(mockError);

      await expect(controller.installForOrganization(orgId, userId, mockResponse))
        .rejects.toThrow(mockError);
    });
  });

  describe('getWorkspaces', () => {
    it('should return workspaces', async () => {
      const mockInstallation1 = createMockInstallation('1', 'team-1');
      const mockInstallation2 = createMockInstallation('2', 'team-2');
      const mockOrganization = createMockOrganization('1');

      const botCtx: BotCtx = {
        installations: [mockInstallation1, mockInstallation2],
        installation: mockInstallation1,
        organization: mockOrganization,
      };

      const salt = 'test-salt';
      const mockWorkspaces = [
        { id: 'workspace-1', name: 'Workspace 1' },
        { id: 'workspace-2', name: 'Workspace 2' },
      ];
      (mockSlackService.getWorkspaces as Mock).mockResolvedValue(mockWorkspaces);

      const result = await controller.getWorkspaces(botCtx, salt);

      expect(mockSlackService.getWorkspaces).toHaveBeenCalledWith(botCtx, salt);
      expect(result).toEqual(mockWorkspaces);
    });

    it('should throw BadRequestException if installations is not an array', async () => {
      const mockInstallation = createMockInstallation('1', 'team-1');
      const mockOrganization = createMockOrganization('1');

      const botCtx: BotCtx = {
        installations: mockInstallation as any,
        installation: mockInstallation,
        organization: mockOrganization,
      };
      const salt = 'test-salt';

      await expect(controller.getWorkspaces(botCtx, salt))
        .rejects.toThrow(BadRequestException);
      expect(mockSlackService.getWorkspaces).not.toHaveBeenCalled();
    });

    it('should throw BadRequestException if installations array is empty', async () => {
      const mockInstallation = createMockInstallation('1', 'team-1');
      const mockOrganization = createMockOrganization('1');

      const botCtx: BotCtx = {
        installations: [],
        installation: mockInstallation,
        organization: mockOrganization,
      };
      const salt = 'test-salt';

      await expect(controller.getWorkspaces(botCtx, salt))
        .rejects.toThrow(BadRequestException);
      expect(mockSlackService.getWorkspaces).not.toHaveBeenCalled();
    });

    it('should handle service errors and throw InternalServerErrorException', async () => {
      const mockInstallation1 = createMockInstallation('1', 'team-1');
      const mockInstallation2 = createMockInstallation('2', 'team-2');
      const mockOrganization = createMockOrganization('1');

      const botCtx: BotCtx = {
        installations: [mockInstallation1, mockInstallation2],
        installation: mockInstallation1,
        organization: mockOrganization,
      };
      const salt = 'test-salt';
      const mockError = new Error('Service error');
      (mockSlackService.getWorkspaces as Mock).mockRejectedValue(mockError);

      await expect(controller.getWorkspaces(botCtx, salt))
        .rejects.toThrow(InternalServerErrorException);
      expect(mockLogger.error).toHaveBeenCalled();
    });

    it('should propagate HttpException from the service', async () => {
      const mockInstallation1 = createMockInstallation('1', 'team-1');
      const mockInstallation2 = createMockInstallation('2', 'team-2');
      const mockOrganization = createMockOrganization('1');

      const botCtx: BotCtx = {
        installations: [mockInstallation1, mockInstallation2],
        installation: mockInstallation1,
        organization: mockOrganization,
      };
      const salt = 'test-salt';
      const mockError = new BadRequestException('Service error');
      (mockSlackService.getWorkspaces as Mock).mockRejectedValue(mockError);

      await expect(controller.getWorkspaces(botCtx, salt))
        .rejects.toThrow(BadRequestException);
      expect(mockLogger.error).toHaveBeenCalled();
    });
  });

  describe('disconnectWorkspace', () => {
    it('should disconnect the workspace', async () => {
      const mockInstallation = createMockInstallation('1', 'team-1');
      const mockOrganization = createMockOrganization('1');

      const botCtx: BotCtx = {
        installations: [mockInstallation],
        installation: mockInstallation,
        organization: mockOrganization,
      };
      (mockSlackService.disconnectWorkspace as Mock).mockResolvedValue(undefined);

      await controller.disconnectWorkspace(botCtx);

      expect(mockSlackService.disconnectWorkspace).toHaveBeenCalledWith(botCtx);
    });

    it('should handle service errors and throw InternalServerErrorException', async () => {
      const mockInstallation = createMockInstallation('1', 'team-1');
      const mockOrganization = createMockOrganization('1');

      const botCtx: BotCtx = {
        installations: [mockInstallation],
        installation: mockInstallation,
        organization: mockOrganization,
      };
      const mockError = new Error('Service error');
      (mockSlackService.disconnectWorkspace as Mock).mockRejectedValue(mockError);

      await expect(controller.disconnectWorkspace(botCtx))
        .rejects.toThrow(InternalServerErrorException);
      expect(mockLogger.error).toHaveBeenCalled();
    });

    it('should propagate HttpException from the service', async () => {
      const mockInstallation = createMockInstallation('1', 'team-1');
      const mockOrganization = createMockOrganization('1');

      const botCtx: BotCtx = {
        installations: [mockInstallation],
        installation: mockInstallation,
        organization: mockOrganization,
      };
      const mockError = new BadRequestException('Service error');
      (mockSlackService.disconnectWorkspace as Mock).mockRejectedValue(mockError);

      await expect(controller.disconnectWorkspace(botCtx))
        .rejects.toThrow(BadRequestException);
      expect(mockLogger.error).toHaveBeenCalled();
    });
  });
});
