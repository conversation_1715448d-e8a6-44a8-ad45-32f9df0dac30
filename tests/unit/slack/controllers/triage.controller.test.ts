import {
  BadRequestException,
  InternalServerErrorException,
} from '@nestjs/common';
import { Mock, beforeEach, describe, expect, it, vi } from 'vitest';
import { BotCtx } from '../../../../src/auth/interfaces';
import {
  InstallationStatus,
  Installations,
} from '../../../../src/database/entities/installations/installations.entity';
import { Organizations } from '../../../../src/database/entities/organizations/organizations.entity';
import { SlackChannelController } from '../../../../src/slack/controllers/configurations.controller';
import { TriageRulesController } from '../../../../src/slack/controllers/triage-rules.controller';
import {
  ConfigureTriageChannelDTO,
  CreateTriageThreadDTO,
} from '../../../../src/slack/dtos';
import {
  CreateTriageRuleDto,
  UpdateTriageRuleDto,
} from '../../../../src/slack/dtos/triage-rule.dto';
import { SlackChannelService } from '../../../../src/slack/services/slack-channel.service';
import { TriageRulesService } from '../../../../src/slack/services/triage-rules.service';
import { ILogger } from '../../../../src/utils/logger/logger.interface';

// Helper function to create mock installations for testing
const createMockInstallation = (id: string, teamId: string): Installations => ({
  id,
  teamId,
  userId: 'U123456',
  isEnterpriseInstall: false,
  status: InstallationStatus.ACTIVE,
  installationDump: {
    team: { id: teamId, name: 'Team Name' },
    enterprise: null,
    user: { id: 'U123456', name: 'User Name' },
  },
  appId: 'A123456',
  organization: { id: 'O123456' } as Organizations,
  createdAt: new Date(),
  updatedAt: new Date(),
});

// Helper function to create mock organizations for testing
const createMockOrganization = (id: string): Organizations => ({
  id,
  displayName: 'Org Name',
  createdAt: new Date(),
  updatedAt: new Date(),
});

describe('Triage Controller Tests', () => {
  // Combined testing for both TriageRulesController and the triage-related endpoints in SlackChannelController

  describe('TriageRulesController', () => {
    let controller: TriageRulesController;
    let mockLogger: ILogger;
    let mockTriageRulesService: TriageRulesService;
    let botCtx: BotCtx;

    beforeEach(() => {
      vi.resetAllMocks();

      // Mock the installations and organization
      const mockInstallation = createMockInstallation('1', 'T123456');
      const mockOrganization = createMockOrganization('1');

      // Set up the bot context
      botCtx = {
        installations: [mockInstallation],
        installation: mockInstallation,
        organization: mockOrganization,
      };

      // Mock the logger
      mockLogger = {
        log: vi.fn(),
        error: vi.fn(),
        warn: vi.fn(),
        debug: vi.fn(),
        verbose: vi.fn(),
      } as unknown as ILogger;

      // Mock the triage rules service
      mockTriageRulesService = {
        createRule: vi.fn(),
        getTeamRules: vi.fn(),
        updateRule: vi.fn(),
        deleteRule: vi.fn(),
      } as unknown as TriageRulesService;

      // Create the controller
      controller = new TriageRulesController(
        mockLogger,
        mockTriageRulesService,
      );
    });

    describe('createRule', () => {
      const createRuleDto: CreateTriageRuleDto = {
        isDefault: false,
        channelIds: ['C12345', 'C67890'],
        triageRules: {
          AND: [
            {
              category: 'message' as any,
              field: 'content',
              operator: 'contains' as any,
              value: 'test',
            },
          ],
        },
      };
      const teamId = 'T123456';

      it('should create a triage rule successfully', async () => {
        const expectedResponse = {
          id: 'rule-1',
          ...createRuleDto,
        };
        (mockTriageRulesService.createRule as Mock).mockResolvedValue(
          expectedResponse,
        );

        const result = await controller.createRule(
          teamId,
          createRuleDto,
          botCtx,
        );

        expect(result).toEqual(expectedResponse);
        expect(mockTriageRulesService.createRule).toHaveBeenCalledWith(
          teamId,
          createRuleDto,
          botCtx,
        );
      });

      it('should handle HttpException from service', async () => {
        const httpError = new BadRequestException('Invalid rule data');
        (mockTriageRulesService.createRule as Mock).mockRejectedValue(
          httpError,
        );

        await expect(
          controller.createRule(teamId, createRuleDto, botCtx),
        ).rejects.toThrow(httpError);
      });

      it('should handle general errors and log them', async () => {
        const error = new Error('Service error');
        (mockTriageRulesService.createRule as Mock).mockImplementation(() => {
          throw error;
        });

        await expect(
          controller.createRule(teamId, createRuleDto, botCtx),
        ).rejects.toThrow(InternalServerErrorException);
        expect(mockLogger.error).toHaveBeenCalled();
      });
    });

    describe('getRules', () => {
      const teamId = 'T123456';

      it('should get all triage rules for a team', async () => {
        const expectedRules = [
          {
            id: 'rule-1',
            isDefault: false,
            channelIds: ['C12345'],
            triageRules: {
              AND: [
                {
                  category: 'message' as any,
                  field: 'content',
                  operator: 'contains' as any,
                  value: 'test',
                },
              ],
            },
          },
          {
            id: 'rule-2',
            isDefault: true,
            channelIds: ['C67890'],
          },
        ];
        (mockTriageRulesService.getTeamRules as Mock).mockResolvedValue(
          expectedRules,
        );

        const result = await controller.getRules(teamId, botCtx);

        expect(result).toEqual(expectedRules);
        expect(mockTriageRulesService.getTeamRules).toHaveBeenCalledWith(
          teamId,
          botCtx,
        );
      });

      it('should handle HttpException from service', async () => {
        const httpError = new BadRequestException('Team not found');
        (mockTriageRulesService.getTeamRules as Mock).mockRejectedValue(
          httpError,
        );

        await expect(controller.getRules(teamId, botCtx)).rejects.toThrow(
          httpError,
        );
      });

      it('should handle general errors and log them', async () => {
        const error = new Error('Service error');
        (mockTriageRulesService.getTeamRules as Mock).mockImplementation(() => {
          throw error;
        });

        await expect(controller.getRules(teamId, botCtx)).rejects.toThrow(
          InternalServerErrorException,
        );
        expect(mockLogger.error).toHaveBeenCalled();
      });
    });

    describe('updateRule', () => {
      const ruleId = 'rule-1';
      const updateRuleDto: UpdateTriageRuleDto = {
        isEnabled: false,
        channelIds: ['C12345', 'C67890'],
        triageRules: {
          OR: [
            {
              category: 'message' as any,
              field: 'content',
              operator: 'contains' as any,
              value: 'updated',
            },
          ],
        },
      };

      it('should update a triage rule successfully', async () => {
        const expectedResponse = {
          id: ruleId,
          ...updateRuleDto,
        };
        (mockTriageRulesService.updateRule as Mock).mockResolvedValue(
          expectedResponse,
        );

        const result = await controller.updateRule(
          ruleId,
          updateRuleDto,
          botCtx,
        );

        expect(result).toEqual(expectedResponse);
        expect(mockTriageRulesService.updateRule).toHaveBeenCalledWith(
          ruleId,
          updateRuleDto,
          botCtx,
        );
      });

      it('should handle HttpException from service', async () => {
        const httpError = new BadRequestException('Rule not found');
        (mockTriageRulesService.updateRule as Mock).mockRejectedValue(
          httpError,
        );

        await expect(
          controller.updateRule(ruleId, updateRuleDto, botCtx),
        ).rejects.toThrow(httpError);
      });

      it('should handle general errors and log them', async () => {
        const error = new Error('Service error');
        (mockTriageRulesService.updateRule as Mock).mockImplementation(() => {
          throw error;
        });

        await expect(
          controller.updateRule(ruleId, updateRuleDto, botCtx),
        ).rejects.toThrow(InternalServerErrorException);
        expect(mockLogger.error).toHaveBeenCalled();
      });
    });

    describe('deleteRule', () => {
      const ruleId = 'rule-1';

      it('should delete a triage rule successfully', async () => {
        const expectedResponse = { success: true };
        (mockTriageRulesService.deleteRule as Mock).mockResolvedValue(
          expectedResponse,
        );

        const result = await controller.deleteRule(ruleId, botCtx);

        expect(result).toEqual(expectedResponse);
        expect(mockTriageRulesService.deleteRule).toHaveBeenCalledWith(
          ruleId,
          botCtx,
        );
      });

      it('should handle HttpException from service', async () => {
        const httpError = new BadRequestException('Rule not found');
        (mockTriageRulesService.deleteRule as Mock).mockRejectedValue(
          httpError,
        );

        await expect(controller.deleteRule(ruleId, botCtx)).rejects.toThrow(
          httpError,
        );
      });

      it('should handle general errors and log them', async () => {
        const error = new Error('Service error');
        (mockTriageRulesService.deleteRule as Mock).mockImplementation(() => {
          throw error;
        });

        await expect(controller.deleteRule(ruleId, botCtx)).rejects.toThrow(
          InternalServerErrorException,
        );
        expect(mockLogger.error).toHaveBeenCalled();
      });
    });
  });

  describe('SlackChannelController - Triage Operations', () => {
    let controller: SlackChannelController;
    let mockLogger: ILogger;
    let mockSlackChannelService: SlackChannelService;
    let botCtx: BotCtx;

    beforeEach(() => {
      vi.resetAllMocks();

      // Mock the installations and organization
      const mockInstallation = createMockInstallation('1', 'T123456');
      const mockOrganization = createMockOrganization('1');

      // Set up the bot context
      botCtx = {
        installations: [mockInstallation],
        installation: mockInstallation,
        organization: mockOrganization,
      };

      // Mock the logger
      mockLogger = {
        log: vi.fn(),
        error: vi.fn(),
        warn: vi.fn(),
        debug: vi.fn(),
        verbose: vi.fn(),
      } as unknown as ILogger;

      // Mock the slack channel service
      mockSlackChannelService = {
        configureTriageChannel: vi.fn(),
        createTriageThread: vi.fn(),
      } as unknown as SlackChannelService;

      // Create the controller
      controller = new SlackChannelController(
        mockLogger,
        mockSlackChannelService,
      );
    });

    describe('configureTriageChannel', () => {
      it('should configure a triage channel successfully', async () => {
        // Setup
        const data: ConfigureTriageChannelDTO = {
          channelId: 'C12345',
          teamId: 'T123456',
        };
        (
          mockSlackChannelService.configureTriageChannel as Mock
        ).mockResolvedValue(undefined);

        // Execute
        await controller.configureTriageChannel(data, botCtx);

        // Verify
        expect(
          mockSlackChannelService.configureTriageChannel,
        ).toHaveBeenCalledWith(data, botCtx);
      });

      it('should propagate errors from the service', async () => {
        // Setup
        const data: ConfigureTriageChannelDTO = {
          channelId: 'C12345',
          teamId: 'T123456',
        };
        const error = new Error('Service error');
        (
          mockSlackChannelService.configureTriageChannel as Mock
        ).mockRejectedValue(error);

        // Execute and verify
        await expect(
          controller.configureTriageChannel(data, botCtx),
        ).rejects.toThrow(error);
      });
    });

    describe('createTriageThread', () => {
      it('should create a triage thread successfully', async () => {
        // Setup
        const channelId = 'C12345';
        const data: CreateTriageThreadDTO = {
          ticketId: 'ticket-123',
          title: 'Test Ticket',
          description: 'Test Description',
        };
        const mockResult = { ts: '1234567890.123456' };
        (mockSlackChannelService.createTriageThread as Mock).mockResolvedValue(
          mockResult,
        );

        // Execute
        const result = await controller.createTriageThread(
          channelId,
          data,
          botCtx,
        );

        // Verify
        expect(mockSlackChannelService.createTriageThread).toHaveBeenCalledWith(
          data,
          channelId,
          botCtx,
        );
        expect(result).toEqual({ ok: true, data: mockResult });
      });

      it('should throw BadRequestException if channelId is not provided', async () => {
        // Setup
        const channelId = '';
        const data: CreateTriageThreadDTO = {
          ticketId: 'ticket-123',
          title: 'Test Ticket',
          description: 'Test Description',
        };

        // Execute and verify
        await expect(
          controller.createTriageThread(channelId, data, botCtx),
        ).rejects.toThrow(BadRequestException);
        expect(
          mockSlackChannelService.createTriageThread,
        ).not.toHaveBeenCalled();
      });

      it('should handle HttpException from service', async () => {
        // Setup
        const channelId = 'C12345';
        const data: CreateTriageThreadDTO = {
          ticketId: 'ticket-123',
          title: 'Test Ticket',
          description: 'Test Description',
        };
        const httpError = new BadRequestException('Channel not found');
        (mockSlackChannelService.createTriageThread as Mock).mockRejectedValue(
          httpError,
        );

        // Execute and verify
        await expect(
          controller.createTriageThread(channelId, data, botCtx),
        ).rejects.toThrow(httpError);
      });

      it('should handle general errors and log them', async () => {
        // Setup
        const channelId = 'C12345';
        const data: CreateTriageThreadDTO = {
          ticketId: 'ticket-123',
          title: 'Test Ticket',
          description: 'Test Description',
        };
        const error = new Error('Service error');
        (mockSlackChannelService.createTriageThread as Mock).mockRejectedValue(
          error,
        );

        // Execute and verify
        await expect(
          controller.createTriageThread(channelId, data, botCtx),
        ).rejects.toThrow(InternalServerErrorException);
        expect(mockLogger.error).toHaveBeenCalled();
      });
    });
  });
});
