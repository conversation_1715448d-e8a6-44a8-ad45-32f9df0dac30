import { describe, it, expect, beforeEach, vi, <PERSON>ck } from 'vitest';
import { SlackChannelController } from '../../../../src/slack/controllers/configurations.controller';
import { ILogger } from '../../../../src/utils/logger/logger.interface';
import { SlackChannelService } from '../../../../src/slack/services/slack-channel.service';
import { BadRequestException, HttpException, InternalServerErrorException } from '@nestjs/common';
import { BotCtx } from '../../../../src/auth/interfaces';
import { InstallationStatus, Installations } from '../../../../src/database/entities/installations/installations.entity';
import { Organizations } from '../../../../src/database/entities/organizations/organizations.entity';
import { GetAllSlackChannelsQueryParams } from '../../../../src/slack/query-params';
import { ConfigureTriageChannelDTO, CreateTriageThreadDTO } from '../../../../src/slack/dtos';

describe('SlackChannelController', () => {
  let controller: SlackChannelController;
  let mockLogger: ILogger;
  let mockSlackChannelService: SlackChannelService;

  const createMockInstallation = (id: string, teamId: string): Installations => ({
    id,
    name: `Installation ${id}`,
    installationDump: {},
    teamId,
    domains: 'example.com',
    teamName: `Team ${id}`,
    enterpriseId: null,
    teamInfo: {},
    status: InstallationStatus.SYNCED,
    installingUserId: `user-${id}`,
    installingUserSlackId: `slack-user-${id}`,
    installingUserName: `User ${id}`,
    botToken: `xoxb-token-${id}`,
    botSlackId: `bot-${id}`,
    botSlackUserId: `bot-user-${id}`,
    slackAppAuthToken: `auth-token-${id}`,
    disconnected: false,
    disconnectedOn: null,
    platformDump: null,
    createdAt: new Date(),
    updatedAt: new Date(),
    deletedAt: null,
    bots: [],
    channels: [],
    users: [],
    teams: [],
    subgroups: [],
    organization: null as any,
  });

  const createMockOrganization = (id: string): Organizations => ({
    id,
    externalPk: `ext-${id}`,
    name: `Organization ${id}`,
    uid: `org-uid-${id}`,
    apiKey: `api-key-${id}`,
    installingUserId: `user-${id}`,
    metadata: {
      applicationId: `app-${id}`,
      installationId: `inst-${id}`,
      createdBy: `user-${id}`,
    },
    bots: [],
    users: [],
    channels: [],
    installations: [],
    teams: [],
    subgroups: [],
  });

  beforeEach(() => {
    vi.resetAllMocks();
    
    mockLogger = {
      log: vi.fn(),
      error: vi.fn(),
      warn: vi.fn(),
      debug: vi.fn(),
      verbose: vi.fn(),
    } as unknown as ILogger;

    mockSlackChannelService = {
      getAllSlackChannelsByTeamId: vi.fn(),
      getAllSlackChannels: vi.fn(),
      configureTriageChannel: vi.fn(),
      createTriageThread: vi.fn(),
    } as unknown as SlackChannelService;

    controller = new SlackChannelController(
      mockLogger,
      mockSlackChannelService,
    );
  });

  describe('getAllSlackChannelsByTeamId', () => {
    it('should return channels for a specific team ID', async () => {
      const teamId = 'team-123';
      const query: GetAllSlackChannelsQueryParams = { page: 1, limit: 10 };
      const mockInstallation = createMockInstallation('1', 'team-1');
      const mockOrganization = createMockOrganization('1');

      const botCtx: BotCtx = {
        installations: [mockInstallation],
        installation: mockInstallation,
        organization: mockOrganization,
      };

      const mockChannels = {
        data: [
          { id: 'C12345', name: 'general' },
          { id: 'C67890', name: 'random' },
        ],
        meta: {
          total: 2,
          page: 1,
          limit: 10,
        },
      };

      (mockSlackChannelService.getAllSlackChannelsByTeamId as Mock).mockResolvedValue(mockChannels);

      const result = await controller.getAllSlackChannelsByTeamId(teamId, query, botCtx);

      expect(mockSlackChannelService.getAllSlackChannelsByTeamId).toHaveBeenCalledWith(
        botCtx,
        teamId,
        query,
      );
      expect(result).toEqual(mockChannels);
      expect(mockLogger.log).toHaveBeenCalledTimes(2);
    });

    it('should propagate HttpException from the service', async () => {
      const teamId = 'team-123';
      const query: GetAllSlackChannelsQueryParams = { page: 1, limit: 10 };
      const mockInstallation = createMockInstallation('1', 'team-1');
      const mockOrganization = createMockOrganization('1');

      const botCtx: BotCtx = {
        installations: [mockInstallation],
        installation: mockInstallation,
        organization: mockOrganization,
      };

      const mockError = new BadRequestException('Invalid team ID');
      (mockSlackChannelService.getAllSlackChannelsByTeamId as Mock).mockRejectedValue(mockError);

      await expect(controller.getAllSlackChannelsByTeamId(teamId, query, botCtx))
        .rejects.toThrow(BadRequestException);
    });

    it('should handle service errors and throw InternalServerErrorException', async () => {
      const teamId = 'team-123';
      const query: GetAllSlackChannelsQueryParams = { page: 1, limit: 10 };
      const mockInstallation = createMockInstallation('1', 'team-1');
      const mockOrganization = createMockOrganization('1');

      const botCtx: BotCtx = {
        installations: [mockInstallation],
        installation: mockInstallation,
        organization: mockOrganization,
      };

      const mockError = new Error('Service error');
      (mockSlackChannelService.getAllSlackChannelsByTeamId as Mock).mockRejectedValue(mockError);

      await expect(controller.getAllSlackChannelsByTeamId(teamId, query, botCtx))
        .rejects.toThrow(InternalServerErrorException);
      expect(mockLogger.error).toHaveBeenCalled();
    });
  });

  describe('getAllSlackChannels', () => {
    it('should return all channels for the current installation', async () => {
      const query: GetAllSlackChannelsQueryParams = { page: 1, limit: 10 };
      const mockInstallation = createMockInstallation('1', 'team-1');
      const mockOrganization = createMockOrganization('1');

      const botCtx: BotCtx = {
        installations: [mockInstallation],
        installation: mockInstallation,
        organization: mockOrganization,
      };

      const mockChannels = {
        data: [
          { id: 'C12345', name: 'general' },
          { id: 'C67890', name: 'random' },
        ],
        meta: {
          total: 2,
          page: 1,
          limit: 10,
        },
      };

      (mockSlackChannelService.getAllSlackChannels as Mock).mockResolvedValue(mockChannels);

      const result = await controller.getAllSlackChannels(query, botCtx);

      expect(mockSlackChannelService.getAllSlackChannels).toHaveBeenCalledWith(
        botCtx,
        query,
      );
      expect(result).toEqual(mockChannels);
      expect(mockLogger.log).toHaveBeenCalledTimes(2);
    });

    it('should propagate HttpException from the service', async () => {
      const query: GetAllSlackChannelsQueryParams = { page: 1, limit: 10 };
      const mockInstallation = createMockInstallation('1', 'team-1');
      const mockOrganization = createMockOrganization('1');

      const botCtx: BotCtx = {
        installations: [mockInstallation],
        installation: mockInstallation,
        organization: mockOrganization,
      };

      const mockError = new BadRequestException('Invalid query parameters');
      (mockSlackChannelService.getAllSlackChannels as Mock).mockRejectedValue(mockError);

      await expect(controller.getAllSlackChannels(query, botCtx))
        .rejects.toThrow(BadRequestException);
    });

    it('should handle service errors and throw InternalServerErrorException', async () => {
      const query: GetAllSlackChannelsQueryParams = { page: 1, limit: 10 };
      const mockInstallation = createMockInstallation('1', 'team-1');
      const mockOrganization = createMockOrganization('1');

      const botCtx: BotCtx = {
        installations: [mockInstallation],
        installation: mockInstallation,
        organization: mockOrganization,
      };

      const mockError = new Error('Service error');
      (mockSlackChannelService.getAllSlackChannels as Mock).mockRejectedValue(mockError);

      await expect(controller.getAllSlackChannels(query, botCtx))
        .rejects.toThrow(InternalServerErrorException);
      expect(mockLogger.error).toHaveBeenCalled();
    });
  });

  describe('configureTriageChannel', () => {
    it('should configure a triage channel successfully', async () => {
      const data: ConfigureTriageChannelDTO = {
        channelId: 'C12345',
        teamId: 'team-123',
      };
      const mockInstallation = createMockInstallation('1', 'team-1');
      const mockOrganization = createMockOrganization('1');

      const botCtx: BotCtx = {
        installations: [mockInstallation],
        installation: mockInstallation,
        organization: mockOrganization,
      };

      (mockSlackChannelService.configureTriageChannel as Mock).mockResolvedValue(undefined);

      await controller.configureTriageChannel(data, botCtx);

      expect(mockSlackChannelService.configureTriageChannel).toHaveBeenCalledWith(data, botCtx);
    });

    it('should propagate errors from the service', async () => {
      const data: ConfigureTriageChannelDTO = {
        channelId: 'C12345',
        teamId: 'team-123',
      };
      const mockInstallation = createMockInstallation('1', 'team-1');
      const mockOrganization = createMockOrganization('1');

      const botCtx: BotCtx = {
        installations: [mockInstallation],
        installation: mockInstallation,
        organization: mockOrganization,
      };

      const mockError = new Error('Service error');
      (mockSlackChannelService.configureTriageChannel as Mock).mockRejectedValue(mockError);

      await expect(controller.configureTriageChannel(data, botCtx))
        .rejects.toThrow(mockError);
    });
  });

  describe('createTriageThread', () => {
    it('should create a triage thread successfully', async () => {
      const channelId = 'C12345';
      const data: CreateTriageThreadDTO = {
        ticketId: 'ticket-123',
        title: 'Test Ticket',
        description: 'Test Description',
      };
      const mockInstallation = createMockInstallation('1', 'team-1');
      const mockOrganization = createMockOrganization('1');

      const botCtx: BotCtx = {
        installations: [mockInstallation],
        installation: mockInstallation,
        organization: mockOrganization,
      };

      const mockResult = { ts: '1234567890.123456' };
      (mockSlackChannelService.createTriageThread as Mock).mockResolvedValue(mockResult);

      const result = await controller.createTriageThread(channelId, data, botCtx);

      expect(mockSlackChannelService.createTriageThread).toHaveBeenCalledWith(
        data,
        channelId,
        botCtx,
      );
      expect(result).toEqual({ ok: true, data: mockResult });
    });

    it('should throw BadRequestException if channelId is not provided', async () => {
      const channelId = '';
      const data: CreateTriageThreadDTO = {
        ticketId: 'ticket-123',
        title: 'Test Ticket',
        description: 'Test Description',
      };
      const mockInstallation = createMockInstallation('1', 'team-1');
      const mockOrganization = createMockOrganization('1');

      const botCtx: BotCtx = {
        installations: [mockInstallation],
        installation: mockInstallation,
        organization: mockOrganization,
      };

      await expect(controller.createTriageThread(channelId, data, botCtx))
        .rejects.toThrow(BadRequestException);
      expect(mockSlackChannelService.createTriageThread).not.toHaveBeenCalled();
    });

    it('should propagate HttpException from the service', async () => {
      const channelId = 'C12345';
      const data: CreateTriageThreadDTO = {
        ticketId: 'ticket-123',
        title: 'Test Ticket',
        description: 'Test Description',
      };
      const mockInstallation = createMockInstallation('1', 'team-1');
      const mockOrganization = createMockOrganization('1');

      const botCtx: BotCtx = {
        installations: [mockInstallation],
        installation: mockInstallation,
        organization: mockOrganization,
      };

      const mockError = new BadRequestException('Invalid ticket data');
      (mockSlackChannelService.createTriageThread as Mock).mockRejectedValue(mockError);

      await expect(controller.createTriageThread(channelId, data, botCtx))
        .rejects.toThrow(BadRequestException);
    });

    it('should handle service errors and throw InternalServerErrorException', async () => {
      const channelId = 'C12345';
      const data: CreateTriageThreadDTO = {
        ticketId: 'ticket-123',
        title: 'Test Ticket',
        description: 'Test Description',
      };
      const mockInstallation = createMockInstallation('1', 'team-1');
      const mockOrganization = createMockOrganization('1');

      const botCtx: BotCtx = {
        installations: [mockInstallation],
        installation: mockInstallation,
        organization: mockOrganization,
      };

      const mockError = new Error('Service error');
      (mockSlackChannelService.createTriageThread as Mock).mockRejectedValue(mockError);

      await expect(controller.createTriageThread(channelId, data, botCtx))
        .rejects.toThrow(InternalServerErrorException);
      expect(mockLogger.error).toHaveBeenCalled();
    });
  });
});
