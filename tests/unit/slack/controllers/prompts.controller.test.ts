import {
  BadRequestException,
  InternalServerErrorException,
} from '@nestjs/common';
import { Mock, beforeEach, describe, expect, it, vi } from 'vitest';
import { BotCtx } from '../../../../src/auth/interfaces';
import {
  InstallationStatus,
  Installations,
} from '../../../../src/database/entities/installations/installations.entity';
import { TriageRules } from '../../../../src/database/entities/mappings/teams-triage-mappings.entity';
import { Organizations } from '../../../../src/database/entities/organizations/organizations.entity';
import { Prompts } from '../../../../src/database/entities/prompts/prompts.entity';
import { PromptsController } from '../../../../src/slack/controllers/prompts.controller';
import { PromptsService } from '../../../../src/slack/services/prompts.service';
import { ILogger } from '../../../../src/utils/logger/logger.interface';

describe('PromptsController', () => {
  let controller: PromptsController;
  let mockLogger: ILogger;
  let mockPromptsService: PromptsService;

  const createMockInstallation = (
    id: string,
    teamId: string,
  ): Installations => ({
    id,
    name: `Installation ${id}`,
    installationDump: {
      team: { id: teamId },
      enterprise: undefined,
      user: {
        id: `user-${id}`,
        token: `token-${id}`,
        scopes: ['chat:write'],
      },
    },
    teamId,
    domains: 'example.com',
    teamName: `Team ${id}`,
    enterpriseId: null,
    teamInfo: {},
    status: InstallationStatus.SYNCED,
    installingUserId: `user-${id}`,
    installingUserSlackId: `slack-user-${id}`,
    installingUserName: `User ${id}`,
    botToken: `xoxb-token-${id}`,
    botSlackId: `bot-${id}`,
    botSlackUserId: `bot-user-${id}`,
    slackAppAuthToken: `auth-token-${id}`,
    disconnected: false,
    disconnectedOn: undefined as unknown as Date,
    platformDump: {} as any,
    createdAt: new Date(),
    updatedAt: new Date(),
    deletedAt: undefined as unknown as Date,
    bots: [],
    channels: [],
    users: [],
    teams: [],
    subgroups: [],
    organization: null as any,
  });

  const createMockOrganization = (id: string): Organizations => ({
    id,
    externalPk: `ext-${id}`,
    name: `Organization ${id}`,
    uid: `org-uid-${id}`,
    apiKey: `api-key-${id}`,
    installingUserId: `user-${id}`,
    metadata: {
      applicationId: `app-${id}`,
      installationId: `inst-${id}`,
      createdBy: `user-${id}`,
    },
    bots: [],
    users: [],
    channels: [],
    installations: [],
    teams: [],
    subgroups: [],
  });

  const mockInstallation = createMockInstallation('1', 'team-1');
  const mockOrganization = createMockOrganization('1');

  const botCtx: BotCtx = {
    installations: [mockInstallation],
    installation: mockInstallation,
    organization: mockOrganization,
  };

  const mockPrompt: Prompts = {
    id: 'prompt-1',
    name: 'Test Prompt',
    prompts: {
      ticket_detection: 'Test ticket detection prompt',
      sentiment_analysis: 'Test sentiment analysis prompt',
      urgency_detection: 'Test urgency detection prompt',
      custom_fields: 'Test custom fields prompt',
      title_generation: 'Test title generation prompt',
      description_generation: 'Test description generation prompt',
    },
    selectionRules: {} as TriageRules,
    isDefault: false,
    platformTeam: null as any,
    isEnabled: true,
    installation: null as any,
    organization: null as any,
    createdAt: new Date(),
    updatedAt: new Date(),
    deletedAt: undefined as unknown as Date,
  };

  beforeEach(() => {
    vi.resetAllMocks();

    mockLogger = {
      log: vi.fn(),
      error: vi.fn(),
      warn: vi.fn(),
      debug: vi.fn(),
      verbose: vi.fn(),
    } as unknown as ILogger;

    mockPromptsService = {
      getPrompts: vi.fn(),
      getPromptById: vi.fn(),
      createPrompt: vi.fn(),
      updatePrompt: vi.fn(),
      deletePrompt: vi.fn(),
      setDefaultPrompt: vi.fn(),
      seedDefaultPrompts: vi.fn(),
      seedDefaultPromptsForAllTeams: vi.fn(),
      getPromptsByTeamId: vi.fn(),
    } as unknown as PromptsService;

    controller = new PromptsController(mockLogger, mockPromptsService);
  });

  describe('getPrompts', () => {
    it('should get all prompts successfully', async () => {
      const mockPrompts = [mockPrompt];
      (mockPromptsService.getPrompts as Mock).mockResolvedValue(mockPrompts);

      const result = await controller.getPrompts(botCtx);

      expect(result).toEqual({
        ok: true,
        data: mockPrompts,
      });
      expect(mockPromptsService.getPrompts).toHaveBeenCalledWith(botCtx);
    });

    it('should handle HttpException errors', async () => {
      const error = new BadRequestException('Bad request');
      (mockPromptsService.getPrompts as Mock).mockRejectedValue(error);

      await expect(controller.getPrompts(botCtx)).rejects.toThrow(
        BadRequestException,
      );
      expect(mockPromptsService.getPrompts).toHaveBeenCalledWith(botCtx);
    });

    it('should handle general errors', async () => {
      const error = new Error('Service error');
      (mockPromptsService.getPrompts as Mock).mockRejectedValue(error);

      await expect(controller.getPrompts(botCtx)).rejects.toThrow(
        InternalServerErrorException,
      );
      expect(mockLogger.error).toHaveBeenCalled();
    });
  });

  describe('getPromptById', () => {
    it('should get a prompt by ID successfully', async () => {
      (mockPromptsService.getPromptById as Mock).mockResolvedValue(mockPrompt);

      const result = await controller.getPromptById(botCtx, mockPrompt.id);

      expect(result).toEqual({
        ok: true,
        data: mockPrompt,
      });
      expect(mockPromptsService.getPromptById).toHaveBeenCalledWith(
        botCtx,
        mockPrompt.id,
      );
    });

    it('should handle HttpException errors', async () => {
      const error = new BadRequestException('Bad request');
      (mockPromptsService.getPromptById as Mock).mockRejectedValue(error);

      await expect(
        controller.getPromptById(botCtx, mockPrompt.id),
      ).rejects.toThrow(BadRequestException);
    });

    it('should handle general errors', async () => {
      const error = new Error('Service error');
      (mockPromptsService.getPromptById as Mock).mockRejectedValue(error);

      await expect(
        controller.getPromptById(botCtx, mockPrompt.id),
      ).rejects.toThrow(InternalServerErrorException);
      expect(mockLogger.error).toHaveBeenCalled();
    });
  });

  describe('createPrompt', () => {
    const newPromptData: Partial<Prompts> = {
      name: 'New Prompt',
      prompts: {
        ticket_detection: 'New ticket detection prompt',
        sentiment_analysis: 'New sentiment analysis prompt',
        urgency_detection: 'New urgency detection prompt',
        custom_fields: 'New custom fields prompt',
        title_generation: 'New title generation prompt',
        description_generation: 'New description generation prompt',
      },
    };

    it('should create a prompt successfully', async () => {
      const createdPrompt = { ...mockPrompt, ...newPromptData };
      (mockPromptsService.createPrompt as Mock).mockResolvedValue(
        createdPrompt,
      );

      const result = await controller.createPrompt(botCtx, newPromptData);

      expect(result).toEqual({
        ok: true,
        data: createdPrompt,
      });
      expect(mockPromptsService.createPrompt).toHaveBeenCalledWith(
        botCtx,
        newPromptData,
      );
    });

    it('should validate required name field', async () => {
      const invalidData = { ...newPromptData, name: undefined };

      await expect(
        controller.createPrompt(botCtx, invalidData),
      ).rejects.toThrow(BadRequestException);
      expect(mockPromptsService.createPrompt).not.toHaveBeenCalled();
    });

    it('should validate required prompts field', async () => {
      const invalidData = { ...newPromptData, prompts: undefined };

      await expect(
        controller.createPrompt(botCtx, invalidData),
      ).rejects.toThrow(BadRequestException);
      expect(mockPromptsService.createPrompt).not.toHaveBeenCalled();
    });

    it('should validate required prompt fields', async () => {
      // Using type assertion to create a prompt with missing fields for test
      const invalidPromptData = {
        name: 'Invalid Prompt',
        prompts: {
          ticket_detection: undefined,
          sentiment_analysis: 'Test sentiment analysis prompt',
          urgency_detection: 'Test urgency detection prompt',
          custom_fields: 'Test custom fields prompt',
          title_generation: 'Test title generation prompt',
          description_generation: 'Test description generation prompt',
        },
      } as unknown as Partial<Prompts>;

      await expect(
        controller.createPrompt(botCtx, invalidPromptData),
      ).rejects.toThrow(BadRequestException);
      expect(mockPromptsService.createPrompt).not.toHaveBeenCalled();
    });

    it('should handle HttpException errors', async () => {
      const error = new BadRequestException('Bad request');
      (mockPromptsService.createPrompt as Mock).mockRejectedValue(error);

      await expect(
        controller.createPrompt(botCtx, newPromptData),
      ).rejects.toThrow(BadRequestException);
    });

    it('should handle general errors', async () => {
      const error = new Error('Service error');
      (mockPromptsService.createPrompt as Mock).mockRejectedValue(error);

      await expect(
        controller.createPrompt(botCtx, newPromptData),
      ).rejects.toThrow(InternalServerErrorException);
      expect(mockLogger.error).toHaveBeenCalled();
    });
  });

  describe('updatePrompt', () => {
    const updatePromptData: Partial<Prompts> = {
      name: 'Updated Prompt',
      prompts: {
        ticket_detection: 'Updated ticket detection prompt',
        sentiment_analysis: 'Updated sentiment analysis prompt',
        urgency_detection: 'Updated urgency detection prompt',
        custom_fields: 'Updated custom fields prompt',
        title_generation: 'Updated title generation prompt',
        description_generation: 'Updated description generation prompt',
      },
    };

    it('should update a prompt successfully', async () => {
      const updatedPrompt = { ...mockPrompt, ...updatePromptData };
      (mockPromptsService.updatePrompt as Mock).mockResolvedValue(
        updatedPrompt,
      );

      const result = await controller.updatePrompt(
        botCtx,
        mockPrompt.id,
        updatePromptData,
      );

      expect(result).toEqual({
        ok: true,
        data: updatedPrompt,
      });
      expect(mockPromptsService.updatePrompt).toHaveBeenCalledWith(
        botCtx,
        mockPrompt.id,
        updatePromptData,
      );
    });

    it('should validate prompt fields if prompts are provided', async () => {
      // Using type assertion to create a prompt with missing fields for test
      const invalidData = {
        name: 'Updated Prompt',
        prompts: {
          ticket_detection: 'Updated ticket detection prompt',
          // Missing required fields
        },
      } as unknown as Partial<Prompts>;

      await expect(
        controller.updatePrompt(botCtx, mockPrompt.id, invalidData),
      ).rejects.toThrow(BadRequestException);
      expect(mockPromptsService.updatePrompt).not.toHaveBeenCalled();
    });

    it('should handle HttpException errors', async () => {
      const error = new BadRequestException('Bad request');
      (mockPromptsService.updatePrompt as Mock).mockRejectedValue(error);

      await expect(
        controller.updatePrompt(botCtx, mockPrompt.id, updatePromptData),
      ).rejects.toThrow(BadRequestException);
    });

    it('should handle general errors', async () => {
      const error = new Error('Service error');
      (mockPromptsService.updatePrompt as Mock).mockRejectedValue(error);

      await expect(
        controller.updatePrompt(botCtx, mockPrompt.id, updatePromptData),
      ).rejects.toThrow(InternalServerErrorException);
      expect(mockLogger.error).toHaveBeenCalled();
    });
  });

  describe('deletePrompt', () => {
    it('should delete a prompt successfully', async () => {
      const deleteResult = { affected: 1 };
      (mockPromptsService.deletePrompt as Mock).mockResolvedValue(deleteResult);

      const result = await controller.deletePrompt(botCtx, mockPrompt.id);

      expect(result).toEqual({
        ok: true,
        data: deleteResult,
      });
      expect(mockPromptsService.deletePrompt).toHaveBeenCalledWith(
        botCtx,
        mockPrompt.id,
      );
    });

    it('should handle HttpException errors', async () => {
      const error = new BadRequestException('Bad request');
      (mockPromptsService.deletePrompt as Mock).mockRejectedValue(error);

      await expect(
        controller.deletePrompt(botCtx, mockPrompt.id),
      ).rejects.toThrow(BadRequestException);
    });

    it('should handle general errors', async () => {
      const error = new Error('Service error');
      (mockPromptsService.deletePrompt as Mock).mockRejectedValue(error);

      await expect(
        controller.deletePrompt(botCtx, mockPrompt.id),
      ).rejects.toThrow(InternalServerErrorException);
      expect(mockLogger.error).toHaveBeenCalled();
    });
  });

  describe('setDefaultPrompt', () => {
    it('should set a prompt as default successfully', async () => {
      const defaultPrompt = { ...mockPrompt, isDefault: true };
      (mockPromptsService.setDefaultPrompt as Mock).mockResolvedValue(
        defaultPrompt,
      );

      const result = await controller.setDefaultPrompt(
        botCtx,
        mockPrompt.id,
        'team-1',
      );

      expect(result).toEqual({
        ok: true,
        data: defaultPrompt,
      });
      expect(mockPromptsService.setDefaultPrompt).toHaveBeenCalledWith(
        botCtx,
        mockPrompt.id,
        'team-1',
      );
    });

    it('should handle HttpException errors', async () => {
      const error = new BadRequestException('Bad request');
      (mockPromptsService.setDefaultPrompt as Mock).mockRejectedValue(error);

      await expect(
        controller.setDefaultPrompt(botCtx, mockPrompt.id, 'team-1'),
      ).rejects.toThrow(BadRequestException);
    });

    it('should handle general errors', async () => {
      const error = new Error('Service error');
      (mockPromptsService.setDefaultPrompt as Mock).mockRejectedValue(error);

      await expect(
        controller.setDefaultPrompt(botCtx, mockPrompt.id, 'team-1'),
      ).rejects.toThrow(InternalServerErrorException);
      expect(mockLogger.error).toHaveBeenCalled();
    });
  });

  describe('seedDefaultPrompts', () => {
    it('should seed default prompts successfully when team has no prompts', async () => {
      const seedResult = [mockPrompt];
      (mockPromptsService.seedDefaultPrompts as Mock).mockResolvedValue(
        seedResult,
      );

      const result = await controller.seedDefaultPrompts(botCtx, 'team-1');

      expect(result).toEqual({
        ok: true,
        data: seedResult,
        message: 'Default prompts created for team',
      });
      expect(mockPromptsService.seedDefaultPrompts).toHaveBeenCalledWith(
        botCtx,
        'team-1',
      );
    });

    it('should handle case when team already has prompts', async () => {
      (mockPromptsService.seedDefaultPrompts as Mock).mockResolvedValue(null);

      const result = await controller.seedDefaultPrompts(botCtx, 'team-1');

      expect(result).toEqual({
        ok: true,
        data: null,
        message: 'Team already has prompts, no action taken',
      });
    });

    it('should handle HttpException errors', async () => {
      const error = new BadRequestException('Bad request');
      (mockPromptsService.seedDefaultPrompts as Mock).mockRejectedValue(error);

      await expect(
        controller.seedDefaultPrompts(botCtx, 'team-1'),
      ).rejects.toThrow(BadRequestException);
    });

    it('should handle general errors', async () => {
      const error = new Error('Service error');
      (mockPromptsService.seedDefaultPrompts as Mock).mockRejectedValue(error);

      await expect(
        controller.seedDefaultPrompts(botCtx, 'team-1'),
      ).rejects.toThrow(InternalServerErrorException);
      expect(mockLogger.error).toHaveBeenCalled();
    });
  });

  describe('seedDefaultPromptsForAllTeams', () => {
    it('should seed default prompts for all teams successfully', async () => {
      const seedResult = {
        seededCount: 2,
        skippedCount: 3,
        teamsUpdated: 2,
        promptsCreated: 2,
      };
      (
        mockPromptsService.seedDefaultPromptsForAllTeams as Mock
      ).mockResolvedValue(seedResult);

      const result = await controller.seedDefaultPromptsForAllTeams(botCtx);

      expect(result).toEqual({
        ok: true,
        data: seedResult,
        message: `Created default prompts for ${seedResult.seededCount} teams, ${seedResult.skippedCount} teams already had prompts`,
      });
      expect(
        mockPromptsService.seedDefaultPromptsForAllTeams,
      ).toHaveBeenCalledWith(botCtx);
    });

    it('should handle HttpException errors', async () => {
      const error = new BadRequestException('Bad request');
      (
        mockPromptsService.seedDefaultPromptsForAllTeams as Mock
      ).mockRejectedValue(error);

      await expect(
        controller.seedDefaultPromptsForAllTeams(botCtx),
      ).rejects.toThrow(BadRequestException);
    });

    it('should handle general errors', async () => {
      const error = new Error('Service error');
      (
        mockPromptsService.seedDefaultPromptsForAllTeams as Mock
      ).mockRejectedValue(error);

      await expect(
        controller.seedDefaultPromptsForAllTeams(botCtx),
      ).rejects.toThrow(InternalServerErrorException);
      expect(mockLogger.error).toHaveBeenCalled();
    });
  });

  describe('getPromptsByTeamId', () => {
    it('should get prompts by team ID successfully', async () => {
      const teamPrompts = [mockPrompt];
      (mockPromptsService.getPromptsByTeamId as Mock).mockResolvedValue(
        teamPrompts,
      );

      const result = await controller.getPromptsByTeamId(botCtx, 'team-1');

      expect(result).toEqual({
        ok: true,
        data: teamPrompts,
      });
      expect(mockPromptsService.getPromptsByTeamId).toHaveBeenCalledWith(
        botCtx,
        'team-1',
      );
    });

    it('should handle HttpException errors', async () => {
      const error = new BadRequestException('Bad request');
      (mockPromptsService.getPromptsByTeamId as Mock).mockRejectedValue(error);

      await expect(
        controller.getPromptsByTeamId(botCtx, 'team-1'),
      ).rejects.toThrow(BadRequestException);
    });

    it('should handle general errors', async () => {
      const error = new Error('Service error');
      (mockPromptsService.getPromptsByTeamId as Mock).mockRejectedValue(error);

      await expect(
        controller.getPromptsByTeamId(botCtx, 'team-1'),
      ).rejects.toThrow(InternalServerErrorException);
      expect(mockLogger.error).toHaveBeenCalled();
    });
  });
});
