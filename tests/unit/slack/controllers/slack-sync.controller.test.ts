import { HttpException, InternalServerErrorException } from '@nestjs/common';
import { Mock, beforeEach, describe, expect, it, vi } from 'vitest';
import { BotCtx } from '../../../../src/auth/interfaces';
import {
  InstallationStatus,
  Installations,
} from '../../../../src/database/entities/installations/installations.entity';
import { Organizations } from '../../../../src/database/entities/organizations/organizations.entity';
import {
  SlackSyncController,
  SlackSyncType,
} from '../../../../src/slack/controllers/slack-sync.controller';
import {
  SlackExternalUsersSyncJob,
  SlackUsersSyncJob,
} from '../../../../src/slack/processors/jobs';
import { ILogger } from '../../../../src/utils';

describe('SlackSyncController', () => {
  let controller: SlackSyncController;
  let mockLogger: ILogger;
  let mockSlackExternalUsersSyncJob: SlackExternalUsersSyncJob;
  let mockSlackUsersSyncJob: SlackUsersSyncJob;
  let botCtx: BotCtx;

  const createMockInstallation = (
    id: string,
    teamId: string,
  ): Installations => ({
    id,
    name: `Installation ${id}`,
    installationDump: {
      team: { id: teamId },
      enterprise: undefined,
      user: {
        id: `user-${id}`,
        token: `token-${id}`,
        scopes: ['chat:write'],
      },
    },
    teamId,
    domains: 'example.com',
    teamName: `Team ${id}`,
    enterpriseId: null,
    teamInfo: {},
    status: InstallationStatus.SYNCED,
    installingUserId: `user-${id}`,
    installingUserSlackId: `slack-user-${id}`,
    installingUserName: `User ${id}`,
    botToken: `xoxb-token-${id}`,
    botSlackId: `bot-${id}`,
    botSlackUserId: `bot-user-${id}`,
    slackAppAuthToken: `auth-token-${id}`,
    disconnected: false,
    disconnectedOn: undefined as unknown as Date,
    platformDump: {} as any,
    createdAt: new Date(),
    updatedAt: new Date(),
    deletedAt: undefined as unknown as Date,
    bots: [],
    channels: [],
    users: [],
    teams: [],
    subgroups: [],
    organization: null as any,
  });

  const createMockOrganization = (id: string): Organizations => ({
    id,
    externalPk: `ext-${id}`,
    name: `Organization ${id}`,
    uid: `org-uid-${id}`,
    apiKey: `api-key-${id}`,
    installingUserId: `user-${id}`,
    metadata: {
      applicationId: `app-${id}`,
      installationId: `inst-${id}`,
      createdBy: `user-${id}`,
    },
    bots: [],
    users: [],
    channels: [],
    installations: [],
    teams: [],
    subgroups: [],
  });

  beforeEach(() => {
    vi.resetAllMocks();

    // Mock the installations and organization
    const mockInstallation = createMockInstallation('1', 'T123456');
    const mockOrganization = createMockOrganization('1');

    // Set up the bot context
    botCtx = {
      installations: [mockInstallation],
      installation: mockInstallation,
      organization: mockOrganization,
    };

    // Mock the logger
    mockLogger = {
      log: vi.fn(),
      error: vi.fn(),
      warn: vi.fn(),
      debug: vi.fn(),
      verbose: vi.fn(),
    } as unknown as ILogger;

    // Mock the external users sync job
    mockSlackExternalUsersSyncJob = {
      execute: vi.fn().mockResolvedValue(undefined),
    } as unknown as SlackExternalUsersSyncJob;

    // Mock the internal users sync job
    mockSlackUsersSyncJob = {
      execute: vi.fn().mockResolvedValue(undefined),
    } as unknown as SlackUsersSyncJob;

    // Create the controller
    controller = new SlackSyncController(
      mockLogger,
      mockSlackExternalUsersSyncJob,
      mockSlackUsersSyncJob,
    );
  });

  describe('sync', () => {
    it('should trigger external users sync job when syncType is EXTERNAL_USERS', async () => {
      // Execute
      const result = await controller.sync(
        SlackSyncType.EXTERNAL_USERS,
        botCtx,
      );

      // Verify
      expect(mockSlackExternalUsersSyncJob.execute).toHaveBeenCalledWith(
        botCtx.installation,
      );
      // The controller doesn't return anything, so we expect undefined
      expect(result).toBeUndefined();
    });

    it('should trigger internal users sync job when syncType is INTERNAL_USERS', async () => {
      // Execute
      const result = await controller.sync(
        SlackSyncType.INTERNAL_USERS,
        botCtx,
      );

      // Verify
      expect(mockSlackUsersSyncJob.execute).toHaveBeenCalledWith(
        botCtx.installation,
      );
      // The controller doesn't return anything, so we expect undefined
      expect(result).toBeUndefined();
    });

    it('should handle errors from the external users sync job', async () => {
      // Setup
      const error = new Error('Sync job error');
      (mockSlackExternalUsersSyncJob.execute as Mock).mockRejectedValue(error);

      // Execute
      await controller.sync(SlackSyncType.EXTERNAL_USERS, botCtx);

      // Verify
      expect(mockLogger.error).toHaveBeenCalledWith(error.message, error.stack);
    });

    it('should handle errors from the internal users sync job', async () => {
      // Setup
      const error = new Error('Sync job error');
      (mockSlackUsersSyncJob.execute as Mock).mockRejectedValue(error);

      // Execute
      await controller.sync(SlackSyncType.INTERNAL_USERS, botCtx);

      // Verify
      expect(mockLogger.error).toHaveBeenCalledWith(error.message, error.stack);
    });

    it('should handle HttpException errors and rethrow them', async () => {
      // Setup
      const httpError = new HttpException('HTTP error', 400);

      // We need to simulate an error in the try block, not in the execute method
      // because the controller uses .catch() on execute which won't propagate to the try-catch
      vi.spyOn(
        controller as any,
        'slackExternalUsersSyncJob',
        'get',
      ).mockImplementation(() => {
        throw httpError;
      });

      // Execute and verify
      await expect(
        controller.sync(SlackSyncType.EXTERNAL_USERS, botCtx),
      ).rejects.toThrow(HttpException);
    });

    it('should handle general errors and throw InternalServerErrorException', async () => {
      // Setup
      const error = new Error('General error');

      // We need to simulate an error in the try block
      vi.spyOn(
        controller as any,
        'slackExternalUsersSyncJob',
        'get',
      ).mockImplementation(() => {
        throw error;
      });

      // Execute and verify
      await expect(
        controller.sync(SlackSyncType.EXTERNAL_USERS, botCtx),
      ).rejects.toThrow(InternalServerErrorException);
      expect(mockLogger.error).toHaveBeenCalled();
    });

    it('should handle non-Error objects and log them', async () => {
      // Setup
      const nonErrorObject = { message: 'Not an Error instance' };

      // We need to simulate an error in the try block
      vi.spyOn(
        controller as any,
        'slackExternalUsersSyncJob',
        'get',
      ).mockImplementation(() => {
        throw nonErrorObject;
      });

      // Execute and verify
      await expect(
        controller.sync(SlackSyncType.EXTERNAL_USERS, botCtx),
      ).rejects.toThrow(InternalServerErrorException);
      // We should verify console.error was called, but that's harder to mock
    });
  });
});
