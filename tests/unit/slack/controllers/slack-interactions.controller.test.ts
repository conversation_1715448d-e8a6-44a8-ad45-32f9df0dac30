import { beforeEach, describe, expect, it, vi } from 'vitest';
import { SlackInteractionsController } from '../../../../src/slack/controllers/slack-interactions.controller';
import { SlackActionRegistry } from '../../../../src/slack/registry';

describe('SlackInteractionsController', () => {
  let controller: SlackInteractionsController;
  let mockActionRegistry: SlackActionRegistry;

  beforeEach(() => {
    vi.resetAllMocks();

    // Mock the action registry
    mockActionRegistry = {
      getHandler: vi.fn(),
    } as unknown as SlackActionRegistry;

    // Create the controller
    controller = new SlackInteractionsController(mockActionRegistry);
  });

  describe('handleInteraction', () => {
    it('should parse the payload and call the appropriate handler', async () => {
      // Setup
      const mockPayload = {
        actions: [
          {
            block_id: 'test-block-id',
            action_id: 'test-action-id',
          },
        ],
        user: { id: 'U12345' },
        team: { id: 'T12345' },
      };

      const mockHandler = {
        handle: vi.fn().mockResolvedValue({}),
      };

      (mockActionRegistry.getHandler as any).mockReturnValue(mockHandler);

      // Execute
      await controller.handleInteraction({
        payload: JSON.stringify(mockPayload),
      });

      // Verify
      expect(mockActionRegistry.getHandler).toHaveBeenCalledWith('test-block-id');
      expect(mockHandler.handle).toHaveBeenCalledWith(mockPayload);
    });

    it('should handle JSON parsing errors', async () => {
      // Setup
      const invalidPayload = '{invalid-json}';

      // Execute and verify
      await expect(
        controller.handleInteraction({ payload: invalidPayload })
      ).rejects.toThrow();
    });

    it('should handle missing actions in payload', async () => {
      // Setup
      const invalidPayload = {
        // Missing actions array
        user: { id: 'U12345' },
        team: { id: 'T12345' },
      };

      // Execute and verify
      await expect(
        controller.handleInteraction({ payload: JSON.stringify(invalidPayload) })
      ).rejects.toThrow();
    });

    it('should handle handler not found', async () => {
      // Setup
      const mockPayload = {
        actions: [
          {
            block_id: 'unknown-block-id',
            action_id: 'test-action-id',
          },
        ],
        user: { id: 'U12345' },
        team: { id: 'T12345' },
      };

      (mockActionRegistry.getHandler as any).mockReturnValue(null);

      // Execute and verify
      await expect(
        controller.handleInteraction({ payload: JSON.stringify(mockPayload) })
      ).rejects.toThrow();
    });

    it('should handle handler execution errors', async () => {
      // Setup
      const mockPayload = {
        actions: [
          {
            block_id: 'test-block-id',
            action_id: 'test-action-id',
          },
        ],
        user: { id: 'U12345' },
        team: { id: 'T12345' },
      };

      const mockHandler = {
        handle: vi.fn().mockRejectedValue(new Error('Handler error')),
      };

      (mockActionRegistry.getHandler as any).mockReturnValue(mockHandler);

      // Execute and verify
      await expect(
        controller.handleInteraction({ payload: JSON.stringify(mockPayload) })
      ).rejects.toThrow('Handler error');
    });
  });
});
