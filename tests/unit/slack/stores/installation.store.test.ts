import { describe, it, expect, beforeEach, vi, Mock } from 'vitest';
import { TypeORMInstallationStore } from '../../../../src/slack/stores/installation.store';
import { ILogger } from '../../../../src/utils/logger';
import { Repository } from 'typeorm';
import { Installations, Organizations } from '../../../../src/database/entities';
import { SlackWebAPIService } from '../../../../src/slack/providers/slack-apis/slack-apis.service';
import { Queue } from 'bullmq';
import { Installation, InstallationQuery } from '@slack/bolt';

describe('TypeORMInstallationStore', () => {
  let store: TypeORMInstallationStore;
  let mockLogger: ILogger;
  let mockInstallationsRepository: Repository<Installations>;
  let mockOrganizationsRepository: Repository<Organizations>;
  let mockSlackWebAPIService: SlackWebAPIService;
  let mockWorkspaceSyncQueue: Queue;

  const mockInstallation: Installation = {
    team: { id: 'T12345', name: 'Test Team' },
    enterprise: undefined,
    user: { id: 'U12345', token: 'user-token', scopes: ['chat:write'] },
    bot: { id: 'B12345', userId: 'U67890', token: 'xoxb-token', scopes: ['chat:write', 'channels:read'] },
    tokenType: 'bot',
    isEnterpriseInstall: false,
    metadata: JSON.stringify({ orgId: 'org-123', installingUser: 'U12345' })
  };

  const mockOrganization = {
    id: 'org-db-123',
    uid: 'org-123',
    name: 'Test Organization'
  };

  const mockExistingInstallation = {
    id: 'installation-123',
    teamId: 'T12345',
    organization: mockOrganization
  };

  beforeEach(() => {
    mockLogger = {
      debug: vi.fn(),
      log: vi.fn(),
      error: vi.fn(),
      warn: vi.fn(),
      verbose: vi.fn()
    } as unknown as ILogger;

    mockInstallationsRepository = {
      findOne: vi.fn(),
      update: vi.fn(),
      save: vi.fn(),
      softDelete: vi.fn()
    } as unknown as Repository<Installations>;

    mockOrganizationsRepository = {
      findOne: vi.fn()
    } as unknown as Repository<Organizations>;

    mockSlackWebAPIService = {
      getTeamInfo: vi.fn()
    } as unknown as SlackWebAPIService;

    mockWorkspaceSyncQueue = {
      add: vi.fn()
    } as unknown as Queue;

    store = new TypeORMInstallationStore(
      mockLogger,
      mockInstallationsRepository,
      mockOrganizationsRepository,
      mockSlackWebAPIService,
      mockWorkspaceSyncQueue
    );

    vi.spyOn(console, 'warn').mockImplementation(() => {});
    vi.spyOn(console, 'error').mockImplementation(() => {});
  });

  describe('storeInstallation', () => {
    it('should update existing installation if found', async () => {
      (mockInstallationsRepository.findOne as Mock).mockResolvedValue(mockExistingInstallation);

      await store.storeInstallation(mockInstallation);

      expect(mockInstallationsRepository.findOne).toHaveBeenCalledWith({
        where: { teamId: 'T12345' },
        relations: ['organization']
      });
      expect(mockWorkspaceSyncQueue.add).toHaveBeenCalledWith('workspace-installed', {
        installationId: 'installation-123'
      });
      expect(mockInstallationsRepository.update).toHaveBeenCalledWith('installation-123', {
        botToken: 'xoxb-token',
        installingUserId: 'U12345'
      });
      expect(mockInstallationsRepository.save).not.toHaveBeenCalled();
    });

    it('should create new installation if not found', async () => {
      (mockInstallationsRepository.findOne as Mock).mockResolvedValue(null);
      (mockOrganizationsRepository.findOne as Mock).mockResolvedValue(mockOrganization);
      (mockSlackWebAPIService.getTeamInfo as Mock).mockResolvedValue({
        ok: true,
        team: { id: 'T12345', name: 'Test Team', domain: 'test' }
      });
      (mockInstallationsRepository.save as Mock).mockResolvedValue({
        id: 'new-installation-123'
      });

      await store.storeInstallation(mockInstallation);

      expect(mockInstallationsRepository.findOne).toHaveBeenCalledWith({
        where: { teamId: 'T12345' },
        relations: ['organization']
      });
      expect(mockOrganizationsRepository.findOne).toHaveBeenCalledWith({
        where: { uid: 'org-123' }
      });
      expect(mockSlackWebAPIService.getTeamInfo).toHaveBeenCalledWith('xoxb-token');
      expect(mockInstallationsRepository.save).toHaveBeenCalledWith(expect.objectContaining({
        teamId: 'T12345',
        botToken: 'xoxb-token',
        organization: mockOrganization
      }));
      expect(mockWorkspaceSyncQueue.add).toHaveBeenCalledWith('workspace-installed', {
        installationId: 'new-installation-123'
      });
    });

    it('should throw error if organization mismatch', async () => {
      const mismatchInstallation = {
        ...mockExistingInstallation,
        organization: {
          ...mockOrganization,
          uid: 'different-org-id'
        }
      };
      (mockInstallationsRepository.findOne as Mock).mockResolvedValue(mismatchInstallation);

      await store.storeInstallation(mockInstallation);

      expect(mockLogger.error).toHaveBeenCalledWith(
        'Organization for installation T12345 does not match!'
      );
      expect(mockWorkspaceSyncQueue.add).not.toHaveBeenCalled();
      expect(mockInstallationsRepository.update).not.toHaveBeenCalled();
    });

    it('should throw error if no orgId in metadata', async () => {
      const invalidInstallation = {
        ...mockInstallation,
        metadata: JSON.stringify({})
      };
      (mockInstallationsRepository.findOne as Mock).mockResolvedValue(null);

      await store.storeInstallation(invalidInstallation);

      expect(mockLogger.error).toHaveBeenCalledWith(
        'Error storing installation: No orgId found in installation metadata',
        expect.any(String)
      );
      expect(mockOrganizationsRepository.findOne).not.toHaveBeenCalled();
    });

    it('should throw error if organization not found', async () => {
      (mockInstallationsRepository.findOne as Mock).mockResolvedValue(null);
      (mockOrganizationsRepository.findOne as Mock).mockResolvedValue(null);

      await store.storeInstallation(mockInstallation);

      expect(mockLogger.error).toHaveBeenCalledWith(
        'Error storing installation: Organization not found',
        expect.any(String)
      );
      expect(mockInstallationsRepository.save).not.toHaveBeenCalled();
    });

    it('should handle non-Error exceptions', async () => {
      (mockInstallationsRepository.findOne as Mock).mockRejectedValue('String error');

      await store.storeInstallation(mockInstallation);

      expect(console.error).toHaveBeenCalledWith('Failed to store installation', 'String error');
    });
  });

  describe('fetchInstallation', () => {
    it('should fetch installation if found', async () => {
      const query: InstallationQuery<boolean> = { 
        teamId: 'T12345',
        isEnterpriseInstall: false,
        enterpriseId: undefined
      };
      const mockFoundInstallation = {
        ...mockExistingInstallation,
        installationDump: mockInstallation
      };
      (mockInstallationsRepository.findOne as Mock).mockResolvedValue(mockFoundInstallation);

      const result = await store.fetchInstallation(query);

      expect(mockInstallationsRepository.findOne).toHaveBeenCalledWith({
        where: { teamId: 'T12345' },
        select: ['installationDump']
      });
      expect(result).toEqual(mockInstallation);
    });

    it('should throw error if installation not found', async () => {
      const query: InstallationQuery<boolean> = { 
        teamId: 'T12345',
        isEnterpriseInstall: false,
        enterpriseId: undefined
      };
      (mockInstallationsRepository.findOne as Mock).mockResolvedValue(null);

      const result = await store.fetchInstallation(query);

      expect(mockLogger.error).toHaveBeenCalledWith(
        'Error fetching installation: No installation found',
        expect.any(String)
      );
      expect(result).toBeUndefined();
    });

    it('should handle non-Error exceptions', async () => {
      const query: InstallationQuery<boolean> = { 
        teamId: 'T12345',
        isEnterpriseInstall: false,
        enterpriseId: undefined
      };
      (mockInstallationsRepository.findOne as Mock).mockRejectedValue('String error');

      const result = await store.fetchInstallation(query);

      expect(console.error).toHaveBeenCalledWith('Failed to fetch installation', 'String error');
      expect(result).toBeUndefined();
    });
  });

  describe('deleteInstallation', () => {
    it('should delete installation', async () => {
      const query: InstallationQuery<boolean> = { 
        teamId: 'T12345',
        isEnterpriseInstall: false,
        enterpriseId: undefined
      };

      await store.deleteInstallation(query);

      expect(mockInstallationsRepository.softDelete).toHaveBeenCalledWith({ teamId: 'T12345' });
    });

    it('should handle error during deletion', async () => {
      const query: InstallationQuery<boolean> = { 
        teamId: 'T12345',
        isEnterpriseInstall: false,
        enterpriseId: undefined
      };
      const error = new Error('Database error');
      (mockInstallationsRepository.softDelete as Mock).mockRejectedValue(error);

      await store.deleteInstallation(query);

      expect(mockLogger.error).toHaveBeenCalledWith(
        'Error deleting installation: Database error',
        error.stack
      );
    });

    it('should handle non-Error exceptions', async () => {
      const query: InstallationQuery<boolean> = { 
        teamId: 'T12345',
        isEnterpriseInstall: false,
        enterpriseId: undefined
      };
      (mockInstallationsRepository.softDelete as Mock).mockRejectedValue('String error');

      await store.deleteInstallation(query);

      expect(console.error).toHaveBeenCalledWith('Failed to delete installation', 'String error');
    });
  });
});
