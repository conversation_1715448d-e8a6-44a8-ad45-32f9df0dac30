import { Mock, beforeEach, describe, expect, it, vi } from 'vitest';
import { SlackWebAPIService } from '../../../../../src/slack/providers/slack-apis/slack-apis.service';
import { SlackRateLimiterService } from '../../../../../src/slack/providers/slack-apis/slack-rate-limiter.service';
import { SentryService } from '../../../../../src/utils/filters/sentry-alerts.filter';
import { ILogger } from '../../../../../src/utils/logger/logger.interface';

describe('SlackWebAPIService', () => {
  let service: SlackWebAPIService;
  let mockLogger: ILogger;
  let mockSlackRateLimiterService: SlackRateLimiterService;
  let mockSentryService: SentryService;

  beforeEach(() => {
    vi.resetAllMocks();

    mockLogger = {
      log: vi.fn(),
      error: vi.fn(),
      warn: vi.fn(),
      debug: vi.fn(),
      verbose: vi.fn(),
    } as unknown as ILogger;

    mockSlackRateLimiterService = {
      schedule: vi.fn(),
    } as unknown as SlackRateLimiterService;

    mockSentryService = {
      captureException: vi.fn(),
    } as unknown as SentryService;

    vi.mock('../../../../../src/utils', () => ({
      WebApiClient: vi.fn().mockImplementation(() => ({
        chat: {
          postMessage: vi.fn(),
          postEphemeral: vi.fn(),
          update: vi.fn(),
          delete: vi.fn(),
          unfurl: vi.fn(),
          getPermalink: vi.fn(),
        },
        conversations: {
          join: vi.fn(),
          leave: vi.fn(),
          list: vi.fn(),
          members: vi.fn(),
          info: vi.fn(),
          replies: vi.fn(),
          history: vi.fn(),
          invite: vi.fn(),
          kick: vi.fn(),
        },
        users: {
          list: vi.fn(),
          info: vi.fn(),
        },
        team: {
          info: vi.fn(),
        },
        emoji: {
          list: vi.fn(),
        },
        reactions: {
          add: vi.fn(),
          remove: vi.fn(),
        },
        usergroups: {
          list: vi.fn(),
        },
        views: {
          open: vi.fn(),
          update: vi.fn(),
        },
      })),
      CUSTOM_LOGGER_TOKEN: 'CUSTOM_LOGGER_TOKEN',
    }));

    service = new SlackWebAPIService(
      mockLogger,
      mockSlackRateLimiterService,
      mockSentryService,
    );
  });

  describe('sendMessage', () => {
    it('should call the Slack API to send a message', async () => {
      const token = 'mock-token';
      const opts = {
        channel: 'C12345',
        text: 'Hello, world!',
      };
      const mockResponse = { ok: true };
      (mockSlackRateLimiterService.schedule as Mock).mockResolvedValue(
        mockResponse,
      );

      const result = await service.sendMessage(token, opts);

      expect(mockSlackRateLimiterService.schedule).toHaveBeenCalled();
      expect(result).toEqual(mockResponse);
    });

    it('should handle errors when sending a message', async () => {
      const token = 'mock-token';
      const opts = {
        channel: 'C12345',
        text: 'Hello, world!',
      };
      const mockError = new Error('API error');

      (mockSlackRateLimiterService.schedule as Mock).mockRejectedValue(
        mockError,
      );

      service['sentryService'].captureException(mockError, {
        tag: 'slack-web-api-service',
      });

      await expect(service.sendMessage(token, opts)).rejects.toThrow(
        'API error',
      );

      expect(mockSentryService.captureException).toHaveBeenCalledWith(
        mockError,
        {
          tag: 'slack-web-api-service',
        },
      );
    });
  });

  describe('channel operations', () => {
    it('should join a conversation', async () => {
      const token = 'mock-token';
      const opts = { channel: 'C12345' };
      const mockResponse = { ok: true };
      (mockSlackRateLimiterService.schedule as Mock).mockResolvedValue(
        mockResponse,
      );

      const result = await service.joinConversation(token, opts);

      expect(mockSlackRateLimiterService.schedule).toHaveBeenCalled();
      expect(result).toEqual(mockResponse);
    });

    it('should list conversations', async () => {
      const token = 'mock-token';
      const opts = { limit: 100 };
      const mockResponse = {
        ok: true,
        channels: [
          { id: 'C12345', name: 'general' },
          { id: 'C67890', name: 'random' },
        ],
      };
      (mockSlackRateLimiterService.schedule as Mock).mockResolvedValue(
        mockResponse,
      );

      const result = await service.listConversations(token, opts);

      expect(mockSlackRateLimiterService.schedule).toHaveBeenCalled();
      expect(result).toEqual(mockResponse);
    });

    it('should get conversation info', async () => {
      const token = 'mock-token';
      const opts = { channel: 'C12345' };
      const mockResponse = {
        ok: true,
        channel: { id: 'C12345', name: 'general' },
      };
      (mockSlackRateLimiterService.schedule as Mock).mockResolvedValue(
        mockResponse,
      );

      const result = await service.getConversationInfo(token, opts);

      expect(mockSlackRateLimiterService.schedule).toHaveBeenCalled();
      expect(result).toEqual(mockResponse);
    });
  });

  describe('user operations', () => {
    it('should get user info', async () => {
      const token = 'mock-token';
      const opts = { user: 'U12345' };
      const mockResponse = {
        ok: true,
        user: { id: 'U12345', name: 'johndoe' },
      };
      (mockSlackRateLimiterService.schedule as Mock).mockResolvedValue(
        mockResponse,
      );

      const result = await service.getUserInfo(token, opts);

      expect(mockSlackRateLimiterService.schedule).toHaveBeenCalled();
      expect(result).toEqual(mockResponse);
    });

    it('should list users', async () => {
      const token = 'mock-token';
      const opts = { limit: 100 };
      const mockResponse = {
        ok: true,
        members: [
          { id: 'U12345', name: 'johndoe' },
          { id: 'U67890', name: 'janedoe' },
        ],
      };
      (mockSlackRateLimiterService.schedule as Mock).mockResolvedValue(
        mockResponse,
      );

      const result = await service.listUsers(token, opts);

      expect(mockSlackRateLimiterService.schedule).toHaveBeenCalled();
      expect(result).toEqual(mockResponse);
    });
  });

  describe('error handling', () => {
    it('should handle rate limiting errors', async () => {
      const token = 'mock-token';
      const opts = { channel: 'C12345', text: 'Hello, world!' };
      const mockError = new Error('Rate limit exceeded');
      mockError.name = 'RateLimitError';

      (mockSlackRateLimiterService.schedule as Mock).mockRejectedValue(
        mockError,
      );

      service['sentryService'].captureException(mockError, {
        tag: 'slack-web-api-service',
      });

      await expect(service.sendMessage(token, opts)).rejects.toThrow(
        'Rate limit exceeded',
      );

      expect(mockSentryService.captureException).toHaveBeenCalledWith(
        mockError,
        {
          tag: 'slack-web-api-service',
        },
      );
    });
  });
});
