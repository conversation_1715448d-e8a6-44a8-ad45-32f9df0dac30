import { describe, expect, it } from 'vitest';
import { SLACK_METHOD_CONFIG } from '../../../../../src/slack/providers/slack-apis/slack-method.config';
import {
  SLACK_RATE_LIMIT_CONFIG,
  c__delete,
  c__getPermalink,
  c__postEphemeral,
  c__postMessage,
  c__unfurl,
  c__update,
  con__conversations__history,
  con__conversations__info,
  con__conversations__invite,
  con__conversations__join,
  con__conversations__kick,
  con__conversations__leave,
  con__conversations__list,
  con__conversations__members,
  con__conversations__replies,
  e__listEmojis,
  r__reactions__add,
  r__reactions__remove,
  t__team__info,
  u__users__info,
  u__users__list,
  ug__usergroups__list,
} from '../../../../../src/slack/providers/slack-apis/slack.constants';

describe('SLACK_METHOD_CONFIG', () => {
  describe('Tier 4 APIs', () => {
    it('should have the correct rate limit config for postMessage', () => {
      expect(SLACK_METHOD_CONFIG[c__postMessage]).toBe(SLACK_RATE_LIMIT_CONFIG.tier_4);
    });

    it('should have the correct rate limit config for postEphemeral', () => {
      expect(SLACK_METHOD_CONFIG[c__postEphemeral]).toBe(SLACK_RATE_LIMIT_CONFIG.tier_4);
    });

    it('should have the correct rate limit config for users.info', () => {
      expect(SLACK_METHOD_CONFIG[u__users__info]).toBe(SLACK_RATE_LIMIT_CONFIG.tier_4);
    });

    it('should have the correct rate limit config for conversations.members', () => {
      expect(SLACK_METHOD_CONFIG[con__conversations__members]).toBe(SLACK_RATE_LIMIT_CONFIG.tier_4);
    });
  });

  describe('Tier 3 APIs', () => {
    it('should have the correct rate limit config for update', () => {
      expect(SLACK_METHOD_CONFIG[c__update]).toBe(SLACK_RATE_LIMIT_CONFIG.tier_3);
    });

    it('should have the correct rate limit config for delete', () => {
      expect(SLACK_METHOD_CONFIG[c__delete]).toBe(SLACK_RATE_LIMIT_CONFIG.tier_3);
    });

    it('should have the correct rate limit config for conversations.join', () => {
      expect(SLACK_METHOD_CONFIG[con__conversations__join]).toBe(SLACK_RATE_LIMIT_CONFIG.tier_3);
    });

    it('should have the correct rate limit config for conversations.leave', () => {
      expect(SLACK_METHOD_CONFIG[con__conversations__leave]).toBe(SLACK_RATE_LIMIT_CONFIG.tier_3);
    });

    it('should have the correct rate limit config for conversations.info', () => {
      expect(SLACK_METHOD_CONFIG[con__conversations__info]).toBe(SLACK_RATE_LIMIT_CONFIG.tier_3);
    });

    it('should have the correct rate limit config for conversations.history', () => {
      expect(SLACK_METHOD_CONFIG[con__conversations__history]).toBe(SLACK_RATE_LIMIT_CONFIG.tier_3);
    });

    it('should have the correct rate limit config for conversations.invite', () => {
      expect(SLACK_METHOD_CONFIG[con__conversations__invite]).toBe(SLACK_RATE_LIMIT_CONFIG.tier_3);
    });

    it('should have the correct rate limit config for conversations.kick', () => {
      expect(SLACK_METHOD_CONFIG[con__conversations__kick]).toBe(SLACK_RATE_LIMIT_CONFIG.tier_3);
    });

    it('should have the correct rate limit config for team.info', () => {
      expect(SLACK_METHOD_CONFIG[t__team__info]).toBe(SLACK_RATE_LIMIT_CONFIG.tier_3);
    });

    it('should have the correct rate limit config for usergroups.list', () => {
      expect(SLACK_METHOD_CONFIG[ug__usergroups__list]).toBe(SLACK_RATE_LIMIT_CONFIG.tier_3);
    });

    it('should have the correct rate limit config for reactions.add', () => {
      expect(SLACK_METHOD_CONFIG[r__reactions__add]).toBe(SLACK_RATE_LIMIT_CONFIG.tier_3);
    });

    it('should have the correct rate limit config for unfurl', () => {
      expect(SLACK_METHOD_CONFIG[c__unfurl]).toBe(SLACK_RATE_LIMIT_CONFIG.tier_3);
    });

    it('should have the correct rate limit config for conversations.replies', () => {
      expect(SLACK_METHOD_CONFIG[con__conversations__replies]).toBe(SLACK_RATE_LIMIT_CONFIG.tier_3);
    });
  });

  describe('Tier 2 APIs', () => {
    it('should have the correct rate limit config for conversations.list', () => {
      expect(SLACK_METHOD_CONFIG[con__conversations__list]).toBe(SLACK_RATE_LIMIT_CONFIG.tier_2);
    });

    it('should have the correct rate limit config for users.list', () => {
      expect(SLACK_METHOD_CONFIG[u__users__list]).toBe(SLACK_RATE_LIMIT_CONFIG.tier_2);
    });

    it('should have the correct rate limit config for reactions.remove', () => {
      expect(SLACK_METHOD_CONFIG[r__reactions__remove]).toBe(SLACK_RATE_LIMIT_CONFIG.tier_2);
    });

    it('should have the correct rate limit config for emoji.list', () => {
      expect(SLACK_METHOD_CONFIG[e__listEmojis]).toBe(SLACK_RATE_LIMIT_CONFIG.tier_2);
    });
  });

  describe('Tier Special APIs', () => {
    it('should have the correct rate limit config for chat.getPermalink', () => {
      expect(SLACK_METHOD_CONFIG[c__getPermalink]).toBe(SLACK_RATE_LIMIT_CONFIG.tier_special);
    });
  });
});
