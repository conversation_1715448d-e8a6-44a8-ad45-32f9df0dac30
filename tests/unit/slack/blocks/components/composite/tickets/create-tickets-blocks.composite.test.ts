import { describe, it, expect, beforeEach } from 'vitest';
import { CreateTicketsBlocksComposite } from '../../../../../../../src/slack/blocks/components/composite/tickets/create-tickets-blocks.composite';

describe('CreateTicketsBlocksComposite', () => {
  let createTicketsBlocksComposite: CreateTicketsBlocksComposite;

  beforeEach(() => {
    createTicketsBlocksComposite = new CreateTicketsBlocksComposite();
  });

  describe('build', () => {
    it('should build blocks for creating a ticket with required parameters', () => {
      // Arrange
      const platformTeamUid = 'team-123';

      // Act
      const result = createTicketsBlocksComposite.build(platformTeamUid);

      // Assert
      expect(result).toBeDefined();
      expect(result.blocks).toBeInstanceOf(Array);
      expect(result.blocks.length).toBeGreaterThan(0);
      expect(result.private_metadata).toBeUndefined();

      // Check header
      expect(result.blocks[0].type).toBe('header');
      expect(result.blocks[0].text.type).toBe('plain_text');
      expect(result.blocks[0].text.text).toBe('Create a ticket');

      // Check description section
      expect(result.blocks[1].type).toBe('section');
      expect(result.blocks[1].text.type).toBe('mrkdwn');
      expect(result.blocks[1].text.text).toContain(`team ${platformTeamUid}`);

      // Check title input
      const titleBlock = result.blocks.find(block => block.block_id === 'title_block');
      expect(titleBlock).toBeDefined();
      expect(titleBlock.type).toBe('input');
      expect(titleBlock.element.type).toBe('plain_text_input');
      expect(titleBlock.element.action_id).toBe('title_input');
      expect(titleBlock.label.text).toBe('Title');

      // Check team select
      const teamSelectBlock = result.blocks.find(block => block.block_id === 'team_select_block');
      expect(teamSelectBlock).toBeDefined();
      expect(teamSelectBlock.type).toBe('input');
      expect(teamSelectBlock.element.type).toBe('external_select');
      expect(teamSelectBlock.element.action_id).toBe(CreateTicketsBlocksComposite.ACTION_IDS.TEAM);
      expect(teamSelectBlock.element.min_query_length).toBe(0);
      expect(teamSelectBlock.label.text).toBe('Team');

      // Check requestor email input
      const requestorEmailBlock = result.blocks.find(block => block.block_id === 'requestor_email_block');
      expect(requestorEmailBlock).toBeDefined();
      expect(requestorEmailBlock.type).toBe('input');
      expect(requestorEmailBlock.element.type).toBe('plain_text_input');
      expect(requestorEmailBlock.element.action_id).toBe('requestor_email_input');
      expect(requestorEmailBlock.label.text).toBe('Requestor Email');

      // Check description input
      const descriptionBlock = result.blocks.find(block => block.block_id === 'description_block');
      expect(descriptionBlock).toBeDefined();
      expect(descriptionBlock.type).toBe('input');
      expect(descriptionBlock.element.type).toBe('plain_text_input');
      expect(descriptionBlock.element.multiline).toBe(true);
      expect(descriptionBlock.element.action_id).toBe('description_input');
      expect(descriptionBlock.label.text).toBe('Description');
      expect(descriptionBlock.optional).toBe(true);
    });

    it('should include channel ID in private metadata when provided', () => {
      // Arrange
      const platformTeamUid = 'team-123';
      const channelId = 'C12345';

      // Act
      const result = createTicketsBlocksComposite.build(platformTeamUid, channelId);

      // Assert
      expect(result.private_metadata).toBeDefined();
      expect(JSON.parse(result.private_metadata)).toEqual({ channelId });
    });

    it('should use custom form name when provided', () => {
      // Arrange
      const platformTeamUid = 'team-123';
      const channelId = 'C12345';
      const formName = 'Custom Support Request';

      // Act
      const result = createTicketsBlocksComposite.build(platformTeamUid, channelId, formName);

      // Assert
      expect(result.blocks[0].text.text).toBe(formName);
    });

    it('should have correct static properties', () => {
      // Assert
      expect(CreateTicketsBlocksComposite.BLOCK_ID).toBe('create_tickets_blocks');
      expect(CreateTicketsBlocksComposite.ACTION_IDS.STATUS).toBe('update_status');
      expect(CreateTicketsBlocksComposite.ACTION_IDS.PRIORITY).toBe('set_priority');
      expect(CreateTicketsBlocksComposite.ACTION_IDS.TEAM).toBe('team_select');
    });
  });
});
