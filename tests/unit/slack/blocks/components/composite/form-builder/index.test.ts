import { describe, expect, it } from 'vitest';
import * as FormBuilderModule from '../../../../../../../src/slack/blocks/components/composite/form-builder';
import { ConditionalFormBuilderComposite } from '../../../../../../../src/slack/blocks/components/composite/form-builder/conditional-form-builder.composite';
import { FormSelectorComposite } from '../../../../../../../src/slack/blocks/components/composite/form-builder/form-selector.composite';

describe('Form Builder Module Exports', () => {
  it('should export ConditionalFormBuilderComposite', () => {
    expect(FormBuilderModule.ConditionalFormBuilderComposite).toBeDefined();
    expect(FormBuilderModule.ConditionalFormBuilderComposite).toBe(ConditionalFormBuilderComposite);
  });
  
  it('should export FormSelectorComposite', () => {
    expect(FormBuilderModule.FormSelectorComposite).toBeDefined();
    expect(FormBuilderModule.FormSelectorComposite).toBe(FormSelectorComposite);
  });
});
