import { beforeEach, describe, expect, it } from 'vitest';
import { FormSelectorComposite } from '../../../../../../../src/slack/blocks/components/composite/form-builder/form-selector.composite';

describe('FormSelectorComposite', () => {
  let formSelector: FormSelectorComposite;
  
  beforeEach(() => {
    formSelector = new FormSelectorComposite();
  });
  
  describe('build', () => {
    it('should build a form selector with multiple forms', () => {
      // Arrange
      const forms = [
        {
          id: 'form1',
          name: 'Bug Report',
          description: 'Report a bug',
          teamId: 'team1',
        },
        {
          id: 'form2',
          name: 'Feature Request',
          description: 'Request a new feature',
          teamId: 'team1',
        },
      ];
      
      // Act
      const result = formSelector.build({
        forms,
        messageText: 'Select a form to submit',
        placeholderText: 'Choose a form',
      });
      
      // Assert
      expect(result).toBeDefined();
      expect(result.blocks).toHaveLength(2);
      
      // Check message text
      expect(result.blocks[0].type).toBe('section');
      expect(result.blocks[0].text.text).toBe('Select a form to submit');
      
      // Check dropdown
      expect(result.blocks[1].type).toBe('actions');
      expect(result.blocks[1].elements).toHaveLength(2);
      expect(result.blocks[1].elements[0].type).toBe('static_select');
      expect(result.blocks[1].elements[0].action_id).toBe(FormSelectorComposite.ACTION_ID);
      expect(result.blocks[1].elements[0].options).toHaveLength(2);
      expect(result.blocks[1].elements[0].options[0].text.text).toBe('Bug Report');
      expect(result.blocks[1].elements[0].options[0].value).toBe('form1');
      expect(result.blocks[1].elements[0].options[1].text.text).toBe('Feature Request');
      expect(result.blocks[1].elements[0].options[1].value).toBe('form2');
      expect(result.blocks[1].elements[0].placeholder.text).toBe('Choose a form');
      
      // Check continue button
      expect(result.blocks[1].elements[1].type).toBe('button');
      expect(result.blocks[1].elements[1].text.text).toBe('Continue');
      expect(result.blocks[1].elements[1].action_id).toBe('form_continue');
      expect(result.blocks[1].elements[1].style).toBe('primary');
    });
    
    it('should build a message for no available forms', () => {
      // Act
      const result = formSelector.build({
        forms: [],
      });
      
      // Assert
      expect(result).toBeDefined();
      expect(result.blocks).toHaveLength(1);
      
      // Check message
      expect(result.blocks[0].type).toBe('section');
      expect(result.blocks[0].text.text).toBe('No forms are available at this time.');
    });
    
    it('should include channel ID in private metadata when provided', () => {
      // Arrange
      const forms = [
        {
          id: 'form1',
          name: 'Bug Report',
          description: 'Report a bug',
          teamId: 'team1',
        },
      ];
      
      // Act
      const result = formSelector.build({
        forms,
        channelId: 'C12345',
      });
      
      // Assert
      expect(result).toBeDefined();
      expect(result.private_metadata).toBe(JSON.stringify({ channelId: 'C12345' }));
    });
    
    it('should use default message text when not provided', () => {
      // Arrange
      const forms = [
        {
          id: 'form1',
          name: 'Bug Report',
          description: 'Report a bug',
          teamId: 'team1',
        },
      ];
      
      // Act
      const result = formSelector.build({
        forms,
      });
      
      // Assert
      expect(result).toBeDefined();
      expect(result.blocks[0].text.text).toBe('Please select a form to fill out');
    });
    
    it('should use default placeholder text when not provided', () => {
      // Arrange
      const forms = [
        {
          id: 'form1',
          name: 'Bug Report',
          description: 'Report a bug',
          teamId: 'team1',
        },
      ];
      
      // Act
      const result = formSelector.build({
        forms,
      });
      
      // Assert
      expect(result).toBeDefined();
      expect(result.blocks[1].elements[0].placeholder.text).toBe('Select a form');
    });
  });
});
