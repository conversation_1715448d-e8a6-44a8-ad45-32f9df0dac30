import { beforeEach, describe, expect, it, vi } from 'vitest';
import {
  Condition,
  ConditionalFormBuilderComposite,
  Field,
  FormValues,
  TargetFieldActionType,
  TargetFieldConditionsType,
} from '../../../../../../../src/slack/blocks/components/composite/form-builder/conditional-form-builder.composite';

describe('ConditionalFormBuilderComposite', () => {
  let service: ConditionalFormBuilderComposite;
  const mockLogger = {
    debug: vi.fn(),
    info: vi.fn(),
    warn: vi.fn(),
    error: vi.fn(),
  };

  beforeEach(() => {
    vi.clearAllMocks();
    service = new ConditionalFormBuilderComposite(mockLogger);
  });

  describe('build', () => {
    it('should build a form with fields', () => {
      const fields: Field[] = [
        {
          id: 'field1',
          name: 'Field 1',
          type: 'text',
          mandatoryOnCreation: true,
          visibleToCustomer: true,
          accessibleInTicketCreationForm: true,
        },
        {
          id: 'field2',
          name: 'Field 2',
          type: 'select',
          mandatoryOnCreation: false,
          visibleToCustomer: true,
          accessibleInTicketCreationForm: true,
          options: [
            { label: 'Option 1', value: 'option1' },
            { label: 'Option 2', value: 'option2' },
          ],
        },
      ];

      const result = service.build({
        fields,
        conditions: [],
        conditionOrder: [],
      });

      expect(result).toBeDefined();
      expect(result.blocks).toHaveLength(2); // One block for each field
      expect(result.blocks[0].type).toBe('input');
      expect((result.blocks[0] as any).element.type).toBe('plain_text_input');
      expect((result.blocks[0] as any).optional).toBe(false); // Mandatory field
      expect(result.blocks[1].type).toBe('input');
      expect((result.blocks[1] as any).element.type).toBe('static_select');
      expect((result.blocks[1] as any).optional).toBe(true); // Optional field
    });

    it('should filter out non-visible fields', () => {
      const fields: Field[] = [
        {
          id: 'field1',
          name: 'Field 1',
          type: 'text',
          mandatoryOnCreation: true,
          visibleToCustomer: true,
          accessibleInTicketCreationForm: true,
        },
        {
          id: 'field2',
          name: 'Field 2',
          type: 'select',
          mandatoryOnCreation: false,
          visibleToCustomer: false, // Not visible
          accessibleInTicketCreationForm: true,
        },
        {
          id: 'field3',
          name: 'Field 3',
          type: 'text',
          mandatoryOnCreation: false,
          visibleToCustomer: true,
          accessibleInTicketCreationForm: false, // Not accessible
        },
      ];

      const result = service.build({
        fields,
        conditions: [],
        conditionOrder: [],
      });

      expect(result).toBeDefined();
      expect(result.blocks).toHaveLength(1); // Only one field should be visible and accessible
      expect((result.blocks[0] as any).label.text).toBe('Field 1');
    });

    it('should apply conditions to fields', () => {
      const fields: Field[] = [
        {
          id: 'field1',
          name: 'Field 1',
          type: 'select',
          mandatoryOnCreation: true,
          visibleToCustomer: true,
          accessibleInTicketCreationForm: true,
          options: [
            { label: 'Option 1', value: 'option1' },
            { label: 'Option 2', value: 'option2' },
          ],
        },
        {
          id: 'field2',
          name: 'Field 2',
          type: 'text',
          mandatoryOnCreation: false,
          visibleToCustomer: true,
          accessibleInTicketCreationForm: true,
        },
        {
          id: 'field3',
          name: 'Field 3',
          type: 'text',
          mandatoryOnCreation: false,
          visibleToCustomer: true,
          accessibleInTicketCreationForm: true,
        },
      ];

      const conditions: Condition[] = [
        {
          id: 'condition1',
          triggerFieldId: 'field1',
          conditionType: TargetFieldConditionsType.EQUALS,
          value: 'option1',
          targetFields: [
            {
              id: 'target1',
              type: TargetFieldActionType.ADD_FIELD,
              value: null,
              fieldId: 'field2',
            },
            {
              id: 'target2',
              type: TargetFieldActionType.REMOVE_FIELD,
              value: null,
              fieldId: 'field3',
            },
          ],
        },
      ];

      const values: FormValues = {
        field1: 'option1',
      };

      const result = service.build({
        fields,
        conditions,
        conditionOrder: ['condition1'],
        values,
      });

      expect(result).toBeDefined();
      expect(result.blocks).toHaveLength(2); // field1 and field2 should be visible, field3 removed
      expect((result.blocks[0] as any).label.text).toBe('Field 1');
      expect((result.blocks[1] as any).label.text).toBe('Field 2');
    });
  });

  describe('renderField', () => {
    it('should render an email field when type is email', () => {
      const field: Field = {
        id: 'email-field',
        name: 'Email Field',
        type: 'email',
        mandatoryOnCreation: true,
        visibleToCustomer: true,
        accessibleInTicketCreationForm: true,
      };

      const values: FormValues = {
        'email-field': '<EMAIL>',
      };

      const result = (service as any).renderField(field, values);

      expect(result).toHaveLength(2); // Input block + hint block
      expect(result[0].type).toBe('input');
      expect(result[0].element.type).toBe('plain_text_input');
      expect(result[0].element.initial_value).toBe('<EMAIL>');
      expect(result[0].element.dispatch_action_config).toBeDefined();
      expect(
        result[0].element.dispatch_action_config.trigger_actions_on,
      ).toContain('on_character_entered');
      expect(result[1].type).toBe('context'); // Hint block
    });

    it('should render an email field when type is specialized and originalType is email', () => {
      const field: Field = {
        id: 'specialized-email',
        name: 'Specialized Email',
        type: 'specialized',
        mandatoryOnCreation: false,
        visibleToCustomer: true,
        accessibleInTicketCreationForm: true,
        metadata: {
          originalType: 'email',
        },
      };

      const values: FormValues = {
        'specialized-email': '<EMAIL>',
      };

      const result = (service as any).renderField(field, values);

      expect(result).toHaveLength(2); // Input block + hint block
      expect(result[0].type).toBe('input');
      expect(result[0].element.type).toBe('plain_text_input');
      expect(result[0].element.initial_value).toBe('<EMAIL>');
      expect(result[0].element.dispatch_action_config).toBeDefined();
      expect(result[0].optional).toBe(true); // Not mandatory
      expect(result[1].type).toBe('context'); // Hint block
    });

    it('should render an email field when name contains email', () => {
      const field: Field = {
        id: 'contact-email',
        name: 'Contact Email',
        type: 'text', // Not explicitly email type
        mandatoryOnCreation: true,
        visibleToCustomer: true,
        accessibleInTicketCreationForm: true,
      };

      const values: FormValues = {
        'contact-email': '<EMAIL>',
      };

      const result = (service as any).renderField(field, values);

      expect(result).toHaveLength(1); // Just input block in this case
      expect(result[0].type).toBe('input');
      expect(result[0].element.type).toBe('plain_text_input');
      expect(result[0].element.initial_value).toBe('<EMAIL>');
      expect(result[0].element.dispatch_action_config).toBeDefined();
    });

    it('should render a text input field', () => {
      const field: Field = {
        id: 'text-field',
        name: 'Text Field',
        type: 'text',
        mandatoryOnCreation: true,
        visibleToCustomer: true,
        accessibleInTicketCreationForm: true,
      };

      const values: FormValues = {
        'text-field': 'Sample text',
      };

      const result = (service as any).renderField(field, values);

      expect(result).toHaveLength(1);
      expect(result[0].type).toBe('input');
      expect(result[0].element.type).toBe('plain_text_input');
      expect(result[0].element.initial_value).toBe('Sample text');
      expect(result[0].optional).toBe(false); // Mandatory field
    });

    it('should render a select input field', () => {
      const field: Field = {
        id: 'select-field',
        name: 'Select Field',
        type: 'select',
        mandatoryOnCreation: false,
        visibleToCustomer: true,
        accessibleInTicketCreationForm: true,
        options: [
          { label: 'Option 1', value: 'option1' },
          { label: 'Option 2', value: 'option2' },
        ],
      };

      const values: FormValues = {
        'select-field': 'option1',
      };

      const result = (service as any).renderField(field, values);

      expect(result).toHaveLength(1);
      expect(result[0].type).toBe('input');
      expect(result[0].element.type).toBe('static_select');
      expect(result[0].element.initial_option.value).toBe('option1');
      expect(result[0].optional).toBe(true); // Optional field
    });

    it('should render a multi-select input field', () => {
      const field: Field = {
        id: 'multi-select-field',
        name: 'Multi Select Field',
        type: 'multiselect',
        mandatoryOnCreation: false,
        visibleToCustomer: true,
        accessibleInTicketCreationForm: true,
        options: [
          { label: 'Option 1', value: 'option1' },
          { label: 'Option 2', value: 'option2' },
        ],
      };

      const values: FormValues = {
        'multi-select-field': ['option1', 'option2'],
      };

      const result = (service as any).renderField(field, values);

      expect(result).toHaveLength(1);
      expect(result[0].type).toBe('input');
      expect(result[0].element.type).toBe('multi_static_select');
      expect(result[0].element.initial_options).toHaveLength(2);
      expect(result[0].element.initial_options[0].value).toBe('option1');
      expect(result[0].element.initial_options[1].value).toBe('option2');
    });

    it('should render a number input field', () => {
      const field: Field = {
        id: 'number-field',
        name: 'Number Field',
        type: 'number',
        mandatoryOnCreation: false,
        visibleToCustomer: true,
        accessibleInTicketCreationForm: true,
      };

      const values: FormValues = {
        'number-field': 42,
      };

      const result = (service as any).renderField(field, values);

      expect(result).toHaveLength(1);
      expect(result[0].type).toBe('input');
      expect(result[0].element.type).toBe('number_input');
      expect(result[0].element.initial_value).toBe('42');
    });

    it('should render a date input field', () => {
      const field: Field = {
        id: 'date-field',
        name: 'Date Field',
        type: 'date',
        mandatoryOnCreation: false,
        visibleToCustomer: true,
        accessibleInTicketCreationForm: true,
      };

      const values: FormValues = {
        'date-field': '2023-01-01',
      };

      const result = (service as any).renderField(field, values);

      expect(result).toHaveLength(1);
      expect(result[0].type).toBe('input');
      expect(result[0].element.type).toBe('datepicker');
      expect(result[0].element.initial_date).toBe('2023-01-01');
    });

    it('should render a checkbox input field', () => {
      const field: Field = {
        id: 'checkbox-field',
        name: 'Checkbox Field',
        type: 'checkbox',
        mandatoryOnCreation: false,
        visibleToCustomer: true,
        accessibleInTicketCreationForm: true,
      };

      const values: FormValues = {
        'checkbox-field': true,
      };

      const result = (service as any).renderField(field, values);

      expect(result).toHaveLength(1);
      expect(result[0].type).toBe('input');
      expect(result[0].element.type).toBe('checkboxes');
      expect(result[0].element.initial_options).toHaveLength(1);
      expect(result[0].element.initial_options[0].value).toBe('true');
    });

    it('should handle unknown field types by defaulting to text input', () => {
      const field: Field = {
        id: 'unknown-field',
        name: 'Unknown Field',
        type: 'unknown-type',
        mandatoryOnCreation: false,
        visibleToCustomer: true,
        accessibleInTicketCreationForm: true,
      };

      const result = (service as any).renderField(field);

      expect(result).toHaveLength(1);
      expect(result[0].type).toBe('input');
      expect(result[0].element.type).toBe('plain_text_input');
    });
  });

  describe('evaluateCondition', () => {
    it('should evaluate EQUALS condition correctly', () => {
      const condition: Condition = {
        id: 'condition1',
        triggerFieldId: 'field1',
        conditionType: TargetFieldConditionsType.EQUALS,
        value: 'test',
        targetFields: [],
      };

      const values: FormValues = {
        field1: 'test',
      };

      const result = (service as any).evaluateCondition(condition, values);
      expect(result).toBe(true);

      const values2: FormValues = {
        field1: 'different',
      };
      const result2 = (service as any).evaluateCondition(condition, values2);
      expect(result2).toBe(false);
    });

    it('should evaluate NOT_EQUALS condition correctly', () => {
      const condition: Condition = {
        id: 'condition1',
        triggerFieldId: 'field1',
        conditionType: TargetFieldConditionsType.NOT_EQUALS,
        value: 'test',
        targetFields: [],
      };

      const values: FormValues = {
        field1: 'different',
      };

      const result = (service as any).evaluateCondition(condition, values);
      expect(result).toBe(true);

      const values2: FormValues = {
        field1: 'test',
      };
      const result2 = (service as any).evaluateCondition(condition, values2);
      expect(result2).toBe(false);
    });

    it('should evaluate CONTAINS condition correctly', () => {
      const condition: Condition = {
        id: 'condition1',
        triggerFieldId: 'field1',
        conditionType: TargetFieldConditionsType.CONTAINS,
        value: 'test',
        targetFields: [],
      };

      const values: FormValues = {
        field1: 'this is a test string',
      };

      const result = (service as any).evaluateCondition(condition, values);
      expect(result).toBe(true);

      const values2: FormValues = {
        field1: 'no match here',
      };
      const result2 = (service as any).evaluateCondition(condition, values2);
      expect(result2).toBe(false);
    });

    it('should evaluate IS_EMPTY condition correctly', () => {
      const condition: Condition = {
        id: 'condition1',
        triggerFieldId: 'field1',
        conditionType: TargetFieldConditionsType.IS_EMPTY,
        value: null,
        targetFields: [],
      };

      const values1: FormValues = {
        field1: '',
      };
      const result1 = (service as any).evaluateCondition(condition, values1);
      expect(result1).toBe(true);

      const values2: FormValues = {
        field1: null,
      };
      const result2 = (service as any).evaluateCondition(condition, values2);
      expect(result2).toBe(true);

      const values3: FormValues = {
        field1: [],
      };
      const result3 = (service as any).evaluateCondition(condition, values3);
      expect(result3).toBe(true);

      const values4: FormValues = {
        field1: 'not empty',
      };
      const result4 = (service as any).evaluateCondition(condition, values4);
      expect(result4).toBe(false);
    });

    it('should evaluate IS_NOT_EMPTY condition correctly', () => {
      const condition: Condition = {
        id: 'condition1',
        triggerFieldId: 'field1',
        conditionType: TargetFieldConditionsType.IS_NOT_EMPTY,
        value: null,
        targetFields: [],
      };

      const values1: FormValues = {
        field1: 'not empty',
      };
      const result1 = (service as any).evaluateCondition(condition, values1);
      expect(result1).toBe(true);

      const values2: FormValues = {
        field1: '',
      };
      const result2 = (service as any).evaluateCondition(condition, values2);
      expect(result2).toBe(false);
    });

    it('should evaluate numeric comparison conditions correctly', () => {
      const gtCondition: Condition = {
        id: 'condition1',
        triggerFieldId: 'field1',
        conditionType: TargetFieldConditionsType.GREATER_THAN,
        value: 10,
        targetFields: [],
      };

      const gtValues: FormValues = {
        field1: 15,
      };
      const gtResult = (service as any).evaluateCondition(
        gtCondition,
        gtValues,
      );
      expect(gtResult).toBe(true);

      const ltCondition: Condition = {
        id: 'condition2',
        triggerFieldId: 'field1',
        conditionType: TargetFieldConditionsType.LESS_THAN,
        value: 10,
        targetFields: [],
      };

      const ltValues: FormValues = {
        field1: 5,
      };
      const ltResult = (service as any).evaluateCondition(
        ltCondition,
        ltValues,
      );
      expect(ltResult).toBe(true);

      const gteCondition: Condition = {
        id: 'condition3',
        triggerFieldId: 'field1',
        conditionType: TargetFieldConditionsType.GREATER_THAN_EQUAL,
        value: 10,
        targetFields: [],
      };

      const gteValues: FormValues = {
        field1: 10,
      };
      const gteResult = (service as any).evaluateCondition(
        gteCondition,
        gteValues,
      );
      expect(gteResult).toBe(true);

      const lteCondition: Condition = {
        id: 'condition4',
        triggerFieldId: 'field1',
        conditionType: TargetFieldConditionsType.LESS_THAN_EQUAL,
        value: 10,
        targetFields: [],
      };

      const lteValues: FormValues = {
        field1: 10,
      };
      const lteResult = (service as any).evaluateCondition(
        lteCondition,
        lteValues,
      );
      expect(lteResult).toBe(true);
    });

    it('should return false for undefined trigger values', () => {
      const condition: Condition = {
        id: 'condition1',
        triggerFieldId: 'field1',
        conditionType: TargetFieldConditionsType.EQUALS,
        value: 'test',
        targetFields: [],
      };

      const values: FormValues = {
        field2: 'test', // Different field ID
      };

      const result = (service as any).evaluateCondition(condition, values);
      expect(result).toBe(false);
    });

    it('should return false for unknown condition types', () => {
      const condition: Condition = {
        id: 'condition1',
        triggerFieldId: 'field1',
        conditionType: '' as TargetFieldConditionsType, // Invalid condition type
        value: 'test',
        targetFields: [],
      };

      const values: FormValues = {
        field1: 'test',
      };

      const result = (service as any).evaluateCondition(condition, values);
      expect(result).toBe(false);
    });
  });

  describe('applyConditions', () => {
    it('should apply ADD_FIELD action correctly', () => {
      const fields: Field[] = [
        {
          id: 'field1',
          name: 'Field 1',
          type: 'select',
          mandatoryOnCreation: true,
          visibleToCustomer: true,
          accessibleInTicketCreationForm: true,
        },
        {
          id: 'field2',
          name: 'Field 2',
          type: 'text',
          mandatoryOnCreation: false,
          visibleToCustomer: false, // Initially not visible
          editableByCustomer: false, // Initially not editable
          accessibleInTicketCreationForm: true,
        },
      ];

      const conditions: Condition[] = [
        {
          id: 'condition1',
          triggerFieldId: 'field1',
          conditionType: TargetFieldConditionsType.EQUALS,
          value: 'option1',
          targetFields: [
            {
              id: 'target1',
              type: TargetFieldActionType.ADD_FIELD,
              value: null,
              fieldId: 'field2',
            },
          ],
        },
      ];

      const values: FormValues = {
        field1: 'option1',
      };

      const result = (service as any).applyConditions(
        fields,
        conditions,
        ['condition1'],
        values,
      );

      expect(result).toHaveLength(2);
      expect(result[1].id).toBe('field2');
      expect(result[1].visibleToCustomer).toBe(true); // Should now be visible
      expect(result[1].editableByCustomer).toBe(true); // Should now be editable
    });

    it('should apply REMOVE_FIELD action correctly', () => {
      const fields: Field[] = [
        {
          id: 'field1',
          name: 'Field 1',
          type: 'select',
          mandatoryOnCreation: true,
          visibleToCustomer: true,
          accessibleInTicketCreationForm: true,
        },
        {
          id: 'field2',
          name: 'Field 2',
          type: 'text',
          mandatoryOnCreation: true,
          visibleToCustomer: true, // Initially visible
          editableByCustomer: true, // Initially editable
          accessibleInTicketCreationForm: true,
        },
      ];

      const conditions: Condition[] = [
        {
          id: 'condition1',
          triggerFieldId: 'field1',
          conditionType: TargetFieldConditionsType.EQUALS,
          value: 'option1',
          targetFields: [
            {
              id: 'target1',
              type: TargetFieldActionType.REMOVE_FIELD,
              value: null,
              fieldId: 'field2',
            },
          ],
        },
      ];

      const values: FormValues = {
        field1: 'option1',
      };

      const result = (service as any).applyConditions(
        fields,
        conditions,
        ['condition1'],
        values,
      );

      expect(result).toHaveLength(2);
      expect(result[1].id).toBe('field2');
      expect(result[1].visibleToCustomer).toBe(false); // Should now be hidden
      expect(result[1].editableByCustomer).toBe(false); // Should now be non-editable
      expect(result[1].mandatoryOnCreation).toBe(false); // Should now be non-mandatory
    });

    it('should apply MARK_MANDATORY action correctly', () => {
      const fields: Field[] = [
        {
          id: 'field1',
          name: 'Field 1',
          type: 'select',
          mandatoryOnCreation: true,
          visibleToCustomer: true,
          accessibleInTicketCreationForm: true,
        },
        {
          id: 'field2',
          name: 'Field 2',
          type: 'text',
          mandatoryOnCreation: false, // Initially not mandatory
          visibleToCustomer: true,
          accessibleInTicketCreationForm: true,
        },
      ];

      const conditions: Condition[] = [
        {
          id: 'condition1',
          triggerFieldId: 'field1',
          conditionType: TargetFieldConditionsType.EQUALS,
          value: 'option1',
          targetFields: [
            {
              id: 'target1',
              type: TargetFieldActionType.MARK_MANDATORY,
              value: null,
              fieldId: 'field2',
            },
          ],
        },
      ];

      const values: FormValues = {
        field1: 'option1',
      };

      const result = (service as any).applyConditions(
        fields,
        conditions,
        ['condition1'],
        values,
      );

      expect(result).toHaveLength(2);
      expect(result[1].id).toBe('field2');
      expect(result[1].mandatoryOnCreation).toBe(true); // Should now be mandatory
    });

    it('should apply MARK_NON_MANDATORY action correctly', () => {
      const fields: Field[] = [
        {
          id: 'field1',
          name: 'Field 1',
          type: 'select',
          mandatoryOnCreation: true,
          visibleToCustomer: true,
          accessibleInTicketCreationForm: true,
        },
        {
          id: 'field2',
          name: 'Field 2',
          type: 'text',
          mandatoryOnCreation: true, // Initially mandatory
          visibleToCustomer: true,
          accessibleInTicketCreationForm: true,
        },
      ];

      const conditions: Condition[] = [
        {
          id: 'condition1',
          triggerFieldId: 'field1',
          conditionType: TargetFieldConditionsType.EQUALS,
          value: 'option1',
          targetFields: [
            {
              id: 'target1',
              type: TargetFieldActionType.MARK_NON_MANDATORY,
              value: null,
              fieldId: 'field2',
            },
          ],
        },
      ];

      const values: FormValues = {
        field1: 'option1',
      };

      const result = (service as any).applyConditions(
        fields,
        conditions,
        ['condition1'],
        values,
      );

      expect(result).toHaveLength(2);
      expect(result[1].id).toBe('field2');
      expect(result[1].mandatoryOnCreation).toBe(false); // Should now be non-mandatory
    });

    it('should process conditions in the specified order', () => {
      const fields: Field[] = [
        {
          id: 'field1',
          name: 'Field 1',
          type: 'select',
          mandatoryOnCreation: true,
          visibleToCustomer: true,
          accessibleInTicketCreationForm: true,
        },
        {
          id: 'field2',
          name: 'Field 2',
          type: 'text',
          mandatoryOnCreation: false,
          visibleToCustomer: true,
          accessibleInTicketCreationForm: true,
        },
      ];

      const conditions: Condition[] = [
        {
          id: 'condition1',
          triggerFieldId: 'field1',
          conditionType: TargetFieldConditionsType.EQUALS,
          value: 'option1',
          targetFields: [
            {
              id: 'target1',
              type: TargetFieldActionType.MARK_MANDATORY,
              value: null,
              fieldId: 'field2',
            },
          ],
        },
        {
          id: 'condition2',
          triggerFieldId: 'field1',
          conditionType: TargetFieldConditionsType.EQUALS,
          value: 'option1',
          targetFields: [
            {
              id: 'target2',
              type: TargetFieldActionType.MARK_NON_MANDATORY,
              value: null,
              fieldId: 'field2',
            },
          ],
        },
      ];

      const values: FormValues = {
        field1: 'option1',
      };

      const result1 = (service as any).applyConditions(
        fields,
        conditions,
        ['condition1', 'condition2'],
        values,
      );

      expect(result1[1].mandatoryOnCreation).toBe(false); // Should end up as non-mandatory

      const result2 = (service as any).applyConditions(
        fields,
        conditions,
        ['condition2', 'condition1'],
        values,
      );

      expect(result2[1].mandatoryOnCreation).toBe(true); // Should end up as mandatory
    });

    it('should handle non-existent field IDs gracefully', () => {
      const fields: Field[] = [
        {
          id: 'field1',
          name: 'Field 1',
          type: 'select',
          mandatoryOnCreation: true,
          visibleToCustomer: true,
          accessibleInTicketCreationForm: true,
        },
      ];

      const conditions: Condition[] = [
        {
          id: 'condition1',
          triggerFieldId: 'field1',
          conditionType: TargetFieldConditionsType.EQUALS,
          value: 'option1',
          targetFields: [
            {
              id: 'target1',
              type: TargetFieldActionType.MARK_MANDATORY,
              value: null,
              fieldId: 'non-existent-field', // Field doesn't exist
            },
          ],
        },
      ];

      const values: FormValues = {
        field1: 'option1',
      };

      const result = (service as any).applyConditions(
        fields,
        conditions,
        ['condition1'],
        values,
      );

      expect(result).toHaveLength(1); // Original field still exists
      expect(result[0].id).toBe('field1');
    });

    it('should handle non-existent condition IDs gracefully', () => {
      const fields: Field[] = [
        {
          id: 'field1',
          name: 'Field 1',
          type: 'select',
          mandatoryOnCreation: true,
          visibleToCustomer: true,
          accessibleInTicketCreationForm: true,
        },
      ];

      const conditions: Condition[] = [
        {
          id: 'condition1',
          triggerFieldId: 'field1',
          conditionType: TargetFieldConditionsType.EQUALS,
          value: 'option1',
          targetFields: [
            {
              id: 'target1',
              type: TargetFieldActionType.MARK_MANDATORY,
              value: null,
              fieldId: 'field1',
            },
          ],
        },
      ];

      const values: FormValues = {
        field1: 'option1',
      };

      const result = (service as any).applyConditions(
        fields,
        conditions,
        ['non-existent-condition'], // Condition doesn't exist
        values,
      );

      expect(result).toHaveLength(1); // Original field still exists
      expect(result[0].id).toBe('field1');
      expect(result[0].mandatoryOnCreation).toBe(true); // Unchanged
    });
  });
});
