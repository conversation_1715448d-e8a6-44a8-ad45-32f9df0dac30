import { describe, it, expect, beforeEach, vi } from 'vitest';
import { TriageMessageBlock } from '../../../../../../../src/slack/blocks/components/composite/triage/ticket-triage-blocks.composite';
import { ConfigService } from '../../../../../../../src/config/config.service';
import { CUSTOM_LOGGER_TOKEN } from '../../../../../../../src/utils';

describe('TriageMessageBlock', () => {
  let triageMessageBlock: TriageMessageBlock;
  let mockConfigService: any;
  let mockLogger: any;

  beforeEach(() => {
    // Mock ConfigService
    mockConfigService = {
      get: vi.fn().mockImplementation((key) => {
        if (key === 'THENA_WEB_URL') return 'https://app.thena.io';
        return null;
      }),
    };

    // Mock Logger
    mockLogger = {
      log: vi.fn(),
      error: vi.fn(),
      warn: vi.fn(),
      debug: vi.fn(),
    };

    triageMessageBlock = new TriageMessageBlock(
      mockConfigService,
      mockLogger
    );
  });

  describe('build', () => {
    it('should handle archived tickets', () => {
      // Arrange
      const ticket = {
        id: 'ticket-123',
        ticketId: 'TKT-123',
        ticketTeamId: 'team-456',
        priority: 'High',
        priorityId: 'priority-1',
        status: 'Closed',
        statusId: 'status-1',
        customer: {
          name: 'John Doe',
          email: '<EMAIL>',
          company: 'Example Corp',
        },
        subject: 'Test Ticket',
        content: 'This is a test ticket content',
        timestamp: '2023-01-01T12:00:00Z',
        archivedAt: '2023-01-02T12:00:00Z', // Archived ticket
      };

      // Act
      const result = triageMessageBlock.build({
        ticket,
      });

      // Assert
      expect(result).toBeDefined();
      expect(result.blocks).toBeInstanceOf(Array);
      
      // Check that no actions block is present for archived tickets
      const actionsBlock = result.blocks.find((block) => block.type === 'actions');
      expect(actionsBlock).toBeUndefined();
      
      // Check that logger was called
      expect(mockLogger.log).toHaveBeenCalledWith(
        expect.stringContaining(`Ticket ${ticket.id} is archived, skipping interactivity`)
      );
    });

    it('should have correct static properties', () => {
      // Assert
      expect(TriageMessageBlock.BLOCK_ID).toBe('support_triage_block');
      expect(TriageMessageBlock.ACTION_IDS.STATUS).toBe('update_status');
      expect(TriageMessageBlock.ACTION_IDS.PRIORITY).toBe('set_priority');
      expect(TriageMessageBlock.ACTION_IDS.ASSIGN).toBe('assign_ticket');
      expect(TriageMessageBlock.ACTION_IDS.LOOKING).toBe('looking_into_ticket');
      expect(TriageMessageBlock.ACTION_IDS.CLOSE).toBe('close_ticket');
      expect(TriageMessageBlock.ACTION_IDS.ARCHIVE).toBe('archive_ticket');
    });
  });
});
