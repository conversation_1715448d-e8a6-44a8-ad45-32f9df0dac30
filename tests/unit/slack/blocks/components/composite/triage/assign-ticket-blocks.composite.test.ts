import { describe, it, expect, beforeEach } from 'vitest';
import { SelectAssigneeComposite } from '../../../../../../../src/slack/blocks/components/composite/triage/assign-ticket-blocks.composite';

describe('SelectAssigneeComposite', () => {
  let selectAssigneeComposite: SelectAssigneeComposite;

  beforeEach(() => {
    selectAssigneeComposite = new SelectAssigneeComposite();
  });

  describe('build', () => {
    it('should build blocks for selecting an assignee', () => {
      // Act
      const result = selectAssigneeComposite.build();

      // Assert
      expect(result).toBeDefined();
      expect(result.blocks).toBeInstanceOf(Array);
      expect(result.blocks.length).toBe(3);

      // Check header
      expect(result.blocks[0].type).toBe('header');
      expect(result.blocks[0].text.type).toBe('plain_text');
      expect(result.blocks[0].text.text).toBe('Assign Ticket');

      // Check description section
      expect(result.blocks[1].type).toBe('section');
      expect(result.blocks[1].text.type).toBe('mrkdwn');
      expect(result.blocks[1].text.text).toContain('Assign the ticket to an agent');

      // Check assignee select input
      expect(result.blocks[2].type).toBe('input');
      expect(result.blocks[2].block_id).toBe(SelectAssigneeComposite.BLOCK_ID);
      expect(result.blocks[2].element.type).toBe('external_select');
      expect(result.blocks[2].element.action_id).toBe(SelectAssigneeComposite.ACTION_IDS.ASSIGNEE_SELECT);
      expect(result.blocks[2].element.min_query_length).toBe(0);
      expect(result.blocks[2].element.placeholder.text).toBe('Search for an assignee');
      expect(result.blocks[2].label.text).toBe('Assignee');
    });

    it('should have correct static properties', () => {
      // Assert
      expect(SelectAssigneeComposite.BLOCK_ID).toBe('assign_ticket_modal');
      expect(SelectAssigneeComposite.ACTION_IDS.ASSIGNEE_SELECT).toBe('assignee_select');
    });
  });
});
