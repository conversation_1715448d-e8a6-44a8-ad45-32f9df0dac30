import { describe, it, expect, beforeEach } from 'vitest';
import { CreateNewTicketBlocks } from '../../../../../../../src/slack/blocks/components/composite/channels/create-new-ticket-blocks.composite';

describe('CreateNewTicketBlocks', () => {
  let createNewTicketBlocks: CreateNewTicketBlocks;

  beforeEach(() => {
    createNewTicketBlocks = new CreateNewTicketBlocks();
  });

  describe('build', () => {
    it('should build blocks for creating a new ticket', () => {
      // Act
      const result = createNewTicketBlocks.build();

      // Assert
      expect(result).toBeDefined();
      expect(result.blocks).toBeInstanceOf(Array);
      expect(result.blocks.length).toBe(4);

      // Check header
      expect(result.blocks[0].type).toBe('header');
      expect(result.blocks[0].text.type).toBe('plain_text');
      expect(result.blocks[0].text.text).toBe('Create a Support Ticket');

      // Check first description section
      expect(result.blocks[1].type).toBe('section');
      expect(result.blocks[1].text.type).toBe('mrkdwn');
      expect(result.blocks[1].text.text).toContain('To submit a support request');

      // Check second description section
      expect(result.blocks[2].type).toBe('section');
      expect(result.blocks[2].text.type).toBe('mrkdwn');
      expect(result.blocks[2].text.text).toContain('Creating a detailed ticket');

      // Check actions block with button
      expect(result.blocks[3].type).toBe('actions');
      expect(result.blocks[3].block_id).toBe(CreateNewTicketBlocks.BLOCK_ID);
      expect(result.blocks[3].elements).toHaveLength(1);
      expect(result.blocks[3].elements[0].type).toBe('button');
      expect(result.blocks[3].elements[0].text.text).toBe('Create ticket');
      expect(result.blocks[3].elements[0].style).toBe('primary');
      expect(result.blocks[3].elements[0].action_id).toBe(
        CreateNewTicketBlocks.ACTION_IDS.CREATE_TICKET_FROM_REACTION
      );
    });

    it('should have correct static properties', () => {
      // Assert
      expect(CreateNewTicketBlocks.BLOCK_ID).toBe('create_new_ticket_form');
      expect(CreateNewTicketBlocks.ACTION_IDS.CREATE_TICKET_FROM_REACTION).toBe(
        'create_ticket_button_from_reaction'
      );
    });
  });
});
