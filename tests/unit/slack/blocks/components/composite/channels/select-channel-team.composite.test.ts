import { describe, it, expect, beforeEach } from 'vitest';
import { SelectChannelTeamComposite } from '../../../../../../../src/slack/blocks/components/composite/channels/select-channel-team.composite';

describe('SelectChannelTeamComposite', () => {
  let selectChannelTeamComposite: SelectChannelTeamComposite;

  beforeEach(() => {
    selectChannelTeamComposite = new SelectChannelTeamComposite();
  });

  describe('build', () => {
    it('should build blocks for team selection', () => {
      // Act
      const result = selectChannelTeamComposite.build();

      // Assert
      expect(result).toBeDefined();
      expect(result.blocks).toBeInstanceOf(Array);
      expect(result.blocks.length).toBe(3);

      // Check header
      expect(result.blocks[0].type).toBe('header');
      expect(result.blocks[0].text.type).toBe('plain_text');
      expect(result.blocks[0].text.text).toBe('Configure Team for Channel');

      // Check description section
      expect(result.blocks[1].type).toBe('section');
      expect(result.blocks[1].text.type).toBe('mrkdwn');
      expect(result.blocks[1].text.text).toContain('Select the team');

      // Check team select input
      expect(result.blocks[2].type).toBe('input');
      expect(result.blocks[2].block_id).toBe(SelectChannelTeamComposite.BLOCK_ID);
      expect(result.blocks[2].element.type).toBe('external_select');
      expect(result.blocks[2].element.action_id).toBe(SelectChannelTeamComposite.ACTION_IDS.TEAM_SELECT);
      expect(result.blocks[2].element.min_query_length).toBe(0);
      expect(result.blocks[2].element.placeholder.text).toBe('Search for a team');
      expect(result.blocks[2].label.text).toBe('Team');
    });

    it('should have correct static properties', () => {
      // Assert
      expect(SelectChannelTeamComposite.BLOCK_ID).toBe('team_config_modal');
      expect(SelectChannelTeamComposite.ACTION_IDS.TEAM_SELECT).toBe('team_select');
    });
  });
});
