import { describe, it, expect, beforeEach } from 'vitest';
import { ConfigureTriageChannelComposite } from '../../../../../../../src/slack/blocks/components/composite/channels/configure-triage-channel.composite';

describe('ConfigureTriageChannelComposite', () => {
  let configureTriageChannelComposite: ConfigureTriageChannelComposite;

  beforeEach(() => {
    configureTriageChannelComposite = new ConfigureTriageChannelComposite();
  });

  describe('build', () => {
    it('should build blocks for triage channel configuration', () => {
      // Act
      const result = configureTriageChannelComposite.build();

      // Assert
      expect(result).toBeDefined();
      expect(result.blocks).toBeInstanceOf(Array);
      expect(result.blocks.length).toBe(3);

      // Check header
      expect(result.blocks[0].type).toBe('header');
      expect(result.blocks[0].text.type).toBe('plain_text');
      expect(result.blocks[0].text.text).toBe('Configure Triage Channel');

      // Check description section
      expect(result.blocks[1].type).toBe('section');
      expect(result.blocks[1].text.type).toBe('mrkdwn');
      expect(result.blocks[1].text.text).toContain('Select the channel');

      // Check channel select input
      expect(result.blocks[2].type).toBe('input');
      expect(result.blocks[2].block_id).toBe(ConfigureTriageChannelComposite.BLOCK_ID);
      expect(result.blocks[2].element.type).toBe('external_select');
      expect(result.blocks[2].element.action_id).toBe(ConfigureTriageChannelComposite.ACTION_IDS.CHANNEL_SELECT);
      expect(result.blocks[2].element.min_query_length).toBe(0);
      expect(result.blocks[2].element.placeholder.text).toBe('Search for a channel');
      expect(result.blocks[2].label.text).toBe('Search for a triage channel');
    });

    it('should have correct static properties', () => {
      // Assert
      expect(ConfigureTriageChannelComposite.BLOCK_ID).toBe('configure_triage_channel');
      expect(ConfigureTriageChannelComposite.ACTION_IDS.CHANNEL_SELECT).toBe('channel_select');
    });
  });
});
