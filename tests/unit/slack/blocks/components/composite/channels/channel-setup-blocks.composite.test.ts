import { describe, it, expect, beforeEach } from 'vitest';
import { ChannelSetupBlocks } from '../../../../../../../src/slack/blocks/components/composite/channels/channel-setup-blocks.composite';
import { ChannelType } from '../../../../../../../src/database/entities/channels/channels.entity';

describe('ChannelSetupBlocks', () => {
  let channelSetupBlocks: ChannelSetupBlocks;

  beforeEach(() => {
    channelSetupBlocks = new ChannelSetupBlocks();
  });

  describe('build', () => {
    it('should build blocks with empty teams array', () => {
      // Act
      const result = channelSetupBlocks.build();

      // Assert
      expect(result).toBeDefined();
      expect(result.blocks).toBeInstanceOf(Array);
      expect(result.blocks.length).toBeGreaterThan(0);

      // Check header
      expect(result.blocks[0].type).toBe('header');
      expect(result.blocks[0].text.text).toBe('Channel Setup Required 🎯');

      // Check team select
      const teamSelectBlock = result.blocks.find(
        (block) => block.block_id === `${ChannelSetupBlocks.BLOCK_ID}_team`
      );
      expect(teamSelectBlock).toBeDefined();
      expect(teamSelectBlock.element.type).toBe('static_select');
      expect(teamSelectBlock.element.action_id).toBe(ChannelSetupBlocks.ACTION_IDS.TEAM_SELECT);
      expect(teamSelectBlock.element.options).toEqual([]);

      // Check channel type select
      const channelTypeBlock = result.blocks.find(
        (block) => block.block_id === `${ChannelSetupBlocks.BLOCK_ID}_type`
      );
      expect(channelTypeBlock).toBeDefined();
      expect(channelTypeBlock.element.type).toBe('static_select');
      expect(channelTypeBlock.element.action_id).toBe(ChannelSetupBlocks.ACTION_IDS.CHANNEL_TYPE_SELECT);
      
      // Check channel type options
      expect(channelTypeBlock.element.options).toHaveLength(3);
      expect(channelTypeBlock.element.options[0].value).toBe(ChannelType.CUSTOMER_CHANNEL);
      expect(channelTypeBlock.element.options[1].value).toBe(ChannelType.INTERNAL_HELPDESK);
      expect(channelTypeBlock.element.options[2].value).toBe(ChannelType.TRIAGE_CHANNEL);
    });

    it('should build blocks with provided teams array', () => {
      // Arrange
      const teams = [
        { id: 'team1', name: 'Engineering' },
        { id: 'team2', name: 'Marketing' },
        { id: 'team3', name: 'Sales' },
      ];

      // Act
      const result = channelSetupBlocks.build(teams);

      // Assert
      expect(result).toBeDefined();
      
      // Check team select options
      const teamSelectBlock = result.blocks.find(
        (block) => block.block_id === `${ChannelSetupBlocks.BLOCK_ID}_team`
      );
      expect(teamSelectBlock.element.options).toHaveLength(3);
      
      // Check first team option
      expect(teamSelectBlock.element.options[0].text.text).toBe('Engineering');
      expect(teamSelectBlock.element.options[0].value).toBe('team1');
      
      // Check second team option
      expect(teamSelectBlock.element.options[1].text.text).toBe('Marketing');
      expect(teamSelectBlock.element.options[1].value).toBe('team2');
      
      // Check third team option
      expect(teamSelectBlock.element.options[2].text.text).toBe('Sales');
      expect(teamSelectBlock.element.options[2].value).toBe('team3');
    });

    it('should include descriptions for all channel types', () => {
      // Act
      const result = channelSetupBlocks.build();

      // Assert
      const blocks = result.blocks;
      
      // Find description blocks
      const customerChannelBlock = blocks.find(
        (block) => block.type === 'section' && block.text.text.includes('Customer Channel')
      );
      const internalHelpdeskBlock = blocks.find(
        (block) => block.type === 'section' && block.text.text.includes('Internal Helpdesk')
      );
      const triageChannelBlock = blocks.find(
        (block) => block.type === 'section' && block.text.text.includes('Triage Channel')
      );
      
      // Check descriptions
      expect(customerChannelBlock).toBeDefined();
      expect(customerChannelBlock.text.text).toContain('For managing external customer queries');
      
      expect(internalHelpdeskBlock).toBeDefined();
      expect(internalHelpdeskBlock.text.text).toContain('For team collaboration');
      
      expect(triageChannelBlock).toBeDefined();
      expect(triageChannelBlock.text.text).toContain('For triage, alerts, notifications');
    });

    it('should have correct static properties', () => {
      // Assert
      expect(ChannelSetupBlocks.BLOCK_ID).toBe('channel_setup_form');
      expect(ChannelSetupBlocks.ACTION_IDS.TEAM_SELECT).toBe('team_select');
      expect(ChannelSetupBlocks.ACTION_IDS.CHANNEL_TYPE_SELECT).toBe('channel_type_select');
    });
  });
});
