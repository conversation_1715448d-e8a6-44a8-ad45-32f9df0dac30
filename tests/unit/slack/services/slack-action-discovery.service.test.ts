import { DiscoveryService } from '@nestjs/core/discovery';
import { Mock, beforeEach, describe, expect, it, vi } from 'vitest';
import { InstallationRepository } from '../../../../src/database/entities/installations/repositories';
import { SlackAppManagementService } from '../../../../src/slack/core';
import { SlackAction } from '../../../../src/slack/decorators/slack-action.decorator';
import { SlackCommand } from '../../../../src/slack/decorators/slack-command.decorator';
import { SlackView } from '../../../../src/slack/decorators/slack-view.decorator';
import { FormFieldActionHandler } from '../../../../src/slack/handlers/slack-actions/form-builder.handler';
import { SlackActionDiscoveryService } from '../../../../src/slack/services/slack-action-discovery.service';
import { SlackService } from '../../../../src/slack/services/slack.service';
import { SQSConsumerService } from '../../../../src/utils/aws-utils/sqs';
import { ILogger } from '../../../../src/utils/logger/logger.interface';

describe('SlackActionDiscoveryService', () => {
  let service: SlackActionDiscoveryService;
  let mockLogger: ILogger;
  let mockDiscoveryService: DiscoveryService;
  let mockSlackService: SlackService;
  let mockSlackAppManagementService: SlackAppManagementService;
  let mockInstallationRepository: InstallationRepository;
  let mockSQSConsumerService: SQSConsumerService;
  let mockFormFieldActionHandler: FormFieldActionHandler;
  let mockBoltApp: any;

  beforeEach(() => {
    vi.resetAllMocks();

    mockLogger = {
      log: vi.fn(),
      error: vi.fn(),
      warn: vi.fn(),
      debug: vi.fn(),
      verbose: vi.fn(),
    } as unknown as ILogger;

    mockDiscoveryService = {
      getProviders: vi.fn(),
    } as unknown as DiscoveryService;

    // Create a complete mock Bolt app with all necessary methods
    mockBoltApp = {
      action: vi.fn(),
      view: vi.fn(),
      command: vi.fn(),
    };

    mockSlackService = {
      getBolt: vi.fn().mockReturnValue(mockBoltApp),
    } as unknown as SlackService;

    mockSlackAppManagementService = {
      getAndUpsertUser: vi.fn(),
    } as unknown as SlackAppManagementService;

    mockInstallationRepository = {
      findByCondition: vi.fn(),
    } as unknown as InstallationRepository;

    mockSQSConsumerService = {
      startConsumer: vi.fn(),
      registerHandler: vi.fn(),
    } as unknown as SQSConsumerService;

    mockFormFieldActionHandler = {
      handle: vi.fn(),
    } as unknown as FormFieldActionHandler;

    service = new SlackActionDiscoveryService(
      mockLogger,
      mockDiscoveryService,
      mockSlackService,
      mockSlackAppManagementService,
      mockInstallationRepository,
      mockSQSConsumerService,
      mockFormFieldActionHandler,
    );
  });

  describe('onModuleInit', () => {
    it('should discover and register action handlers', async () => {
      @SlackAction('test_action_id')
      class TestActionHandler {
        async handle() {
          return 'test';
        }
      }

      const mockActionHandler = new TestActionHandler();

      (mockDiscoveryService.getProviders as Mock).mockReturnValue([
        {
          instance: mockActionHandler,
          metatype: TestActionHandler,
        },
      ]);

      await service.onModuleInit();

      expect(mockDiscoveryService.getProviders).toHaveBeenCalled();
      expect(mockBoltApp.action).toHaveBeenCalled();
    });

    it('should discover and register view handlers', async () => {
      @SlackView('test_view_id')
      class TestViewHandler {
        async handle() {
          return 'test';
        }
      }

      const mockViewHandler = new TestViewHandler();

      (mockDiscoveryService.getProviders as Mock).mockReturnValue([
        {
          instance: mockViewHandler,
          metatype: TestViewHandler,
        },
      ]);

      await service.onModuleInit();

      expect(mockDiscoveryService.getProviders).toHaveBeenCalled();
      expect(mockBoltApp.view).toHaveBeenCalled();
    });

    it('should discover and register command handlers', async () => {
      @SlackCommand('/test-command')
      class TestCommandHandler {
        async handle() {
          return 'test';
        }
      }

      const mockCommandHandler = new TestCommandHandler();

      (mockDiscoveryService.getProviders as Mock).mockReturnValue([
        {
          instance: mockCommandHandler,
          metatype: TestCommandHandler,
        },
      ]);

      await service.onModuleInit();

      expect(mockDiscoveryService.getProviders).toHaveBeenCalled();
      expect(mockBoltApp.command).toHaveBeenCalled();
    });

    it('should handle errors during discovery', async () => {
      // Mock the getProviders method to throw an error
      (mockDiscoveryService.getProviders as Mock).mockImplementation(() => {
        throw new Error('Discovery error');
      });

      // The method throws an error but we're testing that the logger records it
      try {
        await service.onModuleInit();
        // If it doesn't throw, the test should fail
        expect(true).toBe(false);
      } catch (error) {
        // Expect the error message to match
        expect(error.message).toBe('Discovery error');
      }
    });
  });
});
