import { Repository } from 'typeorm';
import { Mock, beforeEach, describe, expect, it, vi } from 'vitest';
import {
  CustomerContacts,
  SlackMessages,
} from '../../../../src/database/entities';
import { TeamTriageRuleMappingRepository } from '../../../../src/database/entities/mappings/repositories/teams-triage-mappings.repository';
import { TeamsRepository } from '../../../../src/database/entities/teams';
import { ThenaPlatformApiProvider } from '../../../../src/external/provider/thena-platform-api.provider';
import { Ticket } from '../../../../src/platform/interfaces';
import { CoreTriageService } from '../../../../src/slack/core/messages';
import { SlackWebAPIService } from '../../../../src/slack/providers/slack-apis/slack-apis.service';
import { TriageEvaluationService } from '../../../../src/slack/services/triage-evaluation.service';
import { TriageRuleEvaluatorService } from '../../../../src/slack/services/triage-rule-evaluator.service';
import { ILogger } from '../../../../src/utils/logger/logger.interface';

describe('TriageEvaluationService', () => {
  let service: TriageEvaluationService;
  let mockLogger: ILogger;
  let mockTriageRuleRepo: TeamTriageRuleMappingRepository;
  let mockTriageRuleEvaluator: TriageRuleEvaluatorService;
  let mockPlatformTeamRepository: TeamsRepository;
  let mockCustomerContactsRepository: Repository<CustomerContacts>;
  let mockSlackMessagesRepository: Repository<SlackMessages>;
  let mockPlatformApiProvider: ThenaPlatformApiProvider;
  let mockSlackWebAPIService: SlackWebAPIService;
  let mockCoreTriageService: CoreTriageService;

  beforeEach(() => {
    vi.resetAllMocks();

    mockLogger = {
      log: vi.fn(),
      error: vi.fn(),
      warn: vi.fn(),
      debug: vi.fn(),
      verbose: vi.fn(),
    } as unknown as ILogger;

    mockTriageRuleRepo = {
      findActiveRulesForTeam: vi.fn(),
    } as unknown as TeamTriageRuleMappingRepository;

    mockTriageRuleEvaluator = {
      evaluateRules: vi.fn(),
    } as unknown as TriageRuleEvaluatorService;

    mockPlatformTeamRepository = {
      findByCondition: vi.fn(),
    } as unknown as TeamsRepository;

    mockCustomerContactsRepository = {
      findOne: vi.fn(),
    } as unknown as Repository<CustomerContacts>;

    mockSlackMessagesRepository = {
      findOne: vi.fn(),
    } as unknown as Repository<SlackMessages>;

    mockPlatformApiProvider = {
      getEntityDetails: vi.fn(),
    } as unknown as ThenaPlatformApiProvider;

    mockSlackWebAPIService = {
      getTeamInfo: vi.fn(),
    } as unknown as SlackWebAPIService;

    mockCoreTriageService = {
      sendTriageMessageToChannel: vi.fn(),
    } as unknown as CoreTriageService;

    service = new TriageEvaluationService(
      mockLogger,
      mockTriageRuleRepo,
      mockTriageRuleEvaluator,
      mockPlatformTeamRepository,
      mockCustomerContactsRepository,
      mockSlackMessagesRepository,
      mockPlatformApiProvider,
      mockSlackWebAPIService,
      mockCoreTriageService,
    );
  });

  describe('evaluateAndSendTriageMessages', () => {
    it('should evaluate triage rules and send messages to matching channels', async () => {
      const installation = {
        id: 'installation-id',
        botToken: 'mock-bot-token',
        organization: {
          id: 'org-id',
        },
      };

      const ticket: Partial<Ticket> = {
        id: 'ticket-123',
        title: 'Test Ticket',
        customerContactEmail: '<EMAIL>',
        customerContactFirstName: 'John',
        customerContactLastName: 'Doe',
      };

      const slackMessage = {
        id: 'message-id',
        platformTicketId: 'ticket-123',
        channel: {
          id: 'channel-id',
          channelId: 'C12345',
          name: 'general',
        },
      };

      const platformTeam = {
        id: 'team-id',
        uid: 'team-123',
      };

      const entityDetails = {
        data: {
          ...ticket,
          account: {
            id: 'account-123',
            name: 'Test Account',
          },
        },
      };

      const triageRules = [
        {
          id: 'rule-1',
          isDefault: false,
          triageRules: {
            conditions: [
              {
                field: 'ticket.title',
                operator: 'contains',
                value: 'Test',
              },
            ],
          },
          triageChannels: [
            {
              id: 'channel-1',
              channelId: 'C67890',
              name: 'triage-channel',
            },
          ],
        },
      ];

      (mockPlatformTeamRepository.findByCondition as Mock).mockResolvedValue(
        platformTeam,
      );
      (mockPlatformApiProvider.getEntityDetails as Mock).mockResolvedValue(
        entityDetails,
      );
      (mockSlackMessagesRepository.findOne as Mock).mockResolvedValue(
        slackMessage,
      );
      (mockTriageRuleRepo.findActiveRulesForTeam as Mock).mockResolvedValue(
        triageRules,
      );
      (mockTriageRuleEvaluator.evaluateRules as Mock).mockResolvedValue(true);
      (mockCustomerContactsRepository.findOne as Mock).mockResolvedValue({
        id: 'contact-id',
        slackId: 'U12345',
        slackProfileEmail: '<EMAIL>',
        userDump: {
          team_id: 'T12345',
        },
      });
      (mockSlackWebAPIService.getTeamInfo as Mock).mockResolvedValue({
        ok: true,
        team: {
          id: 'T12345',
          name: 'Customer Team',
          icon: {
            image_132: 'https://example.com/icon.png',
          },
        },
      });
      (
        mockCoreTriageService.sendTriageMessageToChannel as Mock
      ).mockResolvedValue({
        id: 'triage-message-id',
      });

      await service.evaluateAndSendTriageMessages(installation as any, {
        ticket,
        platformTeamId: 'team-123',
        slackMessage: slackMessage as any,
      });

      expect(mockPlatformTeamRepository.findByCondition).toHaveBeenCalledWith({
        where: {
          uid: 'team-123',
          installation: { id: 'installation-id' },
          organization: { id: 'org-id' },
        },
      });
      expect(mockPlatformApiProvider.getEntityDetails).toHaveBeenCalledWith(
        installation,
        'ticketId',
        'ticket-123',
        'Ticket',
      );
      expect(mockTriageRuleRepo.findActiveRulesForTeam).toHaveBeenCalledWith(
        'team-id',
        expect.objectContaining({
          installation,
          organization: installation.organization,
        }),
      );
      expect(mockTriageRuleEvaluator.evaluateRules).toHaveBeenCalledWith(
        triageRules[0].triageRules,
        expect.objectContaining({
          ticket: entityDetails.data,
          channel: slackMessage.channel,
          account: entityDetails.data.account,
        }),
      );
      expect(mockCustomerContactsRepository.findOne).toHaveBeenCalledWith({
        where: {
          slackProfileEmail: '<EMAIL>',
          installation: { id: 'installation-id' },
          organization: { id: 'org-id' },
        },
      });
      expect(mockSlackWebAPIService.getTeamInfo).toHaveBeenCalledWith(
        'mock-bot-token',
        {
          team: 'T12345',
        },
      );
      expect(
        mockCoreTriageService.sendTriageMessageToChannel,
      ).toHaveBeenCalledWith(installation, {
        channel: triageRules[0].triageChannels[0],
        slackMessage: slackMessage,
        ticket,
        returnExisting: true,
        slackTeamName: 'Customer Team',
        slackTeamIconUrl: 'https://example.com/icon.png',
      });
    });

    it('should handle case when platform team is not found', async () => {
      const installation = {
        id: 'installation-id',
        organization: {
          id: 'org-id',
        },
      };

      const ticket: Partial<Ticket> = {
        id: 'ticket-123',
        title: 'Test Ticket',
      };

      (mockPlatformTeamRepository.findByCondition as Mock).mockResolvedValue(
        null,
      );

      await service.evaluateAndSendTriageMessages(installation as any, {
        ticket,
        platformTeamId: 'team-123',
      });

      expect(mockPlatformTeamRepository.findByCondition).toHaveBeenCalled();
      expect(mockLogger.error).toHaveBeenCalled();
      expect(mockPlatformApiProvider.getEntityDetails).not.toHaveBeenCalled();
    });

    it('should handle case when no triage rules match', async () => {
      const installation = {
        id: 'installation-id',
        botToken: 'mock-bot-token',
        organization: {
          id: 'org-id',
        },
      };

      const ticket: Partial<Ticket> = {
        id: 'ticket-123',
        title: 'Test Ticket',
      };

      const platformTeam = {
        id: 'team-id',
        uid: 'team-123',
      };

      const entityDetails = {
        data: {
          ...ticket,
          account: {
            id: 'account-123',
            name: 'Test Account',
          },
        },
      };

      const triageRules = [
        {
          id: 'rule-1',
          isDefault: false,
          triageRules: {
            conditions: [
              {
                field: 'ticket.title',
                operator: 'contains',
                value: 'Not Matching',
              },
            ],
          },
          triageChannels: [
            {
              id: 'channel-1',
              channelId: 'C67890',
              name: 'triage-channel',
            },
          ],
        },
      ];

      (mockPlatformTeamRepository.findByCondition as Mock).mockResolvedValue(
        platformTeam,
      );
      (mockPlatformApiProvider.getEntityDetails as Mock).mockResolvedValue(
        entityDetails,
      );
      (mockTriageRuleRepo.findActiveRulesForTeam as Mock).mockResolvedValue(
        triageRules,
      );
      (mockTriageRuleEvaluator.evaluateRules as Mock).mockResolvedValue(false);

      await service.evaluateAndSendTriageMessages(installation as any, {
        ticket,
        platformTeamId: 'team-123',
      });

      expect(mockPlatformTeamRepository.findByCondition).toHaveBeenCalled();
      expect(mockPlatformApiProvider.getEntityDetails).toHaveBeenCalled();
      expect(mockTriageRuleRepo.findActiveRulesForTeam).toHaveBeenCalled();
      expect(mockTriageRuleEvaluator.evaluateRules).toHaveBeenCalled();
      expect(
        mockCoreTriageService.sendTriageMessageToChannel,
      ).not.toHaveBeenCalled();
    });

    it('should use default rule when no other rules match', async () => {
      const installation = {
        id: 'installation-id',
        botToken: 'mock-bot-token',
        organization: {
          id: 'org-id',
        },
      };

      const ticket: Partial<Ticket> = {
        id: 'ticket-123',
        title: 'Test Ticket',
        customerContactEmail: '<EMAIL>',
        customerContactFirstName: 'John',
        customerContactLastName: 'Doe',
      };

      const platformTeam = {
        id: 'team-id',
        uid: 'team-123',
      };

      const entityDetails = {
        data: {
          ...ticket,
          account: {
            id: 'account-123',
            name: 'Test Account',
          },
        },
      };

      const triageRules = [
        {
          id: 'rule-1',
          isDefault: false,
          triageRules: {
            conditions: [
              {
                field: 'ticket.title',
                operator: 'contains',
                value: 'Not Matching',
              },
            ],
          },
          triageChannels: [
            {
              id: 'channel-1',
              channelId: 'C67890',
              name: 'triage-channel-1',
            },
          ],
        },
        {
          id: 'rule-2',
          isDefault: true,
          triageChannels: [
            {
              id: 'channel-2',
              channelId: 'C54321',
              name: 'default-triage-channel',
            },
          ],
        },
      ];

      (mockPlatformTeamRepository.findByCondition as Mock).mockResolvedValue(
        platformTeam,
      );
      (mockPlatformApiProvider.getEntityDetails as Mock).mockResolvedValue(
        entityDetails,
      );
      (mockTriageRuleRepo.findActiveRulesForTeam as Mock).mockResolvedValue(
        triageRules,
      );
      (mockTriageRuleEvaluator.evaluateRules as Mock).mockResolvedValue(false);
      (mockCustomerContactsRepository.findOne as Mock).mockResolvedValue(null);
      (
        mockCoreTriageService.sendTriageMessageToChannel as Mock
      ).mockResolvedValue({
        id: 'triage-message-id',
      });

      await service.evaluateAndSendTriageMessages(installation as any, {
        ticket,
        platformTeamId: 'team-123',
      });

      expect(mockPlatformTeamRepository.findByCondition).toHaveBeenCalled();
      expect(mockPlatformApiProvider.getEntityDetails).toHaveBeenCalled();
      expect(mockTriageRuleRepo.findActiveRulesForTeam).toHaveBeenCalled();
      expect(mockTriageRuleEvaluator.evaluateRules).toHaveBeenCalled();
      expect(
        mockCoreTriageService.sendTriageMessageToChannel,
      ).toHaveBeenCalledWith(installation, {
        channel: triageRules[1].triageChannels[0],
        slackMessage: undefined,
        ticket,
        returnExisting: true,
        slackTeamName: null,
        slackTeamIconUrl: null,
      });
    });

    it('should handle errors when getting team info', async () => {
      const installation = {
        id: 'installation-id',
        botToken: 'mock-bot-token',
        organization: {
          id: 'org-id',
        },
      };

      const ticket: Partial<Ticket> = {
        id: 'ticket-123',
        title: 'Test Ticket',
        customerContactEmail: '<EMAIL>',
        customerContactFirstName: 'John',
        customerContactLastName: 'Doe',
      };

      const platformTeam = {
        id: 'team-id',
        uid: 'team-123',
      };

      const entityDetails = {
        data: {
          ...ticket,
          account: {
            id: 'account-123',
            name: 'Test Account',
          },
        },
      };

      const triageRules = [
        {
          id: 'rule-1',
          isDefault: true,
          triageChannels: [
            {
              id: 'channel-1',
              channelId: 'C67890',
              name: 'triage-channel',
            },
          ],
        },
      ];

      (mockPlatformTeamRepository.findByCondition as Mock).mockResolvedValue(
        platformTeam,
      );
      (mockPlatformApiProvider.getEntityDetails as Mock).mockResolvedValue(
        entityDetails,
      );
      (mockTriageRuleRepo.findActiveRulesForTeam as Mock).mockResolvedValue(
        triageRules,
      );
      (mockCustomerContactsRepository.findOne as Mock).mockResolvedValue({
        id: 'contact-id',
        slackId: 'U12345',
        slackProfileEmail: '<EMAIL>',
        userDump: {
          team_id: 'T12345',
        },
      });
      (mockSlackWebAPIService.getTeamInfo as Mock).mockRejectedValue(
        new Error('API error'),
      );
      (
        mockCoreTriageService.sendTriageMessageToChannel as Mock
      ).mockResolvedValue({
        id: 'triage-message-id',
      });

      await service.evaluateAndSendTriageMessages(installation as any, {
        ticket,
        platformTeamId: 'team-123',
      });

      expect(mockPlatformTeamRepository.findByCondition).toHaveBeenCalled();
      expect(mockPlatformApiProvider.getEntityDetails).toHaveBeenCalled();
      expect(mockTriageRuleRepo.findActiveRulesForTeam).toHaveBeenCalled();
      expect(mockCustomerContactsRepository.findOne).toHaveBeenCalled();
      expect(mockSlackWebAPIService.getTeamInfo).toHaveBeenCalled();
      expect(mockLogger.error).toHaveBeenCalled();
      expect(
        mockCoreTriageService.sendTriageMessageToChannel,
      ).toHaveBeenCalledWith(installation, {
        channel: triageRules[0].triageChannels[0],
        slackMessage: undefined,
        ticket,
        returnExisting: true,
        slackTeamName: null,
        slackTeamIconUrl: null,
      });
    });

    it('should handle errors when sending triage messages', async () => {
      const installation = {
        id: 'installation-id',
        botToken: 'mock-bot-token',
        organization: {
          id: 'org-id',
        },
      };

      const ticket: Partial<Ticket> = {
        id: 'ticket-123',
        title: 'Test Ticket',
        customerContactEmail: '<EMAIL>',
        customerContactFirstName: 'John',
        customerContactLastName: 'Doe',
      };

      const platformTeam = {
        id: 'team-id',
        uid: 'team-123',
      };

      const entityDetails = {
        data: {
          ...ticket,
          account: {
            id: 'account-123',
            name: 'Test Account',
          },
        },
      };

      const triageRules = [
        {
          id: 'rule-1',
          isDefault: true,
          triageChannels: [
            {
              id: 'channel-1',
              channelId: 'C67890',
              name: 'triage-channel',
            },
          ],
        },
      ];

      (mockPlatformTeamRepository.findByCondition as Mock).mockResolvedValue(
        platformTeam,
      );
      (mockPlatformApiProvider.getEntityDetails as Mock).mockResolvedValue(
        entityDetails,
      );
      (mockTriageRuleRepo.findActiveRulesForTeam as Mock).mockResolvedValue(
        triageRules,
      );
      (mockCustomerContactsRepository.findOne as Mock).mockResolvedValue(null);
      (
        mockCoreTriageService.sendTriageMessageToChannel as Mock
      ).mockRejectedValue(new Error('API error'));

      await service.evaluateAndSendTriageMessages(installation as any, {
        ticket,
        platformTeamId: 'team-123',
      });

      expect(mockPlatformTeamRepository.findByCondition).toHaveBeenCalled();
      expect(mockPlatformApiProvider.getEntityDetails).toHaveBeenCalled();
      expect(mockTriageRuleRepo.findActiveRulesForTeam).toHaveBeenCalled();
      expect(
        mockCoreTriageService.sendTriageMessageToChannel,
      ).toHaveBeenCalled();
      expect(mockLogger.error).toHaveBeenCalled();
    });
  });
});
