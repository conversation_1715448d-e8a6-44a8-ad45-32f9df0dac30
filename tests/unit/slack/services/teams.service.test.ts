import {
  BadRequestException,
  ConflictException,
  NotFoundException,
} from '@nestjs/common';
import { <PERSON><PERSON>, beforeEach, describe, expect, it, vi } from 'vitest';
import { BotCtx } from '../../../../src/auth/interfaces';
import { TransactionService } from '../../../../src/database/common';
import { ChannelType } from '../../../../src/database/entities/channels/channels.entity';
import { ChannelsRepository } from '../../../../src/database/entities/channels/repositories';
import { InstallationRepository } from '../../../../src/database/entities/installations/repositories';
import { TeamRelationshipType } from '../../../../src/database/entities/mappings';
import { TeamChannelMapsRepository } from '../../../../src/database/entities/mappings/repositories/team-channel-maps.repository';
import { SettingsRepository } from '../../../../src/database/entities/settings/repositories/settings.repository';
import { TeamsRepository } from '../../../../src/database/entities/teams';
import { ChannelsManagementService } from '../../../../src/slack/core';
import { MapTeamToChannelsDTO } from '../../../../src/slack/dtos/teams.dto';
import { PromptsService } from '../../../../src/slack/services/prompts.service';
import { SettingsService } from '../../../../src/slack/services/settings.service';
import { SlackTeamsService } from '../../../../src/slack/services/teams.service';
import { ILogger } from '../../../../src/utils/logger/logger.interface';

describe('SlackTeamsService', () => {
  let service: SlackTeamsService;
  let mockLogger: ILogger;
  let mockTransactionService: TransactionService;
  let mockChannelsManagementService: ChannelsManagementService;
  let mockSettingsService: SettingsService;
  let mockTeamsRepository: TeamsRepository;
  let mockChannelsRepository: ChannelsRepository;
  let mockInstallationsRepository: InstallationRepository;
  let mockTeamChannelMapsRepository: TeamChannelMapsRepository;
  let mockSettingsRepository: SettingsRepository;
  let mockPromptsService: PromptsService;

  beforeEach(() => {
    vi.resetAllMocks();

    mockLogger = {
      log: vi.fn(),
      error: vi.fn(),
      warn: vi.fn(),
      debug: vi.fn(),
      verbose: vi.fn(),
    } as unknown as ILogger;

    mockTransactionService = {
      runInTransaction: vi.fn(async (callback) => {
        return callback({});
      }),
    } as unknown as TransactionService;

    mockChannelsManagementService = {
      joinChannels: vi.fn(),
    } as unknown as ChannelsManagementService;

    mockSettingsService = {
      prepareTeamSettings: vi.fn(),
    } as unknown as SettingsService;

    mockTeamsRepository = {
      findAll: vi.fn(),
      findByCondition: vi.fn(),
      saveManyWithTxn: vi.fn(),
      removeWithTxn: vi.fn(),
    } as unknown as TeamsRepository;

    mockChannelsRepository = {
      findAll: vi.fn(),
      updateWithTxn: vi.fn(),
    } as unknown as ChannelsRepository;

    mockInstallationsRepository = {
      findAll: vi.fn(),
    } as unknown as InstallationRepository;

    mockTeamChannelMapsRepository = {
      findAll: vi.fn(),
      saveManyWithTxn: vi.fn(),
    } as unknown as TeamChannelMapsRepository;

    mockSettingsRepository = {
      saveManyWithTxn: vi.fn(),
    } as unknown as SettingsRepository;

    mockPromptsService = {
      seedDefaultPrompts: vi.fn(),
    } as unknown as PromptsService;

    service = new SlackTeamsService(
      mockLogger,
      mockTransactionService,
      mockChannelsManagementService,
      mockSettingsService,
      mockTeamsRepository,
      mockChannelsRepository,
      mockInstallationsRepository,
      mockTeamChannelMapsRepository,
      mockSettingsRepository,
      mockPromptsService,
    );
  });

  describe('getAllTeams', () => {
    it('should return teams with correct pagination', async () => {
      const botCtx = {
        installation: {
          id: 'installation-id',
        },
        organization: {
          id: 'org-id',
        },
      } as unknown as BotCtx;

      const query = {
        page: 1,
        limit: 10,
        forOrganization: false,
      };

      const mockTeams = [
        {
          id: 'team-id-1',
          uid: 'team-123',
          installedBy: 'U12345',
          createdAt: new Date(),
          installation: {
            id: 'installation-id',
            teamId: 'T12345',
            teamName: 'Team Name',
          },
        },
        {
          id: 'team-id-2',
          uid: 'team-456',
          installedBy: 'U67890',
          createdAt: new Date(),
          installation: {
            id: 'installation-id-2',
            teamId: 'T67890',
            teamName: 'Another Team',
          },
        },
      ];

      const mockChannelMappings = [
        {
          platformTeam: { id: 'team-id-1' },
          channel: { id: 'channel-id-1' },
        },
        {
          platformTeam: { id: 'team-id-1' },
          channel: { id: 'channel-id-2' },
        },
        {
          platformTeam: { id: 'team-id-2' },
          channel: { id: 'channel-id-3' },
        },
      ];

      (mockTeamsRepository.findAll as Mock).mockResolvedValue(mockTeams);
      (mockTeamChannelMapsRepository.findAll as Mock).mockResolvedValue(
        mockChannelMappings,
      );

      const result = await service.getAllTeams(botCtx, query);

      expect(mockTeamsRepository.findAll).toHaveBeenCalledWith({
        skip: 0,
        take: 10,
        relations: { installation: true },
        where: {
          organization: { id: 'org-id' },
          installation: { id: 'installation-id' },
        },
        select: expect.objectContaining({
          id: true,
          uid: true,
          installedBy: true,
          createdAt: true,
          installation: expect.objectContaining({
            id: true,
            teamId: true,
            teamName: true,
          }),
        }),
      });

      expect(mockTeamChannelMapsRepository.findAll).toHaveBeenCalled();

      expect(result).toEqual({
        data: [
          {
            id: 'team-id-1',
            uid: 'team-123',
            installedBy: 'U12345',
            createdAt: expect.any(Date),
            channelCount: 2,
            installations: [
              {
                id: 'installation-id',
                teamId: 'T12345',
                teamName: 'Team Name',
              },
            ],
          },
          {
            id: 'team-id-2',
            uid: 'team-456',
            installedBy: 'U67890',
            createdAt: expect.any(Date),
            channelCount: 1,
            installations: [
              {
                id: 'installation-id-2',
                teamId: 'T67890',
                teamName: 'Another Team',
              },
            ],
          },
        ],
        meta: {
          page: 1,
          limit: 10,
          total: 2,
          totalPages: 1,
        },
      });
    });

    it('should return teams for the entire organization when forOrganization is true', async () => {
      const botCtx = {
        installation: {
          id: 'installation-id',
        },
        organization: {
          id: 'org-id',
        },
      } as unknown as BotCtx;

      const query = {
        page: 1,
        limit: 10,
        forOrganization: true,
      };

      const mockTeams = [
        {
          id: 'team-id-1',
          uid: 'team-123',
          installedBy: 'U12345',
          createdAt: new Date(),
          installation: {
            id: 'installation-id',
            teamId: 'T12345',
            teamName: 'Team Name',
          },
        },
      ];

      const mockChannelMappings = [
        {
          platformTeam: { id: 'team-id-1' },
          channel: { id: 'channel-id-1' },
        },
      ];

      (mockTeamsRepository.findAll as Mock).mockResolvedValue(mockTeams);
      (mockTeamChannelMapsRepository.findAll as Mock).mockResolvedValue(
        mockChannelMappings,
      );

      const result = await service.getAllTeams(botCtx, query);

      expect(mockTeamsRepository.findAll).toHaveBeenCalledWith({
        skip: 0,
        take: 10,
        relations: { installation: true },
        where: {
          organization: { id: 'org-id' },
        },
        select: expect.anything(),
      });

      expect(result.data).toHaveLength(1);
    });

    it('should throw BadRequestException when limit exceeds 100', async () => {
      const botCtx = {
        installation: {
          id: 'installation-id',
        },
        organization: {
          id: 'org-id',
        },
      } as unknown as BotCtx;

      const query = {
        page: 1,
        limit: 101,
      };

      await expect(service.getAllTeams(botCtx, query)).rejects.toThrow(
        BadRequestException,
      );
      expect(mockTeamsRepository.findAll).not.toHaveBeenCalled();
    });

    it('should throw BadRequestException when page is less than 1', async () => {
      const botCtx = {
        installation: {
          id: 'installation-id',
        },
        organization: {
          id: 'org-id',
        },
      } as unknown as BotCtx;

      const query = {
        page: 0,
        limit: 10,
      };

      await expect(service.getAllTeams(botCtx, query)).rejects.toThrow(
        BadRequestException,
      );
      expect(mockTeamsRepository.findAll).not.toHaveBeenCalled();
    });
  });

  describe('addTeam', () => {
    it('should add a team successfully', async () => {
      const botCtx = {
        installation: {
          id: 'installation-id',
          teamId: 'T12345',
        },
        organization: {
          id: 'org-id',
        },
      } as unknown as BotCtx;

      const data = {
        teamId: 'team-123',
        installedBy: 'U12345',
        workspaces: ['T12345', 'T67890'],
      };

      const mockSlackWorkspaces = [
        { id: 'installation-id', teamId: 'T12345' },
        { id: 'installation-id-2', teamId: 'T67890' },
      ];

      const mockTeamSave = [
        {
          uid: 'team-123',
          installedBy: 'U12345',
          installation: { id: 'installation-id' },
          organization: { id: 'org-id' },
        },
        {
          uid: 'team-123',
          installedBy: 'U12345',
          installation: { id: 'installation-id-2' },
          organization: { id: 'org-id' },
        },
      ];

      const mockSettingsData = [
        { key: 'setting1', value: 'value1' },
        { key: 'setting2', value: 'value2' },
      ];

      const mockNewTeams = [
        {
          id: 'team-id-1',
          uid: 'team-123',
          installedBy: 'U12345',
        },
        {
          id: 'team-id-2',
          uid: 'team-123',
          installedBy: 'U12345',
        },
      ];

      (mockInstallationsRepository.findAll as Mock).mockResolvedValue(
        mockSlackWorkspaces,
      );
      (mockTeamsRepository.findByCondition as Mock).mockResolvedValue(null);
      (mockSettingsService.prepareTeamSettings as Mock).mockResolvedValue(
        mockSettingsData,
      );
      (mockTeamsRepository.saveManyWithTxn as Mock).mockResolvedValue(
        mockNewTeams,
      );

      const result = await service.addTeam(data, botCtx);

      expect(mockInstallationsRepository.findAll).toHaveBeenCalled();

      expect(mockTeamsRepository.findByCondition).toHaveBeenCalledWith({
        where: {
          uid: 'team-123',
          organization: { id: 'org-id' },
        },
      });

      expect(mockSettingsService.prepareTeamSettings).toHaveBeenCalledWith(
        expect.arrayContaining(mockTeamSave),
        botCtx,
      );

      expect(mockTransactionService.runInTransaction).toHaveBeenCalled();
      expect(mockTeamsRepository.saveManyWithTxn).toHaveBeenCalledWith(
        expect.anything(),
        expect.arrayContaining(mockTeamSave),
      );

      expect(mockSettingsRepository.saveManyWithTxn).toHaveBeenCalledWith(
        expect.anything(),
        mockSettingsData,
      );

      expect(mockPromptsService.seedDefaultPrompts).toHaveBeenCalledTimes(2);
      expect(mockPromptsService.seedDefaultPrompts).toHaveBeenCalledWith(
        botCtx,
        'team-id-1',
      );
      expect(mockPromptsService.seedDefaultPrompts).toHaveBeenCalledWith(
        botCtx,
        'team-id-2',
      );

      expect(result).toEqual(mockNewTeams[0]);
    });

    it('should throw BadRequestException when workspaces do not exist', async () => {
      const botCtx = {
        installation: {
          id: 'installation-id',
          teamId: 'T12345',
        },
        organization: {
          id: 'org-id',
        },
      } as unknown as BotCtx;

      const data = {
        teamId: 'team-123',
        installedBy: 'U12345',
        workspaces: ['T12345', 'T67890'],
      };

      const mockSlackWorkspaces = [{ id: 'installation-id', teamId: 'T12345' }];

      (mockInstallationsRepository.findAll as Mock).mockResolvedValue(
        mockSlackWorkspaces,
      );

      await expect(service.addTeam(data, botCtx)).rejects.toThrow(
        BadRequestException,
      );
      expect(mockTeamsRepository.findByCondition).not.toHaveBeenCalled();
      expect(mockTransactionService.runInTransaction).not.toHaveBeenCalled();
    });

    it('should throw ConflictException when team already exists', async () => {
      const botCtx = {
        installation: {
          id: 'installation-id',
          teamId: 'T12345',
        },
        organization: {
          id: 'org-id',
        },
      } as unknown as BotCtx;

      const data = {
        teamId: 'team-123',
        installedBy: 'U12345',
        workspaces: ['T12345'],
      };

      const mockSlackWorkspaces = [{ id: 'installation-id', teamId: 'T12345' }];

      const existingTeam = {
        id: 'team-id-1',
        uid: 'team-123',
      };

      (mockInstallationsRepository.findAll as Mock).mockResolvedValue(
        mockSlackWorkspaces,
      );
      (mockTeamsRepository.findByCondition as Mock).mockResolvedValue(
        existingTeam,
      );

      await expect(service.addTeam(data, botCtx)).rejects.toThrow(
        ConflictException,
      );
      expect(mockTransactionService.runInTransaction).not.toHaveBeenCalled();
    });
  });

  describe('mapTeamToChannels', () => {
    it('should map team to channels successfully for customer channels', async () => {
      const botCtx = {
        installation: {
          id: 'installation-id',
          botToken: 'mock-bot-token',
        },
        organization: {
          id: 'org-id',
        },
      } as unknown as BotCtx;

      const data: MapTeamToChannelsDTO = {
        teamId: 'team-123',
        channelIds: ['C12345', 'C67890'],
        channelType: 'customer_channel',
      };

      const mockPlatformTeam = {
        id: 'team-id-1',
        uid: 'team-123',
      };

      const mockChannels = [
        { id: 'channel-id-1', channelId: 'C12345' },
        { id: 'channel-id-2', channelId: 'C67890' },
      ];

      const mockExistingMappings = [{ channel: { id: 'channel-id-1' } }];

      (mockTeamsRepository.findByCondition as Mock).mockResolvedValue(
        mockPlatformTeam,
      );
      (mockChannelsRepository.findAll as Mock).mockResolvedValue(mockChannels);
      (mockTeamChannelMapsRepository.findAll as Mock).mockResolvedValue(
        mockExistingMappings,
      );

      await service.mapTeamToChannels(data, botCtx);

      expect(mockTeamsRepository.findByCondition).toHaveBeenCalledWith({
        where: {
          uid: 'team-123',
          installation: { id: 'installation-id' },
          organization: { id: 'org-id' },
        },
      });

      expect(mockChannelsRepository.findAll).toHaveBeenCalled();

      expect(mockChannelsManagementService.joinChannels).toHaveBeenCalledWith(
        botCtx.installation,
        mockChannels,
        { channelType: ChannelType.CUSTOMER_CHANNEL },
      );

      expect(mockTeamChannelMapsRepository.findAll).toHaveBeenCalled();
      expect(mockTeamChannelMapsRepository.saveManyWithTxn).toHaveBeenCalled();
    });

    it('should map team to channels successfully for non-customer channels', async () => {
      const botCtx = {
        installation: {
          id: 'installation-id',
          botToken: 'mock-bot-token',
        },
        organization: {
          id: 'org-id',
        },
      } as unknown as BotCtx;

      const data: MapTeamToChannelsDTO = {
        teamId: 'team-123',
        channelIds: ['C12345'],
        channelType: 'triage_channel',
      };

      const mockPlatformTeam = {
        id: 'team-id-1',
        uid: 'team-123',
      };

      const mockChannels = [{ id: 'channel-id-1', channelId: 'C12345' }];

      (mockTeamsRepository.findByCondition as Mock).mockResolvedValue(
        mockPlatformTeam,
      );
      (mockChannelsRepository.findAll as Mock).mockResolvedValue(mockChannels);

      await service.mapTeamToChannels(data, botCtx);

      expect(mockTeamsRepository.findByCondition).toHaveBeenCalled();
      expect(mockChannelsRepository.findAll).toHaveBeenCalled();
      expect(mockChannelsManagementService.joinChannels).toHaveBeenCalled();
      expect(mockTeamChannelMapsRepository.findAll).not.toHaveBeenCalled(); // Not called for non-customer channels

      expect(mockTransactionService.runInTransaction).toHaveBeenCalled();
      expect(
        mockTeamChannelMapsRepository.saveManyWithTxn,
      ).toHaveBeenCalledWith(expect.anything(), [
        {
          channel: mockChannels[0],
          platformTeam: mockPlatformTeam,
          relationshipType: TeamRelationshipType.SECONDARY, // Always SECONDARY for non-customer channels
          installation: { id: 'installation-id' },
          organization: { id: 'org-id' },
        },
      ]);

      expect(mockChannelsRepository.updateWithTxn).toHaveBeenCalled();
    });

    it('should throw BadRequestException when platform team does not exist', async () => {
      const botCtx = {
        installation: {
          id: 'installation-id',
        },
        organization: {
          id: 'org-id',
        },
      } as unknown as BotCtx;

      const data: MapTeamToChannelsDTO = {
        teamId: 'team-123',
        channelIds: ['C12345'],
        channelType: 'customer_channel',
      };

      (mockTeamsRepository.findByCondition as Mock).mockResolvedValue(null);

      await expect(service.mapTeamToChannels(data, botCtx)).rejects.toThrow(
        BadRequestException,
      );
      expect(mockChannelsRepository.findAll).not.toHaveBeenCalled();
      expect(mockTransactionService.runInTransaction).not.toHaveBeenCalled();
    });

    it('should throw BadRequestException when channels do not exist', async () => {
      const botCtx = {
        installation: {
          id: 'installation-id',
        },
        organization: {
          id: 'org-id',
        },
      } as unknown as BotCtx;

      const data: MapTeamToChannelsDTO = {
        teamId: 'team-123',
        channelIds: ['C12345', 'C67890'],
        channelType: 'customer_channel',
      };

      const mockPlatformTeam = {
        id: 'team-id-1',
        uid: 'team-123',
      };

      const mockChannels = [{ id: 'channel-id-1', channelId: 'C12345' }];

      (mockTeamsRepository.findByCondition as Mock).mockResolvedValue(
        mockPlatformTeam,
      );
      (mockChannelsRepository.findAll as Mock).mockResolvedValue(mockChannels);

      await expect(service.mapTeamToChannels(data, botCtx)).rejects.toThrow(
        BadRequestException,
      );
      expect(mockTransactionService.runInTransaction).not.toHaveBeenCalled();
    });
  });

  describe('disconnectTeam', () => {
    it('should disconnect a team successfully', async () => {
      const botCtx = {
        installation: {
          id: 'installation-id',
          teamId: 'T12345',
        },
        organization: {
          id: 'org-id',
          uid: 'org-123',
        },
      } as unknown as BotCtx;

      const teamId = 'team-123';

      const mockPlatformTeam = {
        id: 'team-id-1',
        uid: 'team-123',
      };

      (mockTeamsRepository.findByCondition as Mock).mockResolvedValue(
        mockPlatformTeam,
      );

      await service.disconnectTeam(teamId, botCtx);

      expect(mockTeamsRepository.findByCondition).toHaveBeenCalledWith({
        where: {
          uid: 'team-123',
          installation: { id: 'installation-id' },
          organization: { id: 'org-id' },
        },
      });

      expect(mockTransactionService.runInTransaction).toHaveBeenCalled();
      expect(mockTeamsRepository.removeWithTxn).toHaveBeenCalledWith(
        expect.anything(),
        mockPlatformTeam,
      );
    });

    it('should throw BadRequestException when platform team does not exist', async () => {
      const botCtx = {
        installation: {
          id: 'installation-id',
        },
        organization: {
          id: 'org-id',
        },
      } as unknown as BotCtx;

      const teamId = 'team-123';

      (mockTeamsRepository.findByCondition as Mock).mockResolvedValue(null);

      await expect(service.disconnectTeam(teamId, botCtx)).rejects.toThrow(
        BadRequestException,
      );
      expect(mockTransactionService.runInTransaction).not.toHaveBeenCalled();
    });
  });

  describe('getPlatformTeam', () => {
    it('should return the platform team', async () => {
      const botCtx = {
        installation: {
          id: 'installation-id',
        },
        organization: {
          id: 'org-id',
        },
      } as unknown as BotCtx;

      const teamId = 'team-123';

      const mockPlatformTeam = {
        id: 'team-id-1',
        uid: 'team-123',
      };

      (mockTeamsRepository.findByCondition as Mock).mockResolvedValue(
        mockPlatformTeam,
      );

      const result = await service.getPlatformTeam(teamId, botCtx);

      expect(mockTeamsRepository.findByCondition).toHaveBeenCalledWith({
        where: {
          uid: 'team-123',
          installation: { id: 'installation-id' },
          organization: { id: 'org-id' },
        },
      });

      expect(result).toEqual(mockPlatformTeam);
    });

    it('should throw NotFoundException when platform team does not exist', async () => {
      const botCtx = {
        installation: {
          id: 'installation-id',
        },
        organization: {
          id: 'org-id',
        },
      } as unknown as BotCtx;

      const teamId = 'team-123';

      (mockTeamsRepository.findByCondition as Mock).mockResolvedValue(null);

      await expect(service.getPlatformTeam(teamId, botCtx)).rejects.toThrow(
        NotFoundException,
      );
    });
  });
});
