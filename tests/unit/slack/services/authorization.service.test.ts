import { describe, it, expect, beforeEach, vi, Mock } from 'vitest';
import { AuthorizationService } from '../../../../src/slack/services/authorization.service';
import { WebClient } from '@slack/web-api';
import { ConfigService } from '../../../../src/config/config.service';
import { TransactionService } from '../../../../src/database/common';
import { InstallationRepository } from '../../../../src/database/entities/installations/repositories';
import { UsersRepository } from '../../../../src/database/entities/users/repositories/users.repository';
import { ThenaPlatformApiProvider } from '../../../../src/external/provider/thena-platform-api.provider';
import { CUSTOM_LOGGER_TOKEN } from '../../../../src/utils';

vi.mock('@slack/web-api', () => ({
  WebClient: vi.fn().mockImplementation(() => ({
    users: {
      list: vi.fn().mockResolvedValue({
        ok: true,
        members: [
          { id: 'U12345', name: 'johndoe', profile: { email: '<EMAIL>' } }
        ]
      })
    },
    oauth: {
      v2: {
        access: vi.fn()
      }
    }
  }))
}));

describe('AuthorizationService', () => {
  let service: AuthorizationService;
  let mockLogger: any;
  let mockConfigService: any;
  let mockTransactionService: any;
  let mockUserRepository: any;
  let mockInstallationRepository: any;
  let mockThenaPlatformApiProvider: any;

  beforeEach(() => {
    vi.resetAllMocks();

    mockLogger = {
      log: vi.fn(),
      error: vi.fn(),
      warn: vi.fn(),
      debug: vi.fn(),
    };

    mockConfigService = {
      get: vi.fn().mockImplementation((key) => {
        const config = {
          'SLACK_APP_BASE_URL': 'https://example.com',
          'SLACK_CLIENT_ID': 'test-client-id',
          'SLACK_CLIENT_SECRET': 'test-client-secret',
        };
        return config[key];
      }),
    };

    mockTransactionService = {
      runInTransaction: vi.fn().mockImplementation(async (callback) => {
        return callback({ manager: {} });
      }),
    };

    mockUserRepository = {
      findByCondition: vi.fn(),
      updateWithTxn: vi.fn().mockResolvedValue({ affected: 1 }),
    };

    mockInstallationRepository = {
      findByCondition: vi.fn(),
    };

    mockThenaPlatformApiProvider = {
      setSlackAuth: vi.fn().mockResolvedValue(true),
    };

    service = new AuthorizationService(
      mockLogger,
      mockTransactionService,
      mockConfigService,
      mockUserRepository,
      mockInstallationRepository,
      mockThenaPlatformApiProvider,
    );
  });

  describe('getAuthorizeUserURL', () => {
    it('should return a properly formatted authorization URL', async () => {
      const userEmail = '<EMAIL>';
      const mockInstallation = { 
        id: 'installation-id',
        teamId: 'T12345',
        botToken: 'xoxb-123456'
      };
      const botCtx = { installation: mockInstallation } as any;
      
      const result = await service.getAuthorizeUserURL(userEmail, botCtx);
      
      expect(result).toHaveProperty('url');
      expect(result.url).toContain('https://slack.com/oauth/v2/authorize');
      expect(result.url).toContain('client_id=test-client-id');
      expect(result.url).toContain('team_id=T12345');
      expect(result.url).toContain('user_email=test%40example.com');
      expect(result.url).toContain('redirect_uri=https%3A%2F%2Fexample.com%2Fv1%2Fslack%2Fauthorization%2Fuser%2Fcallback');
      expect(result.url).toContain('state=');
      
      const stateParam = result.url.match(/state=([^&]*)/)![1];
      const decodedState = JSON.parse(decodeURIComponent(stateParam));
      expect(decodedState).toEqual({
        userEmail: '<EMAIL>',
        teamId: 'T12345'
      });
    });

    it('should include all required user scopes in the URL', async () => {
      const userEmail = '<EMAIL>';
      const mockInstallation = { 
        id: 'installation-id',
        teamId: 'T12345',
        botToken: 'xoxb-123456'
      };
      const botCtx = { installation: mockInstallation } as any;
      
      const result = await service.getAuthorizeUserURL(userEmail, botCtx);
      
      expect(result.url).toContain('user_scope=');
      
      const userScopeParam = result.url.match(/user_scope=([^&]*)/)![1];
      expect(userScopeParam).toBeTruthy();
    });
  });
});
