import { Test, TestingModule } from '@nestjs/testing';
import { getRepositoryToken } from '@nestjs/typeorm';
import { Repository } from 'typeorm';
import { beforeEach, describe, expect, it, vi } from 'vitest';
import { Installations, Organizations, Users } from '../../../../src/database/entities';
import { UserPlatformLookupService } from '../../../../src/slack/services/user-platform-lookup.service';
import { CUSTOM_LOGGER_TOKEN, ILogger } from '../../../../src/utils/logger';

describe('UserPlatformLookupService', () => {
  let service: UserPlatformLookupService;
  let mockLogger: ILogger;
  let mockUsersRepository: Repository<Users>;
  let mockInstallation: Installations;

  beforeEach(async () => {
    // Mock logger
    mockLogger = {
      log: vi.fn(),
      error: vi.fn(),
      warn: vi.fn(),
      debug: vi.fn(),
      verbose: vi.fn(),
    } as unknown as ILogger;

    // Mock users repository
    mockUsersRepository = {
      findOne: vi.fn(),
    } as unknown as Repository<Users>;

    // Mock installation
    mockInstallation = {
      id: 'installation-1',
      teamId: 'team-1',
      organization: {
        id: 'org-1',
      } as Organizations,
    } as Installations;

    const module: TestingModule = await Test.createTestingModule({
      providers: [
        UserPlatformLookupService,
        {
          provide: CUSTOM_LOGGER_TOKEN,
          useValue: mockLogger,
        },
        {
          provide: getRepositoryToken(Users),
          useValue: mockUsersRepository,
        },
      ],
    }).compile();

    service = module.get<UserPlatformLookupService>(UserPlatformLookupService);
  });

  describe('getPlatformUserId', () => {
    it('should return platform user ID when user exists and has valid metadata', async () => {
      // Setup
      const slackUserId = 'U12345';
      const platformUserId = 'platform-user-123';
      const mockUser = {
        metadata: {
          platformUserId,
          lastLinkedAt: '2023-01-01T00:00:00Z',
          linkingStatus: 'success' as const,
        },
      } as Users;

      vi.mocked(mockUsersRepository.findOne).mockResolvedValue(mockUser);

      // Execute
      const result = await service.getPlatformUserId(slackUserId, mockInstallation);

      // Verify
      expect(result).toBe(platformUserId);
      expect(mockUsersRepository.findOne).toHaveBeenCalledWith({
        where: {
          slackId: slackUserId,
          installation: { id: mockInstallation.id },
        },
        select: ['metadata'],
      });
    });

    it('should return null when user does not exist', async () => {
      // Setup
      const slackUserId = 'U12345';
      vi.mocked(mockUsersRepository.findOne).mockResolvedValue(null);

      // Execute
      const result = await service.getPlatformUserId(slackUserId, mockInstallation);

      // Verify
      expect(result).toBeNull();
    });

    it('should return null when user exists but has no platform user ID', async () => {
      // Setup
      const slackUserId = 'U12345';
      const mockUser = {
        metadata: null,
      } as Users;

      vi.mocked(mockUsersRepository.findOne).mockResolvedValue(mockUser);

      // Execute
      const result = await service.getPlatformUserId(slackUserId, mockInstallation);

      // Verify
      expect(result).toBeNull();
    });

    it('should handle database errors gracefully', async () => {
      // Setup
      const slackUserId = 'U12345';
      const error = new Error('Database connection failed');
      vi.mocked(mockUsersRepository.findOne).mockRejectedValue(error);

      // Execute
      const result = await service.getPlatformUserId(slackUserId, mockInstallation);

      // Verify
      expect(result).toBeNull();
      expect(mockLogger.error).toHaveBeenCalledWith(
        expect.stringContaining('Failed to lookup platform user info')
      );
    });
  });

  describe('hasValidPlatformUserId', () => {
    it('should return true when user has valid platform user ID', async () => {
      // Setup
      const slackUserId = 'U12345';
      const mockUser = {
        metadata: {
          platformUserId: 'platform-user-123',
          linkingStatus: 'success' as const,
        },
      } as Users;

      vi.mocked(mockUsersRepository.findOne).mockResolvedValue(mockUser);

      // Execute
      const result = await service.hasValidPlatformUserId(slackUserId, mockInstallation);

      // Verify
      expect(result).toBe(true);
    });

    it('should return false when user has failed linking status', async () => {
      // Setup
      const slackUserId = 'U12345';
      const mockUser = {
        metadata: {
          platformUserId: 'platform-user-123',
          linkingStatus: 'failed' as const,
        },
      } as Users;

      vi.mocked(mockUsersRepository.findOne).mockResolvedValue(mockUser);

      // Execute
      const result = await service.hasValidPlatformUserId(slackUserId, mockInstallation);

      // Verify
      expect(result).toBe(false);
    });
  });

  describe('caching', () => {
    it('should cache results and return cached value on subsequent calls', async () => {
      // Setup
      const slackUserId = 'U12345';
      const platformUserId = 'platform-user-123';
      const mockUser = {
        metadata: {
          platformUserId,
          linkingStatus: 'success' as const,
        },
      } as Users;

      vi.mocked(mockUsersRepository.findOne).mockResolvedValue(mockUser);

      // Execute - first call
      const result1 = await service.getPlatformUserId(slackUserId, mockInstallation);

      // Execute - second call
      const result2 = await service.getPlatformUserId(slackUserId, mockInstallation);

      // Verify
      expect(result1).toBe(platformUserId);
      expect(result2).toBe(platformUserId);
      expect(mockUsersRepository.findOne).toHaveBeenCalledTimes(1); // Should only be called once due to caching
    });

    it('should clear cache and fetch fresh data when refreshUserMapping is called', async () => {
      // Setup
      const slackUserId = 'U12345';
      const platformUserId = 'platform-user-123';
      const mockUser = {
        metadata: {
          platformUserId,
          linkingStatus: 'success' as const,
        },
      } as Users;

      vi.mocked(mockUsersRepository.findOne).mockResolvedValue(mockUser);

      // Execute - first call
      await service.getPlatformUserId(slackUserId, mockInstallation);

      // Refresh mapping
      await service.refreshUserMapping(slackUserId, mockInstallation);

      // Execute - second call after refresh
      await service.getPlatformUserId(slackUserId, mockInstallation);

      // Verify
      expect(mockUsersRepository.findOne).toHaveBeenCalledTimes(2); // Should be called twice due to refresh
    });
  });
});
