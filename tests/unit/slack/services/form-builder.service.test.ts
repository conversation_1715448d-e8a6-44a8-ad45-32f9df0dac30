import { Mock, beforeEach, describe, expect, it, vi } from 'vitest';
import { Installations } from '../../../../src/database/entities';
import { ThenaPlatformApiProvider } from '../../../../src/external/provider/thena-platform-api.provider';
import { FormBuilderService } from '../../../../src/slack/services/form-builder.service';
import { ILogger } from '../../../../src/utils';

describe('FormBuilderService', () => {
  let service: FormBuilderService;
  let mockLogger: ILogger;
  let mockPlatformApiProvider: ThenaPlatformApiProvider;
  let mockInstallation: Installations;

  beforeEach(() => {
    vi.resetAllMocks();

    mockLogger = {
      log: vi.fn(),
      error: vi.fn(),
      warn: vi.fn(),
      debug: vi.fn(),
      verbose: vi.fn(),
    } as unknown as <PERSON>ogger;

    mockPlatformApiProvider = {
      fetchFormsForTeam: vi.fn(),
      proxy: vi.fn(),
    } as unknown as ThenaPlatformApiProvider;

    mockInstallation = {
      id: 'installation-id',
      teamId: 'T12345',
      botToken: 'xoxb-token',
      organization: {
        id: 'org-id',
      },
    } as unknown as Installations;

    service = new FormBuilderService(mockLogger, mockPlatformApiProvider);
  });

  describe('getForms', () => {
    it('should return forms for a team', async () => {
      const teamId = 'team-123';
      const mockFormsResponse = {
        results: [
          {
            id: 'form-1',
            name: 'Form 1',
            description: 'Description 1',
            teamId: teamId,
          },
          {
            id: 'form-2',
            name: 'Form 2',
            description: 'Description 2',
            teamId: teamId,
          },
        ],
      };

      (mockPlatformApiProvider.fetchFormsForTeam as Mock).mockResolvedValue(
        mockFormsResponse,
      );

      const result = await service.getForms(mockInstallation, teamId);

      expect(result).toHaveLength(2);
      expect(result[0].id).toBe('form-1');
      expect(result[0].name).toBe('Form 1');
      expect(result[0].description).toBe('Description 1');
      expect(result[0].teamId).toBe(teamId);
      expect(mockPlatformApiProvider.fetchFormsForTeam).toHaveBeenCalledWith(
        mockInstallation,
        teamId,
      );
    });

    it('should return empty array when no forms are found', async () => {
      const teamId = 'team-123';
      const mockFormsResponse = {
        results: [],
      };

      (mockPlatformApiProvider.fetchFormsForTeam as Mock).mockResolvedValue(
        mockFormsResponse,
      );

      const result = await service.getForms(mockInstallation, teamId);

      expect(result).toEqual([]);
      expect(mockPlatformApiProvider.fetchFormsForTeam).toHaveBeenCalledWith(
        mockInstallation,
        teamId,
      );
    });

    it('should return empty array when API response is null', async () => {
      const teamId = 'team-123';
      (mockPlatformApiProvider.fetchFormsForTeam as Mock).mockResolvedValue(
        null,
      );

      const result = await service.getForms(mockInstallation, teamId);

      expect(result).toEqual([]);
      expect(mockPlatformApiProvider.fetchFormsForTeam).toHaveBeenCalledWith(
        mockInstallation,
        teamId,
      );
    });

    it('should handle errors and return empty array', async () => {
      const teamId = 'team-123';
      const mockError = new Error('API error');
      (mockPlatformApiProvider.fetchFormsForTeam as Mock).mockRejectedValue(
        mockError,
      );

      const result = await service.getForms(mockInstallation, teamId);

      expect(result).toEqual([]);
      expect(mockLogger.error).toHaveBeenCalled();
      expect(mockPlatformApiProvider.fetchFormsForTeam).toHaveBeenCalledWith(
        mockInstallation,
        teamId,
      );
    });
  });

  describe('processForm', () => {
    it('should process a form correctly', async () => {
      const mockForm = {
        fields: [
          {
            field: 'field1',
            fieldType: 'text',
            defaultValue: '',
            mandatoryOnClose: false,
            visibleToCustomer: true,
            editableByCustomer: true,
            mandatoryOnCreation: true,
            meta: {
              name: 'Text Field',
              type: 'text',
              accessibleInTicketCreationForm: true,
            },
          },
          {
            field: 'field2',
            fieldType: 'single_choice',
            defaultValue: '',
            mandatoryOnClose: false,
            visibleToCustomer: true,
            editableByCustomer: true,
            mandatoryOnCreation: false,
            meta: {
              name: 'Select Field',
              type: 'single_choice',
              options: [
                { id: 'opt1', value: 'Option 1' },
                { id: 'opt2', value: 'Option 2' },
              ],
              accessibleInTicketCreationForm: true,
            },
          },
        ],
        conditions: [
          {
            targetFields: [
              {
                id: 'field2',
                type: 'mark_mandatory',
                value: true,
              },
            ],
            conditionType: 'equals',
            triggerFieldId: 'field1',
            triggerFieldValue: 'trigger-value',
          },
        ],
      };

      const result = await service.processForm(mockForm);

      expect(result).not.toBeNull();
      expect(result?.fields).toHaveLength(2);
      expect(result?.fields[0].id).toBe('field1');
      expect(result?.fields[0].type).toBe('text');
      expect(result?.fields[1].id).toBe('field2');
      expect(result?.fields[1].type).toBe('choice');
      expect(result?.fields[1].options).toHaveLength(2);

      expect(result?.conditions.size).toBe(1);
      expect(result?.conditionOrder).toHaveLength(1);

      const condition = result?.conditions.get('condition-1');
      expect(condition?.triggerFieldId).toBe('field1');
      expect(condition?.value).toBe('trigger-value');
      expect(condition?.targetFields).toHaveLength(1);
      expect(condition?.targetFields[0].fieldId).toBe('field2');
    });

    it('should return null when form is null', async () => {
      const result = await service.processForm(null);

      expect(result).toBeNull();
    });

    it('should handle errors and return null', async () => {
      const mockForm = {
        fields: 'invalid-fields', // This will cause an error
        conditions: [],
      };

      const result = await service.processForm(mockForm as any);

      expect(result).toBeNull();
      expect(mockLogger.error).toHaveBeenCalled();
    });
  });

  describe('getFormById', () => {
    it('should return a form by ID', async () => {
      const formId = 'form-1';
      const teamId = 'team-123';
      const mockFormsResponse = {
        results: [
          {
            id: 'form-1',
            name: 'Form 1',
            description: 'Description 1',
            teamId: teamId,
            fields: [
              {
                field: 'field1',
                fieldType: 'text',
                defaultValue: '',
                mandatoryOnClose: false,
                visibleToCustomer: true,
                editableByCustomer: true,
                mandatoryOnCreation: true,
                meta: {
                  name: 'Text Field',
                  type: 'text',
                  accessibleInTicketCreationForm: true,
                },
              },
            ],
            conditions: [],
          },
          {
            id: 'form-2',
            name: 'Form 2',
            description: 'Description 2',
            teamId: teamId,
          },
        ],
      };

      (mockPlatformApiProvider.fetchFormsForTeam as Mock).mockResolvedValue(
        mockFormsResponse,
      );

      const result = await service.getFormById(
        mockInstallation,
        formId,
        teamId,
      );

      expect(result).not.toBeNull();
      expect(result?.name).toBe('Form 1');
      expect(result?.fields).toHaveLength(1);
      expect(result?.fields[0].id).toBe('field1');
      expect(result?.conditions).toEqual([]);
      expect(mockPlatformApiProvider.fetchFormsForTeam).toHaveBeenCalledWith(
        mockInstallation,
        teamId,
      );
    });

    it('should return null when form is not found', async () => {
      const formId = 'non-existent-form';
      const teamId = 'team-123';
      const mockFormsResponse = {
        results: [
          {
            id: 'form-1',
            name: 'Form 1',
            description: 'Description 1',
            teamId: teamId,
          },
        ],
      };

      (mockPlatformApiProvider.fetchFormsForTeam as Mock).mockResolvedValue(
        mockFormsResponse,
      );

      const result = await service.getFormById(
        mockInstallation,
        formId,
        teamId,
      );

      expect(result).toBeNull();
      expect(mockPlatformApiProvider.fetchFormsForTeam).toHaveBeenCalledWith(
        mockInstallation,
        teamId,
      );
    });

    it('should return null when API response is null', async () => {
      const formId = 'form-1';
      const teamId = 'team-123';
      (mockPlatformApiProvider.fetchFormsForTeam as Mock).mockResolvedValue(
        null,
      );

      const result = await service.getFormById(
        mockInstallation,
        formId,
        teamId,
      );

      expect(result).toBeNull();
      expect(mockPlatformApiProvider.fetchFormsForTeam).toHaveBeenCalledWith(
        mockInstallation,
        teamId,
      );
    });

    it('should handle errors and return null', async () => {
      const formId = 'form-1';
      const teamId = 'team-123';
      const mockError = new Error('API error');
      (mockPlatformApiProvider.fetchFormsForTeam as Mock).mockRejectedValue(
        mockError,
      );

      const result = await service.getFormById(
        mockInstallation,
        formId,
        teamId,
      );

      expect(result).toBeNull();
      expect(mockLogger.error).toHaveBeenCalled();
      expect(mockPlatformApiProvider.fetchFormsForTeam).toHaveBeenCalledWith(
        mockInstallation,
        teamId,
      );
    });
  });

  describe('getFieldOptions', () => {
    it('should fetch options from an external API', async () => {
      const apiEndpoint = '/api/options';
      const teamId = 'team-123';
      const searchValue = 'search-term';

      const mockResponse = {
        ok: true,
        json: vi.fn().mockResolvedValue({
          data: [
            { label: 'Option 1', value: 'opt1' },
            { label: 'Option 2', value: 'opt2' },
          ],
        }),
      };

      (mockPlatformApiProvider.proxy as Mock).mockResolvedValue(mockResponse);

      const result = await service.getFieldOptions(
        mockInstallation,
        apiEndpoint,
        teamId,
        searchValue,
      );

      expect(result).toHaveLength(2);
      expect(result[0].label).toBe('Option 1');
      expect(result[0].value).toBe('opt1');
      expect(mockPlatformApiProvider.proxy).toHaveBeenCalledWith(
        mockInstallation.organization,
        'GET',
        `${apiEndpoint}?search=${encodeURIComponent(searchValue)}`,
      );
    });

    it('should handle API errors and return empty array', async () => {
      const apiEndpoint = '/api/options';
      const teamId = 'team-123';

      const mockResponse = {
        ok: false,
      };

      (mockPlatformApiProvider.proxy as Mock).mockResolvedValue(mockResponse);

      const result = await service.getFieldOptions(
        mockInstallation,
        apiEndpoint,
        teamId,
      );

      expect(result).toEqual([]);
      expect(mockLogger.error).toHaveBeenCalled();
    });

    it('should handle exceptions and return empty array', async () => {
      const apiEndpoint = '/api/options';
      const teamId = 'team-123';
      const mockError = new Error('Network error');

      (mockPlatformApiProvider.proxy as Mock).mockRejectedValue(mockError);

      const result = await service.getFieldOptions(
        mockInstallation,
        apiEndpoint,
        teamId,
      );

      expect(result).toEqual([]);
      expect(mockLogger.error).toHaveBeenCalled();
    });
  });

  describe('mapApiFieldsToFields', () => {
    it('should map API fields to internal fields format', async () => {
      const mockForm = {
        fields: [
          {
            field: 'text_field',
            fieldType: 'text',
            defaultValue: '',
            mandatoryOnClose: false,
            visibleToCustomer: true,
            editableByCustomer: true,
            mandatoryOnCreation: true,
            meta: {
              name: 'Text Field',
              type: 'text',
              accessibleInTicketCreationForm: true,
            },
          },
          {
            field: 'email_field',
            fieldType: 'text',
            defaultValue: '',
            mandatoryOnClose: false,
            visibleToCustomer: true,
            editableByCustomer: true,
            mandatoryOnCreation: true,
            meta: {
              name: 'Email Field',
              type: 'email',
              accessibleInTicketCreationForm: true,
            },
          },
          {
            field: 'select_field',
            fieldType: 'single_choice',
            defaultValue: '',
            mandatoryOnClose: false,
            visibleToCustomer: true,
            editableByCustomer: true,
            mandatoryOnCreation: false,
            meta: {
              name: 'Select Field',
              type: 'single_choice',
              options: [
                { id: 'opt1', value: 'Option 1' },
                { id: 'opt2', value: 'Option 2' },
              ],
              accessibleInTicketCreationForm: true,
            },
          },
          {
            field: 'multi_field',
            fieldType: 'multi_choice',
            defaultValue: [],
            mandatoryOnClose: false,
            visibleToCustomer: true,
            editableByCustomer: true,
            mandatoryOnCreation: false,
            meta: {
              name: 'Multi Field',
              type: 'multi_choice',
              options: [
                { id: 'opt1', value: 'Option 1' },
                { id: 'opt2', value: 'Option 2' },
              ],
              accessibleInTicketCreationForm: true,
            },
          },
          {
            field: 'boolean_field',
            fieldType: 'boolean',
            defaultValue: false,
            mandatoryOnClose: false,
            visibleToCustomer: true,
            editableByCustomer: true,
            mandatoryOnCreation: false,
            meta: {
              name: 'Boolean Field',
              type: 'boolean',
              accessibleInTicketCreationForm: true,
            },
          },
          {
            field: 'currency_field',
            fieldType: 'currency',
            defaultValue: 0,
            mandatoryOnClose: false,
            visibleToCustomer: true,
            editableByCustomer: true,
            mandatoryOnCreation: false,
            meta: {
              name: 'Currency Field',
              type: 'currency',
              currencyCode: 'USD',
              accessibleInTicketCreationForm: true,
            },
          },
        ],
        conditions: [],
      };

      const result = await service.processForm(mockForm);

      expect(result).not.toBeNull();
      expect(result?.fields).toHaveLength(6);

      const textField = result?.fields.find((f) => f.id === 'text_field');
      expect(textField?.type).toBe('text');
      expect(textField?.mandatoryOnCreation).toBe(true);

      const emailField = result?.fields.find((f) => f.id === 'email_field');
      expect(emailField?.type).toBe('specialized');
      expect(emailField?.metadata?.originalType).toBe('email');

      const selectField = result?.fields.find((f) => f.id === 'select_field');
      expect(selectField?.type).toBe('choice');
      expect(selectField?.options).toHaveLength(2);
      expect(selectField?.options?.[0].label).toBe('Option 1');
      expect(selectField?.options?.[0].value).toBe('opt1');

      const multiField = result?.fields.find((f) => f.id === 'multi_field');
      expect(multiField?.type).toBe('multiselect');

      const booleanField = result?.fields.find((f) => f.id === 'boolean_field');
      expect(booleanField?.type).toBe('checkbox');

      const currencyField = result?.fields.find(
        (f) => f.id === 'currency_field',
      );
      expect(currencyField?.type).toBe('specialized');
      expect(currencyField?.metadata?.originalType).toBe('currency');
      expect(currencyField?.metadata?.currencyCode).toBe('USD');
    });
  });

  describe('mapApiConditionsToConditions', () => {
    it('should map API conditions to internal conditions format', async () => {
      const mockForm = {
        fields: [],
        conditions: [
          {
            targetFields: [
              {
                id: 'field2',
                type: 'mark_mandatory',
                value: true,
              },
            ],
            conditionType: 'equals',
            triggerFieldId: 'field1',
            triggerFieldValue: 'trigger-value',
          },
          {
            targetFields: [
              {
                id: 'field3',
                type: 'remove_field',
                value: null,
              },
            ],
            conditionType: 'not_equals',
            triggerFieldId: 'field1',
            triggerFieldValue: 'other-value',
          },
        ],
      };

      const result = await service.processForm(mockForm);

      expect(result).not.toBeNull();
      expect(result?.conditions.size).toBe(2);
      expect(result?.conditionOrder).toHaveLength(2);

      const condition1 = result?.conditions.get('condition-1');
      expect(condition1?.triggerFieldId).toBe('field1');
      expect(condition1?.value).toBe('trigger-value');
      expect(condition1?.targetFields).toHaveLength(1);
      expect(condition1?.targetFields[0].fieldId).toBe('field2');
      expect(condition1?.targetFields[0].type).toBe('mark_mandatory');

      const condition2 = result?.conditions.get('condition-2');
      expect(condition2?.triggerFieldId).toBe('field1');
      expect(condition2?.value).toBe('other-value');
      expect(condition2?.targetFields).toHaveLength(1);
      expect(condition2?.targetFields[0].fieldId).toBe('field3');
      expect(condition2?.targetFields[0].type).toBe('remove_field');
    });
  });
});
