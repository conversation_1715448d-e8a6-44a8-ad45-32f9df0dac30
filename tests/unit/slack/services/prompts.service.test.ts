import { Test, TestingModule } from '@nestjs/testing';
import { getRepositoryToken } from '@nestjs/typeorm';
import { beforeEach, describe, expect, it, vi } from 'vitest';
import { DEFAULT_PROMPTS } from '../../../../src/ai/constants/default-prompts';
import { PromptType } from '../../../../src/ai/constants/prompt-types.enum';
import { BotCtx } from '../../../../src/auth/interfaces';
import { Prompts } from '../../../../src/database/entities/prompts/prompts.entity';
import { PlatformTeams } from '../../../../src/database/entities/teams';
import { PromptsService } from '../../../../src/slack/services/prompts.service';
import { CUSTOM_LOGGER_TOKEN } from '../../../../src/utils';

describe('PromptsService', () => {
  let service: PromptsService;
  let mockPromptsRepository: any;
  let mockTeamsRepository: any;
  let mockLogger: any;
  let mockBotCtx: BotCtx;

  beforeEach(async () => {
    mockPromptsRepository = {
      find: vi.fn(),
      findOne: vi.fn(),
      create: vi.fn(),
      save: vi.fn(),
      update: vi.fn(),
      softDelete: vi.fn(),
    };

    mockTeamsRepository = {
      find: vi.fn(),
      findOne: vi.fn(),
    };

    mockLogger = {
      log: vi.fn(),
      error: vi.fn(),
      debug: vi.fn(),
      warn: vi.fn(),
    };

    mockBotCtx = {
      installation: { id: 'test-installation-id' },
      organization: { id: 'test-org-id' },
    } as any;

    const module: TestingModule = await Test.createTestingModule({
      providers: [
        PromptsService,
        {
          provide: getRepositoryToken(Prompts),
          useValue: mockPromptsRepository,
        },
        {
          provide: getRepositoryToken(PlatformTeams),
          useValue: mockTeamsRepository,
        },
        {
          provide: CUSTOM_LOGGER_TOKEN,
          useValue: mockLogger,
        },
      ],
    }).compile();

    service = module.get<PromptsService>(PromptsService);
  });

  describe('getPrompts', () => {
    it('should return all prompts for an organization and installation', async () => {
      const mockPrompts = [
        { id: 'prompt1', name: 'Prompt 1' },
        { id: 'prompt2', name: 'Prompt 2' },
      ];
      
      mockPromptsRepository.find.mockResolvedValue(mockPrompts);
      
      const result = await service.getPrompts(mockBotCtx);
      
      expect(result).toEqual(mockPrompts);
      expect(mockPromptsRepository.find).toHaveBeenCalledWith({
        where: {
          installation: { id: 'test-installation-id' },
          organization: { id: 'test-org-id' },
        },
        relations: ['platformTeam'],
      });
    });

    it('should handle errors and rethrow them', async () => {
      const error = new Error('Test error');
      mockPromptsRepository.find.mockRejectedValue(error);
      
      await expect(service.getPrompts(mockBotCtx)).rejects.toThrow(error);
      expect(mockLogger.error).toHaveBeenCalledWith(`Error fetching prompts: ${error.message}`);
    });
  });

  describe('getPromptsByTeamId', () => {
    it('should return prompts for a specific team', async () => {
      const teamId = 'team1-uid';
      const mockPrompts = [
        { id: 'prompt1', name: 'Prompt 1', platformTeam: { uid: teamId } },
      ];
      
      mockPromptsRepository.find.mockResolvedValue(mockPrompts);
      
      const result = await service.getPromptsByTeamId(mockBotCtx, teamId);
      
      expect(result).toEqual(mockPrompts);
      expect(mockPromptsRepository.find).toHaveBeenCalledWith({
        where: {
          installation: { id: 'test-installation-id' },
          organization: { id: 'test-org-id' },
          platformTeam: { uid: teamId },
        },
        relations: ['platformTeam'],
      });
    });

    it('should handle errors and rethrow them', async () => {
      const teamId = 'team1-uid';
      const error = new Error('Test error');
      mockPromptsRepository.find.mockRejectedValue(error);
      
      await expect(service.getPromptsByTeamId(mockBotCtx, teamId)).rejects.toThrow(error);
      expect(mockLogger.error).toHaveBeenCalledWith(`Error fetching prompts by team ID: ${error.message}`);
    });
  });

  describe('getPromptById', () => {
    it('should return a prompt by ID', async () => {
      const promptId = 'prompt1';
      const mockPrompt = { id: promptId, name: 'Prompt 1' };
      
      mockPromptsRepository.findOne.mockResolvedValue(mockPrompt);
      
      const result = await service.getPromptById(mockBotCtx, promptId);
      
      expect(result).toEqual(mockPrompt);
      expect(mockPromptsRepository.findOne).toHaveBeenCalledWith({
        where: {
          id: promptId,
          installation: { id: 'test-installation-id' },
          organization: { id: 'test-org-id' },
        },
        relations: ['platformTeam'],
      });
    });

    it('should throw NotFoundException when prompt is not found', async () => {
      const promptId = 'prompt1';
      mockPromptsRepository.findOne.mockResolvedValue(null);
      
      await expect(service.getPromptById(mockBotCtx, promptId)).rejects.toThrow(`Prompt with ID ${promptId} not found`);
    });

    it('should handle errors and rethrow them', async () => {
      const promptId = 'prompt1';
      const error = new Error('Test error');
      mockPromptsRepository.findOne.mockRejectedValue(error);
      
      await expect(service.getPromptById(mockBotCtx, promptId)).rejects.toThrow(error);
      expect(mockLogger.error).toHaveBeenCalledWith(`Error fetching prompt by ID: ${error.message}`);
    });
  });

  describe('createPrompt', () => {
    it('should create a new prompt', async () => {
      const promptData = { name: 'New Prompt', prompts: { test: 'test prompt' } };
      const mockPrompt = { id: 'new-prompt', ...promptData };
      
      mockPromptsRepository.create.mockReturnValue(mockPrompt);
      mockPromptsRepository.save.mockResolvedValue(mockPrompt);
      
      const result = await service.createPrompt(mockBotCtx, promptData as any);
      
      expect(result).toEqual(mockPrompt);
      expect(mockPromptsRepository.create).toHaveBeenCalledWith({
        ...promptData,
        installation: mockBotCtx.installation,
        organization: mockBotCtx.organization,
      });
      expect(mockPromptsRepository.save).toHaveBeenCalledWith(mockPrompt);
    });

    it('should handle errors and rethrow them', async () => {
      const promptData = { name: 'New Prompt', prompts: { test: 'test prompt' } };
      const error = new Error('Test error');
      mockPromptsRepository.save.mockRejectedValue(error);
      
      await expect(service.createPrompt(mockBotCtx, promptData as any)).rejects.toThrow(error);
      expect(mockLogger.error).toHaveBeenCalledWith(`Error creating prompt: ${error.message}`);
    });
  });

  describe('updatePrompt', () => {
    it('should update an existing prompt', async () => {
      const promptId = 'prompt1';
      const promptData = { name: 'Updated Prompt' };
      const existingPrompt = { id: promptId, name: 'Old Prompt' };
      const updatedPrompt = { id: promptId, name: 'Updated Prompt' };
      
      mockPromptsRepository.findOne.mockResolvedValueOnce(existingPrompt);
      mockPromptsRepository.update.mockResolvedValue({ affected: 1 });
      
      vi.spyOn(service, 'getPromptById').mockResolvedValue(updatedPrompt as any);
      
      const result = await service.updatePrompt(mockBotCtx, promptId, promptData as any);
      
      expect(result).toEqual(updatedPrompt);
      expect(mockPromptsRepository.findOne).toHaveBeenCalledWith({
        where: {
          id: promptId,
          installation: { id: 'test-installation-id' },
          organization: { id: 'test-org-id' },
        },
      });
      expect(mockPromptsRepository.update).toHaveBeenCalledWith(
        { id: promptId },
        { ...promptData }
      );
      expect(service.getPromptById).toHaveBeenCalledWith(mockBotCtx, promptId);
    });

    it('should throw NotFoundException when prompt is not found', async () => {
      const promptId = 'prompt1';
      const promptData = { name: 'Updated Prompt' };
      
      mockPromptsRepository.findOne.mockResolvedValue(null);
      
      await expect(service.updatePrompt(mockBotCtx, promptId, promptData as any)).rejects.toThrow(`Prompt with ID ${promptId} not found`);
    });

    it('should handle errors and rethrow them', async () => {
      const promptId = 'prompt1';
      const promptData = { name: 'Updated Prompt' };
      const error = new Error('Test error');
      
      mockPromptsRepository.findOne.mockRejectedValue(error);
      
      await expect(service.updatePrompt(mockBotCtx, promptId, promptData as any)).rejects.toThrow(error);
      expect(mockLogger.error).toHaveBeenCalledWith(`Error updating prompt: ${error.message}`);
    });
  });

  describe('deletePrompt', () => {
    it('should delete a prompt', async () => {
      const promptId = 'prompt1';
      const existingPrompt = { id: promptId, name: 'Prompt to Delete' };
      
      mockPromptsRepository.findOne.mockResolvedValue(existingPrompt);
      mockPromptsRepository.softDelete.mockResolvedValue({ affected: 1 });
      
      const result = await service.deletePrompt(mockBotCtx, promptId);
      
      expect(result).toEqual({ success: true, message: 'Prompt deleted successfully' });
      expect(mockPromptsRepository.findOne).toHaveBeenCalledWith({
        where: {
          id: promptId,
          installation: { id: 'test-installation-id' },
          organization: { id: 'test-org-id' },
        },
      });
      expect(mockPromptsRepository.softDelete).toHaveBeenCalledWith(promptId);
    });

    it('should throw NotFoundException when prompt is not found', async () => {
      const promptId = 'prompt1';
      
      mockPromptsRepository.findOne.mockResolvedValue(null);
      
      await expect(service.deletePrompt(mockBotCtx, promptId)).rejects.toThrow(`Prompt with ID ${promptId} not found`);
    });

    it('should handle errors and rethrow them', async () => {
      const promptId = 'prompt1';
      const error = new Error('Test error');
      
      mockPromptsRepository.findOne.mockRejectedValue(error);
      
      await expect(service.deletePrompt(mockBotCtx, promptId)).rejects.toThrow(error);
      expect(mockLogger.error).toHaveBeenCalledWith(`Error deleting prompt: ${error.message}`);
    });
  });

  describe('setDefaultPrompt', () => {
    it('should set a prompt as default for a platform team', async () => {
      const promptId = 'prompt1';
      const platformTeamId = 'team1';
      const prompt = { 
        id: promptId, 
        name: 'Prompt 1',
        platformTeam: { id: platformTeamId },
      };
      const updatedPrompt = { 
        ...prompt,
        isDefault: true,
      };
      
      mockPromptsRepository.findOne.mockResolvedValue(prompt);
      mockPromptsRepository.update.mockResolvedValue({ affected: 1 });
      
      vi.spyOn(service, 'getPromptById').mockResolvedValue(updatedPrompt as any);
      
      const result = await service.setDefaultPrompt(mockBotCtx, promptId, platformTeamId);
      
      expect(result).toEqual(updatedPrompt);
      
      expect(mockPromptsRepository.update).toHaveBeenCalledWith(
        {
          platformTeam: { id: platformTeamId },
          installation: { id: 'test-installation-id' },
          organization: { id: 'test-org-id' },
          isDefault: true,
        },
        { isDefault: false }
      );
      
      expect(mockPromptsRepository.update).toHaveBeenCalledWith(
        { id: promptId },
        { isDefault: true }
      );
      
      expect(service.getPromptById).toHaveBeenCalledWith(mockBotCtx, promptId);
    });

    it('should throw NotFoundException when prompt is not found', async () => {
      const promptId = 'prompt1';
      const platformTeamId = 'team1';
      
      mockPromptsRepository.findOne.mockResolvedValue(null);
      
      await expect(service.setDefaultPrompt(mockBotCtx, promptId, platformTeamId)).rejects.toThrow(`Prompt with ID ${promptId} not found`);
    });

    it('should throw BadRequestException when prompt does not belong to the specified team', async () => {
      const promptId = 'prompt1';
      const platformTeamId = 'team1';
      const prompt = { 
        id: promptId, 
        name: 'Prompt 1',
        platformTeam: { id: 'different-team' },
      };
      
      mockPromptsRepository.findOne.mockResolvedValue(prompt);
      
      await expect(service.setDefaultPrompt(mockBotCtx, promptId, platformTeamId)).rejects.toThrow('Prompt does not belong to the specified platform team');
    });

    it('should handle errors and rethrow them', async () => {
      const promptId = 'prompt1';
      const platformTeamId = 'team1';
      const error = new Error('Test error');
      
      mockPromptsRepository.findOne.mockRejectedValue(error);
      
      await expect(service.setDefaultPrompt(mockBotCtx, promptId, platformTeamId)).rejects.toThrow(error);
      expect(mockLogger.error).toHaveBeenCalledWith(`Error setting default prompt: ${error.message}`);
    });
  });

  describe('seedDefaultPrompts', () => {
    it('should seed default prompts for a team if none exist', async () => {
      const teamId = 'team1';
      const team = { id: teamId, uid: 'team1-uid' };
      const defaultPrompt = {
        id: 'new-prompt',
        name: 'team1-uid Default Prompts',
        prompts: {
          ticket_detection: DEFAULT_PROMPTS[PromptType.TICKET_DETECTION],
          sentiment_analysis: DEFAULT_PROMPTS[PromptType.SENTIMENT_ANALYSIS],
          urgency_detection: DEFAULT_PROMPTS[PromptType.URGENCY_DETECTION],
          custom_fields: DEFAULT_PROMPTS[PromptType.CUSTOM_FIELDS],
          title_generation: DEFAULT_PROMPTS[PromptType.TITLE_GENERATION],
          description_generation: DEFAULT_PROMPTS[PromptType.DESCRIPTION_GENERATION],
        },
        isDefault: true,
        isEnabled: true,
      };
      
      mockTeamsRepository.findOne.mockResolvedValue(team);
      mockPromptsRepository.find.mockResolvedValue([]);
      mockPromptsRepository.create.mockReturnValue(defaultPrompt);
      mockPromptsRepository.save.mockResolvedValue(defaultPrompt);
      
      const result = await service.seedDefaultPrompts(mockBotCtx, teamId);
      
      expect(result).toEqual(defaultPrompt);
      expect(mockTeamsRepository.findOne).toHaveBeenCalledWith({
        where: { id: teamId },
      });
      expect(mockPromptsRepository.find).toHaveBeenCalledWith({
        where: {
          platformTeam: { id: teamId },
          installation: { id: 'test-installation-id' },
          organization: { id: 'test-org-id' },
        },
      });
      expect(mockPromptsRepository.create).toHaveBeenCalledWith({
        name: 'team1-uid Default Prompts',
        prompts: {
          ticket_detection: DEFAULT_PROMPTS[PromptType.TICKET_DETECTION],
          sentiment_analysis: DEFAULT_PROMPTS[PromptType.SENTIMENT_ANALYSIS],
          urgency_detection: DEFAULT_PROMPTS[PromptType.URGENCY_DETECTION],
          custom_fields: DEFAULT_PROMPTS[PromptType.CUSTOM_FIELDS],
          title_generation: DEFAULT_PROMPTS[PromptType.TITLE_GENERATION],
          description_generation: DEFAULT_PROMPTS[PromptType.DESCRIPTION_GENERATION],
        },
        platformTeam: { id: teamId },
        installation: { id: 'test-installation-id' },
        organization: { id: 'test-org-id' },
        isDefault: true,
        isEnabled: true,
      });
      expect(mockPromptsRepository.save).toHaveBeenCalledWith(defaultPrompt);
    });

    it('should return null if team already has prompts', async () => {
      const teamId = 'team1';
      const team = { id: teamId, uid: 'team1-uid' };
      const existingPrompts = [{ id: 'existing-prompt', name: 'Existing Prompt' }];
      
      mockTeamsRepository.findOne.mockResolvedValue(team);
      mockPromptsRepository.find.mockResolvedValue(existingPrompts);
      
      const result = await service.seedDefaultPrompts(mockBotCtx, teamId);
      
      expect(result).toBeNull();
      expect(mockLogger.log).toHaveBeenCalledWith(`Team ${teamId} already has ${existingPrompts.length} prompts, skipping seed`);
      expect(mockPromptsRepository.create).not.toHaveBeenCalled();
      expect(mockPromptsRepository.save).not.toHaveBeenCalled();
    });

    it('should throw NotFoundException when team is not found', async () => {
      const teamId = 'team1';
      
      mockTeamsRepository.findOne.mockResolvedValue(null);
      
      await expect(service.seedDefaultPrompts(mockBotCtx, teamId)).rejects.toThrow(`Team with ID ${teamId} not found`);
    });

    it('should handle errors and rethrow them', async () => {
      const teamId = 'team1';
      const error = new Error('Test error');
      
      mockTeamsRepository.findOne.mockRejectedValue(error);
      
      await expect(service.seedDefaultPrompts(mockBotCtx, teamId)).rejects.toThrow(error);
      expect(mockLogger.error).toHaveBeenCalledWith(`Error seeding default prompts for team ${teamId}: ${error.message}`);
    });
  });

  describe('seedDefaultPromptsForAllTeams', () => {
    it('should seed default prompts for all teams', async () => {
      const teams = [
        { id: 'team1', uid: 'team1-uid' },
        { id: 'team2', uid: 'team2-uid' },
        { id: 'team3', uid: 'team3-uid' },
      ];
      
      mockTeamsRepository.find.mockResolvedValue(teams);
      
      vi.spyOn(service, 'seedDefaultPrompts')
        .mockResolvedValueOnce({ id: 'prompt1' } as any)
        .mockResolvedValueOnce({ id: 'prompt2' } as any)
        .mockResolvedValueOnce(null);
      
      const result = await service.seedDefaultPromptsForAllTeams(mockBotCtx);
      
      expect(result).toEqual({ seededCount: 2, skippedCount: 1 });
      expect(mockTeamsRepository.find).toHaveBeenCalledWith({
        where: {
          organization: { id: 'test-org-id' },
        },
      });
      expect(service.seedDefaultPrompts).toHaveBeenCalledTimes(3);
      expect(service.seedDefaultPrompts).toHaveBeenCalledWith(mockBotCtx, 'team1');
      expect(service.seedDefaultPrompts).toHaveBeenCalledWith(mockBotCtx, 'team2');
      expect(service.seedDefaultPrompts).toHaveBeenCalledWith(mockBotCtx, 'team3');
      expect(mockLogger.log).toHaveBeenCalledWith('Seeded default prompts for 2 teams, skipped 1 teams');
    });

    it('should handle errors and rethrow them', async () => {
      const error = new Error('Test error');
      
      mockTeamsRepository.find.mockRejectedValue(error);
      
      await expect(service.seedDefaultPromptsForAllTeams(mockBotCtx)).rejects.toThrow(error);
      expect(mockLogger.error).toHaveBeenCalledWith(`Error seeding default prompts for all teams: ${error.message}`);
    });
  });
});
