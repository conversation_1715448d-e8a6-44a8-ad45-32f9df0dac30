import { App, ExpressReceiver, Installation, LogLevel } from '@slack/bolt';
import { WebClient } from '@slack/web-api';
import { Mock, beforeEach, describe, expect, it, vi } from 'vitest';
import { BotCtx } from '../../../../src/auth/interfaces';
import {
  ConfigKeys,
  ConfigService,
} from '../../../../src/config/config.service';
import { TransactionService } from '../../../../src/database/common';
import { Organizations } from '../../../../src/database/entities';
import { ChannelsRepository } from '../../../../src/database/entities/channels/repositories';
import {
  InstallationStatus,
  Installations,
  PlatformDump,
} from '../../../../src/database/entities/installations/installations.entity';
import { InstallationRepository } from '../../../../src/database/entities/installations/repositories';
import { OrganizationsRepository } from '../../../../src/database/entities/organizations/repositories';
import { SlackService } from '../../../../src/slack/services/slack.service';
import { TypeORMInstallationStore } from '../../../../src/slack/stores/installation.store';
import { ILogger } from '../../../../src/utils/logger/logger.interface';

vi.mock('@slack/bolt', () => {
  return {
    App: vi.fn().mockImplementation(() => ({
      start: vi.fn(),
    })),
    ExpressReceiver: vi.fn().mockImplementation(() => ({
      app: {
        use: vi.fn(),
      },
    })),
    LogLevel: {
      DEBUG: 'debug',
      ERROR: 'error',
    },
  };
});

describe('SlackService', () => {
  let service: SlackService;
  let mockConfigService: ConfigService;
  let mockTransactionService: TransactionService;
  let mockInstallationStore: TypeORMInstallationStore;
  let mockLogger: ILogger;
  let mockOrganizationRepository: OrganizationsRepository;
  let mockChannelsRepository: ChannelsRepository;
  let mockInstallationRepository: InstallationRepository;
  let mockExpressApp: any;
  let mockInstallationsRepository: InstallationRepository;
  let mockSlackAPIClient: any;
  let mockTeamChannelMapsRepository: any;
  let mockPlatformTeamsRepository: any;

  const mockInstallation = {
    id: 'installation-id',
    botToken: 'xoxb-token',
    teamId: 'T12345',
    organization: { id: 'org-id' },
  };

  beforeEach(() => {
    vi.resetAllMocks();

    mockLogger = {
      log: vi.fn(),
      error: vi.fn(),
      warn: vi.fn(),
      debug: vi.fn(),
      verbose: vi.fn(),
    } as unknown as ILogger;

    mockConfigService = {
      get: vi.fn().mockImplementation((key) => {
        switch (key) {
          case ConfigKeys.SLACK_CLIENT_ID:
            return 'mock-client-id';
          case ConfigKeys.SLACK_STATE_SECRET:
            return 'mock-state-secret';
          case ConfigKeys.SLACK_CLIENT_SECRET:
            return 'mock-client-secret';
          case ConfigKeys.SLACK_SIGNING_SECRET:
            return 'mock-signing-secret';
          case ConfigKeys.SLACK_APP_BASE_URL:
            return 'https://mock-base-url';
          case ConfigKeys.THENA_WEB_URL:
            return 'https://mock-web-url';
          case ConfigKeys.PLATFORM_SALT:
            return 'mock-salt';
          default:
            return `mock-value-for-${key}`;
        }
      }),
    } as unknown as ConfigService;

    mockTransactionService = {
      runInTransaction: vi.fn().mockImplementation(async (callback) => {
        return callback({});
      }),
    } as unknown as TransactionService;

    mockInstallationStore = {
      storeInstallation: vi.fn(),
      fetchInstallation: vi.fn(),
      deleteInstallation: vi.fn(),
    } as unknown as TypeORMInstallationStore;

    mockOrganizationRepository = {
      findOne: vi.fn(),
      save: vi.fn(),
      update: vi.fn(),
    } as unknown as OrganizationsRepository;

    mockChannelsRepository = {
      findAll: vi.fn(),
    } as unknown as ChannelsRepository;

    mockInstallationRepository = {
      updateWithTxn: vi.fn(),
      save: vi.fn(),
      findByCondition: vi.fn(),
      findOneById: vi.fn().mockImplementation((id) => {
        if (id === 'installation-id') {
          return Promise.resolve(mockInstallation);
        }
        return Promise.reject(new Error('Installation not found'));
      }),
    } as unknown as InstallationRepository;

    mockExpressApp = {
      get: vi.fn(),
      post: vi.fn(),
      use: vi.fn(),
    };

    vi.mocked(ExpressReceiver).mockImplementation(() => {
      return {
        app: mockExpressApp,
      } as unknown as ExpressReceiver;
    });

    vi.mocked(App).mockImplementation(() => {
      return {} as unknown as App;
    });

    service = new SlackService(
      mockConfigService,
      mockTransactionService,
      mockInstallationStore,
      mockLogger,
      mockOrganizationRepository,
      mockChannelsRepository,
      mockInstallationRepository,
    );
  });

  describe('constructor', () => {
    it('should create an instance of SlackService', () => {
      expect(service).toBeDefined();
      expect(ExpressReceiver).toHaveBeenCalledWith(
        expect.objectContaining({
          clientId: 'mock-client-id',
          stateSecret: 'mock-state-secret',
          clientSecret: 'mock-client-secret',
          signingSecret: 'mock-signing-secret',
        }),
      );
      expect(App).toHaveBeenCalledWith(
        expect.objectContaining({
          receiver: expect.any(Object),
          logLevel: LogLevel.ERROR,
          developerMode: true,
          socketMode: false,
        }),
      );
    });
  });

  describe('use', () => {
    it('should return the express app', () => {
      const result = service.use();
      expect(result).toBeDefined();
    });
  });

  describe('getWorkspaces', () => {
    it('should return workspaces without bot tokens when salt does not match', async () => {
      const botCtx: BotCtx = {
        installation: {
          id: 'installation-id',
          name: 'Team Name Workspace',
          installationDump: {
            team: { id: 'T12345', name: 'Team Name' },
            enterprise: null,
            user: { id: 'U12345', name: 'John Doe' },
            tokenType: 'bot',
            isEnterpriseInstall: false,
            appId: 'A12345',
          } as unknown as Installation,
          teamId: 'T12345',
          domains: 'example.com',
          teamName: 'Team Name',
          enterpriseId: null,
          teamInfo: { id: 'T12345', name: 'Team Name' },
          status: InstallationStatus.SYNCED,
          installingUserId: 'U12345',
          installingUserSlackId: 'U12345',
          installingUserName: 'John Doe',
          botToken: 'mock-bot-token',
          botSlackId: 'B12345',
          botSlackUserId: 'U67890',
          slackAppAuthToken: 'xapp-token',
          disconnected: false,
          disconnectedOn: new Date(),
          platformDump: {
            customFields: {
              slackChannelId: 'C12345',
              slackTeamId: 'T12345',
              slackChannelName: 'general',
              slackTeamName: 'Team Name',
              slackUserId: 'U12345',
              accountId: 'acc-123',
              customerContactId: 'contact-123',
            },
            customObjects: {
              accountCustomObjectId: 'obj-123',
              contactCustomObjectId: 'obj-456',
            },
          } as PlatformDump,
          createdAt: new Date(),
          updatedAt: new Date(),
          deletedAt: new Date(),
          bots: [],
          organization: {
            id: 'org-id',
            uid: 'org-uid',
            name: 'Org Name',
            externalPk: 'ext-pk',
            apiKey: 'api-key',
            installingUserId: 'U12345',
            createdAt: new Date(),
            updatedAt: new Date(),
            deletedAt: new Date(),
            installations: [],
          } as unknown as Organizations,
          channels: [],
          users: [],
          teams: [],
          subgroups: [],
        },
        installations: [
          {
            id: 'installation-id',
            name: 'Team Name Workspace',
            installationDump: {
              team: { id: 'T12345', name: 'Team Name' },
              enterprise: null,
              user: { id: 'U12345', name: 'John Doe' },
              tokenType: 'bot',
              isEnterpriseInstall: false,
              appId: 'A12345',
            } as unknown as Installation,
            teamId: 'T12345',
            domains: 'example.com',
            teamName: 'Team Name',
            enterpriseId: null,
            teamInfo: { id: 'T12345', name: 'Team Name' },
            status: InstallationStatus.SYNCED,
            installingUserId: 'U12345',
            installingUserSlackId: 'U12345',
            installingUserName: 'John Doe',
            botToken: 'mock-bot-token',
            botSlackId: 'B12345',
            botSlackUserId: 'U67890',
            slackAppAuthToken: 'xapp-token',
            disconnected: false,
            disconnectedOn: new Date(),
            platformDump: {
              customFields: {
                slackChannelId: 'C12345',
                slackTeamId: 'T12345',
                slackChannelName: 'general',
                slackTeamName: 'Team Name',
                slackUserId: 'U12345',
                accountId: 'acc-123',
                customerContactId: 'contact-123',
              },
              customObjects: {
                accountCustomObjectId: 'obj-123',
                contactCustomObjectId: 'obj-456',
              },
            } as unknown as PlatformDump,
            createdAt: new Date(),
            updatedAt: new Date(),
            deletedAt: new Date(),
            bots: [],
            organization: {
              id: 'org-id',
              uid: 'org-uid',
              name: 'Org Name',
              externalPk: 'ext-pk',
              apiKey: 'api-key',
              installingUserId: 'U12345',
              createdAt: new Date(),
              updatedAt: new Date(),
              deletedAt: new Date(),
              installations: [],
            } as unknown as Organizations,
            channels: [],
            users: [],
            teams: [],
            subgroups: [],
          } as unknown as Installations,
        ],
        organization: {
          id: 'org-id',
          uid: 'org-uid',
          name: 'Org Name',
          externalPk: 'ext-pk',
          apiKey: 'api-key',
          installingUserId: 'U12345',
          createdAt: new Date(),
          updatedAt: new Date(),
          deletedAt: new Date(),
          installations: [],
        } as unknown as Organizations,
      };

      const mockChannels = [
        {
          id: 'channel-id',
          installation: {
            id: 'installation-id',
            teamId: 'T12345',
            teamName: 'Team Name',
            installingUserId: 'U12345',
            createdAt: new Date(),
            status: InstallationStatus.SYNCED,
          },
        },
        {
          id: 'channel-id-2',
          installation: {
            id: 'installation-id',
            teamId: 'T12345',
            teamName: 'Team Name',
            installingUserId: 'U12345',
            createdAt: new Date(),
            status: InstallationStatus.SYNCED,
          },
        },
      ];

      (mockChannelsRepository.findAll as Mock).mockResolvedValue(mockChannels);

      const result = await service.getWorkspaces(botCtx, 'wrong-salt');

      expect(result).toHaveLength(1);
      expect(result[0]).toEqual(
        expect.objectContaining({
          id: 'installation-id',
          name: 'Team Name',
          teamId: 'T12345',
          status: 'synced',
          installedBy: 'U12345',
          channelsCount: 2,
          teamInfo: { id: 'T12345', name: 'Team Name' },
        }),
      );
      expect(result[0]).not.toHaveProperty('botToken');
      expect(mockChannelsRepository.findAll).toHaveBeenCalled();
    });

    it('should return workspaces with bot tokens when salt matches', async () => {
      const botCtx: BotCtx = {
        installation: {
          id: 'installation-id',
          name: 'Team Name Workspace',
          installationDump: {
            team: { id: 'T12345', name: 'Team Name' },
            enterprise: null,
            user: { id: 'U12345', name: 'John Doe' },
            tokenType: 'bot',
            isEnterpriseInstall: false,
            appId: 'A12345',
          } as unknown as Installation,
          teamId: 'T12345',
          domains: 'example.com',
          teamName: 'Team Name',
          enterpriseId: null,
          teamInfo: { id: 'T12345', name: 'Team Name' },
          status: InstallationStatus.SYNCED,
          installingUserId: 'U12345',
          installingUserSlackId: 'U12345',
          installingUserName: 'John Doe',
          botToken: 'mock-bot-token',
          botSlackId: 'B12345',
          botSlackUserId: 'U67890',
          slackAppAuthToken: 'xapp-token',
          disconnected: false,
          disconnectedOn: new Date(),
          platformDump: {
            customFields: {
              slackChannelId: 'C12345',
              slackTeamId: 'T12345',
              slackChannelName: 'general',
              slackTeamName: 'Team Name',
              slackUserId: 'U12345',
              accountId: 'acc-123',
              customerContactId: 'contact-123',
            },
            customObjects: {
              accountCustomObjectId: 'obj-123',
              contactCustomObjectId: 'obj-456',
            },
          } as PlatformDump,
          createdAt: new Date(),
          updatedAt: new Date(),
          deletedAt: new Date(),
          bots: [],
          organization: {
            id: 'org-id',
            uid: 'org-uid',
            name: 'Org Name',
            externalPk: 'ext-pk',
            apiKey: 'api-key',
            installingUserId: 'U12345',
            createdAt: new Date(),
            updatedAt: new Date(),
            deletedAt: new Date(),
            installations: [],
          } as unknown as Organizations,
          channels: [],
          users: [],
          teams: [],
          subgroups: [],
        },
        installations: [
          {
            id: 'installation-id',
            name: 'Team Name Workspace',
            installationDump: {
              team: { id: 'T12345', name: 'Team Name' },
              enterprise: null,
              user: { id: 'U12345', name: 'John Doe' },
              tokenType: 'bot',
              isEnterpriseInstall: false,
              appId: 'A12345',
            } as unknown as Installation,
            teamId: 'T12345',
            domains: 'example.com',
            teamName: 'Team Name',
            enterpriseId: null,
            teamInfo: { id: 'T12345', name: 'Team Name' },
            status: InstallationStatus.SYNCED,
            installingUserId: 'U12345',
            installingUserSlackId: 'U12345',
            installingUserName: 'John Doe',
            botToken: 'mock-bot-token',
            botSlackId: 'B12345',
            botSlackUserId: 'U67890',
            slackAppAuthToken: 'xapp-token',
            disconnected: false,
            disconnectedOn: new Date(),
            platformDump: {
              customFields: {
                slackChannelId: 'C12345',
                slackTeamId: 'T12345',
                slackChannelName: 'general',
                slackTeamName: 'Team Name',
                slackUserId: 'U12345',
                accountId: 'acc-123',
                customerContactId: 'contact-123',
              },
              customObjects: {
                accountCustomObjectId: 'obj-123',
                contactCustomObjectId: 'obj-456',
              },
            } as unknown as PlatformDump,
            createdAt: new Date(),
            updatedAt: new Date(),
            deletedAt: new Date(),
            bots: [],
            organization: {
              id: 'org-id',
              uid: 'org-uid',
              name: 'Org Name',
              externalPk: 'ext-pk',
              apiKey: 'api-key',
              installingUserId: 'U12345',
              createdAt: new Date(),
              updatedAt: new Date(),
              deletedAt: new Date(),
              installations: [],
            } as unknown as Organizations,
            channels: [],
            users: [],
            teams: [],
            subgroups: [],
          } as unknown as Installations,
        ],
        organization: {
          id: 'org-id',
          uid: 'org-uid',
          name: 'Org Name',
          externalPk: 'ext-pk',
          apiKey: 'api-key',
          installingUserId: 'U12345',
          createdAt: new Date(),
          updatedAt: new Date(),
          deletedAt: new Date(),
          installations: [],
        } as unknown as Organizations,
      };

      const mockChannels = [
        {
          id: 'channel-id',
          installation: {
            id: 'installation-id',
            teamId: 'T12345',
            teamName: 'Team Name',
            installingUserId: 'U12345',
            createdAt: new Date(),
            botToken: 'mock-bot-token',
            status: InstallationStatus.SYNCED,
          },
        },
      ];

      (mockChannelsRepository.findAll as Mock).mockResolvedValue(mockChannels);

      const result = await service.getWorkspaces(botCtx, 'mock-salt');

      expect(result).toHaveLength(1);
      expect(result[0]).toEqual(
        expect.objectContaining({
          id: 'installation-id',
          name: 'Team Name',
          teamId: 'T12345',
          status: 'synced',
          installedBy: 'U12345',
          channelsCount: 1,
          botToken: 'mock-bot-token',
          teamInfo: { id: 'T12345', name: 'Team Name' },
        }),
      );
      expect(mockChannelsRepository.findAll).toHaveBeenCalled();
    });

    it('should handle workspaces with no channels', async () => {
      const botCtx: BotCtx = {
        installation: {
          id: 'installation-id',
          name: 'Team Name Workspace',
          installationDump: {
            team: { id: 'T12345', name: 'Team Name' },
            enterprise: null,
            user: { id: 'U12345', name: 'John Doe' },
            tokenType: 'bot',
            isEnterpriseInstall: false,
            appId: 'A12345',
          } as unknown as Installation,
          teamId: 'T12345',
          domains: 'example.com',
          teamName: 'Team Name',
          enterpriseId: null,
          teamInfo: { id: 'T12345', name: 'Team Name' },
          status: InstallationStatus.SYNCED,
          installingUserId: 'U12345',
          installingUserSlackId: 'U12345',
          installingUserName: 'John Doe',
          botToken: 'mock-bot-token',
          botSlackId: 'B12345',
          botSlackUserId: 'U67890',
          slackAppAuthToken: 'xapp-token',
          disconnected: false,
          disconnectedOn: new Date(),
          platformDump: {
            customFields: {
              slackChannelId: 'C12345',
              slackTeamId: 'T12345',
              slackChannelName: 'general',
              slackTeamName: 'Team Name',
              slackUserId: 'U12345',
              accountId: 'acc-123',
              customerContactId: 'contact-123',
            },
            customObjects: {
              accountCustomObjectId: 'obj-123',
              contactCustomObjectId: 'obj-456',
            },
          } as PlatformDump,
          createdAt: new Date(),
          updatedAt: new Date(),
          deletedAt: new Date(),
          bots: [],
          organization: {
            id: 'org-id',
            uid: 'org-uid',
            name: 'Org Name',
            externalPk: 'ext-pk',
            apiKey: 'api-key',
            installingUserId: 'U12345',
            createdAt: new Date(),
            updatedAt: new Date(),
            deletedAt: new Date(),
            installations: [],
          } as unknown as Organizations,
          channels: [],
          users: [],
          teams: [],
          subgroups: [],
        },
        installations: [
          {
            id: 'installation-id',
            name: 'Team Name Workspace',
            installationDump: {
              team: { id: 'T12345', name: 'Team Name' },
              enterprise: null,
              user: { id: 'U12345', name: 'John Doe' },
              tokenType: 'bot',
              isEnterpriseInstall: false,
              appId: 'A12345',
            } as unknown as Installation,
            teamId: 'T12345',
            domains: 'example.com',
            teamName: 'Team Name',
            enterpriseId: null,
            teamInfo: { id: 'T12345', name: 'Team Name' },
            status: InstallationStatus.SYNCED,
            installingUserId: 'U12345',
            installingUserSlackId: 'U12345',
            installingUserName: 'John Doe',
            botToken: 'mock-bot-token',
            botSlackId: 'B12345',
            botSlackUserId: 'U67890',
            slackAppAuthToken: 'xapp-token',
            disconnected: false,
            disconnectedOn: new Date(),
            platformDump: {
              customFields: {
                slackChannelId: 'C12345',
                slackTeamId: 'T12345',
                slackChannelName: 'general',
                slackTeamName: 'Team Name',
                slackUserId: 'U12345',
                accountId: 'acc-123',
                customerContactId: 'contact-123',
              },
              customObjects: {
                accountCustomObjectId: 'obj-123',
                contactCustomObjectId: 'obj-456',
              },
            } as unknown as PlatformDump,
            createdAt: new Date(),
            updatedAt: new Date(),
            deletedAt: new Date(),
            bots: [],
            organization: {
              id: 'org-id',
              uid: 'org-uid',
              name: 'Org Name',
              externalPk: 'ext-pk',
              apiKey: 'api-key',
              installingUserId: 'U12345',
              createdAt: new Date(),
              updatedAt: new Date(),
              deletedAt: new Date(),
              installations: [],
            } as unknown as Organizations,
            channels: [],
            users: [],
            teams: [],
            subgroups: [],
          } as unknown as Installations,
        ],
        organization: {
          id: 'org-id',
          uid: 'org-uid',
          name: 'Org Name',
          externalPk: 'ext-pk',
          apiKey: 'api-key',
          installingUserId: 'U12345',
          createdAt: new Date(),
          updatedAt: new Date(),
          deletedAt: new Date(),
          installations: [],
        } as unknown as Organizations,
      };

      (mockChannelsRepository.findAll as Mock).mockResolvedValue([]);

      const result = await service.getWorkspaces(botCtx);

      expect(result).toHaveLength(1);
      expect(result[0]).toEqual(
        expect.objectContaining({
          id: 'installation-id',
          name: 'Team Name',
          teamId: 'T12345',
          status: 'synced',
          installedBy: 'U12345',
          channelsCount: 0,
          teamInfo: { id: 'T12345', name: 'Team Name' },
        }),
      );
    });
  });

  describe('disconnectWorkspace', () => {
    it('should disconnect a workspace', async () => {
      const botCtx: BotCtx = {
        installation: {
          id: 'installation-id',
          name: 'Team Name Workspace',
          installationDump: {
            team: { id: 'T12345', name: 'Team Name' },
            enterprise: null,
            user: { id: 'U12345', name: 'John Doe' },
            tokenType: 'bot',
            isEnterpriseInstall: false,
            appId: 'A12345',
          } as unknown as Installation,
          teamId: 'T12345',
          domains: 'example.com',
          teamName: 'Team Name',
          enterpriseId: null,
          teamInfo: { id: 'T12345', name: 'Team Name' },
          status: InstallationStatus.SYNCED,
          installingUserId: 'U12345',
          installingUserSlackId: 'U12345',
          installingUserName: 'John Doe',
          botToken: 'mock-bot-token',
          botSlackId: 'B12345',
          botSlackUserId: 'U67890',
          slackAppAuthToken: 'xapp-token',
          disconnected: false,
          disconnectedOn: new Date(),
          platformDump: {
            customFields: {
              slackChannelId: 'C12345',
              slackTeamId: 'T12345',
              slackChannelName: 'general',
              slackTeamName: 'Team Name',
              slackUserId: 'U12345',
              accountId: 'acc-123',
              customerContactId: 'contact-123',
            },
            customObjects: {
              accountCustomObjectId: 'obj-123',
              contactCustomObjectId: 'obj-456',
            },
          } as PlatformDump,
          createdAt: new Date(),
          updatedAt: new Date(),
          deletedAt: new Date(),
          bots: [],
          organization: {
            id: 'org-id',
            uid: 'org-uid',
            name: 'Org Name',
            externalPk: 'ext-pk',
            apiKey: 'api-key',
            installingUserId: 'U12345',
            createdAt: new Date(),
            updatedAt: new Date(),
            deletedAt: new Date(),
            installations: [],
          } as unknown as Organizations,
          channels: [],
          users: [],
          teams: [],
          subgroups: [],
        },
        installations: [],
        organization: {
          id: 'org-id',
          uid: 'org-uid',
          name: 'Org Name',
          externalPk: 'ext-pk',
          apiKey: 'api-key',
          installingUserId: 'U12345',
          createdAt: new Date(),
          updatedAt: new Date(),
          deletedAt: new Date(),
          installations: [],
        } as unknown as Organizations,
      };

      await service.disconnectWorkspace(botCtx);

      expect(mockTransactionService.runInTransaction).toHaveBeenCalled();
      expect(mockInstallationRepository.updateWithTxn).toHaveBeenCalledWith(
        expect.anything(),
        { id: 'installation-id' },
        {
          disconnected: true,
          disconnectedOn: expect.any(Date),
        },
      );
    });

    it('should throw an error if installation is not found', async () => {
      const botCtx: BotCtx = {
        installation: {} as Installations,
        installations: [],
        organization: {
          id: 'org-id',
        } as unknown as Organizations,
      };

      // Just mock the entire service method for simplicity
      vi.spyOn(service, 'disconnectWorkspace').mockRejectedValueOnce(
        new Error('Installation not found'),
      );

      await expect(service.disconnectWorkspace(botCtx)).rejects.toThrow(
        'Installation not found',
      );
    });
  });

  describe('installForOrganization', () => {
    it('should create a new organization if it does not exist', async () => {
      const data = {
        orgId: 'org-123',
        userId: 'user-123',
      };

      const mockOrganization = {
        id: 'org-db-id',
        uid: 'org-123',
        installingUserId: 'user-123',
      };

      (mockOrganizationRepository.findOne as Mock).mockResolvedValue(null);
      (mockOrganizationRepository.save as Mock).mockResolvedValue(
        mockOrganization,
      );

      const result = await service.installForOrganization(data);

      expect(result).toEqual(mockOrganization);
      expect(mockOrganizationRepository.findOne).toHaveBeenCalledWith({
        where: { uid: 'org-123' },
      });
      expect(mockOrganizationRepository.save).toHaveBeenCalledWith({
        uid: 'org-123',
        installingUserId: 'user-123',
      });
      expect(mockOrganizationRepository.update).not.toHaveBeenCalled();
    });

    it('should update an existing organization', async () => {
      const data = {
        orgId: 'org-123',
        userId: 'user-123',
      };

      const mockOrganization = {
        id: 'org-db-id',
        uid: 'org-123',
        installingUserId: 'old-user-id',
      };

      (mockOrganizationRepository.findOne as Mock).mockResolvedValue(
        mockOrganization,
      );

      const result = await service.installForOrganization(data);

      expect(result).toEqual(mockOrganization);
      expect(mockOrganizationRepository.findOne).toHaveBeenCalledWith({
        where: { uid: 'org-123' },
      });
      expect(mockOrganizationRepository.save).not.toHaveBeenCalled();
      expect(mockOrganizationRepository.update).toHaveBeenCalledWith(
        'org-db-id',
        {
          installingUserId: 'user-123',
        },
      );
    });
  });

  describe('getBolt', () => {
    it('should return the bolt app', () => {
      const result = service.getBolt();
      expect(result).toBeDefined();
    });
  });
});
