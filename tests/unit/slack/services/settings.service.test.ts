import { Test, TestingModule } from '@nestjs/testing';
import { beforeEach, describe, expect, it, vi } from 'vitest';
import { In } from 'typeorm';
import { BotCtx } from '../../../../src/auth/interfaces';
import { TransactionService } from '../../../../src/database/common';
import { SettingType } from '../../../../src/database/entities/settings/interfaces/slack-app-settings.interface';
import { SettingsSchemasRepository } from '../../../../src/database/entities/settings/repositories/setting-schemas.repository';
import { SettingsRepository } from '../../../../src/database/entities/settings/repositories/settings.repository';
import { TeamsRepository } from '../../../../src/database/entities/teams/repositories/teams.repository';
import { SettingsService } from '../../../../src/slack/services/settings.service';

describe('SettingsService', () => {
  let service: SettingsService;
  let mockSettingsRepository: any;
  let mockTeamsRepository: any;
  let mockSettingsSchemasRepository: any;
  let mockTransactionService: any;
  let mockBotCtx: BotCtx;

  beforeEach(async () => {
    mockSettingsRepository = {
      findByCondition: vi.fn(),
      save: vi.fn(),
      saveManyWithTxn: vi.fn(),
      updateWithTxn: vi.fn(),
      findAll: vi.fn(),
    };

    mockTeamsRepository = {
      findAll: vi.fn(),
    };

    mockSettingsSchemasRepository = {
      findAll: vi.fn(),
      findByCondition: vi.fn(),
    };

    mockTransactionService = {
      runInTransaction: vi.fn().mockImplementation(async (callback) => {
        return callback('mock-txn-context');
      }),
    };

    mockBotCtx = {
      installation: { id: 'test-installation-id' },
      organization: { id: 'test-org-id' },
    } as any;

    const module: TestingModule = await Test.createTestingModule({
      providers: [
        SettingsService,
        {
          provide: SettingsRepository,
          useValue: mockSettingsRepository,
        },
        {
          provide: TeamsRepository,
          useValue: mockTeamsRepository,
        },
        {
          provide: SettingsSchemasRepository,
          useValue: mockSettingsSchemasRepository,
        },
        {
          provide: TransactionService,
          useValue: mockTransactionService,
        },
      ],
    }).compile();

    service = module.get<SettingsService>(SettingsService);
    
    (service as any).settingsRepository = mockSettingsRepository;
    (service as any).teamsRepository = mockTeamsRepository;
    (service as any).settingsSchemasRepository = mockSettingsSchemasRepository;
    (service as any).transactionService = mockTransactionService;
  });

  describe('convertValueByType', () => {
    it('should return null for null or undefined values', async () => {
      const result1 = (service as any).convertValueByType(null, SettingType.STRING);
      const result2 = (service as any).convertValueByType(undefined, SettingType.STRING);
      
      expect(result1).toBeNull();
      expect(result2).toBeNull();
    });

    it('should convert boolean values correctly', async () => {
      const result1 = (service as any).convertValueByType('true', SettingType.BOOLEAN);
      const result2 = (service as any).convertValueByType('false', SettingType.BOOLEAN);
      const result3 = (service as any).convertValueByType('TRUE', SettingType.BOOLEAN);
      
      expect(result1).toBe(true);
      expect(result2).toBe(false);
      expect(result3).toBe(true);
    });

    it('should convert number values correctly', async () => {
      const result = (service as any).convertValueByType('123', SettingType.NUMBER);
      
      expect(result).toBe(123);
    });

    it('should convert array values correctly', async () => {
      const result = (service as any).convertValueByType('["a", "b", "c"]', SettingType.ARRAY);
      
      expect(result).toEqual(['a', 'b', 'c']);
    });

    it('should convert array of numbers correctly', async () => {
      const result = (service as any).convertValueByType('[1, 2, 3]', SettingType.ARRAY_OF_NUMBERS);
      
      expect(result).toEqual([1, 2, 3]);
    });

    it('should convert JSONB values correctly', async () => {
      const result = (service as any).convertValueByType('{"key": "value"}', SettingType.JSONB);
      
      expect(result).toEqual({ key: 'value' });
    });

    it('should return null for invalid JSON', async () => {
      const result = (service as any).convertValueByType('invalid json', SettingType.JSONB);
      
      expect(result).toBeNull();
    });

    it('should return the original value for other types', async () => {
      const result = (service as any).convertValueByType('test', SettingType.STRING);
      
      expect(result).toBe('test');
    });
  });

  describe('getDefaultSettings', () => {
    it('should return default settings from schemas', async () => {
      const mockSchemas = [
        { settingKey: 'key1', defaultValue: 'value1', settingType: SettingType.STRING },
        { settingKey: 'key2', defaultValue: 'true', settingType: SettingType.BOOLEAN },
        { settingKey: 'key3', defaultValue: '123', settingType: SettingType.NUMBER },
        { settingKey: 'key4', defaultValue: null, settingType: SettingType.STRING },
      ];
      
      mockSettingsSchemasRepository.findAll.mockResolvedValue(mockSchemas);
      
      const result = await (service as any).getDefaultSettings();
      
      expect(result).toEqual({
        key1: 'value1',
        key2: true,
        key3: 123,
      });
      
      expect(mockSettingsSchemasRepository.findAll).toHaveBeenCalled();
    });
  });

  describe('prepareTeamSettings', () => {
    it('should prepare team settings entities for bulk insert', async () => {
      const mockTeams = [
        { id: 'team1', uid: 'team1-uid' },
        { id: 'team2', uid: 'team2-uid' },
      ];
      
      const mockDefaultSettings = { key1: 'value1', key2: true };
      
      vi.spyOn(service as any, 'getDefaultSettings').mockResolvedValue(mockDefaultSettings);
      
      const result = await service.prepareTeamSettings(mockTeams as any, mockBotCtx);
      
      expect(result).toEqual([
        {
          settings: mockDefaultSettings,
          platformTeam: mockTeams[0],
          organization: { id: 'test-org-id' },
          installation: { id: 'test-installation-id' },
        },
        {
          settings: mockDefaultSettings,
          platformTeam: mockTeams[1],
          organization: { id: 'test-org-id' },
          installation: { id: 'test-installation-id' },
        },
      ]);
    });
  });

  describe('initializeTeamSettings', () => {
    it('should initialize settings for a single team', async () => {
      const teamId = 'team1-uid';
      const mockTeam = { id: 'team1', uid: teamId };
      const mockSettings = { id: 'settings1', settings: { key1: 'value1' } };
      
      mockTeamsRepository.findAll.mockResolvedValue([mockTeam]);
      
      vi.spyOn(service, 'prepareTeamSettings').mockResolvedValue([{ settings: { key1: 'value1' } }] as any);
      
      mockSettingsRepository.saveManyWithTxn.mockResolvedValue([mockSettings]);
      
      const result = await service.initializeTeamSettings(teamId, mockBotCtx);
      
      expect(result).toEqual(mockSettings);
      
      expect(mockTeamsRepository.findAll).toHaveBeenCalledWith({
        where: {
          uid: In([teamId]),
          organization: { id: 'test-org-id' },
        },
      });
      
      expect(service.prepareTeamSettings).toHaveBeenCalledWith([mockTeam], mockBotCtx);
      
      expect(mockSettingsRepository.saveManyWithTxn).toHaveBeenCalledWith(
        'mock-txn-context',
        [{ settings: { key1: 'value1' } }]
      );
    });

    it('should initialize settings for multiple teams', async () => {
      const teamIds = ['team1-uid', 'team2-uid'];
      const mockTeams = [
        { id: 'team1', uid: 'team1-uid' },
        { id: 'team2', uid: 'team2-uid' },
      ];
      const mockSettings = [
        { id: 'settings1', settings: { key1: 'value1' } },
        { id: 'settings2', settings: { key1: 'value1' } },
      ];
      
      mockTeamsRepository.findAll.mockResolvedValue(mockTeams);
      
      vi.spyOn(service, 'prepareTeamSettings').mockResolvedValue([
        { settings: { key1: 'value1' } },
        { settings: { key1: 'value1' } },
      ] as any);
      
      mockSettingsRepository.saveManyWithTxn.mockResolvedValue(mockSettings);
      
      const result = await service.initializeTeamSettings(teamIds, mockBotCtx);
      
      expect(result).toEqual(mockSettings);
      
      expect(mockTeamsRepository.findAll).toHaveBeenCalledWith({
        where: {
          uid: In(teamIds),
          organization: { id: 'test-org-id' },
        },
      });
      
      expect(service.prepareTeamSettings).toHaveBeenCalledWith(mockTeams, mockBotCtx);
    });

    it('should throw NotFoundException when no teams are found', async () => {
      mockTeamsRepository.findAll.mockResolvedValue([]);
      
      await expect(service.initializeTeamSettings('team1-uid', mockBotCtx)).rejects.toThrow('No teams found for the provided IDs');
    });

    it('should use provided transaction context if available', async () => {
      const teamId = 'team1-uid';
      const mockTeam = { id: 'team1', uid: teamId };
      const mockSettings = { id: 'settings1', settings: { key1: 'value1' } };
      const txnContext = 'provided-txn-context';
      
      mockTeamsRepository.findAll.mockResolvedValue([mockTeam]);
      
      vi.spyOn(service, 'prepareTeamSettings').mockResolvedValue([{ settings: { key1: 'value1' } }] as any);
      
      mockSettingsRepository.saveManyWithTxn.mockResolvedValue([mockSettings]);
      
      const result = await service.initializeTeamSettings(teamId, mockBotCtx, txnContext as any);
      
      expect(result).toEqual(mockSettings);
      
      expect(mockSettingsRepository.saveManyWithTxn).toHaveBeenCalledWith(
        txnContext,
        [{ settings: { key1: 'value1' } }]
      );
      
      expect(mockTransactionService.runInTransaction).not.toHaveBeenCalled();
    });
  });

  describe('toCamelCase', () => {
    it('should convert snake_case keys to camelCase', async () => {
      const input = {
        snake_case_key: 'value1',
        another_key: 'value2',
        simple: 'value3',
      };
      
      const result = (service as any).toCamelCase(input);
      
      expect(result).toEqual({
        snakeCaseKey: 'value1',
        anotherKey: 'value2',
        simple: 'value3',
      });
    });
  });

  describe('formatSettingsResponse', () => {
    it('should format settings response correctly', async () => {
      const settings = {
        id: 'settings1',
        settings: {
          snake_case_key: 'value1',
          another_key: 'value2',
        },
        createdAt: new Date('2023-01-01'),
        updatedAt: new Date('2023-01-02'),
        platformTeam: {
          uid: 'team1-uid',
        },
      };
      
      const result = (service as any).formatSettingsResponse(settings);
      
      expect(result).toEqual({
        snakeCaseKey: 'value1',
        anotherKey: 'value2',
        createdAt: settings.createdAt,
        updatedAt: settings.updatedAt,
        platformTeamId: 'team1-uid',
      });
    });
  });

  describe('getTeamSettings', () => {
    it('should get settings for a team', async () => {
      const teamId = 'team1-uid';
      const mockSettings = {
        id: 'settings1',
        settings: {
          snake_case_key: 'value1',
          another_key: 'value2',
        },
        createdAt: new Date('2023-01-01'),
        updatedAt: new Date('2023-01-02'),
        platformTeam: {
          uid: teamId,
        },
      };
      
      mockSettingsRepository.findByCondition.mockResolvedValue(mockSettings);
      
      vi.spyOn(service as any, 'formatSettingsResponse').mockReturnValue({
        snakeCaseKey: 'value1',
        anotherKey: 'value2',
        createdAt: mockSettings.createdAt,
        updatedAt: mockSettings.updatedAt,
        platformTeamId: teamId,
      });
      
      const result = await service.getTeamSettings(teamId, mockBotCtx);
      
      expect(result).toEqual({
        snakeCaseKey: 'value1',
        anotherKey: 'value2',
        createdAt: mockSettings.createdAt,
        updatedAt: mockSettings.updatedAt,
        platformTeamId: teamId,
      });
      
      expect(mockSettingsRepository.findByCondition).toHaveBeenCalledWith({
        where: {
          platformTeam: { uid: teamId },
          installation: { id: 'test-installation-id' },
          organization: { id: 'test-org-id' },
        },
        relations: ['platformTeam'],
      });
    });

    it('should throw NotFoundException when settings are not found', async () => {
      const teamId = 'team1-uid';
      
      mockSettingsRepository.findByCondition.mockResolvedValue(null);
      
      await expect(service.getTeamSettings(teamId, mockBotCtx)).rejects.toThrow(`Settings not found for team ${teamId}`);
    });
  });

});
