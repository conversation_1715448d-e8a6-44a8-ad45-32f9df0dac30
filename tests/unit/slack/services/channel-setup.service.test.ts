import { beforeEach, describe, expect, it, vi } from 'vitest';
import { ChannelSetupService } from '../../../../src/slack/services/channel-setup.service';
import { ChannelsRepository } from '../../../../src/database/entities/channels/repositories';
import { ThenaPlatformApiProvider } from '../../../../src/external/provider/thena-platform-api.provider';
import { CUSTOM_LOGGER_TOKEN } from '../../../../src/utils';
import { CHANNEL_SETUP_CONFIG } from '../../../../src/slack/constants/channel-setup.constants';
import { ChannelType } from '../../../../src/database/entities/channels/channels.entity';

describe('ChannelSetupService', () => {
  let service: ChannelSetupService;
  let mockLogger: any;
  let mockChannelsRepository: any;
  let mockThenaPlatformApiProvider: any;
  let mockChannelSetupBlocks: any;

  beforeEach(() => {
    mockLogger = {
      log: vi.fn(),
      debug: vi.fn(),
      error: vi.fn(),
      warn: vi.fn(),
    };

    mockChannelsRepository = {
      findByCondition: vi.fn().mockResolvedValue(null),
    };

    mockThenaPlatformApiProvider = {
      getTeams: vi.fn().mockResolvedValue([]),
    };

    mockChannelSetupBlocks = {
      build: vi.fn().mockReturnValue({ blocks: [] }),
    };

    service = new ChannelSetupService(
      mockLogger,
      mockChannelsRepository as unknown as ChannelsRepository,
      mockThenaPlatformApiProvider as unknown as ThenaPlatformApiProvider,
      mockChannelSetupBlocks
    );
  });

  describe('checkChannelState', () => {
    it('should log a message if channel is not found', async () => {
      const channelId = 'C12345';
      const mockInstallation = {
        id: 'test-installation-id',
      } as any;

      mockChannelsRepository.findByCondition.mockResolvedValue(null);

      await service.checkChannelState(channelId, mockInstallation);

      expect(mockLogger.log).toHaveBeenCalledWith(`Checking current database state of channel ${channelId}`);
      expect(mockLogger.log).toHaveBeenCalledWith(`Channel ${channelId} not found in database!`);
    });

    it('should log channel state if channel is found', async () => {
      const channelId = 'C12345';
      const mockInstallation = {
        id: 'test-installation-id',
      } as any;

      const mockChannel = {
        channelId,
        channelType: ChannelType.CUSTOMER_CHANNEL,
        platformTeamsToChannelMappings: [
          {
            relationshipType: 'PRIMARY',
            platformTeam: {
              id: 'team-id-1',
              uid: 'team-uid-1',
            },
          },
          {
            relationshipType: 'SECONDARY',
            platformTeam: {
              id: 'team-id-2',
              uid: 'team-uid-2',
            },
          },
        ],
        sharedTeamIds: ['T12345', 'T67890'],
      };

      mockChannelsRepository.findByCondition.mockResolvedValue(mockChannel);

      const logChannelStateSpy = vi.spyOn(service as any, 'logChannelState');

      await service.checkChannelState(channelId, mockInstallation);

      expect(mockChannelsRepository.findByCondition).toHaveBeenCalledWith({
        where: { channelId, installation: { id: 'test-installation-id' } },
        relations: {
          installation: true,
          organization: true,
          platformTeamsToChannelMappings: {
            platformTeam: true,
          },
        },
      });

      expect(logChannelStateSpy).toHaveBeenCalledWith({
        channelId,
        channelType: ChannelType.CUSTOMER_CHANNEL,
        teamMappings: [
          {
            relationshipType: 'PRIMARY',
            platformTeamId: 'team-id-1',
            platformTeamUid: 'team-uid-1',
          },
          {
            relationshipType: 'SECONDARY',
            platformTeamId: 'team-id-2',
            platformTeamUid: 'team-uid-2',
          },
        ],
        sharedTeamIds: ['T12345', 'T67890'],
      });
    });

    it('should handle errors', async () => {
      const channelId = 'C12345';
      const mockInstallation = {
        id: 'test-installation-id',
      } as any;

      const error = new Error('Test error');
      mockChannelsRepository.findByCondition.mockRejectedValue(error);

      await service.checkChannelState(channelId, mockInstallation);

      expect(mockLogger.error).toHaveBeenCalledWith(
        expect.stringContaining('Error checking channel state:')
      );
    });
  });

  describe('fetchTeams', () => {
    it('should fetch teams successfully', async () => {
      const mockInstallation = {
        id: 'test-installation-id',
        organization: { id: 'test-org-id' },
      } as any;

      const mockTeams = [
        { id: 'team-id-1', name: 'Team 1' },
        { id: 'team-id-2', name: 'Team 2' },
      ];

      mockThenaPlatformApiProvider.getTeams.mockResolvedValue(mockTeams);

      const result = await service.fetchTeams(mockInstallation);

      expect(mockThenaPlatformApiProvider.getTeams).toHaveBeenCalledWith(mockInstallation);
      expect(mockLogger.log).toHaveBeenCalledWith(
        `Fetched ${mockTeams.length} teams for organization ${mockInstallation.organization.id}`
      );
      expect(result).toEqual(mockTeams);
    });

    it('should retry if API returns zero teams', async () => {
      const mockInstallation = {
        id: 'test-installation-id',
        organization: { id: 'test-org-id' },
      } as any;

      vi.useFakeTimers();

      mockThenaPlatformApiProvider.getTeams
        .mockResolvedValueOnce([])
        .mockResolvedValueOnce([
          { id: 'team-id-1', name: 'Team 1' },
          { id: 'team-id-2', name: 'Team 2' },
        ]);

      const resultPromise = service.fetchTeams(mockInstallation);
      
      await vi.runAllTimersAsync();
      
      const result = await resultPromise;

      expect(mockThenaPlatformApiProvider.getTeams).toHaveBeenCalledTimes(2);
      expect(mockLogger.warn).toHaveBeenCalledWith(
        `API returned zero teams for organization ${mockInstallation.organization.id}`
      );
      expect(result).toHaveLength(2);

      vi.useRealTimers();
    });

    it('should retry on error and eventually succeed', async () => {
      const mockInstallation = {
        id: 'test-installation-id',
        organization: { id: 'test-org-id' },
      } as any;

      vi.useFakeTimers();

      const error = new Error('API error');
      const mockTeams = [
        { id: 'team-id-1', name: 'Team 1' },
        { id: 'team-id-2', name: 'Team 2' },
      ];

      mockThenaPlatformApiProvider.getTeams
        .mockRejectedValueOnce(error)
        .mockResolvedValueOnce(mockTeams);

      const resultPromise = service.fetchTeams(mockInstallation);
      
      await vi.runAllTimersAsync();
      
      const result = await resultPromise;

      expect(mockThenaPlatformApiProvider.getTeams).toHaveBeenCalledTimes(2);
      expect(mockLogger.error).toHaveBeenCalledWith(
        expect.stringContaining('Error fetching teams from platform:')
      );
      expect(result).toEqual(mockTeams);

      vi.useRealTimers();
    });

    it('should throw error after max retries', async () => {
      const mockInstallation = {
        id: 'test-installation-id',
        organization: { id: 'test-org-id' },
      } as any;

      vi.useFakeTimers();

      const error = new Error('API error');

      const processEmitSpy = vi.spyOn(process, 'emit');
      const originalProcessEmit = process.emit;
      
      processEmitSpy.mockImplementation((event: string, ...args) => {
        if (event === 'unhandledRejection') {
          return true;
        }
        return originalProcessEmit.apply(process, [event, ...args]);
      });

      mockThenaPlatformApiProvider.getTeams.mockRejectedValue(error);

      const resultPromise = service.fetchTeams(mockInstallation);
      
      for (let i = 0; i < CHANNEL_SETUP_CONFIG.maxRetries; i++) {
        await vi.runAllTimersAsync();
      }
      
      await expect(resultPromise).rejects.toThrow(error);

      expect(mockThenaPlatformApiProvider.getTeams).toHaveBeenCalledTimes(CHANNEL_SETUP_CONFIG.maxRetries + 1);
      expect(mockLogger.error).toHaveBeenCalledTimes(CHANNEL_SETUP_CONFIG.maxRetries + 1);

      vi.useRealTimers();
      processEmitSpy.mockRestore();
    });
  });

  describe('openConfigurationModal', () => {
    it('should open configuration modal successfully', async () => {
      const mockClient = {
        views: {
          open: vi.fn().mockResolvedValue({ ok: true }),
        },
      };

      const mockInstallation = {
        id: 'test-installation-id',
        botToken: 'xoxb-test-token',
      } as any;

      const triggerId = 'test-trigger-id';
      const channelData = {
        channelId: 'C12345',
        channelName: 'test-channel',
      };

      const teams = [
        { id: 'team-id-1', name: 'Team 1' },
        { id: 'team-id-2', name: 'Team 2' },
      ];

      mockChannelSetupBlocks.build.mockReturnValue({
        blocks: [
          { type: 'section', text: { type: 'mrkdwn', text: 'Select a team' } },
        ],
      });

      await service.openConfigurationModal(
        mockClient,
        mockInstallation,
        triggerId,
        channelData,
        teams
      );

      expect(mockClient.views.open).toHaveBeenCalledWith({
        token: 'xoxb-test-token',
        trigger_id: triggerId,
        view: {
          type: 'modal',
          private_metadata: JSON.stringify(channelData),
          callback_id: 'channel_setup_modal',
          title: {
            type: 'plain_text',
            text: CHANNEL_SETUP_CONFIG.modalConfig.title,
            emoji: true,
          },
          blocks: expect.any(Array),
          submit: {
            type: 'plain_text',
            text: CHANNEL_SETUP_CONFIG.modalConfig.submitText,
          },
          close: {
            type: 'plain_text',
            text: CHANNEL_SETUP_CONFIG.modalConfig.closeText,
          },
        },
      });

      expect(mockChannelSetupBlocks.build).toHaveBeenCalledWith(teams);
      expect(mockLogger.log).toHaveBeenCalledWith(
        `Opened channel configuration modal for channel ${channelData.channelId}`
      );
    });

    it('should handle errors when opening modal', async () => {
      const mockClient = {
        views: {
          open: vi.fn().mockRejectedValue(new Error('API error')),
        },
      };

      const mockInstallation = {
        id: 'test-installation-id',
        botToken: 'xoxb-test-token',
      } as any;

      const triggerId = 'test-trigger-id';
      const channelData = {
        channelId: 'C12345',
        channelName: 'test-channel',
      };

      const teams = [
        { id: 'team-id-1', name: 'Team 1' },
        { id: 'team-id-2', name: 'Team 2' },
      ];

      await expect(
        service.openConfigurationModal(
          mockClient,
          mockInstallation,
          triggerId,
          channelData,
          teams
        )
      ).rejects.toThrow('API error');

      expect(mockLogger.error).toHaveBeenCalledWith(
        expect.stringContaining('Error opening configuration modal:')
      );
    });
  });

  describe('logChannelState', () => {
    it('should log channel state correctly', () => {
      const channelState = {
        channelId: 'C12345',
        channelType: ChannelType.CUSTOMER_CHANNEL,
        teamMappings: [
          {
            relationshipType: 'PRIMARY',
            platformTeamId: 'team-id-1',
            platformTeamUid: 'team-uid-1',
          },
          {
            relationshipType: 'SECONDARY',
            platformTeamId: 'team-id-2',
            platformTeamUid: 'team-uid-2',
          },
        ],
        sharedTeamIds: ['T12345', 'T67890'],
      };

      (service as any).logChannelState(channelState);

      expect(mockLogger.log).toHaveBeenCalledWith(`Channel ${channelState.channelId} - Current state:`);
      expect(mockLogger.log).toHaveBeenCalledWith(`  Type: ${channelState.channelType}`);
      expect(mockLogger.log).toHaveBeenCalledWith(`  Team mappings count: ${channelState.teamMappings.length}`);
      
      expect(mockLogger.log).toHaveBeenCalledWith('  Team mapping #1:');
      expect(mockLogger.log).toHaveBeenCalledWith('    Relationship: PRIMARY');
      expect(mockLogger.log).toHaveBeenCalledWith('    Platform team ID: team-id-1');
      expect(mockLogger.log).toHaveBeenCalledWith('    Platform team UID: team-uid-1');
      
      expect(mockLogger.log).toHaveBeenCalledWith('  Team mapping #2:');
      expect(mockLogger.log).toHaveBeenCalledWith('    Relationship: SECONDARY');
      expect(mockLogger.log).toHaveBeenCalledWith('    Platform team ID: team-id-2');
      expect(mockLogger.log).toHaveBeenCalledWith('    Platform team UID: team-uid-2');
      
      expect(mockLogger.log).toHaveBeenCalledWith('  Shared team IDs: T12345, T67890');
    });

    it('should handle empty team mappings and shared team IDs', () => {
      const channelState = {
        channelId: 'C12345',
        channelType: ChannelType.CUSTOMER_CHANNEL,
        teamMappings: [],
        sharedTeamIds: [],
      };

      (service as any).logChannelState(channelState);

      expect(mockLogger.log).toHaveBeenCalledWith(`Channel ${channelState.channelId} - Current state:`);
      expect(mockLogger.log).toHaveBeenCalledWith(`  Type: ${channelState.channelType}`);
      expect(mockLogger.log).toHaveBeenCalledWith('  Team mappings count: 0');
      expect(mockLogger.log).toHaveBeenCalledWith('  Shared team IDs: none');
    });
  });
});
