import { describe, it, expect, beforeEach, vi, Mock } from 'vitest';
import { CreateTicketCommand } from '../../../../src/slack/commands/create-ticket.command';
import { TeamRelationshipType } from '../../../../src/database/entities/mappings';
import { TeamChannelMapsRepository } from '../../../../src/database/entities/mappings/repositories/team-channel-maps.repository';
import { ILogger } from '../../../../src/utils/logger/logger.interface';
import { CreateTicketsBlocksComposite } from '../../../../src/slack/blocks/components/composite/tickets/create-tickets-blocks.composite';
import { SettingsCore } from '../../../../src/slack/core';

describe('CreateTicketCommand', () => {
  let command: CreateTicketCommand;
  let mockLogger: ILogger;
  let mockTeamChannelMapsRepo: TeamChannelMapsRepository;
  let mockCreateTicketBlock: CreateTicketsBlocksComposite;
  let mockSettingsCore: SettingsCore;

  beforeEach(() => {
    vi.resetAllMocks();
    
    mockLogger = {
      log: vi.fn(),
      error: vi.fn(),
      warn: vi.fn(),
      debug: vi.fn(),
      verbose: vi.fn(),
    } as unknown as ILogger;

    mockTeamChannelMapsRepo = {
      findByCondition: vi.fn(),
    } as unknown as TeamChannelMapsRepository;

    mockCreateTicketBlock = {
      build: vi.fn().mockReturnValue({ blocks: [] }),
    } as unknown as CreateTicketsBlocksComposite;

    mockSettingsCore = {
      getValue: vi.fn(),
    } as unknown as SettingsCore;

    command = new CreateTicketCommand(
      mockLogger,
      mockTeamChannelMapsRepo,
      mockCreateTicketBlock,
      mockSettingsCore,
    );
  });

  describe('handle', () => {
    it('should open a modal when called with valid parameters', async () => {
      const args = {
        command: {
          channel_id: 'C12345',
          channel_name: 'general',
          user_id: 'U12345',
          user_name: 'johndoe',
        },
        context: {
          client: {
            chat: {
              postMessage: vi.fn(),
            },
            views: {
              open: vi.fn(),
            },
          },
          installation: {
            id: 'installation-id',
            botToken: 'mock-bot-token',
          },
          organization: {
            id: 'org-id',
          },
        },
        body: {
          trigger_id: 'trigger-123',
          user_id: 'U12345',
        },
      };

      const mockMapping = {
        platformTeam: {
          uid: 'team-123',
        },
      };
      (mockTeamChannelMapsRepo.findByCondition as Mock).mockResolvedValue(mockMapping);

      (mockSettingsCore.getValue as Mock).mockResolvedValue(true);

      await command.handle(args);

      expect(mockTeamChannelMapsRepo.findByCondition).toHaveBeenCalledWith({
        where: {
          channel: { channelId: 'C12345' },
          relationshipType: TeamRelationshipType.PRIMARY,
          installation: { id: 'installation-id' },
          organization: { id: 'org-id' },
        },
        relations: { platformTeam: true },
      });
      expect(mockSettingsCore.getValue).toHaveBeenCalledWith('ticket_command', {
        platformTeam: mockMapping.platformTeam,
        workspace: args.context.installation,
      });
      expect(mockCreateTicketBlock.build).toHaveBeenCalledWith('team-123', 'C12345');
      expect(args.context.client.views.open).toHaveBeenCalledWith({
        token: 'mock-bot-token',
        trigger_id: 'trigger-123',
        view: expect.objectContaining({
          type: 'modal',
          callback_id: CreateTicketCommand.CREATE_TICKET_VIEW_CALLBACK_ID,
        }),
      });
    });

    it('should send an error message when ticket command is not enabled', async () => {
      const args = {
        command: {
          channel_id: 'C12345',
          channel_name: 'general',
          user_id: 'U12345',
          user_name: 'johndoe',
        },
        context: {
          client: {
            chat: {
              postMessage: vi.fn(),
            },
            views: {
              open: vi.fn(),
            },
          },
          installation: {
            id: 'installation-id',
            botToken: 'mock-bot-token',
          },
          organization: {
            id: 'org-id',
          },
        },
        body: {
          trigger_id: 'trigger-123',
          user_id: 'U12345',
        },
      };

      const mockMapping = {
        platformTeam: {
          uid: 'team-123',
        },
      };
      (mockTeamChannelMapsRepo.findByCondition as Mock).mockResolvedValue(mockMapping);

      (mockSettingsCore.getValue as Mock).mockResolvedValue(false);

      await command.handle(args);

      expect(mockTeamChannelMapsRepo.findByCondition).toHaveBeenCalled();
      expect(mockSettingsCore.getValue).toHaveBeenCalled();
      expect(mockLogger.error).toHaveBeenCalled();
      expect(args.context.client.chat.postMessage).toHaveBeenCalledWith({
        channel: 'U12345',
        text: expect.stringContaining('Ticket command is not enabled'),
      });
      expect(args.context.client.views.open).not.toHaveBeenCalled();
    });

    it('should handle errors gracefully', async () => {
      const args = {
        command: {
          channel_id: 'C12345',
          channel_name: 'general',
          user_id: 'U12345',
          user_name: 'johndoe',
        },
        context: {
          client: {
            chat: {
              postMessage: vi.fn(),
            },
            views: {
              open: vi.fn().mockRejectedValue(new Error('API error')),
            },
          },
          installation: {
            id: 'installation-id',
            botToken: 'mock-bot-token',
          },
          organization: {
            id: 'org-id',
          },
        },
        body: {
          trigger_id: 'trigger-123',
          user_id: 'U12345',
        },
      };

      const mockMapping = {
        platformTeam: {
          uid: 'team-123',
        },
      };
      (mockTeamChannelMapsRepo.findByCondition as Mock).mockResolvedValue(mockMapping);

      (mockSettingsCore.getValue as Mock).mockResolvedValue(true);

      await command.handle(args);

      expect(mockLogger.error).toHaveBeenCalled();
      expect(args.context.client.chat.postMessage).toHaveBeenCalledWith({
        channel: 'U12345',
        text: expect.stringContaining('An error occurred'),
      });
    });

    it('should handle the case when no team channel mapping is found', async () => {
      const args = {
        command: {
          channel_id: 'C12345',
          channel_name: 'general',
          user_id: 'U12345',
          user_name: 'johndoe',
        },
        context: {
          client: {
            chat: {
              postMessage: vi.fn(),
            },
            views: {
              open: vi.fn(),
            },
          },
          installation: {
            id: 'installation-id',
            botToken: 'mock-bot-token',
          },
          organization: {
            id: 'org-id',
          },
        },
        body: {
          trigger_id: 'trigger-123',
          user_id: 'U12345',
        },
      };

      (mockTeamChannelMapsRepo.findByCondition as Mock).mockResolvedValue(null);

      await command.handle(args);

      expect(mockLogger.debug).toHaveBeenCalled();
      expect(mockCreateTicketBlock.build).toHaveBeenCalledWith(undefined, 'C12345');
      expect(args.context.client.views.open).toHaveBeenCalled();
    });
  });
});
