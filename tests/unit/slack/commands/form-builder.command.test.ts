import { describe, expect, it } from 'vitest';
import { FormBuilderCommand } from '../../../../src/slack/commands/form-builder.command';

describe('FormBuilderCommand', () => {
  describe('constants', () => {
    it('should have the correct command value', () => {
      expect(FormBuilderCommand.COMMAND).toBe('/form-builder');
    });

    it('should have the correct form continue action ID', () => {
      expect(FormBuilderCommand.FORM_CONTINUE_ACTION_ID).toBe('form_continue');
    });

    it('should have the correct form field action ID prefix', () => {
      expect(FormBuilderCommand.FORM_FIELD_ACTION_ID_PREFIX).toBe('field_');
    });

    it('should have the correct form submission modal callback ID', () => {
      expect(FormBuilderCommand.FORM_SUBMISSION_MODAL_CALLBACK_ID).toBe('form_submission_modal');
    });
  });
});
