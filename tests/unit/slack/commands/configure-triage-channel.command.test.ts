import { Test, TestingModule } from '@nestjs/testing';
import { beforeEach, describe, expect, it, vi } from 'vitest';
import { ChannelsRepository } from '../../../../src/database/entities/channels/repositories/channels.repository';
import { ConfigureTriageChannelComposite } from '../../../../src/slack/blocks/components/composite/channels/configure-triage-channel.composite';
import { ConfigureTriageChannelCommand } from '../../../../src/slack/commands/configure-triage-channel.command';
import { CUSTOM_LOGGER_TOKEN } from '../../../../src/utils';

describe('ConfigureTriageChannelCommand', () => {
  let command: ConfigureTriageChannelCommand;
  let mockLogger: any;
  let mockTriageChannelBlock: any;
  let mockChannelsRepository: any;

  beforeEach(async () => {
    mockLogger = {
      log: vi.fn(),
      error: vi.fn(),
      warn: vi.fn(),
      debug: vi.fn(),
    };

    mockTriageChannelBlock = {
      build: vi.fn().mockReturnValue({
        blocks: [
          {
            type: 'section',
            text: {
              type: 'mrkdwn',
              text: 'Configure your triage channel',
            },
          },
        ],
      }),
    };

    mockChannelsRepository = {
      findByCondition: vi.fn(),
      save: vi.fn(),
      update: vi.fn(),
    };

    const module: TestingModule = await Test.createTestingModule({
      providers: [
        ConfigureTriageChannelCommand,
        {
          provide: CUSTOM_LOGGER_TOKEN,
          useValue: mockLogger,
        },
        {
          provide: ConfigureTriageChannelComposite,
          useValue: mockTriageChannelBlock,
        },
        {
          provide: ChannelsRepository,
          useValue: mockChannelsRepository,
        },
      ],
    }).compile();

    command = module.get<ConfigureTriageChannelCommand>(ConfigureTriageChannelCommand);
    
    (command as any).logger = mockLogger;
    (command as any).triageChannelBlock = mockTriageChannelBlock;
    (command as any).channelsRepository = mockChannelsRepository;
  });

  describe('handle', () => {
    it('should open a modal view when trigger_id is provided', async () => {
      const mockClient = {
        views: {
          open: vi.fn().mockResolvedValue({ ok: true }),
        },
        chat: {
          postMessage: vi.fn(),
        },
      };

      const mockArgs = {
        command: {
          channel_id: 'C12345',
          channel_name: 'test-channel',
          user_id: 'U12345',
          user_name: 'test-user',
        },
        client: mockClient,
        body: {
          trigger_id: 'trigger-123',
        },
      };

      await command.handle(mockArgs as any);

      expect(mockLogger.log).toHaveBeenCalledWith(
        expect.stringContaining('Team config command received for channel: test-channel [C12345]')
      );

      expect(mockTriageChannelBlock.build).toHaveBeenCalled();

      expect(mockClient.views.open).toHaveBeenCalledWith({
        trigger_id: 'trigger-123',
        view: {
          type: 'modal',
          private_metadata: JSON.stringify({
            channelId: 'C12345',
          }),
          callback_id: 'configure_triage_channel_submit',
          title: {
            type: 'plain_text',
            text: 'Configure Triage Channel',
            emoji: true,
          },
          blocks: expect.any(Array),
          submit: {
            type: 'plain_text',
            text: 'Save',
          },
          close: {
            type: 'plain_text',
            text: 'Cancel',
          },
        },
      });

      expect(mockClient.chat.postMessage).not.toHaveBeenCalled();
    });

    it('should post a message with configuration button when trigger_id is not provided', async () => {
      const mockClient = {
        views: {
          open: vi.fn(),
        },
        chat: {
          postMessage: vi.fn().mockResolvedValue({ ok: true }),
        },
      };

      const mockArgs = {
        command: {
          channel_id: 'C12345',
          channel_name: 'test-channel',
          user_id: 'U12345',
          user_name: 'test-user',
        },
        client: mockClient,
        body: {
        },
      };

      await command.handle(mockArgs as any);

      expect(mockLogger.log).toHaveBeenCalledWith(
        expect.stringContaining('Team config command received for channel: test-channel [C12345]')
      );

      expect(mockClient.views.open).not.toHaveBeenCalled();

      expect(mockClient.chat.postMessage).toHaveBeenCalledWith({
        channel: 'C12345',
        blocks: expect.arrayContaining([
          expect.objectContaining({
            type: 'section',
            text: expect.objectContaining({
              text: 'This channel needs to be configured before it can be used.',
            }),
          }),
          expect.objectContaining({
            type: 'actions',
            elements: expect.arrayContaining([
              expect.objectContaining({
                type: 'button',
                text: expect.objectContaining({
                  text: 'Configure Channel',
                }),
                action_id: 'configure_channel_action',
                value: JSON.stringify({
                  channelId: 'C12345',
                  channelName: 'test-channel',
                }),
              }),
            ]),
          }),
        ]),
        text: 'This channel needs to be configured before it can be used. Please click the Configure Channel button to get started.',
      });
    });

    it('should handle errors when opening modal view', async () => {
      const mockClient = {
        views: {
          open: vi.fn().mockRejectedValue(new Error('Failed to open modal')),
        },
        chat: {
          postMessage: vi.fn(),
        },
      };

      const mockArgs = {
        command: {
          channel_id: 'C12345',
          channel_name: 'test-channel',
          user_id: 'U12345',
          user_name: 'test-user',
        },
        client: mockClient,
        body: {
          trigger_id: 'trigger-123',
        },
      };

      await expect(command.handle(mockArgs as any)).rejects.toThrow('Failed to open modal');

      expect(mockLogger.log).toHaveBeenCalledWith(
        expect.stringContaining('Team config command received for channel: test-channel [C12345]')
      );

      expect(mockTriageChannelBlock.build).toHaveBeenCalled();

      expect(mockClient.views.open).toHaveBeenCalled();
      expect(mockClient.chat.postMessage).not.toHaveBeenCalled();
    });

    it('should handle errors when posting message', async () => {
      const mockClient = {
        views: {
          open: vi.fn(),
        },
        chat: {
          postMessage: vi.fn().mockRejectedValue(new Error('Failed to post message')),
        },
      };

      const mockArgs = {
        command: {
          channel_id: 'C12345',
          channel_name: 'test-channel',
          user_id: 'U12345',
          user_name: 'test-user',
        },
        client: mockClient,
        body: {
        },
      };

      await expect(command.handle(mockArgs as any)).rejects.toThrow('Failed to post message');

      expect(mockLogger.log).toHaveBeenCalledWith(
        expect.stringContaining('Team config command received for channel: test-channel [C12345]')
      );

      expect(mockClient.views.open).not.toHaveBeenCalled();
      expect(mockClient.chat.postMessage).toHaveBeenCalled();
    });
  });
});
