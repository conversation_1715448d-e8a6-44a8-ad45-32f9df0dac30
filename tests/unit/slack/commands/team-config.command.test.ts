import { Test, TestingModule } from '@nestjs/testing';
import { beforeEach, describe, expect, it, vi } from 'vitest';
import { SelectChannelTeamComposite } from '../../../../src/slack/blocks/components/composite/channels/select-channel-team.composite';
import { TeamConfigCommand } from '../../../../src/slack/commands/team-config.command';
import { CUSTOM_LOGGER_TOKEN } from '../../../../src/utils';

describe('TeamConfigCommand', () => {
  let command: TeamConfigCommand;
  let mockLogger: any;
  let mockTeamConfigBlock: any;

  beforeEach(async () => {
    mockLogger = {
      log: vi.fn(),
      error: vi.fn(),
      warn: vi.fn(),
      debug: vi.fn(),
    };

    mockTeamConfigBlock = {
      build: vi.fn().mockReturnValue({
        blocks: [
          {
            type: 'section',
            text: {
              type: 'mrkdwn',
              text: 'Configure your team',
            },
          },
        ],
      }),
    };

    const module: TestingModule = await Test.createTestingModule({
      providers: [
        TeamConfigCommand,
        {
          provide: CUSTOM_LOGGER_TOKEN,
          useValue: mockLogger,
        },
        {
          provide: SelectChannelTeamComposite,
          useValue: mockTeamConfigBlock,
        },
      ],
    }).compile();

    command = module.get<TeamConfigCommand>(TeamConfigCommand);
    
    (command as any).logger = mockLogger;
    (command as any).teamConfigBlock = mockTeamConfigBlock;
  });

  describe('handle', () => {
    it('should open a modal view with team configuration', async () => {
      const mockClient = {
        views: {
          open: vi.fn().mockResolvedValue({ ok: true }),
        },
      };

      const mockArgs = {
        command: {
          channel_id: 'C12345',
          channel_name: 'test-channel',
          user_id: 'U12345',
          user_name: 'test-user',
        },
        client: mockClient,
        body: {
          trigger_id: 'trigger-123',
          response_url: 'https://hooks.slack.com/commands/response_url',
        },
      };

      await command.handle(mockArgs as any);

      expect(mockLogger.log).toHaveBeenCalledWith(
        expect.stringContaining('Team config command received for channel: test-channel [C12345]')
      );

      expect(mockTeamConfigBlock.build).toHaveBeenCalled();

      expect(mockClient.views.open).toHaveBeenCalledWith({
        trigger_id: 'trigger-123',
        view: {
          type: 'modal',
          private_metadata: JSON.stringify({
            channelId: 'C12345',
            ephemeralResponseUrl: 'https://hooks.slack.com/commands/response_url',
          }),
          callback_id: 'team_config_modal_submit',
          title: {
            type: 'plain_text',
            text: 'Configure Team',
            emoji: true,
          },
          blocks: expect.any(Array),
          submit: {
            type: 'plain_text',
            text: 'Save',
          },
          close: {
            type: 'plain_text',
            text: 'Cancel',
          },
        },
      });
    });

    it('should handle errors when opening modal view', async () => {
      const mockClient = {
        views: {
          open: vi.fn().mockRejectedValue(new Error('Failed to open modal')),
        },
      };

      const mockArgs = {
        command: {
          channel_id: 'C12345',
          channel_name: 'test-channel',
          user_id: 'U12345',
          user_name: 'test-user',
        },
        client: mockClient,
        body: {
          trigger_id: 'trigger-123',
          response_url: 'https://hooks.slack.com/commands/response_url',
        },
      };

      await expect(command.handle(mockArgs as any)).rejects.toThrow('Failed to open modal');

      expect(mockLogger.log).toHaveBeenCalledWith(
        expect.stringContaining('Team config command received for channel: test-channel [C12345]')
      );

      expect(mockTeamConfigBlock.build).toHaveBeenCalled();
      expect(mockClient.views.open).toHaveBeenCalled();
    });
  });
});
