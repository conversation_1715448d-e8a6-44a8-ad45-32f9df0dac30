import { describe, expect, it } from 'vitest';
import { CustomFieldType } from '../../../../src/slack/constants/custom-field-types.enum';
import { validateFieldValue } from '../../../../src/slack/utils/form-field-validation.utils';

describe('form-field-validation.utils', () => {
  describe('validateFieldValue', () => {
    it('should validate text fields with minLength and maxLength', () => {
      // Create a field with minLength and maxLength metadata
      const field = {
        id: 'field1',
        name: 'Text Field',
        type: CustomFieldType.SINGLE_LINE,
        mandatoryOnCreation: false,
        metadata: {
          minLength: 5,
          maxLength: 10,
        },
      };

      // Test valid value
      const validResult = validateFieldValue(field, 'Hello');
      expect(validResult.isValid).toBe(true);
      expect(validResult.errorMessage).toBeNull();

      // Test too short value
      const tooShortResult = validateFieldValue(field, 'Hi');
      expect(tooShortResult.isValid).toBe(false);
      expect(tooShortResult.errorMessage).toContain('at least 5 characters');

      // Test too long value
      const tooLongResult = validateFieldValue(
        field,
        'Hello World This Is Too Long',
      );
      expect(tooLongResult.isValid).toBe(false);
      expect(tooLongResult.errorMessage).toContain(
        'cannot exceed 10 characters',
      );
    });

    it('should validate URL fields with requireHttps', () => {
      // Create a field with requireHttps metadata
      const field = {
        id: 'field2',
        name: 'Website',
        type: CustomFieldType.URL,
        mandatoryOnCreation: false,
        metadata: {
          requireHttps: true,
        },
      };

      // Test valid HTTPS URL
      const validResult = validateFieldValue(field, 'https://example.com');
      expect(validResult.isValid).toBe(true);
      expect(validResult.errorMessage).toBeNull();

      // Test invalid HTTP URL
      const invalidResult = validateFieldValue(field, 'http://example.com');
      expect(invalidResult.isValid).toBe(false);
      expect(invalidResult.errorMessage).toContain('must be a secure URL');
    });

    it('should validate phone number fields with allowInternational', () => {
      // Create a field with allowInternational metadata
      const field = {
        id: 'field3',
        name: 'Phone',
        type: CustomFieldType.PHONE_NUMBER,
        mandatoryOnCreation: false,
        metadata: {
          allowInternational: false,
        },
      };

      // Test valid US phone number
      const validResult = validateFieldValue(field, '(*************');
      expect(validResult.isValid).toBe(true);
      expect(validResult.errorMessage).toBeNull();

      // Test invalid international phone number
      const invalidResult = validateFieldValue(field, '+44 20 1234 5678');
      expect(invalidResult.isValid).toBe(false);
      expect(invalidResult.errorMessage).toContain(
        'must be a valid US phone number',
      );
    });

    it('should validate password fields with custom requirements', () => {
      // Create a field with password requirements metadata
      const field = {
        id: 'field4',
        name: 'Password',
        type: CustomFieldType.PASSWORD,
        mandatoryOnCreation: false,
        metadata: {
          minLength: 10,
          requireUppercase: true,
          requireLowercase: true,
          requireNumbers: true,
          requireSpecialChars: true,
        },
      };

      // Test valid password
      const validResult = validateFieldValue(field, 'Password123!');
      expect(validResult.isValid).toBe(true);
      expect(validResult.errorMessage).toBeNull();

      // Test invalid password (missing special character)
      const invalidResult = validateFieldValue(field, 'Password123');
      expect(invalidResult.isValid).toBe(false);
      expect(invalidResult.errorMessage).toContain('must include');
      expect(invalidResult.errorMessage).toContain('special characters');
    });

    it('should skip validation for empty non-mandatory fields', () => {
      const field = {
        id: 'field5',
        name: 'Optional Field',
        type: CustomFieldType.SINGLE_LINE,
        mandatoryOnCreation: false,
      };

      const result = validateFieldValue(field, '');
      expect(result.isValid).toBe(true);
      expect(result.errorMessage).toBeNull();
    });

    it('should validate mandatory fields', () => {
      const field = {
        id: 'field6',
        name: 'Required Field',
        type: CustomFieldType.SINGLE_LINE,
        mandatoryOnCreation: true,
      };

      const result = validateFieldValue(field, '');
      expect(result.isValid).toBe(false);
      expect(result.errorMessage).toBe('This field is required');
    });

    it('should validate time fields with Slack timepicker format', () => {
      const field = {
        id: 'field7',
        name: 'Time Field',
        type: CustomFieldType.TIME,
        mandatoryOnCreation: false,
      };

      // Valid time in HH:MM format
      const validResult = validateFieldValue(field, '13:30');
      expect(validResult.isValid).toBe(true);
      expect(validResult.errorMessage).toBeNull();

      // Valid time from Slack timepicker
      const validSlackResult = validateFieldValue(field, {
        selected_time: '13:30',
      });
      expect(validSlackResult.isValid).toBe(true);
      expect(validSlackResult.errorMessage).toBeNull();

      // Invalid time format
      const invalidResult = validateFieldValue(field, '1:30');
      expect(invalidResult.isValid).toBe(false);
      expect(invalidResult.errorMessage).toContain('must be a valid time');

      // Invalid Slack timepicker format
      const invalidSlackResult = validateFieldValue(field, {
        selected_time: '25:00',
      });
      expect(invalidSlackResult.isValid).toBe(false);
      expect(invalidSlackResult.errorMessage).toContain('must be a valid time');
    });
  });
});
