import { describe, expect, it } from 'vitest';
import {
  isValidEmail,
  isValidPassword,
  isValidPhoneNumber,
  isValidString,
  isValidTime,
  isValidUrl,
} from '../../../../src/slack/utils/field-validation.utils';

describe('field-validation.utils', () => {
  describe('isValidString', () => {
    it('should validate basic strings', () => {
      expect(isValidString('hello')).toBe(true);
      expect(isValidString('')).toBe(true);
      expect(isValidString(null)).toBe(false);
      expect(isValidString(undefined)).toBe(false);
      expect(isValidString(123 as any)).toBe(false);
    });

    it('should validate strings with minLength option', () => {
      expect(isValidString('hello', { minLength: 3 })).toBe(true);
      expect(isValidString('hi', { minLength: 3 })).toBe(false);
      expect(isValidString('', { minLength: 1 })).toBe(false);
    });

    it('should validate strings with maxLength option', () => {
      expect(isValidString('hello', { maxLength: 10 })).toBe(true);
      expect(isValidString('hello world', { maxLength: 10 })).toBe(false);
    });

    it('should validate strings with pattern option', () => {
      expect(isValidString('abc123', { pattern: '^[a-z0-9]+$' })).toBe(true);
      expect(isValidString('ABC123', { pattern: '^[a-z0-9]+$' })).toBe(false);
      expect(isValidString('hello', { pattern: /^h.*o$/ })).toBe(true);
    });

    it('should validate strings with multiple options', () => {
      expect(
        isValidString('abc123', {
          minLength: 3,
          maxLength: 10,
          pattern: '^[a-z0-9]+$',
        }),
      ).toBe(true);

      expect(
        isValidString('ab', {
          minLength: 3,
          maxLength: 10,
          pattern: '^[a-z0-9]+$',
        }),
      ).toBe(false);

      expect(
        isValidString('abcdefghijk', {
          minLength: 3,
          maxLength: 10,
          pattern: '^[a-z0-9]+$',
        }),
      ).toBe(false);

      expect(
        isValidString('abc-123', {
          minLength: 3,
          maxLength: 10,
          pattern: '^[a-z0-9]+$',
        }),
      ).toBe(false);
    });
  });

  describe('isValidPhoneNumber', () => {
    it('should validate basic phone numbers', () => {
      expect(isValidPhoneNumber('************')).toBe(true);
      expect(isValidPhoneNumber('(*************')).toBe(true);
      expect(isValidPhoneNumber('+1 ************')).toBe(true);
      expect(isValidPhoneNumber('123')).toBe(false);
      expect(isValidPhoneNumber('abcdefghij')).toBe(false);
    });

    it('should validate phone numbers with allowInternational option', () => {
      expect(
        isValidPhoneNumber('+44 20 1234 5678', { allowInternational: true }),
      ).toBe(true);
      expect(
        isValidPhoneNumber('+44 20 1234 5678', { allowInternational: false }),
      ).toBe(false);
    });

    it('should validate phone numbers with minLength and maxLength options', () => {
      expect(isValidPhoneNumber('************', { minLength: 10 })).toBe(true);
      expect(isValidPhoneNumber('123-456-789', { minLength: 10 })).toBe(false);
      expect(isValidPhoneNumber('************', { maxLength: 15 })).toBe(true);
      expect(
        isValidPhoneNumber('+**************** ext 123', { maxLength: 15 }),
      ).toBe(false);
    });
  });

  describe('isValidUrl', () => {
    it('should validate basic URLs', () => {
      expect(isValidUrl('http://example.com')).toBe(true);
      expect(isValidUrl('https://example.com')).toBe(true);
      expect(isValidUrl('example.com')).toBe(false);
      expect(isValidUrl('not a url')).toBe(false);
    });

    it('should validate URLs with requireHttps option', () => {
      expect(isValidUrl('https://example.com', { requireHttps: true })).toBe(
        true,
      );
      expect(isValidUrl('http://example.com', { requireHttps: true })).toBe(
        false,
      );
    });

    it('should validate URLs with allowedProtocols option', () => {
      expect(
        isValidUrl('https://example.com', { allowedProtocols: ['https:'] }),
      ).toBe(true);
      expect(
        isValidUrl('http://example.com', { allowedProtocols: ['https:'] }),
      ).toBe(false);
      expect(
        isValidUrl('ftp://example.com', {
          allowedProtocols: ['ftp:', 'sftp:'],
        }),
      ).toBe(true);
    });

    it('should validate URLs with requireTld option', () => {
      expect(isValidUrl('http://example.com', { requireTld: true })).toBe(true);
      expect(isValidUrl('http://localhost', { requireTld: true })).toBe(false);
      expect(isValidUrl('http://localhost', { requireTld: false })).toBe(true);
    });
  });

  describe('isValidTime', () => {
    it('should validate basic time strings', () => {
      expect(isValidTime('13:30')).toBe(true);
      expect(isValidTime('00:00')).toBe(true);
      expect(isValidTime('23:59')).toBe(true);
      expect(isValidTime('24:00')).toBe(false);
      expect(isValidTime('13:60')).toBe(false);
      expect(isValidTime('1:30')).toBe(false); // Must be 01:30
      expect(isValidTime('not a time')).toBe(false);
      expect(isValidTime('')).toBe(false);
      expect(isValidTime(null)).toBe(false);
      expect(isValidTime(undefined)).toBe(false);
    });

    it('should validate Slack timepicker objects', () => {
      expect(isValidTime({ selected_time: '13:30' })).toBe(true);
      expect(isValidTime({ selected_time: '00:00' })).toBe(true);
      expect(isValidTime({ selected_time: '23:59' })).toBe(true);
      expect(isValidTime({ selected_time: '24:00' })).toBe(false);
      expect(isValidTime({ selected_time: '13:60' })).toBe(false);
      expect(isValidTime({ selected_time: 'not a time' })).toBe(false);
      expect(isValidTime({ selected_time: '' })).toBe(false);
      expect(isValidTime({ selected_time: null })).toBe(false);
      expect(isValidTime({ not_time_field: '13:30' })).toBe(false);
    });

    it('should respect allowSlackFormat option', () => {
      expect(
        isValidTime({ selected_time: '13:30' }, { allowSlackFormat: true }),
      ).toBe(true);
      expect(
        isValidTime({ selected_time: '13:30' }, { allowSlackFormat: false }),
      ).toBe(false);
    });
  });

  describe('isValidPassword', () => {
    it('should validate basic passwords', () => {
      expect(isValidPassword('Password123')).toBe(true);
      expect(isValidPassword('pass')).toBe(false);
      expect(isValidPassword('password')).toBe(false);
      expect(isValidPassword('PASSWORD')).toBe(false);
      expect(isValidPassword('12345678')).toBe(false);
    });

    it('should validate passwords with minLength and maxLength options', () => {
      expect(isValidPassword('Pass123', { minLength: 6 })).toBe(true);
      expect(isValidPassword('Pass1', { minLength: 6 })).toBe(false);
      expect(isValidPassword('Password123', { maxLength: 10 })).toBe(false);
    });

    it('should validate passwords with requireUppercase option', () => {
      expect(isValidPassword('password123', { requireUppercase: false })).toBe(
        true,
      );
      expect(isValidPassword('password123', { requireUppercase: true })).toBe(
        false,
      );
    });

    it('should validate passwords with requireLowercase option', () => {
      expect(isValidPassword('PASSWORD123', { requireLowercase: false })).toBe(
        true,
      );
      expect(isValidPassword('PASSWORD123', { requireLowercase: true })).toBe(
        false,
      );
    });

    it('should validate passwords with requireNumbers option', () => {
      expect(isValidPassword('PasswordABC', { requireNumbers: false })).toBe(
        true,
      );
      expect(isValidPassword('PasswordABC', { requireNumbers: true })).toBe(
        false,
      );
    });

    it('should validate passwords with requireSpecialChars option', () => {
      expect(
        isValidPassword('Password123!', { requireSpecialChars: true }),
      ).toBe(true);
      expect(
        isValidPassword('Password123', { requireSpecialChars: true }),
      ).toBe(false);
    });

    it('should validate passwords with multiple options', () => {
      const options = {
        minLength: 10,
        requireUppercase: true,
        requireLowercase: true,
        requireNumbers: true,
        requireSpecialChars: true,
      };

      expect(isValidPassword('Password123!')).toBe(true);
      expect(isValidPassword('Password123!', options)).toBe(true);
      expect(isValidPassword('password123!', options)).toBe(false); // No uppercase
      expect(isValidPassword('PASSWORD123!', options)).toBe(false); // No lowercase
      expect(isValidPassword('PasswordABC!', options)).toBe(false); // No numbers
      expect(isValidPassword('Password123', options)).toBe(false); // No special chars
      expect(isValidPassword('Pass123!', options)).toBe(false); // Too short
    });
  });
});
