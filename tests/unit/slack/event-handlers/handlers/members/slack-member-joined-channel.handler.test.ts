import { Test, TestingModule } from '@nestjs/testing';
import { Repository } from 'typeorm';
import { describe, beforeEach, afterEach, it, expect, vi } from 'vitest';
import { SlackMemberJoinedChannelHandler } from '../../../../../../src/slack/event-handlers/handlers/members/slack-member-joined-channel.handler';
import { CUSTOM_LOGGER_TOKEN } from '../../../../../../src/utils';
import { Bot<PERSON>hannelJoinedHandler } from '../../../../../../src/slack/core/slack-channel';
import { SlackAppManagementService } from '../../../../../../src/slack/core/management';
import { CustomerContacts, Installations, Users } from '../../../../../../src/database/entities';
import { ChannelsRepository } from '../../../../../../src/database/entities/channels/repositories';
import { ThenaPlatformApiProvider } from '../../../../../../src/external/provider/thena-platform-api.provider';
import { ThenaAppsPlatformApiProvider } from '../../../../../../src/external/provider/thena-apps-platform-api.provider';
import { EmittableSlackEvents } from '../../../../../../src/external/provider/constants/platform-events.constants';
import { SlackEventMap } from '../../../../../../src/slack/event-handlers/interface';

describe('SlackMemberJoinedChannelHandler', () => {
  let handler: SlackMemberJoinedChannelHandler;
  let botChannelJoinedHandler: BotChannelJoinedHandler;
  let slackAppManagementService: SlackAppManagementService;
  let userRepository: Repository<Users>;
  let customerContactRepository: Repository<CustomerContacts>;
  let channelsRepository: ChannelsRepository;
  let thenaPlatformApiProvider: ThenaPlatformApiProvider;
  let thenaAppsPlatformApiProvider: ThenaAppsPlatformApiProvider;
  let mockLogger: any;

  const mockInstallation = {
    id: 'installation-id',
    botToken: 'xoxb-123456',
    teamId: 'team1',
    name: 'Test Workspace',
    organization: { id: 'org1' },
    platformDump: {
      customFields: {
        slackChannelId: 'channel-id-field',
        slackChannelName: 'channel-name-field',
      },
      customObjects: {
        contactCustomObjectId: 'contact-custom-object-id',
      },
    },
  } as Installations;

  beforeEach(async () => {
    mockLogger = {
      log: vi.fn(),
      debug: vi.fn(),
      error: vi.fn(),
      warn: vi.fn(),
    };

    botChannelJoinedHandler = {
      handleEvent: vi.fn().mockResolvedValue(false),
    } as any;

    slackAppManagementService = {
      upsertPersonWithIdentification: vi.fn(),
    } as any;

    userRepository = {
      findOne: vi.fn(),
    } as any;

    customerContactRepository = {
      findOne: vi.fn(),
      update: vi.fn(),
    } as any;

    channelsRepository = {
      findByCondition: vi.fn(),
    } as any;

    thenaPlatformApiProvider = {
      getCustomerContactsByIds: vi.fn(),
      updateCustomerContact: vi.fn(),
      createCustomerContact: vi.fn(),
      createCustomObjectRecord: vi.fn(),
    } as any;

    thenaAppsPlatformApiProvider = {
      postEventsToPlatform: vi.fn(),
    } as any;

    // Create the handler instance directly instead of using the DI container
    handler = new SlackMemberJoinedChannelHandler(
      mockLogger,
      botChannelJoinedHandler,
      slackAppManagementService,
      userRepository as any,
      customerContactRepository as any,
      channelsRepository,
      thenaPlatformApiProvider,
      thenaAppsPlatformApiProvider
    );

    // Mock implementation of the findPerson method
    (handler as any).findPerson = vi.fn();
    
    // Mock implementation of the syncPlatform method
    (handler as any).syncPlatform = vi.fn().mockResolvedValue(undefined);
    
    // Mock the postEventsToPlatform method
    thenaAppsPlatformApiProvider.postEventsToPlatform = vi.fn().mockResolvedValue(undefined);
  });

  afterEach(() => {
    vi.clearAllMocks();
  });

  describe('canHandle', () => {
    it('should return true if event type is member_joined_channel', () => {
      const event = {
        event: {
          type: 'member_joined_channel',
          user: 'U12345',
          channel: 'C12345',
          team: 'T12345',
        },
      };

      expect(handler.canHandle(event as any)).toBe(true);
    });

    it('should return false if event type is not member_joined_channel', () => {
      const event = {
        event: {
          type: 'some_other_event',
        },
      };

      expect(handler.canHandle(event as any)).toBe(false);
    });
  });

  describe('handle', () => {
    it('should handle when the Thena bot joins a channel', async () => {
      // Arrange
      const mockEvent = {
        context: {
          installation: mockInstallation,
          organization: mockInstallation.organization,
        },
        event: {
          type: 'member_joined_channel',
          user: 'U12345',
          channel: 'C12345',
          team: 'team1',
          event_ts: '**********.123456',
        },
      } as SlackEventMap['member_joined_channel'];

      // Mock bot channel joined handler to return true (indicating it was the bot)
      vi.spyOn(botChannelJoinedHandler, 'handleEvent').mockResolvedValue(true);

      // Act
      await handler.handle(mockEvent);

      // Assert
      expect(botChannelJoinedHandler.handleEvent).toHaveBeenCalledWith(mockEvent);
      expect(mockLogger.debug).toHaveBeenCalled();
      // Verify that no further processing was done
      expect(channelsRepository.findByCondition).not.toHaveBeenCalled();
      expect(thenaAppsPlatformApiProvider.postEventsToPlatform).not.toHaveBeenCalled();
    });

    it('should handle when a regular user joins a channel', async () => {
      // Arrange
      const mockEvent = {
        context: {
          installation: mockInstallation,
          organization: mockInstallation.organization,
        },
        event: {
          type: 'member_joined_channel',
          user: 'U12345',
          channel: 'C12345',
          team: 'team1',
          event_ts: '**********.123456',
        },
      } as SlackEventMap['member_joined_channel'];

      // Mock bot channel joined handler to return false (indicating it was not the bot)
      vi.spyOn(botChannelJoinedHandler, 'handleEvent').mockResolvedValue(false);

      // Mock channel lookup
      const mockChannel = {
        id: 'db-channel-id',
        channelId: 'C12345',
        name: 'general',
      } as any;
      vi.spyOn(channelsRepository, 'findByCondition').mockResolvedValue(mockChannel);

      // Mock user lookup - user exists
      const mockUser = {
        id: 'user-id',
        slackId: 'U12345',
        name: 'Test User',
      } as any;
      
      // Mock the findPerson implementation to return a user
      (handler as any).findPerson.mockResolvedValue({
        type: 'user',
        person: mockUser
      });

      // Act
      await handler.handle(mockEvent);

      // Assert
      expect(botChannelJoinedHandler.handleEvent).toHaveBeenCalledWith(mockEvent);
      expect(channelsRepository.findByCondition).toHaveBeenCalledWith({
        where: { channelId: 'C12345', installation: { id: 'installation-id' } },
      });
      expect((handler as any).findPerson).toHaveBeenCalledWith('U12345', mockInstallation);
      expect(thenaAppsPlatformApiProvider.postEventsToPlatform).toHaveBeenCalledWith(
        mockInstallation.organization,
        expect.objectContaining({
          type: EmittableSlackEvents.MEMBER_JOINED_CHANNEL,
        })
      );
    });

    it('should handle when a customer contact joins a channel', async () => {
      // Arrange
      const mockEvent = {
        context: {
          installation: mockInstallation,
          organization: mockInstallation.organization,
        },
        event: {
          type: 'member_joined_channel',
          user: 'U12345',
          channel: 'C12345',
          team: 'team1',
          event_ts: '**********.123456',
        },
      } as SlackEventMap['member_joined_channel'];

      // Mock bot channel joined handler to return false (indicating it was not the bot)
      vi.spyOn(botChannelJoinedHandler, 'handleEvent').mockResolvedValue(false);

      // Mock channel lookup
      const mockChannel = {
        id: 'db-channel-id',
        channelId: 'C12345',
        name: 'general',
      } as any;
      vi.spyOn(channelsRepository, 'findByCondition').mockResolvedValue(mockChannel);

      // Mock customer contact lookup - customer contact exists
      const mockCustomerContact = {
        id: 'customer-id',
        slackId: 'U12345',
        name: 'Customer User',
        channels: [],
        platformDump: { customerContactId: 'platform-customer-id' },
      } as any;

      // Mock the findPerson implementation to return a customer contact
      (handler as any).findPerson.mockResolvedValue({
        type: 'customer',
        person: mockCustomerContact
      });

      // Mock platform API calls
      vi.spyOn(thenaPlatformApiProvider, 'getCustomerContactsByIds').mockResolvedValue([
        {
          id: 'platform-customer-id',
          firstName: 'Customer',
          lastName: 'User',
          metadata: { sinks: { slack: { channels: [] } } },
        } as any,
      ]);

      // Mock the syncPlatform method to avoid calling it directly
      (handler as any).syncPlatform = vi.fn().mockResolvedValue(undefined);

      // Act
      await handler.handle(mockEvent);

      // Assert
      expect(botChannelJoinedHandler.handleEvent).toHaveBeenCalledWith(mockEvent);
      expect(channelsRepository.findByCondition).toHaveBeenCalledWith({
        where: { channelId: 'C12345', installation: { id: 'installation-id' } },
      });
      expect((handler as any).findPerson).toHaveBeenCalledWith('U12345', mockInstallation);
      expect((handler as any).syncPlatform).toHaveBeenCalled();
      expect(customerContactRepository.update).toHaveBeenCalledWith(
        'customer-id',
        expect.objectContaining({
          channels: [mockChannel],
        })
      );
      expect(thenaAppsPlatformApiProvider.postEventsToPlatform).toHaveBeenCalledWith(
        mockInstallation.organization,
        expect.objectContaining({
          type: EmittableSlackEvents.MEMBER_JOINED_CHANNEL,
        })
      );
    });

    it.todo('should handle when a new user joins a channel and needs to be created', async () => {
      // Arrange
      const mockEvent = {
        context: {
          installation: mockInstallation,
          organization: mockInstallation.organization,
        },
        event: {
          type: 'member_joined_channel',
          user: 'U12345',
          channel: 'C12345',
          team: 'team1',
          event_ts: '**********.123456',
        },
      } as SlackEventMap['member_joined_channel'];

      // Mock bot channel joined handler to return false (indicating it was not the bot)
      vi.spyOn(botChannelJoinedHandler, 'handleEvent').mockResolvedValue(false);

      // Mock channel lookup
      const mockChannel = {
        id: 'db-channel-id',
        channelId: 'C12345',
        name: 'general',
      } as any;
      vi.spyOn(channelsRepository, 'findByCondition').mockResolvedValue(mockChannel);

      // Mock user lookup - user doesn't exist
      (handler as any).findPerson.mockResolvedValue(null);

      // Mock the upsertPersonWithIdentification method to create a new user
      const mockUser = {
        id: 'new-user-id',
        slackId: 'U12345',
        name: 'New User',
      } as any;
      vi.spyOn(slackAppManagementService, 'upsertPersonWithIdentification').mockResolvedValue(mockUser);

      // Act
      await handler.handle(mockEvent);

      // Assert
      expect(botChannelJoinedHandler.handleEvent).toHaveBeenCalledWith(mockEvent);
      expect(channelsRepository.findByCondition).toHaveBeenCalledWith({
        where: { channelId: 'C12345', installation: { id: 'installation-id' } },
      });
      expect((handler as any).findPerson).toHaveBeenCalledWith('U12345', mockInstallation);
      expect(slackAppManagementService.upsertPersonWithIdentification).toHaveBeenCalledWith(
        'U12345',
        mockInstallation,
        mockChannel
      );
      expect(thenaAppsPlatformApiProvider.postEventsToPlatform).toHaveBeenCalledWith(
        mockInstallation.organization,
        expect.objectContaining({
          type: EmittableSlackEvents.MEMBER_JOINED_CHANNEL,
        })
      );
    });

    it.todo('should handle when a new customer contact joins a channel and needs to be created', async () => {
      // Arrange
      const mockEvent = {
        context: {
          installation: mockInstallation,
          organization: mockInstallation.organization,
        },
        event: {
          type: 'member_joined_channel',
          user: 'U12345',
          channel: 'C12345',
          team: 'team1',
          event_ts: '**********.123456',
        },
      } as SlackEventMap['member_joined_channel'];

      // Mock bot channel joined handler to return false (indicating it was not the bot)
      vi.spyOn(botChannelJoinedHandler, 'handleEvent').mockResolvedValue(false);

      // Mock channel lookup
      const mockChannel = {
        id: 'db-channel-id',
        channelId: 'C12345',
        name: 'general',
      } as any;
      vi.spyOn(channelsRepository, 'findByCondition').mockResolvedValue(mockChannel);

      // Mock user lookup - user doesn't exist
      (handler as any).findPerson.mockResolvedValue(null);

      // Mock the upsertPersonWithIdentification method to create a new customer contact
      const mockCustomerContact = {
        id: 'new-customer-id',
        slackId: 'U12345',
        name: 'New Customer',
      } as any;
      vi.spyOn(slackAppManagementService, 'upsertPersonWithIdentification').mockResolvedValue(mockCustomerContact);

      // Mock the constructing of the result in handle method
      (handler as any).syncPlatform = vi.fn().mockResolvedValue(undefined);

      // Act
      await handler.handle(mockEvent);

      // Assert
      expect(botChannelJoinedHandler.handleEvent).toHaveBeenCalledWith(mockEvent);
      expect(channelsRepository.findByCondition).toHaveBeenCalledWith({
        where: { channelId: 'C12345', installation: { id: 'installation-id' } },
      });
      expect((handler as any).findPerson).toHaveBeenCalledWith('U12345', mockInstallation);
      expect(slackAppManagementService.upsertPersonWithIdentification).toHaveBeenCalledWith(
        'U12345',
        mockInstallation,
        mockChannel
      );
      expect(thenaAppsPlatformApiProvider.postEventsToPlatform).toHaveBeenCalledWith(
        mockInstallation.organization,
        expect.objectContaining({
          type: EmittableSlackEvents.MEMBER_JOINED_CHANNEL,
        })
      );
    });

    it('should handle errors during platform sync', async () => {
      // Arrange
      const mockEvent = {
        context: {
          installation: mockInstallation,
          organization: mockInstallation.organization,
        },
        event: {
          type: 'member_joined_channel',
          user: 'U12345',
          channel: 'C12345',
          team: 'team1',
          event_ts: '**********.123456',
        },
      } as SlackEventMap['member_joined_channel'];

      // Mock bot channel joined handler to return false (indicating it was not the bot)
      vi.spyOn(botChannelJoinedHandler, 'handleEvent').mockResolvedValue(false);

      // Mock channel lookup
      const mockChannel = {
        id: 'db-channel-id',
        channelId: 'C12345',
        name: 'general',
      } as any;
      vi.spyOn(channelsRepository, 'findByCondition').mockResolvedValue(mockChannel);

      // Mock customer contact lookup - customer contact exists
      const mockCustomerContact = {
        id: 'customer-id',
        slackId: 'U12345',
        name: 'Customer User',
        channels: [],
        platformDump: { customerContactId: 'platform-customer-id' },
      } as any;

      // Mock the findPerson implementation to return a customer contact
      (handler as any).findPerson.mockResolvedValue({
        type: 'customer',
        person: mockCustomerContact
      });

      // Mock platform API calls to throw an error
      (handler as any).syncPlatform = vi.fn().mockRejectedValue(new Error('Platform sync failed'));
      
      // Need to ensure postEventsToPlatform gets called for this test
      thenaAppsPlatformApiProvider.postEventsToPlatform = vi.fn().mockResolvedValue(undefined);

      // Act
      await handler.handle(mockEvent);

      // Assert
      expect(botChannelJoinedHandler.handleEvent).toHaveBeenCalledWith(mockEvent);
      expect(channelsRepository.findByCondition).toHaveBeenCalledWith({
        where: { channelId: 'C12345', installation: { id: 'installation-id' } },
      });
      expect((handler as any).findPerson).toHaveBeenCalledWith('U12345', mockInstallation);
      expect((handler as any).syncPlatform).toHaveBeenCalled();
      expect(mockLogger.error).toHaveBeenCalled();
      expect(thenaAppsPlatformApiProvider.postEventsToPlatform).toHaveBeenCalledWith(
        mockInstallation.organization,
        expect.objectContaining({
          type: EmittableSlackEvents.MEMBER_JOINED_CHANNEL,
        })
      );
    });

    it('should handle general errors', async () => {
      // Arrange
      const mockEvent = {
        context: {
          installation: mockInstallation,
          organization: mockInstallation.organization,
        },
        event: {
          type: 'member_joined_channel',
          user: 'U12345',
          channel: 'C12345',
          team: 'team1',
          event_ts: '**********.123456',
        },
      } as SlackEventMap['member_joined_channel'];

      // Mock bot channel joined handler to throw an error
      vi.spyOn(botChannelJoinedHandler, 'handleEvent').mockRejectedValue(new Error('Test error'));

      // Act
      await handler.handle(mockEvent);

      // Assert
      expect(mockLogger.error).toHaveBeenCalled();
    });
  });
});
