import { Test, TestingModule } from '@nestjs/testing';
import { Repository } from 'typeorm';
import { describe, beforeEach, afterEach, it, expect, vi } from 'vitest';
import { SlackMemberLeftChannelHandler } from '../../../../../../src/slack/event-handlers/handlers/members/slack-member-left-channel.handler';
import { CUSTOM_LOGGER_TOKEN } from '../../../../../../src/utils';
import { CustomerContacts, Installations, Users } from '../../../../../../src/database/entities';
import { ChannelsRepository } from '../../../../../../src/database/entities/channels/repositories';
import { ThenaPlatformApiProvider } from '../../../../../../src/external/provider/thena-platform-api.provider';
import { ThenaAppsPlatformApiProvider } from '../../../../../../src/external/provider/thena-apps-platform-api.provider';
import { EmittableSlackEvents } from '../../../../../../src/external/provider/constants/platform-events.constants';
import { SlackEventMap } from '../../../../../../src/slack/event-handlers/interface';

describe('SlackMemberLeftChannelHandler', () => {
  let handler: SlackMemberLeftChannelHandler;
  let userRepository: Repository<Users>;
  let customerContactRepository: Repository<CustomerContacts>;
  let channelsRepository: ChannelsRepository;
  let thenaPlatformApiProvider: ThenaPlatformApiProvider;
  let thenaAppsPlatformApiProvider: ThenaAppsPlatformApiProvider;
  let mockLogger: any;

  const mockInstallation = {
    id: 'installation-id',
    botToken: 'xoxb-123456',
    teamId: 'team1',
    name: 'Test Workspace',
    organization: { id: 'org1' },
    platformDump: {
      customFields: {
        slackChannelId: 'channel-id-field',
      },
      customObjects: {
        contactCustomObjectId: 'contact-custom-object-id',
      },
    },
  } as Installations;

  beforeEach(async () => {
    mockLogger = {
      log: vi.fn(),
      debug: vi.fn(),
      error: vi.fn(),
      warn: vi.fn(),
    };

    userRepository = {
      findOne: vi.fn(),
    } as any;

    customerContactRepository = {
      findOne: vi.fn(),
      update: vi.fn(),
    } as any;

    channelsRepository = {
      findByCondition: vi.fn(),
    } as any;

    thenaPlatformApiProvider = {
      getCustomerContactsByIds: vi.fn(),
      updateCustomerContact: vi.fn(),
      getCustomObjectRecordsByIds: vi.fn(),
      deleteCustomObjectRecord: vi.fn(),
    } as any;

    thenaAppsPlatformApiProvider = {
      postEventsToPlatform: vi.fn(),
    } as any;

    // Create the handler instance directly instead of using the DI container
    handler = new SlackMemberLeftChannelHandler(
      mockLogger,
      customerContactRepository as any,
      userRepository as any,
      channelsRepository,
      thenaPlatformApiProvider,
      thenaAppsPlatformApiProvider
    );
  });

  afterEach(() => {
    vi.clearAllMocks();
  });

  describe('canHandle', () => {
    it('should return true if event type is member_left_channel', () => {
      const event = {
        event: {
          type: 'member_left_channel',
          user: 'U12345',
          channel: 'C12345',
          team: 'T12345',
        },
      };

      expect(handler.canHandle(event as any)).toBe(true);
    });

    it('should return false if event type is not member_left_channel', () => {
      const event = {
        event: {
          type: 'some_other_event',
        },
      };

      expect(handler.canHandle(event as any)).toBe(false);
    });
  });

  describe('handle', () => {
    it('should handle when team ID does not match installation team ID', async () => {
      // Arrange
      const mockEvent = {
        context: {
          installation: mockInstallation,
          organization: mockInstallation.organization,
        },
        event: {
          type: 'member_left_channel',
          user: 'U12345',
          channel: 'C12345',
          team: 'different-team', // Different from installation.teamId
          event_ts: '**********.123456',
        },
      } as SlackEventMap['member_left_channel'];

      // Act
      await handler.handle(mockEvent);

      // Assert
      expect(mockLogger.warn).toHaveBeenCalled();
      expect(channelsRepository.findByCondition).not.toHaveBeenCalled();
      expect(thenaAppsPlatformApiProvider.postEventsToPlatform).not.toHaveBeenCalled();
    });

    it('should handle when user is not found', async () => {
      // Arrange
      const mockEvent = {
        context: {
          installation: mockInstallation,
          organization: mockInstallation.organization,
        },
        event: {
          type: 'member_left_channel',
          user: 'U12345',
          channel: 'C12345',
          team: 'team1', // Same as installation.teamId
          event_ts: '**********.123456',
        },
      } as SlackEventMap['member_left_channel'];

      // Mock channel lookup
      const mockChannel = {
        id: 'db-channel-id',
        channelId: 'C12345',
        name: 'general',
      };
      vi.spyOn(channelsRepository, 'findByCondition').mockResolvedValue(mockChannel);

      // Mock user and customer contact lookups to return null (user doesn't exist)
      vi.spyOn(userRepository, 'findOne').mockResolvedValue(null);
      vi.spyOn(customerContactRepository, 'findOne').mockResolvedValue(null);

      // Act
      await handler.handle(mockEvent);

      // Assert
      expect(channelsRepository.findByCondition).toHaveBeenCalledWith({
        where: { channelId: 'C12345', installation: { id: 'installation-id' } },
      });
      expect(userRepository.findOne).toHaveBeenCalledWith({
        where: { slackId: 'U12345', installation: { id: 'installation-id' } },
      });
      expect(customerContactRepository.findOne).toHaveBeenCalledWith({
        where: { slackId: 'U12345', installation: { id: 'installation-id' } },
      });
      expect(mockLogger.warn).toHaveBeenCalled();
      expect(thenaAppsPlatformApiProvider.postEventsToPlatform).not.toHaveBeenCalled();
    });

    it('should handle when a regular user leaves a channel', async () => {
      // Arrange
      const mockEvent = {
        context: {
          installation: mockInstallation,
          organization: mockInstallation.organization,
        },
        event: {
          type: 'member_left_channel',
          user: 'U12345',
          channel: 'C12345',
          team: 'team1', // Same as installation.teamId
          event_ts: '**********.123456',
        },
      } as SlackEventMap['member_left_channel'];

      // Mock channel lookup
      const mockChannel = {
        id: 'db-channel-id',
        channelId: 'C12345',
        name: 'general',
      };
      vi.spyOn(channelsRepository, 'findByCondition').mockResolvedValue(mockChannel);

      // Mock user lookup - user exists
      const mockUser = {
        id: 'user-id',
        slackId: 'U12345',
        name: 'Test User',
      };
      vi.spyOn(userRepository, 'findOne').mockResolvedValue(mockUser);
      vi.spyOn(customerContactRepository, 'findOne').mockResolvedValue(null);

      // Act
      await handler.handle(mockEvent);

      // Assert
      expect(channelsRepository.findByCondition).toHaveBeenCalledWith({
        where: { channelId: 'C12345', installation: { id: 'installation-id' } },
      });
      expect(userRepository.findOne).toHaveBeenCalledWith({
        where: { slackId: 'U12345', installation: { id: 'installation-id' } },
      });
      expect(customerContactRepository.findOne).toHaveBeenCalledWith({
        where: { slackId: 'U12345', installation: { id: 'installation-id' } },
      });
      expect(thenaAppsPlatformApiProvider.postEventsToPlatform).toHaveBeenCalledWith(
        mockInstallation.organization,
        expect.objectContaining({
          type: EmittableSlackEvents.MEMBER_LEFT_CHANNEL,
        })
      );
    });

    it('should handle when a customer contact leaves a channel', async () => {
      // Arrange
      const mockEvent = {
        context: {
          installation: mockInstallation,
          organization: mockInstallation.organization,
        },
        event: {
          type: 'member_left_channel',
          user: 'U12345',
          channel: 'C12345',
          team: 'team1', // Same as installation.teamId
          event_ts: '**********.123456',
        },
      } as SlackEventMap['member_left_channel'];

      // Mock channel lookup
      const mockChannel = {
        id: 'db-channel-id',
        channelId: 'C12345',
        name: 'general',
      };
      vi.spyOn(channelsRepository, 'findByCondition').mockResolvedValue(mockChannel);

      // Mock customer contact lookup - customer exists
      const mockCustomer = {
        id: 'customer-id',
        slackId: 'U12345',
        name: 'Test Customer',
        platformDump: {
          customerContactId: 'platform-contact-id',
          customObjectRecordIds: ['record1', 'record2'],
        },
      };
      vi.spyOn(customerContactRepository, 'findOne').mockResolvedValue(mockCustomer);
      vi.spyOn(userRepository, 'findOne').mockResolvedValue(null);

      // Mock platform contact lookup
      const mockPlatformContact = {
        id: 'platform-contact-id',
        metadata: {
          sinks: {
            slack: {
              channels: [
                { channelId: 'C12345', channelName: 'general' },
                { channelId: 'C54321', channelName: 'other-channel' },
              ],
            },
          },
        },
      };
      vi.spyOn(thenaPlatformApiProvider, 'getCustomerContactsByIds').mockResolvedValue([mockPlatformContact]);

      // Mock custom object records
      const mockCustomObjectRecords = [
        {
          id: 'record1',
          customFieldValues: [
            {
              customFieldId: 'channel-id-field',
              data: [{ value: 'C12345' }],
            },
          ],
        },
        {
          id: 'record2',
          customFieldValues: [
            {
              customFieldId: 'channel-id-field',
              data: [{ value: 'C54321' }],
            },
          ],
        },
      ];
      vi.spyOn(thenaPlatformApiProvider, 'getCustomObjectRecordsByIds').mockResolvedValue(mockCustomObjectRecords);

      // Act
      await handler.handle(mockEvent);

      // Assert
      expect(channelsRepository.findByCondition).toHaveBeenCalledWith({
        where: { channelId: 'C12345', installation: { id: 'installation-id' } },
      });
      expect(customerContactRepository.findOne).toHaveBeenCalledWith({
        where: { slackId: 'U12345', installation: { id: 'installation-id' } },
      });
      expect(thenaPlatformApiProvider.getCustomerContactsByIds).toHaveBeenCalledWith(
        mockInstallation,
        ['platform-contact-id']
      );
      expect(thenaPlatformApiProvider.updateCustomerContact).toHaveBeenCalledWith(
        mockInstallation,
        'platform-contact-id',
        expect.objectContaining({
          metadata: expect.anything(),
        })
      );
      expect(thenaPlatformApiProvider.getCustomObjectRecordsByIds).toHaveBeenCalledWith(
        mockInstallation,
        'contact-custom-object-id',
        ['record1', 'record2']
      );
      expect(thenaPlatformApiProvider.deleteCustomObjectRecord).toHaveBeenCalledWith(
        mockInstallation,
        'contact-custom-object-id',
        'record1'
      );
      expect(customerContactRepository.update).toHaveBeenCalled();
      expect(thenaAppsPlatformApiProvider.postEventsToPlatform).toHaveBeenCalledWith(
        mockInstallation.organization,
        expect.objectContaining({
          type: EmittableSlackEvents.MEMBER_LEFT_CHANNEL,
        })
      );
    });

    it('should handle errors during platform sync', async () => {
      // Arrange
      const mockEvent = {
        context: {
          installation: mockInstallation,
          organization: mockInstallation.organization,
        },
        event: {
          type: 'member_left_channel',
          user: 'U12345',
          channel: 'C12345',
          team: 'team1', // Same as installation.teamId
          event_ts: '**********.123456',
        },
      } as SlackEventMap['member_left_channel'];

      // Mock channel lookup
      const mockChannel = {
        id: 'db-channel-id',
        channelId: 'C12345',
        name: 'general',
      };
      vi.spyOn(channelsRepository, 'findByCondition').mockResolvedValue(mockChannel);

      // Mock customer contact lookup - customer exists
      const mockCustomer = {
        id: 'customer-id',
        slackId: 'U12345',
        name: 'Test Customer',
        platformDump: {
          customerContactId: 'platform-contact-id',
        },
      };
      vi.spyOn(customerContactRepository, 'findOne').mockResolvedValue(mockCustomer);
      vi.spyOn(userRepository, 'findOne').mockResolvedValue(null);

      // Mock platform API error
      vi.spyOn(thenaPlatformApiProvider, 'getCustomerContactsByIds').mockRejectedValue(new Error('API error'));

      // Act
      await handler.handle(mockEvent);

      // Assert
      expect(channelsRepository.findByCondition).toHaveBeenCalledWith({
        where: { channelId: 'C12345', installation: { id: 'installation-id' } },
      });
      expect(customerContactRepository.findOne).toHaveBeenCalledWith({
        where: { slackId: 'U12345', installation: { id: 'installation-id' } },
      });
      expect(thenaPlatformApiProvider.getCustomerContactsByIds).toHaveBeenCalledWith(
        mockInstallation,
        ['platform-contact-id']
      );
      expect(mockLogger.error).toHaveBeenCalled();
      // Should still post the event to the platform
      expect(thenaAppsPlatformApiProvider.postEventsToPlatform).toHaveBeenCalledWith(
        mockInstallation.organization,
        expect.objectContaining({
          type: EmittableSlackEvents.MEMBER_LEFT_CHANNEL,
        })
      );
    });

    it('should handle when customer contact has no platform contact ID', async () => {
      // Arrange
      const mockEvent = {
        context: {
          installation: mockInstallation,
          organization: mockInstallation.organization,
        },
        event: {
          type: 'member_left_channel',
          user: 'U12345',
          channel: 'C12345',
          team: 'team1', // Same as installation.teamId
          event_ts: '**********.123456',
        },
      } as SlackEventMap['member_left_channel'];

      // Mock channel lookup
      const mockChannel = {
        id: 'db-channel-id',
        channelId: 'C12345',
        name: 'general',
      };
      vi.spyOn(channelsRepository, 'findByCondition').mockResolvedValue(mockChannel);

      // Mock customer contact lookup - customer exists but has no platform contact ID
      const mockCustomer = {
        id: 'customer-id',
        slackId: 'U12345',
        name: 'Test Customer',
        platformDump: {
          // No customerContactId
        },
      };
      vi.spyOn(customerContactRepository, 'findOne').mockResolvedValue(mockCustomer);
      vi.spyOn(userRepository, 'findOne').mockResolvedValue(null);

      // Act
      await handler.handle(mockEvent);

      // Assert
      expect(channelsRepository.findByCondition).toHaveBeenCalledWith({
        where: { channelId: 'C12345', installation: { id: 'installation-id' } },
      });
      expect(customerContactRepository.findOne).toHaveBeenCalledWith({
        where: { slackId: 'U12345', installation: { id: 'installation-id' } },
      });
      expect(mockLogger.error).toHaveBeenCalled();
      expect(thenaAppsPlatformApiProvider.postEventsToPlatform).toHaveBeenCalledWith(
        mockInstallation.organization,
        expect.objectContaining({
          type: EmittableSlackEvents.MEMBER_LEFT_CHANNEL,
        })
      );
    });

    it('should handle when platform contact is not found', async () => {
      // Arrange
      const mockEvent = {
        context: {
          installation: mockInstallation,
          organization: mockInstallation.organization,
        },
        event: {
          type: 'member_left_channel',
          user: 'U12345',
          channel: 'C12345',
          team: 'team1', // Same as installation.teamId
          event_ts: '**********.123456',
        },
      } as SlackEventMap['member_left_channel'];

      // Mock channel lookup
      const mockChannel = {
        id: 'db-channel-id',
        channelId: 'C12345',
        name: 'general',
      };
      vi.spyOn(channelsRepository, 'findByCondition').mockResolvedValue(mockChannel);

      // Mock customer contact lookup - customer exists
      const mockCustomer = {
        id: 'customer-id',
        slackId: 'U12345',
        name: 'Test Customer',
        platformDump: {
          customerContactId: 'platform-contact-id',
        },
      };
      vi.spyOn(customerContactRepository, 'findOne').mockResolvedValue(mockCustomer);
      vi.spyOn(userRepository, 'findOne').mockResolvedValue(null);

      // Mock platform contact not found
      vi.spyOn(thenaPlatformApiProvider, 'getCustomerContactsByIds').mockResolvedValue([]);

      // Act
      await handler.handle(mockEvent);

      // Assert
      expect(channelsRepository.findByCondition).toHaveBeenCalledWith({
        where: { channelId: 'C12345', installation: { id: 'installation-id' } },
      });
      expect(customerContactRepository.findOne).toHaveBeenCalledWith({
        where: { slackId: 'U12345', installation: { id: 'installation-id' } },
      });
      expect(thenaPlatformApiProvider.getCustomerContactsByIds).toHaveBeenCalledWith(
        mockInstallation,
        ['platform-contact-id']
      );
      expect(mockLogger.error).toHaveBeenCalled();
      expect(thenaAppsPlatformApiProvider.postEventsToPlatform).toHaveBeenCalledWith(
        mockInstallation.organization,
        expect.objectContaining({
          type: EmittableSlackEvents.MEMBER_LEFT_CHANNEL,
        })
      );
    });

    it('should handle general errors', async () => {
      // Arrange
      const mockEvent = {
        context: {
          installation: mockInstallation,
          organization: mockInstallation.organization,
        },
        event: {
          type: 'member_left_channel',
          user: 'U12345',
          channel: 'C12345',
          team: 'team1', // Same as installation.teamId
          event_ts: '**********.123456',
        },
      } as SlackEventMap['member_left_channel'];

      // Mock unexpected error
      vi.spyOn(channelsRepository, 'findByCondition').mockRejectedValue(new Error('Unexpected error'));

      // Act
      await handler.handle(mockEvent);

      // Assert
      expect(channelsRepository.findByCondition).toHaveBeenCalledWith({
        where: { channelId: 'C12345', installation: { id: 'installation-id' } },
      });
      expect(mockLogger.error).toHaveBeenCalled();
      expect(thenaAppsPlatformApiProvider.postEventsToPlatform).not.toHaveBeenCalled();
    });
  });
});
