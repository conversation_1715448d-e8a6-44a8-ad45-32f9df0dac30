import { describe, expect, it } from 'vitest';
import { slackLinkEventHandlers } from '../../../../../../src/slack/event-handlers/handlers/links';
import { SlackLinkSharedHandler } from '../../../../../../src/slack/event-handlers/handlers/links/link-shared.handler';

describe('slackLinkEventHandlers', () => {
  it('should export the SlackLinkSharedHandler', () => {
    expect(slackLinkEventHandlers).toContain(SlackLinkSharedHandler);
  });

  it('should have the correct number of handlers', () => {
    expect(slackLinkEventHandlers).toHaveLength(1);
  });
});
