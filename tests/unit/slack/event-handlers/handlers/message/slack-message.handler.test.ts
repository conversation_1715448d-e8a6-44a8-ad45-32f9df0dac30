import { Mock, beforeEach, describe, expect, it, vi } from 'vitest';
import { AuditLogOp, AuditLogVisibility } from '../../../../../../src/database/constants';
import { SlackAuditLogRepository } from '../../../../../../src/database/entities/slack-audit-logs/repositories/slack-audit-log.repository';
import { EmittableSlackEvents } from '../../../../../../src/external/provider/constants/platform-events.constants';
import { ThenaAppsPlatformApiProvider } from '../../../../../../src/external/provider/thena-apps-platform-api.provider';
import { ILogger } from '../../../../../../src/utils';
import { OnMessageHandler } from '../../../../../../src/slack/core/on-message';
import { SlackEventMap } from '../../../../../../src/slack/event-handlers/interface';
import { SlackMessageHandler } from '../../../../../../src/slack/event-handlers/handlers/message/slack-message.handler';
import { SlackSubTypeHandler } from '../../../../../../src/slack/event-handlers/handlers/message/subtypes';
import { DecoratedSlackEventMiddlewareArgs } from '../../../../../../src/slack/event-handlers/interface';
// Import these types from @slack/bolt
import { SlackEventMiddlewareArgs, EnvelopedEvent } from '@slack/bolt';
import { KnownEventFromType } from '@slack/bolt/dist/types/events';

describe('SlackMessageHandler', () => {
  let handler: SlackMessageHandler;
  let mockLogger: ILogger;
  let mockOnMessageHandler: OnMessageHandler;
  let mockSlackAuditLogRepository: SlackAuditLogRepository;
  let mockThenaAppsPlatformApiProvider: ThenaAppsPlatformApiProvider;
  let mockSlackSubTypeHandler: SlackSubTypeHandler;

  beforeEach(() => {
    vi.resetAllMocks();

    // Mock the logger
    mockLogger = {
      log: vi.fn(),
      error: vi.fn(),
      warn: vi.fn(),
      debug: vi.fn(),
      verbose: vi.fn(),
    } as unknown as ILogger;

    // Mock the OnMessageHandler
    mockOnMessageHandler = {
      onMessage: vi.fn(),
    } as unknown as OnMessageHandler;

    // Mock the SlackAuditLogRepository
    mockSlackAuditLogRepository = {
      recordAuditLog: vi.fn(),
    } as unknown as SlackAuditLogRepository;

    // Mock the ThenaAppsPlatformApiProvider
    mockThenaAppsPlatformApiProvider = {
      postEventsToPlatform: vi.fn(),
    } as unknown as ThenaAppsPlatformApiProvider;

    // Mock the SlackSubTypeHandler
    mockSlackSubTypeHandler = {
      handle: vi.fn(),
    } as unknown as SlackSubTypeHandler;

    // Create the handler
    handler = new SlackMessageHandler(
      mockLogger,
      mockOnMessageHandler,
      mockSlackAuditLogRepository,
      mockThenaAppsPlatformApiProvider,
      mockSlackSubTypeHandler
    );
  });

  describe('canHandle', () => {
    it('should return true if the event has text property', () => {
      // Setup
      const event = {
        event: {
          type: 'message',
          text: 'Hello world',
        },
      } as SlackEventMap['message'];

      // Execute
      const result = handler.canHandle(event);

      // Verify
      expect(result).toBe(true);
    });

    it('should return false if the event does not have text property', () => {
      // Setup
      const event = {
        event: {
          type: 'message',
        },
      } as SlackEventMap['message'];

      // Execute
      const result = handler.canHandle(event);

      // Verify
      expect(result).toBe(false);
    });
  });

  describe('handle', () => {
    it('should handle a message event successfully', async () => {
      // Setup
      const mockEvent = {
        payload: {
          type: 'message',
          text: 'Test message',
          user: 'U**********',
          ts: '**********.123456',
          event_ts: '**********.123456',
          channel: 'C12345',
          subtype: 'message_changed',
          channel_type: 'channel'
        },
        event: {
          type: 'message',
          text: 'Test message',
          user: 'U**********',
          ts: '**********.123456',
          event_ts: '**********.123456',
          channel: 'C12345',
          subtype: 'message_changed',
          channel_type: 'channel'
        },
        message: {
          type: 'message',
          text: 'Test message',
          user: 'U**********',
          ts: '**********.123456',
          event_ts: '**********.123456',
          channel: 'C12345',
          subtype: 'message_changed',
          channel_type: 'channel'
        },
        body: {
          token: 'test-token',
          team_id: 'T**********',
          api_app_id: 'A12345',
          event: {
            type: 'message',
            text: 'Test message',
            user: 'U**********',
            ts: '**********.123456',
            event_ts: '**********.123456',
            channel: 'C12345',
            subtype: 'message_changed',
            channel_type: 'channel'
          },
          type: 'event_callback',
          event_id: 'Ev12345',
          event_time: **********
        },
        context: {
          installation: {
            id: 'test-installation',
            name: 'Test Installation',
            teamId: 'T**********',
            organization: {
              id: 'test-org',
              externalPk: 'test-external-pk',
              name: 'Test Org',
              uid: 'test-uid',
              apiKey: 'test-api-key'
            } as any
          },
          organization: {
            id: 'test-org',
            externalPk: 'test-external-pk',
            name: 'Test Org',
            uid: 'test-uid',
            apiKey: 'test-api-key'
          } as any,
          client: {} as any,
          user: {} as any
        }
      } as unknown as SlackEventMap['message'];

      // Execute
      await handler.handle(mockEvent);

      // Verify
      expect(mockThenaAppsPlatformApiProvider.postEventsToPlatform).toHaveBeenCalledWith(
        mockEvent.context.installation.organization,
        {
          ...mockEvent.body.event,
          type: EmittableSlackEvents.MESSAGE,
        }
      );

      expect(mockSlackAuditLogRepository.recordAuditLog).toHaveBeenCalledWith(
        expect.objectContaining({
          eventTs: mockEvent.body.event.event_ts,
          activityPerformedBy: (mockEvent.body.event as any).user,
          activity: expect.stringContaining(mockEvent.body.event.ts),
          slackTs: mockEvent.body.event.ts,
          op: AuditLogOp.INFO,
          visibility: AuditLogVisibility.ORGANIZATION,
          installation: mockEvent.context.installation,
          organization: mockEvent.context.organization,
        })
      );

      expect(mockOnMessageHandler.onMessage).toHaveBeenCalledWith(mockEvent);
    });

    it('should handle a message with a subtype', async () => {
      // Setup
      const mockEvent = {
        payload: {
          type: 'message',
          text: 'Hello world',
          user: 'U12345',
          ts: '**********.123456',
          event_ts: '**********.123456',
          channel: 'C12345',
          subtype: 'message_changed',
          channel_type: 'channel'
        },
        event: {
          type: 'message',
          text: 'Hello world',
          user: 'U12345',
          ts: '**********.123456',
          event_ts: '**********.123456',
          channel: 'C12345',
          subtype: 'message_changed',
          channel_type: 'channel'
        },
        message: {
          type: 'message',
          text: 'Hello world',
          user: 'U12345',
          ts: '**********.123456',
          event_ts: '**********.123456',
          channel: 'C12345',
          subtype: 'message_changed',
          channel_type: 'channel'
        },
        body: {
          token: 'test-token',
          team_id: 'T12345',
          api_app_id: 'A12345',
          event: {
            type: 'message',
            text: 'Hello world',
            user: 'U12345',
            ts: '**********.123456',
            event_ts: '**********.123456',
            channel: 'C12345',
            subtype: 'message_changed',
            channel_type: 'channel'
          },
          type: 'event_callback',
          event_id: 'Ev12345',
          event_time: **********
        },
        context: {
          installation: {
            id: 'inst-1',
            name: 'Test Installation',
            teamId: 'T12345',
            organization: {
              id: 'org-1',
              externalPk: 'external-1',
              name: 'Org 1',
              uid: 'uid-1',
              apiKey: 'api-key-1'
            } as any
          },
          organization: {
            id: 'org-1',
            externalPk: 'external-1',
            name: 'Org 1',
            uid: 'uid-1',
            apiKey: 'api-key-1'
          } as any,
          client: {} as any,
          user: {} as any
        }
      } as unknown as SlackEventMap['message'];

      (mockSlackSubTypeHandler.handle as Mock).mockResolvedValue(true);

      // Execute
      await handler.handle(mockEvent);

      // Verify
      expect(mockSlackSubTypeHandler.handle).toHaveBeenCalledWith(mockEvent);
      expect(mockOnMessageHandler.onMessage).not.toHaveBeenCalled();
    });

    it('should handle a message with a subtype that is not handled by the subtype handler', async () => {
      // Setup
      const mockEvent = {
        payload: {
          type: 'message',
          text: 'Hello world',
          user: 'U12345',
          ts: '**********.123456',
          event_ts: '**********.123456',
          channel: 'C12345',
          subtype: 'message_changed',
          channel_type: 'channel'
        },
        event: {
          type: 'message',
          text: 'Hello world',
          user: 'U12345',
          ts: '**********.123456',
          event_ts: '**********.123456',
          channel: 'C12345',
          subtype: 'message_changed',
          channel_type: 'channel'
        },
        message: {
          type: 'message',
          text: 'Hello world',
          user: 'U12345',
          ts: '**********.123456',
          event_ts: '**********.123456',
          channel: 'C12345',
          subtype: 'message_changed',
          channel_type: 'channel'
        },
        body: {
          token: 'test-token',
          team_id: 'T12345',
          api_app_id: 'A12345',
          event: {
            type: 'message',
            text: 'Hello world',
            user: 'U12345',
            ts: '**********.123456',
            event_ts: '**********.123456',
            channel: 'C12345',
            subtype: 'message_changed',
            channel_type: 'channel'
          },
          type: 'event_callback',
          event_id: 'Ev12345',
          event_time: **********
        },
        context: {
          installation: {
            id: 'inst-1',
            name: 'Test Installation',
            teamId: 'T12345',
            organization: {
              id: 'org-1',
              externalPk: 'external-1',
              name: 'Org 1',
              uid: 'uid-1',
              apiKey: 'api-key-1'
            } as any
          },
          organization: {
            id: 'org-1',
            externalPk: 'external-1',
            name: 'Org 1',
            uid: 'uid-1',
            apiKey: 'api-key-1'
          } as any,
          client: {} as any,
          user: {} as any
        }
      } as unknown as SlackEventMap['message'];

      (mockSlackSubTypeHandler.handle as Mock).mockResolvedValue(false);

      // Execute
      await handler.handle(mockEvent);

      // Verify
      expect(mockSlackSubTypeHandler.handle).toHaveBeenCalledWith(mockEvent);
      expect(mockOnMessageHandler.onMessage).toHaveBeenCalledWith(mockEvent);
    });

    it('should handle a file_share subtype directly', async () => {
      // Setup
      const mockEvent = {
        payload: {
          type: 'message',
          text: 'Hello world',
          user: 'U12345',
          ts: '**********.123456',
          event_ts: '**********.123456',
          channel: 'C12345',
          subtype: 'file_share',
          channel_type: 'channel'
        },
        event: {
          type: 'message',
          text: 'Hello world',
          user: 'U12345',
          ts: '**********.123456',
          event_ts: '**********.123456',
          channel: 'C12345',
          subtype: 'file_share',
          channel_type: 'channel'
        },
        message: {
          type: 'message',
          text: 'Hello world',
          user: 'U12345',
          ts: '**********.123456',
          event_ts: '**********.123456',
          channel: 'C12345',
          subtype: 'file_share',
          channel_type: 'channel'
        },
        body: {
          token: 'test-token',
          team_id: 'T12345',
          api_app_id: 'A12345',
          event: {
            type: 'message',
            text: 'Hello world',
            user: 'U12345',
            ts: '**********.123456',
            event_ts: '**********.123456',
            channel: 'C12345',
            subtype: 'file_share',
            channel_type: 'channel'
          },
          type: 'event_callback',
          event_id: 'Ev12345',
          event_time: **********
        },
        context: {
          installation: {
            id: 'inst-1',
            name: 'Test Installation',
            teamId: 'T12345',
            organization: {
              id: 'org-1',
              externalPk: 'external-1',
              name: 'Org 1',
              uid: 'uid-1',
              apiKey: 'api-key-1'
            } as any
          },
          organization: {
            id: 'org-1',
            externalPk: 'external-1',
            name: 'Org 1',
            uid: 'uid-1',
            apiKey: 'api-key-1'
          } as any,
          client: {} as any,
          user: {} as any
        }
      } as unknown as SlackEventMap['message'];

      // Execute
      await handler.handle(mockEvent);

      // Verify
      expect(mockSlackSubTypeHandler.handle).not.toHaveBeenCalled();
      expect(mockOnMessageHandler.onMessage).toHaveBeenCalledWith(mockEvent);
    });

    it('should handle errors when posting to platform', async () => {
      // Setup
      const mockEvent = {
        payload: {
          type: 'message',
          text: 'Test message',
          user: 'U**********',
          ts: '**********.123456',
          event_ts: '**********.123456',
          channel: 'C12345',
          subtype: 'message_changed',
          channel_type: 'channel'
        },
        event: {
          type: 'message',
          text: 'Test message',
          user: 'U**********',
          ts: '**********.123456',
          event_ts: '**********.123456',
          channel: 'C12345',
          subtype: 'message_changed',
          channel_type: 'channel'
        },
        message: {
          type: 'message',
          text: 'Test message',
          user: 'U**********',
          ts: '**********.123456',
          event_ts: '**********.123456',
          channel: 'C12345',
          subtype: 'message_changed',
          channel_type: 'channel'
        },
        body: {
          token: 'test-token',
          team_id: 'T**********',
          api_app_id: 'A12345',
          event: {
            type: 'message',
            text: 'Test message',
            user: 'U**********',
            ts: '**********.123456',
            event_ts: '**********.123456',
            channel: 'C12345',
            subtype: 'message_changed',
            channel_type: 'channel'
          },
          type: 'event_callback',
          event_id: 'Ev12345',
          event_time: **********
        },
        context: {
          installation: {
            id: 'test-installation',
            name: 'Test Installation',
            teamId: 'T**********',
            organization: {
              id: 'test-org',
              externalPk: 'test-external-pk',
              name: 'Test Org',
              uid: 'test-uid',
              apiKey: 'test-api-key'
            } as any
          },
          organization: {
            id: 'test-org',
            externalPk: 'test-external-pk',
            name: 'Test Org',
            uid: 'test-uid',
            apiKey: 'test-api-key'
          } as any,
          client: {} as any,
          user: {} as any
        }
      } as unknown as SlackEventMap['message'];

      const error = new Error('Failed to post to platform');
      (mockThenaAppsPlatformApiProvider.postEventsToPlatform as Mock).mockRejectedValue(error);

      // Execute
      await handler.handle(mockEvent);

      // Verify - the error message should match exactly what's in the implementation
      expect(mockLogger.error).toHaveBeenCalledWith(
        '[ts=**********.123456] Error posting message event to the platform: Failed to post to platform'
      );
      expect(mockSlackAuditLogRepository.recordAuditLog).toHaveBeenCalled();
      expect(mockOnMessageHandler.onMessage).toHaveBeenCalledWith(mockEvent);
    });

    it('should handle general errors', async () => {
      // Setup
      const mockEvent = {
        payload: {
          type: 'message',
          text: 'Hello world',
          user: 'U12345',
          ts: '**********.123456',
          event_ts: '**********.123456',
          channel: 'C12345',
          channel_type: 'channel'
        },
        event: {
          type: 'message',
          text: 'Hello world',
          user: 'U12345',
          ts: '**********.123456',
          event_ts: '**********.123456',
          channel: 'C12345',
          channel_type: 'channel'
        },
        message: {
          type: 'message',
          text: 'Hello world',
          user: 'U12345',
          ts: '**********.123456',
          event_ts: '**********.123456',
          channel: 'C12345',
          channel_type: 'channel'
        },
        body: {
          token: 'test-token',
          team_id: 'T12345',
          api_app_id: 'A12345',
          event: {
            type: 'message',
            text: 'Hello world',
            user: 'U12345',
            ts: '**********.123456',
            event_ts: '**********.123456',
            channel: 'C12345',
            channel_type: 'channel'
          },
          type: 'event_callback',
          event_id: 'Ev12345',
          event_time: **********
        },
        context: {
          installation: {
            id: 'inst-1',
            name: 'Test Installation',
            teamId: 'T12345',
            organization: {
              id: 'org-1',
              externalPk: 'external-1',
              name: 'Org 1',
              uid: 'uid-1',
              apiKey: 'api-key-1'
            } as any
          },
          organization: {
            id: 'org-1',
            externalPk: 'external-1',
            name: 'Org 1',
            uid: 'uid-1',
            apiKey: 'api-key-1'
          } as any,
          client: {} as any,
          user: {} as any
        }
      } as unknown as SlackEventMap['message'];

      const error = new Error('General error');
      (mockOnMessageHandler.onMessage as Mock).mockRejectedValue(error);

      // Execute
      await handler.handle(mockEvent);

      // Verify
      expect(mockLogger.error).toHaveBeenCalledWith(
        '[ts=**********.123456] Error handling message event: General error',
        expect.any(String) // Allow any string for stack trace
      );
      expect(mockSlackAuditLogRepository.recordAuditLog).toHaveBeenCalledTimes(2);
    });
  });
});
