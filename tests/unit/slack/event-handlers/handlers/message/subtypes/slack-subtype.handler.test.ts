import { beforeEach, describe, expect, it, vi } from 'vitest';
import { SlackSubTypeHandler } from '../../../../../../../src/slack/event-handlers/handlers/message/subtypes/slack-subtype.handler';
import { MessageChangedHandler } from '../../../../../../../src/slack/event-handlers/handlers/message/subtypes/message-changed.handler';
import { MessageDeletedHandler } from '../../../../../../../src/slack/event-handlers/handlers/message/subtypes/message-deleted.handler';

describe('SlackSubTypeHandler', () => {
  let handler: SlackSubTypeHandler;
  let messageChangedHandler: any;
  let messageDeletedHandler: any;
  let mockLogger: any;

  beforeEach(() => {
    mockLogger = {
      log: vi.fn(),
      debug: vi.fn(),
      error: vi.fn(),
      warn: vi.fn(),
    };

    messageChangedHandler = {
      handle: vi.fn().mockResolvedValue(undefined),
    };

    messageDeletedHandler = {
      handle: vi.fn().mockResolvedValue(undefined),
    };

    handler = new SlackSubTypeHandler(
      mockLogger,
      messageChangedHandler,
      messageDeletedHandler
    );
  });

  describe('handle', () => {
    it('should return undefined if no subtype is found in the event', async () => {
      const event = {
        context: {
          installation: { id: 'test-installation-id' },
          organization: { id: 'test-org-id' },
        },
        event: {
          type: 'message',
          text: 'Hello, world!',
          channel: 'C12345',
          user: 'U12345',
          ts: '1234567890.123456',
          event_ts: '1234567890.123456',
        },
      } as any;

      const result = await handler.handle(event);

      expect(result).toBeUndefined();
      expect(mockLogger.debug).toHaveBeenCalledWith('No subtype found in message event, skipping...');
      expect(messageChangedHandler.handle).not.toHaveBeenCalled();
      expect(messageDeletedHandler.handle).not.toHaveBeenCalled();
    });

    it('should handle message_changed subtype', async () => {
      const event = {
        context: {
          installation: { id: 'test-installation-id' },
          organization: { id: 'test-org-id' },
        },
        event: {
          type: 'message',
          subtype: 'message_changed',
          text: 'Hello, world!',
          channel: 'C12345',
          user: 'U12345',
          ts: '1234567890.123456',
          event_ts: '1234567890.123456',
        },
      } as any;

      const result = await handler.handle(event);

      expect(result).toBe(true);
      expect(mockLogger.debug).toHaveBeenCalledWith('Message changed event encountered!');
      expect(messageChangedHandler.handle).toHaveBeenCalledWith(event);
      expect(messageDeletedHandler.handle).not.toHaveBeenCalled();
    });

    it('should handle message_deleted subtype', async () => {
      const event = {
        context: {
          installation: { id: 'test-installation-id' },
          organization: { id: 'test-org-id' },
        },
        event: {
          type: 'message',
          subtype: 'message_deleted',
          text: 'Hello, world!',
          channel: 'C12345',
          user: 'U12345',
          ts: '1234567890.123456',
          event_ts: '1234567890.123456',
        },
      } as any;

      const result = await handler.handle(event);

      expect(result).toBe(true);
      expect(mockLogger.debug).toHaveBeenCalledWith('Message deleted event encountered!');
      expect(messageChangedHandler.handle).not.toHaveBeenCalled();
      expect(messageDeletedHandler.handle).toHaveBeenCalledWith(event);
    });

    it('should handle unknown subtype', async () => {
      const event = {
        context: {
          installation: { id: 'test-installation-id' },
          organization: { id: 'test-org-id' },
        },
        event: {
          type: 'message',
          subtype: 'unknown_subtype',
          text: 'Hello, world!',
          channel: 'C12345',
          user: 'U12345',
          ts: '1234567890.123456',
          event_ts: '1234567890.123456',
        },
      } as any;

      const result = await handler.handle(event);

      expect(result).toBe(true);
      expect(mockLogger.debug).toHaveBeenCalledWith('Unknown subtype: unknown_subtype, skipping...');
      expect(messageChangedHandler.handle).not.toHaveBeenCalled();
      expect(messageDeletedHandler.handle).not.toHaveBeenCalled();
    });

    it('should handle errors', async () => {
      const event = {
        context: {
          installation: { id: 'test-installation-id' },
          organization: { id: 'test-org-id' },
        },
        event: {
          type: 'message',
          subtype: 'message_changed',
          text: 'Hello, world!',
          channel: 'C12345',
          user: 'U12345',
          ts: '1234567890.123456',
          event_ts: '1234567890.123456',
        },
      } as any;

      const error = new Error('Test error');
      (messageChangedHandler.handle as any).mockRejectedValue(error);

      await expect(handler.handle(event)).rejects.toThrow(error);
      expect(mockLogger.error).toHaveBeenCalledWith(
        'Error handling message event: Test error',
        error.stack
      );
    });

    it('should handle non-Error errors', async () => {
      const event = {
        context: {
          installation: { id: 'test-installation-id' },
          organization: { id: 'test-org-id' },
        },
        event: {
          type: 'message',
          subtype: 'message_changed',
          text: 'Hello, world!',
          channel: 'C12345',
          user: 'U12345',
          ts: '1234567890.123456',
          event_ts: '1234567890.123456',
        },
      } as any;

      const consoleErrorSpy = vi.spyOn(console, 'error').mockImplementation(() => {});
      (messageChangedHandler.handle as any).mockRejectedValue('String error');

      await expect(handler.handle(event)).rejects.toEqual('String error');
      expect(consoleErrorSpy).toHaveBeenCalledWith('Error handling message event', 'String error');

      consoleErrorSpy.mockRestore();
    });
  });
});
