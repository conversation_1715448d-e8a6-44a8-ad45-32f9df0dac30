import { describe, it, expect, beforeEach, vi, <PERSON><PERSON> } from 'vitest';
import { SlackSubTeamUpdatedHandler } from '../../../../../../src/slack/event-handlers/handlers/subteams/sub-team-updated.handler';
import { TransactionService } from '../../../../../../src/database/common';
import { SlackSubgroupsRepository } from '../../../../../../src/database/entities/subgroups/repositories/subgroups.repository';
import { ILogger } from '../../../../../../src/utils';
import { SlackEventMap } from '../../../../../../src/slack/event-handlers/interface';
import { Installations } from '../../../../../../src/database/entities';

describe('SlackSubTeamUpdatedHandler', () => {
  let handler: SlackSubTeamUpdatedHandler;
  let mockLogger: ILogger;
  let mockTransactionService: TransactionService;
  let mockSlackSubgroupsRepository: SlackSubgroupsRepository;

  const mockInstallation = {
    id: 'installation-123',
    teamId: 'T12345',
    botToken: 'xoxb-token',
    organization: { id: 'org-123' }
  } as unknown as Installations;

  const mockSubteam = {
    id: 'S12345',
    team_id: 'T12345',
    handle: 'test-subteam',
    description: 'Test subteam description',
    is_external: false,
    user_count: 5,
    created_by: 'U12345',
    updated_by: 'U12345',
    users: ['U12345', 'U67890'],
    date_delete: 0
  };

  const mockSubteamEntity = {
    id: 'entity-123',
    slackGroupId: 'S12345',
    slackHandle: 'test-subteam',
    description: 'Test subteam description',
    isExternal: false,
    usersCount: 5,
    users: ['U12345', 'U67890'],
    installation: { id: 'installation-123' },
    organization: { id: 'org-123' }
  };

  beforeEach(() => {
    mockLogger = {
      debug: vi.fn(),
      log: vi.fn(),
      error: vi.fn(),
      warn: vi.fn(),
      verbose: vi.fn()
    } as unknown as ILogger;

    mockTransactionService = {
      runInTransaction: vi.fn().mockImplementation(async (callback) => {
        return callback({ entityManager: {} });
      })
    } as unknown as TransactionService;

    mockSlackSubgroupsRepository = {
      findByCondition: vi.fn(),
      updateWithTxn: vi.fn(),
      saveWithTxn: vi.fn()
    } as unknown as SlackSubgroupsRepository;

    handler = new SlackSubTeamUpdatedHandler(
      mockLogger,
      mockTransactionService,
      mockSlackSubgroupsRepository
    );
  });

  describe('canHandle', () => {
    it('should return true for subteam_updated events', () => {
      const event = {
        event: {
          type: 'subteam_updated',
          subteam: mockSubteam
        }
      } as unknown as SlackEventMap['subteam_updated'];

      expect(handler.canHandle(event)).toBe(true);
    });

    it('should return false for other event types', () => {
      const event = {
        event: {
          type: 'some_other_event'
        }
      } as unknown as SlackEventMap['subteam_updated'];

      expect(handler.canHandle(event)).toBe(false);
    });
  });

  describe('handle', () => {
    it('should throw an error if event is invalid', async () => {
      const event = {
        event: {
          type: 'subteam_updated'
        },
        context: {
          installation: mockInstallation,
          organization: { id: 'org-123' }
        }
      } as unknown as SlackEventMap['subteam_updated'];

      await expect(handler.handle(event)).rejects.toThrow(
        'Invalid event received in the `subteam_updated` handler'
      );
    });

    it('should create subteam if it does not exist', async () => {
      const event = {
        event: {
          type: 'subteam_updated',
          subteam: mockSubteam
        },
        context: {
          installation: mockInstallation,
          organization: { id: 'org-123' }
        }
      } as unknown as SlackEventMap['subteam_updated'];

      (mockSlackSubgroupsRepository.findByCondition as Mock).mockResolvedValue(null);
      (mockSlackSubgroupsRepository.saveWithTxn as Mock).mockResolvedValue(mockSubteamEntity);

      await handler.handle(event);

      expect(mockLogger.log).toHaveBeenCalledWith(
        expect.stringContaining(`Received 'subteam_updated' event for slack team ${mockSubteam.team_id}`)
      );
      expect(mockSlackSubgroupsRepository.findByCondition).toHaveBeenCalledWith({
        where: {
          slackGroupId: mockSubteam.id,
          installation: { id: mockInstallation.id },
          organization: { id: 'org-123' }
        }
      });
      expect(mockLogger.debug).toHaveBeenCalledWith(
        `Subteam ${mockSubteam.id} does not exist in our database`
      );
      expect(mockTransactionService.runInTransaction).toHaveBeenCalled();
      expect(mockSlackSubgroupsRepository.saveWithTxn).toHaveBeenCalledWith(
        expect.anything(),
        expect.objectContaining({
          slackGroupId: mockSubteam.id,
          slackGroupDump: mockSubteam,
          slackHandle: mockSubteam.handle,
          description: mockSubteam.description,
          isExternal: mockSubteam.is_external,
          usersCount: mockSubteam.user_count,
          createdBy: { slackId: mockSubteam.created_by },
          updatedBy: { slackId: mockSubteam.updated_by },
          installation: { id: mockInstallation.id },
          organization: { id: mockInstallation.organization.id },
          users: mockSubteam.users
        })
      );
    });

    it('should update subteam if it exists', async () => {
      const event = {
        event: {
          type: 'subteam_updated',
          subteam: mockSubteam
        },
        context: {
          installation: mockInstallation,
          organization: { id: 'org-123' }
        }
      } as unknown as SlackEventMap['subteam_updated'];

      (mockSlackSubgroupsRepository.findByCondition as Mock).mockResolvedValue(mockSubteamEntity);

      await handler.handle(event);

      expect(mockLogger.log).toHaveBeenCalledWith(
        expect.stringContaining(`Received 'subteam_updated' event for slack team ${mockSubteam.team_id}`)
      );
      expect(mockSlackSubgroupsRepository.findByCondition).toHaveBeenCalledWith({
        where: {
          slackGroupId: mockSubteam.id,
          installation: { id: mockInstallation.id },
          organization: { id: 'org-123' }
        }
      });
      expect(mockTransactionService.runInTransaction).toHaveBeenCalled();
      expect(mockSlackSubgroupsRepository.updateWithTxn).toHaveBeenCalledWith(
        expect.anything(),
        { id: mockSubteamEntity.id, slackGroupId: mockSubteam.id },
        {
          slackDeletedAt: mockSubteam.date_delete,
          slackHandle: mockSubteam.handle,
          description: mockSubteam.description,
          isExternal: mockSubteam.is_external,
          usersCount: mockSubteam.user_count,
          updatedBy: { slackId: mockSubteam.updated_by },
          users: mockSubteam.users
        }
      );
    });

    it('should handle errors during processing', async () => {
      const event = {
        event: {
          type: 'subteam_updated',
          subteam: mockSubteam
        },
        context: {
          installation: mockInstallation,
          organization: { id: 'org-123' }
        }
      } as unknown as SlackEventMap['subteam_updated'];

      const error = new Error('Test error');
      (mockSlackSubgroupsRepository.findByCondition as Mock).mockRejectedValue(error);

      await handler.handle(event);

      expect(mockLogger.error).toHaveBeenCalledWith(
        `Error updating subteam ${mockSubteam.id} in our database`,
        error.stack
      );
    });
  });
});
