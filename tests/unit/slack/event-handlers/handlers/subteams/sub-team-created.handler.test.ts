import { describe, it, expect, beforeEach, vi, Mock } from 'vitest';
import { SlackSubTeamCreated<PERSON>andler } from '../../../../../../src/slack/event-handlers/handlers/subteams/sub-team-created.handler';
import { TransactionService } from '../../../../../../src/database/common';
import { SlackSubgroupsRepository } from '../../../../../../src/database/entities/subgroups/repositories/subgroups.repository';
import { ILogger } from '../../../../../../src/utils';
import { SlackEventMap } from '../../../../../../src/slack/event-handlers/interface';

describe('SlackSubTeamCreatedHandler', () => {
  let handler: SlackSubTeamCreatedHandler;
  let mockLogger: ILogger;
  let mockTransactionService: TransactionService;
  let mockSlackSubgroupsRepository: SlackSubgroupsRepository;

  const mockInstallation = {
    id: 'installation-123',
    teamId: 'T12345',
    botToken: 'xoxb-token',
    organization: { id: 'org-123' }
  };

  const mockSubteam = {
    id: 'S12345',
    team_id: 'T12345',
    handle: 'test-subteam',
    description: 'Test subteam description',
    is_external: false,
    user_count: 5,
    created_by: 'U12345',
    updated_by: 'U12345',
    users: ['U12345', 'U67890']
  };

  beforeEach(() => {
    mockLogger = {
      debug: vi.fn(),
      log: vi.fn(),
      error: vi.fn(),
      warn: vi.fn(),
      verbose: vi.fn()
    } as unknown as ILogger;

    mockTransactionService = {
      runInTransaction: vi.fn().mockImplementation(async (callback) => {
        return callback({ entityManager: {} });
      })
    } as unknown as TransactionService;

    mockSlackSubgroupsRepository = {
      exists: vi.fn(),
      saveWithTxn: vi.fn()
    } as unknown as SlackSubgroupsRepository;

    handler = new SlackSubTeamCreatedHandler(
      mockLogger,
      mockTransactionService,
      mockSlackSubgroupsRepository
    );
  });

  describe('canHandle', () => {
    it('should return true for subteam_created events', () => {
      const event = {
        event: {
          type: 'subteam_created',
          subteam: mockSubteam
        }
      } as unknown as SlackEventMap['subteam_created'];

      expect(handler.canHandle(event)).toBe(true);
    });

    it('should return false for other event types', () => {
      const event = {
        event: {
          type: 'some_other_event'
        }
      } as unknown as SlackEventMap['subteam_created'];

      expect(handler.canHandle(event)).toBe(false);
    });
  });

  describe('handle', () => {
    it('should throw an error if event is invalid', async () => {
      const event = {
        event: {
          type: 'subteam_created'
        },
        context: {
          installation: mockInstallation,
          organization: { id: 'org-123' }
        }
      } as unknown as SlackEventMap['subteam_created'];

      await expect(handler.handle(event)).rejects.toThrow(
        'Invalid event received in the `subteam_created` handler'
      );
    });

    it('should skip creating subteam if it already exists', async () => {
      const event = {
        event: {
          type: 'subteam_created',
          subteam: mockSubteam
        },
        context: {
          installation: mockInstallation,
          organization: { id: 'org-123' }
        }
      } as unknown as SlackEventMap['subteam_created'];

      (mockSlackSubgroupsRepository.exists as Mock).mockResolvedValue(true);

      await handler.handle(event);

      expect(mockLogger.log).toHaveBeenCalledWith(
        expect.stringContaining(`Received 'subteam_created' event for slack team ${mockSubteam.team_id}`)
      );
      expect(mockSlackSubgroupsRepository.exists).toHaveBeenCalledWith({
        where: {
          slackGroupId: mockSubteam.id,
          installation: { id: mockInstallation.id },
          organization: { id: 'org-123' }
        }
      });
      expect(mockLogger.debug).toHaveBeenCalledWith(
        `Subteam ${mockSubteam.id} already exists in our database`
      );
      expect(mockTransactionService.runInTransaction).not.toHaveBeenCalled();
    });

    it('should create subteam if it does not exist', async () => {
      const event = {
        event: {
          type: 'subteam_created',
          subteam: mockSubteam
        },
        context: {
          installation: mockInstallation,
          organization: { id: 'org-123' }
        }
      } as unknown as SlackEventMap['subteam_created'];

      (mockSlackSubgroupsRepository.exists as Mock).mockResolvedValue(false);

      await handler.handle(event);

      expect(mockLogger.log).toHaveBeenCalledWith(
        expect.stringContaining(`Received 'subteam_created' event for slack team ${mockSubteam.team_id}`)
      );
      expect(mockSlackSubgroupsRepository.exists).toHaveBeenCalledWith({
        where: {
          slackGroupId: mockSubteam.id,
          installation: { id: mockInstallation.id },
          organization: { id: 'org-123' }
        }
      });
      expect(mockTransactionService.runInTransaction).toHaveBeenCalled();
      expect(mockSlackSubgroupsRepository.saveWithTxn).toHaveBeenCalledWith(
        expect.anything(),
        {
          slackGroupId: mockSubteam.id,
          slackGroupDump: mockSubteam,
          slackHandle: mockSubteam.handle,
          description: mockSubteam.description,
          isExternal: mockSubteam.is_external,
          usersCount: mockSubteam.user_count,
          createdBy: { slackId: mockSubteam.created_by },
          updatedBy: { slackId: mockSubteam.updated_by },
          installation: { id: mockInstallation.id },
          organization: { id: mockInstallation.organization.id },
          users: mockSubteam.users
        }
      );
    });

    it('should handle errors during processing', async () => {
      const event = {
        event: {
          type: 'subteam_created',
          subteam: mockSubteam
        },
        context: {
          installation: mockInstallation,
          organization: { id: 'org-123' }
        }
      } as unknown as SlackEventMap['subteam_created'];

      const error = new Error('Test error');
      (mockSlackSubgroupsRepository.exists as Mock).mockRejectedValue(error);

      await handler.handle(event);

      expect(mockLogger.error).toHaveBeenCalledWith(
        `Error creating subteam ${mockSubteam.id} in our database`,
        error.stack
      );
    });
  });
});
