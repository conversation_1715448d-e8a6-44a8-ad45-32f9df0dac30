import { beforeEach, describe, expect, it, vi } from 'vitest';
import { EmittableSlackEvents } from '../../../../../../src/external/provider/constants/platform-events.constants';
import { ThenaAppsPlatformApiProvider } from '../../../../../../src/external/provider/thena-apps-platform-api.provider';
import { SlackReactionRemovedHandler } from '../../../../../../src/slack/event-handlers/handlers/reactions/slack-reaction-removed.handler';
import { CUSTOM_LOGGER_TOKEN } from '../../../../../../src/utils';

describe('SlackReactionRemovedHandler', () => {
  let handler: SlackReactionRemovedHandler;
  let mockLogger: any;
  let mockThenaAppsPlatformApiProvider: any;

  beforeEach(() => {
    mockLogger = {
      log: vi.fn(),
      debug: vi.fn(),
      error: vi.fn(),
      warn: vi.fn(),
    };

    mockThenaAppsPlatformApiProvider = {
      postEventsToPlatform: vi.fn().mockResolvedValue(undefined),
    };

    handler = new SlackReactionRemovedHandler(
      mockLogger,
      mockThenaAppsPlatformApiProvider
    );
  });

  describe('canHandle', () => {
    it('should return true for reaction_removed events', () => {
      const event = {
        event: {
          type: 'reaction_removed',
          user: 'U12345',
          reaction: 'thumbsup',
          item: {
            type: 'message',
            channel: 'C12345',
            ts: '**********.123456',
          },
          event_ts: '**********.123456',
        },
      } as any;

      expect(handler.canHandle(event)).toBe(true);
    });

    it('should return false for other event types', () => {
      const event = {
        event: {
          type: 'reaction_added',
          user: 'U12345',
          reaction: 'thumbsup',
          item: {
            type: 'message',
            channel: 'C12345',
            ts: '**********.123456',
          },
          event_ts: '**********.123456',
        },
      } as any;

      expect(handler.canHandle(event)).toBe(false);
    });
  });

  describe('handle', () => {
    it.todo('should post events to platform for valid reaction_removed events', async () => {
      const event = {
        context: {
          installation: {
            id: 'test-installation-id',
            teamId: 'T12345',
            organization: { id: 'test-org-id' },
          },
          organization: { id: 'test-org-id' },
        },
        event: {
          type: 'reaction_removed',
          user: 'U12345',
          reaction: 'thumbsup',
          item: {
            type: 'message',
            channel: 'C12345',
            ts: '**********.123456',
          },
          event_ts: '**********.123456',
        },
      } as any;

      await handler.handle(event);

      expect(mockThenaAppsPlatformApiProvider.postEventsToPlatform).toHaveBeenCalledWith(
        event.context.installation.organization,
        {
          ...event.event,
          type: EmittableSlackEvents.REACTION_REMOVED,
        }
      );
    });

    it('should throw an error for invalid events without user property', async () => {
      const event = {
        context: {
          installation: {
            id: 'test-installation-id',
            teamId: 'T12345',
            organization: { id: 'test-org-id' },
          },
          organization: { id: 'test-org-id' },
        },
        event: {
          type: 'reaction_removed',
          reaction: 'thumbsup',
          item: {
            type: 'message',
            channel: 'C12345',
            ts: '**********.123456',
          },
          event_ts: '**********.123456',
        },
      } as any;

      await handler.handle(event);

      expect(mockThenaAppsPlatformApiProvider.postEventsToPlatform).not.toHaveBeenCalled();
      expect(mockLogger.error).toHaveBeenCalledWith(
        expect.stringContaining('Failed to handle \'reaction_removed\' event for team T12345'),
        expect.any(String)
      );
    });

    it('should throw an error for invalid events without reaction property', async () => {
      const event = {
        context: {
          installation: {
            id: 'test-installation-id',
            teamId: 'T12345',
            organization: { id: 'test-org-id' },
          },
          organization: { id: 'test-org-id' },
        },
        event: {
          type: 'reaction_removed',
          user: 'U12345',
          item: {
            type: 'message',
            channel: 'C12345',
            ts: '**********.123456',
          },
          event_ts: '**********.123456',
        },
      } as any;

      await handler.handle(event);

      expect(mockThenaAppsPlatformApiProvider.postEventsToPlatform).not.toHaveBeenCalled();
      expect(mockLogger.error).toHaveBeenCalledWith(
        expect.stringContaining('Failed to handle \'reaction_removed\' event for team T12345'),
        expect.any(String)
      );
    });

    it.todo('should handle errors and log them properly', async () => {
      const event = {
        context: {
          installation: {
            id: 'test-installation-id',
            teamId: 'T12345',
            organization: { id: 'test-org-id' },
          },
          organization: { id: 'test-org-id' },
        },
        event: {
          type: 'reaction_removed',
          user: 'U12345',
          reaction: 'thumbsup',
          item: {
            type: 'message',
            channel: 'C12345',
            ts: '**********.123456',
          },
          event_ts: '**********.123456',
        },
      } as any;

      const error = new Error('Test error');
      mockThenaAppsPlatformApiProvider.postEventsToPlatform.mockRejectedValue(error);

      await handler.handle(event);

      expect(mockLogger.error).toHaveBeenCalledWith(
        expect.stringContaining('Failed to handle \'reaction_removed\' event for team T12345'),
        error.stack
      );
    });

    it.todo('should handle non-Error errors and log them properly', async () => {
      const event = {
        context: {
          installation: {
            id: 'test-installation-id',
            teamId: 'T12345',
            organization: { id: 'test-org-id' },
          },
          organization: { id: 'test-org-id' },
        },
        event: {
          type: 'reaction_removed',
          user: 'U12345',
          reaction: 'thumbsup',
          item: {
            type: 'message',
            channel: 'C12345',
            ts: '**********.123456',
          },
          event_ts: '**********.123456',
        },
      } as any;

      const consoleErrorSpy = vi.spyOn(console, 'error').mockImplementation(() => {});
      mockThenaAppsPlatformApiProvider.postEventsToPlatform.mockRejectedValue('String error');

      await handler.handle(event);

      expect(consoleErrorSpy).toHaveBeenCalledWith(
        expect.stringContaining('Failed to handle \'reaction_removed\' event for team T12345'),
        'String error'
      );

      consoleErrorSpy.mockRestore();
    });

    it('should handle events with wrong type', async () => {
      const event = {
        context: {
          installation: {
            id: 'test-installation-id',
            teamId: 'T12345',
            organization: { id: 'test-org-id' },
          },
          organization: { id: 'test-org-id' },
        },
        event: {
          type: 'reaction_added', // Wrong type
          user: 'U12345',
          reaction: 'thumbsup',
          item: {
            type: 'message',
            channel: 'C12345',
            ts: '**********.123456',
          },
          event_ts: '**********.123456',
        },
      } as any;

      await handler.handle(event);

      expect(mockThenaAppsPlatformApiProvider.postEventsToPlatform).not.toHaveBeenCalled();
      expect(mockLogger.error).toHaveBeenCalledWith(
        expect.stringContaining('Failed to handle \'reaction_removed\' event for team T12345'),
        expect.any(String)
      );
    });
  });
});
