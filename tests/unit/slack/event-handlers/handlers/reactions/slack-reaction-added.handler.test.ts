import { Repository } from 'typeorm';
import { Mock, beforeEach, describe, expect, it, vi } from 'vitest';
import {
  ConfigKeys,
  ConfigService,
} from '../../../../../../src/config/config.service';
import { TeamChannelMapsRepository } from '../../../../../../src/database/entities/mappings/repositories/team-channel-maps.repository';
import { Users } from '../../../../../../src/database/entities/users/users.entity';
import { EmittableSlackEvents } from '../../../../../../src/external/provider/constants/platform-events.constants';
import { ThenaAppsPlatformApiProvider } from '../../../../../../src/external/provider/thena-apps-platform-api.provider';
import { CreateNewTicketBlocks } from '../../../../../../src/slack/blocks/components/composite/channels';
import {
  ConditionalFormBuilderComposite,
  FormSelectorComposite,
} from '../../../../../../src/slack/blocks/components/composite/form-builder';
import { SettingsCore } from '../../../../../../src/slack/core/management/settings.management';
import { SlackReactionAddedHandler } from '../../../../../../src/slack/event-handlers/handlers/reactions/slack-reaction-added.handler';
import { SlackEventMap } from '../../../../../../src/slack/event-handlers/interface';
import { SlackWebAPIService } from '../../../../../../src/slack/providers/slack-apis/slack-apis.service';
import { FormBuilderService } from '../../../../../../src/slack/services/form-builder.service';
import { ILogger } from '../../../../../../src/utils';
import { processSlackMessageText } from '../../../../../../src/utils/parsers/slack/text-processing.utils';

describe('SlackReactionAddedHandler', () => {
  let handler: SlackReactionAddedHandler;
  let mockLogger: ILogger;
  let mockUserRepository: Repository<Users>;
  let mockTeamChannelMapRepo: TeamChannelMapsRepository;
  let mockSlackMessagesRepository: any;
  let mockGroupedSlackMessagesRepository: any;
  let mockChannelRepository: any;
  let mockFormBuilder: ConditionalFormBuilderComposite;
  let mockFormSelector: FormSelectorComposite;
  let mockFormBuilderService: FormBuilderService;
  let mockSettingsCore: SettingsCore;
  let mockThenaPlatformApiProvider: any;
  let mockThenaAppsPlatformApiProvider: ThenaAppsPlatformApiProvider;
  let mockSlackApiProvider: SlackWebAPIService;
  let mockCreateNewTicketBlock: CreateNewTicketBlocks;
  let mockConfigService: ConfigService;

  beforeEach(() => {
    vi.resetAllMocks();

    // Mock the logger
    mockLogger = {
      log: vi.fn(),
      error: vi.fn(),
      warn: vi.fn(),
      debug: vi.fn(),
      verbose: vi.fn(),
    } as unknown as ILogger;

    // Mock the UserRepository
    mockUserRepository = {
      findOne: vi.fn(),
    } as unknown as Repository<Users>;

    // Mock the TeamChannelMapsRepository
    mockTeamChannelMapRepo = {
      findByCondition: vi.fn(),
    } as unknown as TeamChannelMapsRepository;

    // Mock the SlackMessagesRepository
    mockSlackMessagesRepository = {
      findByCondition: vi.fn(),
    };

    // Mock the GroupedSlackMessagesRepository
    mockGroupedSlackMessagesRepository = {
      findByCondition: vi.fn(),
    };

    // Mock the ChannelRepository
    mockChannelRepository = {
      findByCondition: vi.fn(),
    };

    // Mock the FormBuilder
    mockFormBuilder = {
      build: vi.fn(),
    } as unknown as ConditionalFormBuilderComposite;

    // Mock the FormSelector
    mockFormSelector = {
      build: vi.fn(),
    } as unknown as FormSelectorComposite;

    // Mock the FormBuilderService
    mockFormBuilderService = {
      getForms: vi.fn(),
    } as unknown as FormBuilderService;

    // Mock the SettingsCore
    mockSettingsCore = {
      getValue: vi.fn(),
    } as unknown as SettingsCore;

    // Mock the ThenaPlatformApiProvider
    mockThenaPlatformApiProvider = {
      proxy: vi.fn(),
      createNewTicket: vi.fn(),
      addReaction: vi.fn(),
      getCommentThreads: vi.fn(),
    };

    // Mock the ThenaAppsPlatformApiProvider
    mockThenaAppsPlatformApiProvider = {
      postEventsToPlatform: vi.fn(),
    } as unknown as ThenaAppsPlatformApiProvider;

    // Mock the SlackWebAPIService
    mockSlackApiProvider = {
      sendEphemeral: vi.fn(),
      getUserInfo: vi.fn(),
      getConversationHistory: vi.fn(),
    } as unknown as SlackWebAPIService;

    // Mock the CreateNewTicketBlocks
    mockCreateNewTicketBlock = {
      build: vi.fn().mockReturnValue({ blocks: [] }),
    } as unknown as CreateNewTicketBlocks;

    // Mock the ConfigService
    mockConfigService = {
      get: vi.fn().mockImplementation((key) => {
        if (key === ConfigKeys.THENA_WEB_URL) {
          return 'https://mock-web-url';
        }
        return `mock-value-for-${key}`;
      }),
    } as unknown as ConfigService;

    // Create the handler
    handler = new SlackReactionAddedHandler(
      mockLogger,
      mockUserRepository,
      mockTeamChannelMapRepo,
      mockSlackMessagesRepository,
      mockGroupedSlackMessagesRepository,
      mockChannelRepository,
      mockFormBuilder,
      mockFormSelector,
      mockFormBuilderService,
      mockSettingsCore,
      mockThenaPlatformApiProvider,
      mockThenaAppsPlatformApiProvider,
      mockSlackApiProvider,
      mockConfigService,
      mockCreateNewTicketBlock,
    );
  });

  describe('canHandle', () => {
    it('should return true if the event type is reaction_added', () => {
      // Setup
      const event = {
        event: {
          type: 'reaction_added',
          user: 'U12345',
          reaction: 'ticket',
          item: {
            type: 'message',
            channel: 'C12345',
            ts: '**********.123456',
          },
        },
      } as SlackEventMap['reaction_added'];

      // Execute
      const result = handler.canHandle(event);

      // Verify
      expect(result).toBe(true);
    });

    it('should return false if the event type is not reaction_added', () => {
      // Setup
      const event = {
        event: {
          type: 'not_reaction_added',
        },
      } as unknown as SlackEventMap['reaction_added'];

      // Execute
      const result = handler.canHandle(event);

      // Verify
      expect(result).toBe(false);
    });
  });

  describe('handle', () => {
    it.todo('should handle a reaction_added event successfully', async () => {
      // Setup
      const mockEvent = {
        event: {
          type: 'reaction_added',
          user: 'U12345',
          reaction: 'ticket',
          item: {
            type: 'message',
            channel: 'C12345',
            ts: '**********.123456',
          },
          event_ts: '**********.123456',
        },
        context: {
          installation: {
            id: 'inst-1',
            name: 'Test Installation',
            teamId: 'T12345',
            botToken: 'xoxb-token',
            organization: { id: 'org-1' },
          },
          organization: { id: 'org-1' },
        },
      } as SlackEventMap['reaction_added'];

      // Spy on the handleTicketCreation method
      const handleTicketCreationSpy = vi.spyOn(handler, 'handleTicketCreation');

      // Execute
      await handler.handle(mockEvent);

      // Verify
      expect(handleTicketCreationSpy).toHaveBeenCalledWith(mockEvent);
      expect(
        mockThenaAppsPlatformApiProvider.postEventsToPlatform,
      ).toHaveBeenCalledWith(mockEvent.context.installation.organization, {
        ...mockEvent.event,
        type: EmittableSlackEvents.REACTION_ADDED,
      });
    });

    it('should handle errors when the event is invalid', async () => {
      // Setup
      const mockEvent = {
        event: {
          type: 'reaction_added',
          // Missing user property
          reaction: 'ticket',
          item: {
            type: 'message',
            channel: 'C12345',
            ts: '**********.123456',
          },
        },
        context: {
          installation: {
            id: 'inst-1',
            name: 'Test Installation',
            teamId: 'T12345',
            organization: { id: 'org-1' },
          },
          organization: { id: 'org-1' },
        },
      } as SlackEventMap['reaction_added'];

      // Execute
      await handler.handle(mockEvent);

      // Verify
      expect(mockLogger.error).toHaveBeenCalled();
      expect(
        mockThenaAppsPlatformApiProvider.postEventsToPlatform,
      ).not.toHaveBeenCalled();
    });

    it('should handle errors when posting to platform', async () => {
      // Setup
      const mockEvent = {
        event: {
          type: 'reaction_added',
          user: 'U12345',
          reaction: 'ticket',
          item: {
            type: 'message',
            channel: 'C12345',
            ts: '**********.123456',
          },
          event_ts: '**********.123456',
        },
        context: {
          installation: {
            id: 'inst-1',
            name: 'Test Installation',
            teamId: 'T12345',
            botToken: 'xoxb-token',
            organization: { id: 'org-1' },
          },
          organization: { id: 'org-1' },
        },
      } as SlackEventMap['reaction_added'];

      const error = new Error('Failed to post to platform');
      (
        mockThenaAppsPlatformApiProvider.postEventsToPlatform as Mock
      ).mockRejectedValue(error);

      // Spy on the handleTicketCreation method
      vi.spyOn(handler, 'handleTicketCreation').mockResolvedValue();

      // Execute
      await handler.handle(mockEvent);

      // Verify
      expect(mockLogger.error).toHaveBeenCalled();
    });
  });

  describe('text processing', () => {
    it('should use processSlackMessageText utility for message text processing', () => {
      // Setup
      const mockText =
        'Test message with <mailto:<EMAIL>|<EMAIL>>';
      const expectedProcessedText = 'Test <NAME_EMAIL>';

      // Execute - this is just to verify the handler is using the utility function
      const result = processSlackMessageText(mockText);

      // Verify
      expect(result).toBe(expectedProcessedText);
    });
  });

  describe('handleTicketCreation', () => {
    it('should not create a ticket if the item type is not message', async () => {
      // Setup
      const mockEvent = {
        event: {
          type: 'reaction_added',
          user: 'U12345',
          reaction: 'ticket',
          item: {
            type: 'file', // Not a message
            channel: 'C12345',
            ts: '**********.123456',
          },
        },
        context: {
          installation: {
            id: 'inst-1',
            name: 'Test Installation',
            teamId: 'T12345',
            botToken: 'xoxb-token',
            organization: { id: 'org-1' },
          },
          organization: { id: 'org-1' },
        },
      } as SlackEventMap['reaction_added'];

      // Execute
      await handler.handleTicketCreation(mockEvent);

      // Verify
      expect(mockLogger.error).toHaveBeenCalled();
      expect(mockTeamChannelMapRepo.findByCondition).not.toHaveBeenCalled();
    });

    it('should not create a ticket if the platform team mapping is not found', async () => {
      // Setup
      const mockEvent = {
        event: {
          type: 'reaction_added',
          user: 'U12345',
          reaction: 'ticket',
          item: {
            type: 'message',
            channel: 'C12345',
            ts: '**********.123456',
          },
        },
        context: {
          installation: {
            id: 'inst-1',
            name: 'Test Installation',
            teamId: 'T12345',
            botToken: 'xoxb-token',
            organization: { id: 'org-1' },
          },
          organization: { id: 'org-1' },
        },
      } as SlackEventMap['reaction_added'];

      (mockTeamChannelMapRepo.findByCondition as Mock).mockResolvedValue(null);

      // Execute
      await handler.handleTicketCreation(mockEvent);

      // Verify
      expect(mockLogger.error).toHaveBeenCalled();
      expect(mockSettingsCore.getValue).not.toHaveBeenCalled();
    });

    it.todo(
      'should not create a ticket if ticket creation via reaction is not enabled',
      async () => {
        // Setup
        const mockEvent = {
          event: {
            type: 'reaction_added',
            user: 'U12345',
            reaction: 'ticket',
            item: {
              type: 'message',
              channel: 'C12345',
              ts: '**********.123456',
            },
          },
          context: {
            installation: {
              id: 'inst-1',
              name: 'Test Installation',
              teamId: 'T12345',
              botToken: 'xoxb-token',
              organization: { id: 'org-1' },
            },
            organization: { id: 'org-1' },
          },
        } as SlackEventMap['reaction_added'];

        const mockPlatformTeamMapping = {
          platformTeam: {
            id: 'team-1',
            uid: 'team-uid-1',
          },
        };

        (mockTeamChannelMapRepo.findByCondition as Mock).mockResolvedValue(
          mockPlatformTeamMapping,
        );
        (mockSettingsCore.getValue as Mock).mockResolvedValue(false); // Ticket creation via reaction is not enabled

        // Execute
        await handler.handleTicketCreation(mockEvent);

        // Verify
        expect(mockLogger.debug).toHaveBeenCalledWith(
          expect.stringContaining(
            'Ticket creation via reaction is not enabled',
          ),
        );
        expect(mockSlackApiProvider.sendEphemeral).not.toHaveBeenCalled();
      },
    );

    it.todo(
      'should not create a ticket if the emoji to create a ticket is not set',
      async () => {
        // Setup
        const mockEvent = {
          event: {
            type: 'reaction_added',
            user: 'U12345',
            reaction: 'ticket',
            item: {
              type: 'message',
              channel: 'C12345',
              ts: '**********.123456',
            },
          },
          context: {
            installation: {
              id: 'inst-1',
              name: 'Test Installation',
              teamId: 'T12345',
              botToken: 'xoxb-token',
              organization: { id: 'org-1' },
            },
            organization: { id: 'org-1' },
          },
        } as SlackEventMap['reaction_added'];

        const mockPlatformTeamMapping = {
          platformTeam: {
            id: 'team-1',
            uid: 'team-uid-1',
          },
        };

        (mockTeamChannelMapRepo.findByCondition as Mock).mockResolvedValue(
          mockPlatformTeamMapping,
        );
        (mockSettingsCore.getValue as Mock).mockImplementation((key) => {
          if (key === 'enable_ticket_creation_via_reaction') {
            return Promise.resolve(true);
          }
          if (key === 'emoji_to_create_ticket') {
            return Promise.resolve(null); // Emoji to create a ticket is not set
          }
          return Promise.resolve(null);
        });

        // Execute
        await handler.handleTicketCreation(mockEvent);

        // Verify
        expect(mockLogger.debug).toHaveBeenCalledWith(
          expect.stringContaining('Emoji to create a ticket is not set'),
        );
        expect(mockSlackApiProvider.sendEphemeral).not.toHaveBeenCalled();
      },
    );

    it.todo(
      'should not create a ticket if the emoji is not the same as the emoji to create a ticket setting',
      async () => {
        // Setup
        const mockEvent = {
          event: {
            type: 'reaction_added',
            user: 'U12345',
            reaction: 'ticket',
            item: {
              type: 'message',
              channel: 'C12345',
              ts: '**********.123456',
            },
          },
          context: {
            installation: {
              id: 'inst-1',
              name: 'Test Installation',
              teamId: 'T12345',
              botToken: 'xoxb-token',
              organization: { id: 'org-1' },
            },
            organization: { id: 'org-1' },
          },
        } as SlackEventMap['reaction_added'];

        const mockPlatformTeamMapping = {
          platformTeam: {
            id: 'team-1',
            uid: 'team-uid-1',
          },
        };

        (mockTeamChannelMapRepo.findByCondition as Mock).mockResolvedValue(
          mockPlatformTeamMapping,
        );
        (mockSettingsCore.getValue as Mock).mockImplementation((key) => {
          if (key === 'enable_ticket_creation_via_reaction') {
            return Promise.resolve(true);
          }
          if (key === 'emoji_to_create_ticket') {
            return Promise.resolve('different_emoji'); // Different emoji
          }
          return Promise.resolve(null);
        });

        // Execute
        await handler.handleTicketCreation(mockEvent);

        // Verify
        expect(mockLogger.debug).toHaveBeenCalledWith(
          expect.stringContaining('Emoji to create a ticket is not the same'),
        );
        expect(mockSlackApiProvider.sendEphemeral).not.toHaveBeenCalled();
      },
    );

    it.todo(
      'should send the default ticket creation block if no forms are available',
      async () => {
        // Setup
        const mockEvent = {
          event: {
            type: 'reaction_added',
            user: 'U12345',
            reaction: 'ticket',
            item: {
              type: 'message',
              channel: 'C12345',
              ts: '**********.123456',
            },
          },
          context: {
            installation: {
              id: 'inst-1',
              name: 'Test Installation',
              teamId: 'T12345',
              botToken: 'xoxb-token',
              organization: { id: 'org-1' },
            },
            organization: { id: 'org-1' },
          },
        } as SlackEventMap['reaction_added'];

        const mockPlatformTeamMapping = {
          platformTeam: {
            id: 'team-1',
            uid: 'team-uid-1',
          },
        };

        (mockTeamChannelMapRepo.findByCondition as Mock).mockResolvedValue(
          mockPlatformTeamMapping,
        );
        (mockSettingsCore.getValue as Mock).mockImplementation((key) => {
          if (key === 'enable_ticket_creation_via_reaction') {
            return Promise.resolve(true);
          }
          if (key === 'emoji_to_create_ticket') {
            return Promise.resolve('ticket'); // Same emoji
          }
          return Promise.resolve(null);
        });
        (mockFormBuilderService.getForms as Mock).mockResolvedValue([]); // No forms available

        // Execute
        await handler.handleTicketCreation(mockEvent);

        // Verify
        expect(mockCreateNewTicketBlock.build).toHaveBeenCalled();
        expect(mockSlackApiProvider.sendEphemeral).toHaveBeenCalledWith(
          mockEvent.context.installation.botToken,
          {
            channel: mockEvent.event.item.channel,
            thread_ts: mockEvent.event.item.ts,
            user: mockEvent.event.user,
            blocks: expect.any(Array),
          },
        );
      },
    );

    it.todo(
      'should send the form selector if forms are available and require_form is true',
    );

    it.todo(
      'should skip form selector and directly create ticket when require_form is false',
    );
  });
});
