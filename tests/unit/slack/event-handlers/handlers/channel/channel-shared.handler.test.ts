import { beforeEach, describe, expect, it, vi } from 'vitest';
import { TransactionService } from '../../../../../../src/database/common';
import { ChannelsRepository } from '../../../../../../src/database/entities/channels/repositories';
import { ThenaPlatformApiProvider } from '../../../../../../src/external/provider/thena-platform-api.provider';
import { SlackChannelSharedHandler } from '../../../../../../src/slack/event-handlers/handlers/channel/channel-shared.handler';
import { SlackExternalUsersSyncJob } from '../../../../../../src/slack/processors/jobs';
import { SlackWebAPIService } from '../../../../../../src/slack/providers/slack-apis/slack-apis.service';
import { CUSTOM_LOGGER_TOKEN } from '../../../../../../src/utils';

describe('SlackChannelSharedHandler', () => {
  let handler: SlackChannelSharedHandler;
  let mockLogger: any;
  let mockTransactionService: any;
  let mockChannelsRepository: any;
  let mockSlackWebApiService: any;
  let mockPlatformApiProvider: any;
  let mockSlackExternalUsersSyncJob: any;
  let mockInstallation: any;

  beforeEach(() => {
    mockLogger = {
      log: vi.fn(),
      debug: vi.fn(),
      error: vi.fn(),
      warn: vi.fn(),
    };

    mockTransactionService = {
      runInTransaction: vi.fn().mockImplementation(async (callback) => {
        return callback('mock-txn-context');
      }),
    };

    mockChannelsRepository = {
      findByCondition: vi.fn(),
      updateWithTxn: vi.fn(),
    };

    mockSlackWebApiService = {
      getConversationInfo: vi.fn(),
      getTeamInfo: vi.fn(),
    };

    mockPlatformApiProvider = {
      getAccountsByDomains: vi.fn(),
      createAccount: vi.fn(),
      updateAccount: vi.fn(),
      createCustomObjectRecord: vi.fn(),
    };

    mockSlackExternalUsersSyncJob = {
      execute: vi.fn(),
    };

    mockInstallation = {
      id: 'test-installation-id',
      name: 'Test Installation',
      teamId: 'T12345',
      botToken: 'xoxb-test-token',
      organization: {
        id: 'test-org-id',
      },
      platformDump: {
        customFields: {
          accountId: 'account-id-field',
          slackChannelId: 'slack-channel-id-field',
          slackChannelName: 'slack-channel-name-field',
          slackTeamId: 'slack-team-id-field',
          slackTeamName: 'slack-team-name-field',
        },
        customObjects: {
          accountCustomObjectId: 'account-custom-object-id',
        },
      },
    };

    handler = new SlackChannelSharedHandler(
      mockLogger,
      mockTransactionService,
      mockChannelsRepository,
      mockSlackWebApiService,
      mockPlatformApiProvider,
      mockSlackExternalUsersSyncJob
    );
  });

  describe('canHandle', () => {
    it('should return true for channel_shared events', () => {
      const event = {
        event: {
          type: 'channel_shared',
          channel: 'C12345',
          connected_team_id: 'T67890',
        },
      } as any;

      expect(handler.canHandle(event)).toBe(true);
    });

    it('should return false for other event types', () => {
      const event = {
        event: {
          type: 'channel_unshared',
          channel: 'C12345',
          connected_team_id: 'T67890',
        },
      } as any;

      expect(handler.canHandle(event)).toBe(false);
    });
  });

  describe('handle', () => {
    it.todo('should handle valid channel_shared events with account creation', async () => {
      const event = {
        context: {
          installation: mockInstallation,
        },
        event: {
          type: 'channel_shared',
          channel: 'C12345',
          connected_team_id: 'T67890',
        },
      } as any;

      const mockChannel = {
        id: 'channel-db-id',
        channelId: 'C12345',
        name: 'test-channel',
        isShared: false,
        sharedTeamIds: [],
        platformDump: {
          accounts: [],
        },
      };

      const mockConversation = {
        id: 'C12345',
        name: 'test-channel',
        is_channel: true,
      };

      const mockTeam = {
        id: 'T67890',
        name: 'Connected Team',
        domain: 'connected-team.slack.com',
        icon: {
          image_102: 'https://example.com/icon.png',
        },
      };

      const mockAccount = {
        id: 'account-123',
        name: 'Connected Team',
        primaryDomain: 'connected-team.slack.com',
      };

      const mockCustomObjectRecord = {
        id: 'custom-object-record-123',
      };

      mockTransactionService.runInTransaction.mockImplementation(async (callback) => {
        return callback('mock-txn-context');
      });

      mockChannelsRepository.findByCondition.mockResolvedValue(mockChannel);
      
      mockSlackWebApiService.getConversationInfo.mockResolvedValue({
        ok: true,
        channel: mockConversation,
      });
      
      mockSlackWebApiService.getTeamInfo.mockResolvedValue({
        ok: true,
        team: mockTeam,
      });
      
      mockPlatformApiProvider.getAccountsByDomains.mockResolvedValue([]);
      mockPlatformApiProvider.createAccount.mockResolvedValue(mockAccount);
      mockPlatformApiProvider.createCustomObjectRecord.mockResolvedValue(mockCustomObjectRecord);

      await handler.handle(event);

      expect(mockSlackWebApiService.getConversationInfo).toHaveBeenCalledWith(
        mockInstallation.botToken,
        { channel: 'C12345' }
      );

      expect(mockChannelsRepository.findByCondition).toHaveBeenCalledWith({
        where: {
          channelId: 'C12345',
          installation: { id: 'test-installation-id' },
        },
      });

      expect(mockSlackWebApiService.getTeamInfo).toHaveBeenCalledWith(
        mockInstallation.botToken,
        { team: 'T67890' }
      );

      expect(mockPlatformApiProvider.getAccountsByDomains).toHaveBeenCalledWith(
        mockInstallation,
        ['connected-team.slack.com']
      );

      expect(mockPlatformApiProvider.createAccount).toHaveBeenCalledWith(
        mockInstallation,
        expect.objectContaining({
          name: 'Connected Team',
          primaryDomain: 'connected-team.slack.com',
          logo: 'https://example.com/icon.png',
          source: 'slack',
        })
      );

      expect(mockPlatformApiProvider.createCustomObjectRecord).toHaveBeenCalledWith(
        mockInstallation,
        expect.objectContaining({
          customObjectId: 'account-custom-object-id',
          customFieldValues: expect.arrayContaining([
            {
              customFieldId: 'account-id-field',
              data: [{ value: 'account-123' }],
            },
            {
              customFieldId: 'slack-channel-id-field',
              data: [{ value: 'C12345' }],
            },
          ]),
        })
      );

      expect(mockSlackExternalUsersSyncJob.execute).toHaveBeenCalledWith(
        mockInstallation,
        [mockChannel]
      );
    });

    it.todo('should handle valid channel_shared events with account update', async () => {
      const event = {
        context: {
          installation: mockInstallation,
        },
        event: {
          type: 'channel_shared',
          channel: 'C12345',
          connected_team_id: 'T67890',
        },
      } as any;

      const mockChannel = {
        id: 'channel-db-id',
        channelId: 'C12345',
        name: 'test-channel',
        isShared: false,
        sharedTeamIds: [],
        platformDump: {
          accounts: [],
        },
      };

      const mockConversation = {
        id: 'C12345',
        name: 'test-channel',
        is_channel: true,
      };

      const mockTeam = {
        id: 'T67890',
        name: 'Connected Team',
        domain: 'connected-team.slack.com',
        icon: {
          image_102: 'https://example.com/icon.png',
        },
      };

      const existingAccount = {
        id: 'account-123',
        name: 'Connected Team',
        primaryDomain: 'connected-team.slack.com',
        metadata: {
          sinks: {
            slack: {
              syncStatus: 'success',
              lastSyncedAt: '2023-01-01T00:00:00.000Z',
              channels: [],
            },
          },
        },
      };

      const mockCustomObjectRecord = {
        id: 'custom-object-record-123',
      };

      mockChannelsRepository.findByCondition.mockResolvedValue(mockChannel);
      
      mockSlackWebApiService.getConversationInfo.mockResolvedValue({
        ok: true,
        channel: mockConversation,
      });
      
      mockSlackWebApiService.getTeamInfo.mockResolvedValue({
        ok: true,
        team: mockTeam,
      });
      
      mockPlatformApiProvider.getAccountsByDomains.mockResolvedValue([existingAccount]);
      mockPlatformApiProvider.updateAccount.mockResolvedValue(existingAccount);
      mockPlatformApiProvider.createCustomObjectRecord.mockResolvedValue(mockCustomObjectRecord);

      await handler.handle(event);

      expect(mockPlatformApiProvider.updateAccount).toHaveBeenCalledWith(
        mockInstallation,
        'account-123',
        expect.objectContaining({
          metadata: expect.objectContaining({
            sinks: expect.objectContaining({
              slack: expect.objectContaining({
                channels: expect.arrayContaining([
                  expect.objectContaining({
                    teamId: 'T67890',
                    teamName: 'Connected Team',
                    channelId: 'C12345',
                    channelName: 'test-channel',
                  }),
                ]),
              }),
            }),
          }),
        })
      );
    });

    it('should handle when conversation info fails', async () => {
      const event = {
        context: {
          installation: mockInstallation,
        },
        event: {
          type: 'channel_shared',
          channel: 'C12345',
          connected_team_id: 'T67890',
        },
      } as any;

      mockSlackWebApiService.getConversationInfo.mockResolvedValue({
        ok: false,
        error: 'channel_not_found',
      });

      await handler.handle(event);

      expect(mockLogger.error).toHaveBeenCalledWith(
        `[ChannelSharedHandler] Failed to get conversation info for channel C12345, channel_not_found`
      );

      expect(mockChannelsRepository.findByCondition).not.toHaveBeenCalled();
    });

    it('should handle when channel is not found', async () => {
      const event = {
        context: {
          installation: mockInstallation,
        },
        event: {
          type: 'channel_shared',
          channel: 'C12345',
          connected_team_id: 'T67890',
        },
      } as any;

      const mockConversation = {
        id: 'C12345',
        name: 'test-channel',
        is_channel: true,
      };

      mockSlackWebApiService.getConversationInfo.mockResolvedValue({
        ok: true,
        channel: mockConversation,
      });

      mockChannelsRepository.findByCondition.mockResolvedValue(null);

      await handler.handle(event);

      expect(mockLogger.error).toHaveBeenCalledWith(
        `[ChannelSharedHandler] Failed to find channel data for channel C12345, test-installation-id`
      );

      expect(mockSlackWebApiService.getTeamInfo).not.toHaveBeenCalled();
    });

    it.todo('should handle when team info fails', async () => {
      const event = {
        context: {
          installation: mockInstallation,
        },
        event: {
          type: 'channel_shared',
          channel: 'C12345',
          connected_team_id: 'T67890',
        },
      } as any;

      const mockChannel = {
        id: 'channel-db-id',
        channelId: 'C12345',
        name: 'test-channel',
        isShared: false,
        sharedTeamIds: [],
        platformDump: {
          accounts: [],
        },
      };

      const mockConversation = {
        id: 'C12345',
        name: 'test-channel',
        is_channel: true,
      };

      mockChannelsRepository.findByCondition.mockResolvedValue(mockChannel);
      
      mockSlackWebApiService.getConversationInfo.mockResolvedValue({
        ok: true,
        channel: mockConversation,
      });
      
      mockSlackWebApiService.getTeamInfo.mockResolvedValue({
        ok: false,
        error: 'team_not_found',
      });

      await handler.handle(event);

      expect(mockLogger.error).toHaveBeenCalledWith(
        `[ChannelSharedHandler] Failed to get team info for team T67890, team_not_found`
      );
    });

    it('should handle invalid events', async () => {
      const event = {
        context: {
          installation: mockInstallation,
        },
        event: {
          type: 'channel_shared',
          connected_team_id: 'T67890',
        },
      } as any;

      await handler.handle(event);

      expect(mockLogger.error).toHaveBeenCalledWith(
        '[ChannelSharedHandler] Failed to handle channel shared event',
        expect.any(String)
      );
    });

    it('should handle non-Error errors', async () => {
      const event = {
        context: {
          installation: mockInstallation,
        },
        event: {
          type: 'channel_shared',
          channel: 'C12345',
          connected_team_id: 'T67890',
        },
      } as any;

      mockSlackWebApiService.getConversationInfo.mockRejectedValue('String error');

      const consoleErrorSpy = vi.spyOn(console, 'error').mockImplementation(() => {});

      await handler.handle(event);

      expect(consoleErrorSpy).toHaveBeenCalledWith(
        '[ChannelSharedHandler] Failed to handle channel shared event',
        'String error'
      );

      consoleErrorSpy.mockRestore();
    });
  });

  describe('syncChannelMembers', () => {
    it('should handle errors during channel member sync', async () => {
      const mockChannel = {
        id: 'channel-db-id',
        channelId: 'C12345',
        name: 'test-channel',
      };

      mockSlackExternalUsersSyncJob.execute.mockRejectedValue(new Error('Sync failed'));

      const consoleErrorSpy = vi.spyOn(console, 'error').mockImplementation(() => {});

      await (handler as any).syncChannelMembers(mockInstallation, [mockChannel]);

      expect(mockLogger.error).toHaveBeenCalledWith(
        `[ChannelSharedHandler] Failed to sync channel members for channel C12345, test-installation-id`,
        expect.any(String)
      );

      consoleErrorSpy.mockRestore();
    });
  });
});
