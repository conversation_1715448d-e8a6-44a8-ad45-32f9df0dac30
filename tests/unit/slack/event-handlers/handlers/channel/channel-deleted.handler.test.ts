import { SlackChannelDeletedHandler } from '../../../../../../src/slack/event-handlers/handlers/channel/channel-deleted.handler';
import { CUSTOM_LOGGER_TOKEN } from '../../../../../../src/utils';
import { SlackAuditLogRepository } from '../../../../../../src/database/entities/slack-audit-logs/repositories/slack-audit-log.repository';
import { Installations, Channels, CustomerContacts } from '../../../../../../src/database/entities';
import { ChannelsRepository } from '../../../../../../src/database/entities/channels/repositories';
import { Repository } from 'typeorm';
import { AuditLogOp, AuditLogVisibility } from '../../../../../../src/database/constants';
import { SlackChannelService } from '../../../../../../src/slack/services/slack-channel.service';
import { TransactionService } from '../../../../../../src/database/common';
import { ThenaPlatformApiProvider } from '../../../../../../src/external/provider/thena-platform-api.provider';
import { ThenaAppsPlatformApiProvider } from '../../../../../../src/external/provider/thena-apps-platform-api.provider';
import { describe, beforeEach, afterEach, it, expect, vi } from 'vitest';

describe('SlackChannelDeletedHandler', () => {
  let handler: SlackChannelDeletedHandler;
  let channelsRepository: ChannelsRepository;
  let customerContactsRepository: Repository<CustomerContacts>;
  let slackAuditLogRepository: SlackAuditLogRepository;
  let slackChannelService: SlackChannelService;
  let transactionService: TransactionService;
  let thenaPlatformApiProvider: ThenaPlatformApiProvider;
  let thenaAppsPlatformApiProvider: ThenaAppsPlatformApiProvider;
  let mockLogger: any;

  const mockInstallation = {
    id: 'installation-id',
    botToken: 'xoxb-123456',
    teamId: 'team1',
    name: 'Test Workspace',
    organization: { id: 'org1' },
    platformDump: {
      customObjects: {
        accountCustomObjectId: 'acc-obj-id',
        contactCustomObjectId: 'contact-obj-id',
      },
      customFields: {
        slackChannelId: 'channel-field-id',
      },
    },
  } as Installations;

  beforeEach(async () => {
    mockLogger = {
      log: vi.fn(),
      debug: vi.fn(),
      error: vi.fn(),
      warn: vi.fn(),
    };

    // Create mock objects
    channelsRepository = {
      findByCondition: vi.fn(),
      updateWithTxn: vi.fn(),
      delete: vi.fn(),
    } as any;

    customerContactsRepository = {
      find: vi.fn(),
      save: vi.fn(),
    } as any;

    slackAuditLogRepository = {
      recordAuditLog: vi.fn(),
    } as any;

    slackChannelService = {
      getAllCustomersFromChannel: vi.fn(),
    } as any;

    transactionService = {
      runInTransaction: vi.fn((callback) => callback({ entityManager: {} })),
    } as any;

    thenaPlatformApiProvider = {
      getAccountsByIds: vi.fn(),
      getCustomObjectRecordsByIds: vi.fn(),
      getCustomerContactsByIds: vi.fn(),
      updateAccount: vi.fn(),
    } as any;

    thenaAppsPlatformApiProvider = {
      postEventsToPlatform: vi.fn(),
      proxy: vi.fn(),
    } as any;

    // Directly instantiate the handler with all dependencies
    handler = new SlackChannelDeletedHandler(
      mockLogger,
      transactionService,
      channelsRepository,
      customerContactsRepository,
      slackChannelService,
      slackAuditLogRepository,
      thenaPlatformApiProvider,
      thenaAppsPlatformApiProvider
    );
  });

  afterEach(() => {
    vi.clearAllMocks();
  });

  describe('canHandle', () => {
    it('should return true if event type is channel_deleted', () => {
      const event = {
        event: {
          type: 'channel_deleted',
          channel: 'C12345',
        },
      };

      expect(handler.canHandle(event as any)).toBe(true);
    });

    it('should return false if event type is not channel_deleted', () => {
      const event = {
        event: {
          type: 'some_other_event',
        },
      };

      expect(handler.canHandle(event as any)).toBe(false);
    });
  });

  describe('handle', () => {
    it('should handle channel_deleted event for a non-shared channel', async () => {
      // Arrange
      const mockEvent = {
        context: {
          installation: mockInstallation,
          organization: mockInstallation.organization,
        },
        event: {
          type: 'channel_deleted',
          channel: 'C12345',
          event_ts: '**********.123456',
        },
      };

      // Mock channel lookup
      const mockChannel = {
        id: 'db-channel-id',
        channelId: 'C12345',
        name: 'general',
        isShared: false,
        platformDump: {
          accounts: [],
        },
        channelDump: {},
        slackCreatedAt: '*********',
        isBotActive: true,
        isBotJoined: true,
        installation: mockInstallation,
        organization: mockInstallation.organization,
      } as unknown as Channels;

      vi.spyOn(channelsRepository, 'findByCondition').mockResolvedValue(mockChannel);

      // Act
      await handler.handle(mockEvent as any);

      // Assert
      // Check audit log recording
      expect(slackAuditLogRepository.recordAuditLog).toHaveBeenCalledWith(
        expect.objectContaining({
          eventTs: '**********.123456',
          externalId: 'C12345',
          op: AuditLogOp.INFO,
          visibility: AuditLogVisibility.ORGANIZATION,
          installation: mockInstallation,
          organization: mockInstallation.organization,
        })
      );

      // Check channel update in transaction
      expect(transactionService.runInTransaction).toHaveBeenCalled();
      expect(channelsRepository.updateWithTxn).toHaveBeenCalledWith(
        expect.anything(),
        { id: 'db-channel-id' },
        expect.objectContaining({
          slackDeletedAt: expect.any(String),
          isBotActive: false,
          isBotJoined: false,
        })
      );

      // Check final audit log
      expect(slackAuditLogRepository.recordAuditLog).toHaveBeenCalledWith(
        expect.objectContaining({
          op: AuditLogOp.DELETED,
          visibility: AuditLogVisibility.ORGANIZATION,
        })
      );
    });

    it('should handle channel_deleted event for a shared channel with customers and accounts', async () => {
      // Arrange
      const mockEvent = {
        context: {
          installation: mockInstallation,
          organization: mockInstallation.organization,
        },
        event: {
          type: 'channel_deleted',
          channel: 'C12345',
          event_ts: '**********.123456',
        },
      };

      // Mock channel lookup
      const mockChannel = {
        id: 'db-channel-id',
        channelId: 'C12345',
        name: 'shared-channel',
        isShared: true,
        platformDump: {
          accounts: [
            {
              accountId: 'account1',
              customObjectRecordIds: ['record1', 'record2'],
            },
          ],
        },
      } as Channels;

      vi.spyOn(channelsRepository, 'findByCondition').mockResolvedValue(mockChannel);

      // Mock customers lookup
      const mockCustomers = [
        { 
          id: 'customer1', 
          slackId: 'U1',
          userDump: {},
          slackDeleted: false,
          name: 'Test Customer 1',
          installation: mockInstallation,
        },
        { 
          id: 'customer2', 
          slackId: 'U2',
          userDump: {},
          slackDeleted: false,
          name: 'Test Customer 2',
          installation: mockInstallation,
        }
      ] as CustomerContacts[];
      
      vi.spyOn(slackChannelService, 'getAllCustomersFromChannel').mockResolvedValue(mockCustomers);

      // Mock customer contacts with channels
      const mockChannel2 = { channelId: 'C54321' } as Channels;
      const mockCustomerContacts = [
        { 
          id: 'customer1', 
          channels: [mockChannel, mockChannel2],
          platformDump: { 
            customerContactId: 'platform-contact1',
            customObjectRecordIds: ['contact-record1'] 
          },
          userDump: {},
          slackId: 'U1',
          slackDeleted: false,
          name: 'Test Customer 1',
          installation: mockInstallation,
        },
        { 
          id: 'customer2', 
          channels: [mockChannel],
          platformDump: { 
            customerContactId: 'platform-contact2',
            customObjectRecordIds: ['contact-record2'] 
          },
          userDump: {},
          slackId: 'U2',
          slackDeleted: false,
          name: 'Test Customer 2',
          installation: mockInstallation,
        }
      ] as any; // Using any to bypass full entity requirements

      vi.spyOn(customerContactsRepository, 'find').mockResolvedValue(mockCustomerContacts);

      // Mock platform API responses - use type casting
      vi.spyOn(thenaPlatformApiProvider, 'getAccountsByIds').mockResolvedValue([
        {
          id: 'account1',
          name: 'Test Account',
          metadata: {
            sinks: {
              slack: {
                channels: [
                  { channelId: 'C12345' },
                  { channelId: 'C54321' },
                ],
              },
            },
          },
          description: '',
          source: '',
          logo: '',
        }
      ] as any); // Using any for external types

      vi.spyOn(thenaPlatformApiProvider, 'getCustomObjectRecordsByIds').mockResolvedValue([
        {
          id: 'record1',
          version: 1,
          customFieldValues: [
            {
              customFieldId: 'channel-field-id',
              data: [{ value: 'C12345' }],
            },
          ],
        },
        {
          id: 'record2',
          version: 1,
          customFieldValues: [
            {
              customFieldId: 'channel-field-id',
              data: [{ value: 'C54321' }],
            },
          ],
        }
      ] as any);

      vi.spyOn(thenaPlatformApiProvider, 'getCustomerContactsByIds').mockResolvedValue([
        {
          id: 'platform-contact1',
          firstName: 'Test',
          lastName: 'User 1',
          email: '<EMAIL>',
          phoneNumber: '',
          metadata: {
            sinks: {
              slack: {
                channels: [
                  { channelId: 'C12345' },
                  { channelId: 'C54321' },
                ],
              },
            },
          }
        },
        {
          id: 'platform-contact2',
          firstName: 'Test',
          lastName: 'User 2',
          email: '<EMAIL>',
          phoneNumber: '',
          metadata: {
            sinks: {
              slack: {
                channels: [
                  { channelId: 'C12345' },
                ],
              },
            },
          }
        }
      ] as any);

      // Act
      await handler.handle(mockEvent as any);

      // Assert
      // Check audit log recording
      expect(slackAuditLogRepository.recordAuditLog).toHaveBeenCalledWith(
        expect.objectContaining({
          eventTs: '**********.123456',
          externalId: 'C12345',
          op: AuditLogOp.INFO,
        })
      );

      // Check customer data retrieval
      expect(slackChannelService.getAllCustomersFromChannel).toHaveBeenCalledWith(
        mockInstallation,
        mockChannel
      );

      // Check platform API calls
      expect(thenaPlatformApiProvider.getAccountsByIds).toHaveBeenCalledWith(
        mockInstallation,
        ['account1']
      );

      expect(thenaPlatformApiProvider.getCustomerContactsByIds).toHaveBeenCalledWith(
        mockInstallation,
        ['platform-contact1', 'platform-contact2']
      );

      // Check transaction was started
      expect(transactionService.runInTransaction).toHaveBeenCalled();

      // Check customer contacts were updated
      expect(customerContactsRepository.save).toHaveBeenCalledTimes(2);
    });

    it('should throw error if channel property is missing from event', async () => {
      // Arrange
      const mockEvent = {
        context: {
          installation: mockInstallation,
          organization: mockInstallation.organization,
        },
        event: {
          type: 'channel_deleted',
          event_ts: '**********.123456',
          // No channel property
        },
      };

      // Since the handler has a try/catch block, we need to spy on the error log
      const errorSpy = vi.spyOn(mockLogger, 'error');

      // Act
      await handler.handle(mockEvent as any);

      // Assert - check that error was logged
      expect(errorSpy).toHaveBeenCalled();
      expect(errorSpy.mock.calls[0][0]).toContain('Failed to handle channel deleted event');
    });

    it('should log a debug message if channel not found in database', async () => {
      // Arrange
      const mockEvent = {
        context: {
          installation: mockInstallation,
          organization: mockInstallation.organization,
        },
        event: {
          type: 'channel_deleted',
          channel: 'C12345',
          event_ts: '**********.123456',
        },
      };

      // Mock channel lookup returning null (channel not found)
      vi.spyOn(channelsRepository, 'findByCondition').mockResolvedValue(null);

      // Act
      await handler.handle(mockEvent as any);

      // Assert
      expect(mockLogger.debug).toHaveBeenCalled();
      expect(transactionService.runInTransaction).not.toHaveBeenCalled();
    });

    it('should handle unexpected errors', async () => {
      // Arrange
      const mockEvent = {
        context: {
          installation: mockInstallation,
          organization: mockInstallation.organization,
        },
        event: {
          type: 'channel_deleted',
          channel: 'C12345',
          event_ts: '**********.123456',
        },
      };

      // Mock unexpected error
      vi.spyOn(channelsRepository, 'findByCondition').mockImplementation(() => {
        throw new Error('Unexpected error');
      });

      // Act
      await handler.handle(mockEvent as any);

      // Assert
      expect(mockLogger.error).toHaveBeenCalled();
      expect(slackAuditLogRepository.recordAuditLog).toHaveBeenCalledWith(
        expect.objectContaining({
          op: AuditLogOp.ERROR,
          visibility: AuditLogVisibility.ORGANIZATION,
        })
      );
    });
  });
}); 