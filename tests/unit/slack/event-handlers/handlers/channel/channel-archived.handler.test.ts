import { beforeEach, describe, expect, it, vi } from 'vitest';
import { TransactionService } from '../../../../../../src/database/common';
import { AuditLogOp, AuditLogVisibility } from '../../../../../../src/database/constants';
import { ChannelsRepository } from '../../../../../../src/database/entities/channels/repositories';
import { SlackAuditLogRepository } from '../../../../../../src/database/entities/slack-audit-logs/repositories/slack-audit-log.repository';
import { EmittableSlackEvents } from '../../../../../../src/external/provider/constants/platform-events.constants';
import { ThenaAppsPlatformApiProvider } from '../../../../../../src/external/provider/thena-apps-platform-api.provider';
import { SlackChannelArchivedHandler } from '../../../../../../src/slack/event-handlers/handlers/channel/channel-archived.handler';
import { CUSTOM_LOGGER_TOKEN } from '../../../../../../src/utils';

describe('SlackChannelArchivedHandler', () => {
  let handler: SlackChannelArchivedHandler;
  let mockLogger: any;
  let mockTransactionService: any;
  let mockChannelsRepository: any;
  let mockSlackAuditLogRepository: any;
  let mockThenaAppsPlatformApiProvider: any;
  let mockInstallation: any;
  let mockOrganization: any;

  beforeEach(() => {
    mockLogger = {
      log: vi.fn(),
      debug: vi.fn(),
      error: vi.fn(),
      warn: vi.fn(),
    };

    mockTransactionService = {
      runInTransaction: vi.fn().mockImplementation(async (callback) => {
        return callback('mock-txn-context');
      }),
    };

    mockChannelsRepository = {
      exists: vi.fn(),
      updateWithTxn: vi.fn(),
    };

    mockSlackAuditLogRepository = {
      recordAuditLog: vi.fn(),
    };

    mockThenaAppsPlatformApiProvider = {
      postEventsToPlatform: vi.fn(),
    };

    mockInstallation = {
      id: 'test-installation-id',
      name: 'Test Installation',
      teamId: 'T12345',
      organization: {
        id: 'test-org-id',
      },
    };

    mockOrganization = {
      id: 'test-org-id',
    };

    handler = new SlackChannelArchivedHandler(
      mockLogger,
      mockTransactionService,
      mockChannelsRepository,
      mockThenaAppsPlatformApiProvider,
      mockSlackAuditLogRepository
    );
  });

  describe('canHandle', () => {
    it('should return true for channel_archive events', () => {
      const event = {
        event: {
          type: 'channel_archive',
          channel: 'C12345',
          user: 'U12345',
          event_ts: '**********.123456',
        },
      } as any;

      expect(handler.canHandle(event)).toBe(true);
    });

    it('should return false for other event types', () => {
      const event = {
        event: {
          type: 'channel_unarchive',
          channel: 'C12345',
          user: 'U12345',
          event_ts: '**********.123456',
        },
      } as any;

      expect(handler.canHandle(event)).toBe(false);
    });
  });

  describe('handle', () => {
    it('should handle valid channel_archive events', async () => {
      const event = {
        context: {
          installation: mockInstallation,
          organization: mockOrganization,
        },
        event: {
          type: 'channel_archive',
          channel: 'C12345',
          user: 'U12345',
          event_ts: '**********.123456',
        },
      } as any;

      mockChannelsRepository.exists.mockResolvedValue(true);

      await handler.handle(event);

      expect(mockSlackAuditLogRepository.recordAuditLog).toHaveBeenCalledWith({
        eventTs: '**********.123456',
        activityPerformedBy: 'U12345',
        activity: 'Channel archive event received for channel id: C12345',
        description: 'Channel archive event received for channel id: C12345 for Installation Test Installation (T12345)',
        externalId: 'C12345',
        op: AuditLogOp.INFO,
        visibility: AuditLogVisibility.ORGANIZATION,
        installation: mockInstallation,
        organization: mockOrganization,
      });

      expect(mockTransactionService.runInTransaction).toHaveBeenCalled();

      expect(mockChannelsRepository.updateWithTxn).toHaveBeenCalledWith(
        'mock-txn-context',
        { channelId: 'C12345', installation: { id: 'test-installation-id' } },
        { isArchived: true, isBotActive: false, isBotJoined: false }
      );

      expect(mockSlackAuditLogRepository.recordAuditLog).toHaveBeenCalledWith({
        eventTs: '**********.123456',
        activityPerformedBy: 'U12345',
        activity: 'Channel archived successfully by U12345 channel id: C12345',
        description: 'Channel archived successfully by U12345 channel id: C12345 for Installation Test Installation (T12345)',
        externalId: 'C12345',
        op: AuditLogOp.UPDATED,
        visibility: AuditLogVisibility.ORGANIZATION,
        installation: mockInstallation,
        organization: mockOrganization,
      });

      expect(mockThenaAppsPlatformApiProvider.postEventsToPlatform).toHaveBeenCalledWith(
        mockInstallation.organization,
        {
          ...event.event,
          type: EmittableSlackEvents.CHANNEL_ARCHIVED,
        }
      );
    });

    it('should handle when channel is not found', async () => {
      const event = {
        context: {
          installation: mockInstallation,
          organization: mockOrganization,
        },
        event: {
          type: 'channel_archive',
          channel: 'C12345',
          user: 'U12345',
          event_ts: '**********.123456',
        },
      } as any;

      mockChannelsRepository.exists.mockResolvedValue(false);

      await handler.handle(event);

      expect(mockSlackAuditLogRepository.recordAuditLog).toHaveBeenCalledWith({
        eventTs: '**********.123456',
        activityPerformedBy: 'U12345',
        activity: 'Channel archive event received for channel id: C12345',
        description: 'Channel archive event received for channel id: C12345 for Installation Test Installation (T12345)',
        externalId: 'C12345',
        op: AuditLogOp.INFO,
        visibility: AuditLogVisibility.ORGANIZATION,
        installation: mockInstallation,
        organization: mockOrganization,
      });

      expect(mockLogger.debug).toHaveBeenCalledWith(
        '[ChannelArchiveHandler] The channel C12345 was not found for installation test-installation-id'
      );

      expect(mockTransactionService.runInTransaction).not.toHaveBeenCalled();

      expect(mockThenaAppsPlatformApiProvider.postEventsToPlatform).not.toHaveBeenCalled();
    });

    it('should handle invalid events', async () => {
      const event = {
        context: {
          installation: mockInstallation,
          organization: mockOrganization,
        },
        event: {
          type: 'channel_archive',
          user: 'U12345',
          event_ts: '**********.123456',
        },
      } as any;

      await handler.handle(event);

      expect(mockLogger.error).toHaveBeenCalledWith(
        expect.stringContaining('[ChannelArchivedHandler] Failed to handle channel archived event'),
        expect.any(String)
      );

      expect(mockSlackAuditLogRepository.recordAuditLog).toHaveBeenCalledWith({
        eventTs: '**********.123456',
        activityPerformedBy: 'U12345',
        activity: 'Failed to archive channel id: undefined',
        description: expect.stringContaining('Failed to archive channel id: undefined'),
        externalId: undefined,
        op: AuditLogOp.ERROR,
        visibility: AuditLogVisibility.ORGANIZATION,
        installation: mockInstallation,
        organization: mockOrganization,
      });
    });

    it('should handle transaction errors', async () => {
      const event = {
        context: {
          installation: mockInstallation,
          organization: mockOrganization,
        },
        event: {
          type: 'channel_archive',
          channel: 'C12345',
          user: 'U12345',
          event_ts: '**********.123456',
        },
      } as any;

      mockChannelsRepository.exists.mockResolvedValue(true);
      mockTransactionService.runInTransaction.mockRejectedValue(new Error('Transaction failed'));

      await handler.handle(event);

      expect(mockLogger.error).toHaveBeenCalledWith(
        expect.stringContaining('[ChannelArchivedHandler] Failed to handle channel archived event'),
        expect.any(String)
      );

      expect(mockSlackAuditLogRepository.recordAuditLog).toHaveBeenCalledWith({
        eventTs: '**********.123456',
        activityPerformedBy: 'U12345',
        activity: 'Failed to archive channel id: C12345',
        description: expect.stringContaining('Failed to archive channel id: C12345'),
        externalId: 'C12345',
        op: AuditLogOp.ERROR,
        visibility: AuditLogVisibility.ORGANIZATION,
        installation: mockInstallation,
        organization: mockOrganization,
      });
    });

    it('should handle platform API errors', async () => {
      const event = {
        context: {
          installation: mockInstallation,
          organization: mockOrganization,
        },
        event: {
          type: 'channel_archive',
          channel: 'C12345',
          user: 'U12345',
          event_ts: '**********.123456',
        },
      } as any;

      mockChannelsRepository.exists.mockResolvedValue(true);
      mockThenaAppsPlatformApiProvider.postEventsToPlatform.mockRejectedValue(new Error('API error'));

      await handler.handle(event);

      expect(mockLogger.error).toHaveBeenCalledWith(
        expect.stringContaining('[ChannelArchivedHandler] Failed to handle channel archived event'),
        expect.any(String)
      );

      expect(mockSlackAuditLogRepository.recordAuditLog).toHaveBeenCalledWith({
        eventTs: '**********.123456',
        activityPerformedBy: 'U12345',
        activity: 'Failed to archive channel id: C12345',
        description: expect.stringContaining('Failed to archive channel id: C12345'),
        externalId: 'C12345',
        op: AuditLogOp.ERROR,
        visibility: AuditLogVisibility.ORGANIZATION,
        installation: mockInstallation,
        organization: mockOrganization,
      });
    });

    it('should handle non-Error errors', async () => {
      const event = {
        context: {
          installation: mockInstallation,
          organization: mockOrganization,
        },
        event: {
          type: 'channel_archive',
          channel: 'C12345',
          user: 'U12345',
          event_ts: '**********.123456',
        },
      } as any;

      mockChannelsRepository.exists.mockResolvedValue(true);
      mockTransactionService.runInTransaction.mockRejectedValue('String error');

      const consoleErrorSpy = vi.spyOn(console, 'error').mockImplementation(() => {});

      await handler.handle(event);

      expect(consoleErrorSpy).toHaveBeenCalledWith(
        expect.stringContaining('[ChannelArchivedHandler] Failed to handle channel archived event, String error')
      );

      consoleErrorSpy.mockRestore();
    });
  });
});
