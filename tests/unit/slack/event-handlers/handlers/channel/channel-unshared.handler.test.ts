import { beforeEach, describe, expect, it, vi } from 'vitest';
import { TransactionService } from '../../../../../../src/database/common';
import { AuditLogOp, AuditLogVisibility } from '../../../../../../src/database/constants';
import { ChannelsRepository } from '../../../../../../src/database/entities/channels/repositories';
import { ThenaPlatformApiProvider } from '../../../../../../src/external/provider/thena-platform-api.provider';
import { SlackChannelUnsharedHandler } from '../../../../../../src/slack/event-handlers/handlers/channel/channel-unshared.handler';
import { SlackWebAPIService } from '../../../../../../src/slack/providers/slack-apis/slack-apis.service';
import { CUSTOM_LOGGER_TOKEN } from '../../../../../../src/utils';

describe('SlackChannelUnsharedHandler', () => {
  let handler: SlackChannelUnsharedHandler;
  let mockLogger: any;
  let mockTransactionService: any;
  let mockChannelsRepository: any;
  let mockSlackWebApiService: any;
  let mockPlatformApiProvider: any;
  let mockInstallation: any;

  beforeEach(() => {
    mockLogger = {
      log: vi.fn(),
      debug: vi.fn(),
      error: vi.fn(),
      warn: vi.fn(),
    };

    mockTransactionService = {
      runInTransaction: vi.fn().mockImplementation(async (callback) => {
        return callback('mock-txn-context');
      }),
    };

    mockChannelsRepository = {
      findByCondition: vi.fn(),
      updateWithTxn: vi.fn(),
    };

    mockSlackWebApiService = {
      getConversationInfo: vi.fn(),
      getTeamInfo: vi.fn(),
    };

    mockPlatformApiProvider = {
      getAccountsByDomains: vi.fn(),
      updateAccount: vi.fn(),
      deleteCustomObjectRecord: vi.fn(),
    };

    mockInstallation = {
      id: 'test-installation-id',
      name: 'Test Installation',
      teamId: 'T12345',
      botToken: 'xoxb-test-token',
      organization: {
        id: 'test-org-id',
      },
      platformDump: {
        customFields: {
          accountId: 'account-id-field',
          slackChannelId: 'slack-channel-id-field',
          slackChannelName: 'slack-channel-name-field',
          slackTeamId: 'slack-team-id-field',
          slackTeamName: 'slack-team-name-field',
        },
        customObjects: {
          accountCustomObjectId: 'account-custom-object-id',
        },
      },
    };

    handler = new SlackChannelUnsharedHandler(
      mockLogger,
      mockTransactionService,
      mockChannelsRepository,
      mockSlackWebApiService,
      mockPlatformApiProvider
    );
  });

  describe('canHandle', () => {
    it('should return true for channel_unshared events', () => {
      const event = {
        event: {
          type: 'channel_unshared',
          channel: 'C12345',
          previously_connected_team_id: 'T67890',
        },
      } as any;

      expect(handler.canHandle(event)).toBe(true);
    });

    it('should return false for other event types', () => {
      const event = {
        event: {
          type: 'channel_shared',
          channel: 'C12345',
          connected_team_id: 'T67890',
        },
      } as any;

      expect(handler.canHandle(event)).toBe(false);
    });
  });

  describe('handle', () => {
    it('should handle valid channel_unshared events', async () => {
      const event = {
        context: {
          installation: mockInstallation,
        },
        event: {
          type: 'channel_unshared',
          channel: 'C12345',
          previously_connected_team_id: 'T67890',
        },
      } as any;

      const mockChannel = {
        id: 'channel-db-id',
        channelId: 'C12345',
        name: 'test-channel',
        isShared: true,
        sharedTeamIds: ['T67890'],
        platformDump: {
          accounts: [
            {
              accountId: 'account-123',
              customObjectRecordIds: ['custom-object-record-123'],
            },
          ],
        },
      };

      const mockConversation = {
        id: 'C12345',
        name: 'test-channel',
        is_channel: true,
      };

      const mockTeam = {
        id: 'T67890',
        name: 'Previously Connected Team',
        domain: 'previously-connected-team.slack.com',
      };

      const existingAccount = {
        id: 'account-123',
        name: 'Previously Connected Team',
        primaryDomain: 'previously-connected-team.slack.com',
        metadata: {
          sinks: {
            slack: {
              syncStatus: 'success',
              lastSyncedAt: '2023-01-01T00:00:00.000Z',
              channels: [
                {
                  teamId: 'T67890',
                  teamName: 'Previously Connected Team',
                  channelId: 'C12345',
                  channelName: 'test-channel',
                },
              ],
            },
          },
        },
      };

      mockChannelsRepository.findByCondition.mockResolvedValue(mockChannel);
      
      mockSlackWebApiService.getConversationInfo.mockResolvedValue({
        ok: true,
        channel: mockConversation,
      });
      
      mockSlackWebApiService.getTeamInfo.mockResolvedValue({
        ok: true,
        team: mockTeam,
      });
      
      mockPlatformApiProvider.getAccountsByDomains.mockResolvedValue([existingAccount]);
      mockPlatformApiProvider.updateAccount.mockResolvedValue(existingAccount);
      mockPlatformApiProvider.deleteCustomObjectRecord.mockResolvedValue({});

      await handler.handle(event);

      expect(mockSlackWebApiService.getConversationInfo).toHaveBeenCalledWith(
        mockInstallation.botToken,
        { channel: 'C12345' }
      );

      expect(mockChannelsRepository.findByCondition).toHaveBeenCalledWith({
        where: {
          channelId: 'C12345',
          installation: { id: 'test-installation-id' },
        },
      });

      expect(mockSlackWebApiService.getTeamInfo).toHaveBeenCalledWith(
        mockInstallation.botToken,
        { team: 'T67890' }
      );

      expect(mockPlatformApiProvider.getAccountsByDomains).toHaveBeenCalledWith(
        mockInstallation,
        ['previously-connected-team.slack.com']
      );

      expect(mockPlatformApiProvider.updateAccount).toHaveBeenCalledWith(
        mockInstallation,
        'account-123',
        expect.objectContaining({
          metadata: expect.objectContaining({
            sinks: expect.objectContaining({
              slack: expect.objectContaining({
                channels: [],
              }),
            }),
          }),
        })
      );

      expect(mockPlatformApiProvider.deleteCustomObjectRecord).toHaveBeenCalledWith(
        mockInstallation,
        'account-custom-object-id',
        'custom-object-record-123'
      );

      expect(mockChannelsRepository.updateWithTxn).toHaveBeenCalledWith(
        expect.anything(),
        { channelId: 'C12345', installation: { id: 'test-installation-id' } },
        expect.objectContaining({
          isShared: false,
          channelDump: mockConversation,
          sharedTeamIds: [],
          platformDump: expect.objectContaining({
            accounts: [],
          }),
        })
      );
    });

    it('should handle when conversation info fails', async () => {
      const event = {
        context: {
          installation: mockInstallation,
        },
        event: {
          type: 'channel_unshared',
          channel: 'C12345',
          previously_connected_team_id: 'T67890',
        },
      } as any;

      mockSlackWebApiService.getConversationInfo.mockResolvedValue({
        ok: false,
        error: 'channel_not_found',
      });

      await handler.handle(event);

      expect(mockLogger.error).toHaveBeenCalledWith(
        `[ChannelUnsharedHandler] Failed to get conversation info for channel C12345, channel_not_found`
      );

      expect(mockChannelsRepository.findByCondition).not.toHaveBeenCalled();
    });

    it('should handle when channel is not found', async () => {
      const event = {
        context: {
          installation: mockInstallation,
        },
        event: {
          type: 'channel_unshared',
          channel: 'C12345',
          previously_connected_team_id: 'T67890',
        },
      } as any;

      const mockConversation = {
        id: 'C12345',
        name: 'test-channel',
        is_channel: true,
      };

      mockSlackWebApiService.getConversationInfo.mockResolvedValue({
        ok: true,
        channel: mockConversation,
      });

      mockChannelsRepository.findByCondition.mockResolvedValue(null);

      await handler.handle(event);

      expect(mockLogger.error).toHaveBeenCalledWith(
        `[ChannelSharedHandler] Failed to find channel data for channel C12345, test-installation-id`
      );

      expect(mockSlackWebApiService.getTeamInfo).not.toHaveBeenCalled();
    });

    it('should handle when team info fails', async () => {
      const event = {
        context: {
          installation: mockInstallation,
        },
        event: {
          type: 'channel_unshared',
          channel: 'C12345',
          previously_connected_team_id: 'T67890',
        },
      } as any;

      const mockChannel = {
        id: 'channel-db-id',
        channelId: 'C12345',
        name: 'test-channel',
        isShared: true,
        sharedTeamIds: ['T67890'],
        platformDump: {
          accounts: [],
        },
      };

      const mockConversation = {
        id: 'C12345',
        name: 'test-channel',
        is_channel: true,
      };

      mockChannelsRepository.findByCondition.mockResolvedValue(mockChannel);
      
      mockSlackWebApiService.getConversationInfo.mockResolvedValue({
        ok: true,
        channel: mockConversation,
      });
      
      mockSlackWebApiService.getTeamInfo.mockResolvedValue({
        ok: false,
        error: 'team_not_found',
      });

      await handler.handle(event);

      expect(mockLogger.error).toHaveBeenCalledWith(
        `[ChannelUnsharedHandler] Failed to get team info for team T67890, team_not_found`
      );
    });

    it('should handle when no account is found', async () => {
      const event = {
        context: {
          installation: mockInstallation,
        },
        event: {
          type: 'channel_unshared',
          channel: 'C12345',
          previously_connected_team_id: 'T67890',
        },
      } as any;

      const mockChannel = {
        id: 'channel-db-id',
        channelId: 'C12345',
        name: 'test-channel',
        isShared: true,
        sharedTeamIds: ['T67890'],
        platformDump: {
          accounts: [],
        },
      };

      const mockConversation = {
        id: 'C12345',
        name: 'test-channel',
        is_channel: true,
      };

      const mockTeam = {
        id: 'T67890',
        name: 'Previously Connected Team',
        domain: 'previously-connected-team.slack.com',
      };

      mockChannelsRepository.findByCondition.mockResolvedValue(mockChannel);
      
      mockSlackWebApiService.getConversationInfo.mockResolvedValue({
        ok: true,
        channel: mockConversation,
      });
      
      mockSlackWebApiService.getTeamInfo.mockResolvedValue({
        ok: true,
        team: mockTeam,
      });
      
      mockPlatformApiProvider.getAccountsByDomains.mockResolvedValue([]);

      await handler.handle(event);

      expect(mockLogger.error).toHaveBeenCalledWith(
        `[ChannelUnsharedHandler] No account found for team previously-connected-team.slack.com`
      );

      expect(mockPlatformApiProvider.updateAccount).not.toHaveBeenCalled();
    });

    it('should handle when multiple accounts are found', async () => {
      const event = {
        context: {
          installation: mockInstallation,
        },
        event: {
          type: 'channel_unshared',
          channel: 'C12345',
          previously_connected_team_id: 'T67890',
        },
      } as any;

      const mockChannel = {
        id: 'channel-db-id',
        channelId: 'C12345',
        name: 'test-channel',
        isShared: true,
        sharedTeamIds: ['T67890'],
        platformDump: {
          accounts: [],
        },
      };

      const mockConversation = {
        id: 'C12345',
        name: 'test-channel',
        is_channel: true,
      };

      const mockTeam = {
        id: 'T67890',
        name: 'Previously Connected Team',
        domain: 'previously-connected-team.slack.com',
      };

      mockChannelsRepository.findByCondition.mockResolvedValue(mockChannel);
      
      mockSlackWebApiService.getConversationInfo.mockResolvedValue({
        ok: true,
        channel: mockConversation,
      });
      
      mockSlackWebApiService.getTeamInfo.mockResolvedValue({
        ok: true,
        team: mockTeam,
      });
      
      mockPlatformApiProvider.getAccountsByDomains.mockResolvedValue([
        { id: 'account-1', name: 'Account 1' },
        { id: 'account-2', name: 'Account 2' },
      ]);

      await handler.handle(event);

      expect(mockLogger.error).toHaveBeenCalledWith(
        `[ChannelUnsharedHandler] Multiple accounts found for team previously-connected-team.slack.com`
      );

      expect(mockPlatformApiProvider.updateAccount).not.toHaveBeenCalled();
    });

    it('should handle invalid events', async () => {
      const event = {
        context: {
          installation: mockInstallation,
        },
        event: {
          type: 'channel_unshared',
          previously_connected_team_id: 'T67890',
        },
      } as any;

      await handler.handle(event);

      expect(mockLogger.error).toHaveBeenCalledWith(
        '[ChannelUnsharedHandler] Failed to handle channel unshared event',
        expect.any(String)
      );
    });

    it('should handle non-Error errors', async () => {
      const event = {
        context: {
          installation: mockInstallation,
        },
        event: {
          type: 'channel_unshared',
          channel: 'C12345',
          previously_connected_team_id: 'T67890',
        },
      } as any;

      mockSlackWebApiService.getConversationInfo.mockRejectedValue('String error');

      const consoleErrorSpy = vi.spyOn(console, 'error').mockImplementation(() => {});

      await handler.handle(event);

      expect(consoleErrorSpy).toHaveBeenCalledWith(
        '[ChannelUnsharedHandler] Failed to handle channel unshared event',
        'String error'
      );

      consoleErrorSpy.mockRestore();
    });
  });
});
