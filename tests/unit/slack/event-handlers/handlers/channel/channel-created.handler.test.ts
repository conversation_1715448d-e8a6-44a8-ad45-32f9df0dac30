import { Mock, beforeEach, describe, expect, it, vi } from 'vitest';
import { TransactionService } from '../../../../../../src/database/common';
import { AuditLogOp, AuditLogVisibility } from '../../../../../../src/database/constants';
import { ChannelType } from '../../../../../../src/database/entities/channels/channels.entity';
import { ChannelsRepository } from '../../../../../../src/database/entities/channels/repositories';
import { SlackAuditLogRepository } from '../../../../../../src/database/entities/slack-audit-logs/repositories/slack-audit-log.repository';
import { EmittableSlackEvents } from '../../../../../../src/external/provider/constants/platform-events.constants';
import { ThenaAppsPlatformApiProvider } from '../../../../../../src/external/provider/thena-apps-platform-api.provider';
import { ILogger } from '../../../../../../src/utils';
import { SlackWebAPIService } from '../../../../../../src/slack/providers/slack-apis/slack-apis.service';
import { SlackEventMap } from '../../../../../../src/slack/event-handlers/interface';
import { SlackChannelCreatedHandler } from '../../../../../../src/slack/event-handlers/handlers/channel/channel-created.handler';

describe('SlackChannelCreatedHandler', () => {
  let handler: SlackChannelCreatedHandler;
  let mockLogger: ILogger;
  let mockTransactionService: TransactionService;
  let mockChannelsRepository: ChannelsRepository;
  let mockSlackAuditLogRepository: SlackAuditLogRepository;
  let mockSlackWebApiService: SlackWebAPIService;
  let mockThenaAppsPlatformApiProvider: ThenaAppsPlatformApiProvider;

  beforeEach(() => {
    vi.resetAllMocks();

    // Mock the logger
    mockLogger = {
      log: vi.fn(),
      error: vi.fn(),
      warn: vi.fn(),
      debug: vi.fn(),
      verbose: vi.fn(),
    } as unknown as ILogger;

    // Mock the TransactionService
    mockTransactionService = {
      runInTransaction: vi.fn().mockImplementation(async (callback) => {
        return callback({ id: 'txn-1' });
      }),
    } as unknown as TransactionService;

    // Mock the ChannelsRepository
    mockChannelsRepository = {
      upsertWithTxn: vi.fn(),
    } as unknown as ChannelsRepository;

    // Mock the SlackAuditLogRepository
    mockSlackAuditLogRepository = {
      recordAuditLog: vi.fn(),
    } as unknown as SlackAuditLogRepository;

    // Mock the SlackWebAPIService
    mockSlackWebApiService = {
      getConversationInfo: vi.fn(),
    } as unknown as SlackWebAPIService;

    // Mock the ThenaAppsPlatformApiProvider
    mockThenaAppsPlatformApiProvider = {
      postEventsToPlatform: vi.fn(),
    } as unknown as ThenaAppsPlatformApiProvider;

    // Create the handler
    handler = new SlackChannelCreatedHandler(
      mockLogger,
      mockTransactionService,
      mockChannelsRepository,
      mockSlackAuditLogRepository,
      mockSlackWebApiService,
      mockThenaAppsPlatformApiProvider
    );
  });

  describe('canHandle', () => {
    it('should return true if the event type is channel_created', () => {
      // Setup
      const event = {
        event: {
          type: 'channel_created',
          channel: {
            id: 'C12345',
            name: 'test-channel',
            created: **********,
            creator: 'U12345',
          },
        },
      } as SlackEventMap['channel_created'];

      // Execute
      const result = handler.canHandle(event);

      // Verify
      expect(result).toBe(true);
    });

    it('should return false if the event type is not channel_created', () => {
      // Setup
      const event = {
        event: {
          type: 'not_channel_created',
        },
      } as unknown as SlackEventMap['channel_created'];

      // Execute
      const result = handler.canHandle(event);

      // Verify
      expect(result).toBe(false);
    });
  });

  describe('handle', () => {
    it('should handle a channel_created event successfully', async () => {
      // Setup
      const mockEvent = {
        event: {
          type: 'channel_created',
          channel: {
            id: 'C12345',
            name: 'test-channel',
            created: **********,
            creator: 'U12345',
          },
          event_ts: '**********.123456',
        },
        context: {
          installation: {
            id: 'inst-1',
            name: 'Test Installation',
            teamId: 'T12345',
            botToken: 'xoxb-token',
            organization: { id: 'org-1' },
          },
          organization: { id: 'org-1' },
        },
      } as SlackEventMap['channel_created'];

      const mockConversationResponse = {
        ok: true,
        channel: {
          id: 'C12345',
          name: 'test-channel',
          name_normalized: 'test-channel',
          created: **********,
          creator: 'U12345',
          is_archived: false,
          is_private: false,
          is_ext_shared: false,
        },
      };

      (mockSlackWebApiService.getConversationInfo as Mock).mockResolvedValue(
        mockConversationResponse
      );

      // Execute
      await handler.handle(mockEvent);

      // Verify
      expect(mockSlackAuditLogRepository.recordAuditLog).toHaveBeenCalledTimes(2);
      expect(mockSlackAuditLogRepository.recordAuditLog).toHaveBeenCalledWith(
        expect.objectContaining({
          eventTs: mockEvent.event.event_ts,
          activityPerformedBy: mockEvent.event.channel.creator,
          activity: expect.stringContaining(mockEvent.event.channel.id),
          op: AuditLogOp.INFO,
          visibility: AuditLogVisibility.ORGANIZATION,
          installation: mockEvent.context.installation,
          organization: mockEvent.context.organization,
        })
      );

      expect(mockSlackWebApiService.getConversationInfo).toHaveBeenCalledWith(
        mockEvent.context.installation.botToken,
        {
          channel: mockEvent.event.channel.id,
        }
      );

      expect(mockTransactionService.runInTransaction).toHaveBeenCalled();
      expect(mockChannelsRepository.upsertWithTxn).toHaveBeenCalledWith(
        expect.anything(),
        expect.objectContaining({
          name: mockConversationResponse.channel.name,
          channelId: mockConversationResponse.channel.id,
          channelType: ChannelType.NOT_SETUP,
          isArchived: mockConversationResponse.channel.is_archived,
          isPrivate: mockConversationResponse.channel.is_private,
          isShared: mockConversationResponse.channel.is_ext_shared,
          installation: { id: mockEvent.context.installation.id },
          organization: { id: mockEvent.context.organization.id },
        }),
        expect.anything()
      );

      expect(mockThenaAppsPlatformApiProvider.postEventsToPlatform).toHaveBeenCalledWith(
        mockEvent.context.installation.organization,
        {
          ...mockEvent.event,
          type: EmittableSlackEvents.CHANNEL_CREATED,
        }
      );
    });

    it('should throw an error if the channel property is not present', async () => {
      // Setup
      const mockEvent = {
        event: {
          type: 'channel_created',
          event_ts: '**********.123456',
        },
        context: {
          installation: {
            id: 'inst-1',
            name: 'Test Installation',
            teamId: 'T12345',
            organization: { id: 'org-1' },
          },
          organization: { id: 'org-1' },
        },
      } as SlackEventMap['channel_created'];

      // Execute
      await handler.handle(mockEvent);

      // Verify
      expect(mockLogger.error).toHaveBeenCalledWith(
        expect.stringContaining('Failed to handle channel created event'),
        expect.any(String)
      );
      expect(mockSlackAuditLogRepository.recordAuditLog).toHaveBeenCalledWith(
        expect.objectContaining({
          op: AuditLogOp.ERROR,
          visibility: AuditLogVisibility.ORGANIZATION,
        })
      );
    });

    it('should handle errors when getting conversation info', async () => {
      // Setup
      const mockEvent = {
        event: {
          type: 'channel_created',
          channel: {
            id: 'C12345',
            name: 'test-channel',
            created: **********,
            creator: 'U12345',
          },
          event_ts: '**********.123456',
        },
        context: {
          installation: {
            id: 'inst-1',
            name: 'Test Installation',
            teamId: 'T12345',
            botToken: 'xoxb-token',
            organization: { id: 'org-1' },
          },
          organization: { id: 'org-1' },
        },
      } as SlackEventMap['channel_created'];

      const mockErrorResponse = {
        ok: false,
        error: 'channel_not_found',
      };

      (mockSlackWebApiService.getConversationInfo as Mock).mockResolvedValue(
        mockErrorResponse
      );

      // Execute
      await handler.handle(mockEvent);

      // Verify
      // Check that the first error log contains the expected message
      expect(mockLogger.error).toHaveBeenCalledWith(
        '[ChannelCreatedHandler] Failed to get conversation info for channel C12345, channel_not_found'
      );

      // Expect the second call with the error and stack trace
      expect(mockLogger.error).toHaveBeenCalledTimes(2);

      expect(mockSlackAuditLogRepository.recordAuditLog).toHaveBeenCalledWith(
        expect.objectContaining({
          op: AuditLogOp.ERROR,
          visibility: AuditLogVisibility.ORGANIZATION,
        })
      );
    });

    it('should handle general errors', async () => {
      // Setup
      const mockEvent = {
        event: {
          type: 'channel_created',
          channel: {
            id: 'C12345',
            name: 'test-channel',
            created: **********,
            creator: 'U12345',
          },
          event_ts: '**********.123456',
        },
        context: {
          installation: {
            id: 'inst-1',
            name: 'Test Installation',
            teamId: 'T12345',
            botToken: 'xoxb-token',
            organization: { id: 'org-1' },
          },
          organization: { id: 'org-1' },
        },
      } as SlackEventMap['channel_created'];

      const error = new Error('General error');
      (mockSlackWebApiService.getConversationInfo as Mock).mockRejectedValue(error);

      // Execute
      await handler.handle(mockEvent);

      // Verify
      expect(mockLogger.error).toHaveBeenCalledWith(
        '[ChannelCreatedHandler] Failed to handle channel created event, General error',
        expect.any(String)
      );
      expect(mockSlackAuditLogRepository.recordAuditLog).toHaveBeenCalledWith(
        expect.objectContaining({
          op: AuditLogOp.ERROR,
          visibility: AuditLogVisibility.ORGANIZATION,
        })
      );
    });
  });
});
