import { beforeEach, describe, expect, it, vi } from 'vitest';
import { TransactionService } from '../../../../../../src/database/common';
import { AuditLogOp, AuditLogVisibility } from '../../../../../../src/database/constants';
import { ChannelType } from '../../../../../../src/database/entities/channels/channels.entity';
import { ChannelsRepository } from '../../../../../../src/database/entities/channels/repositories';
import { SlackAuditLogRepository } from '../../../../../../src/database/entities/slack-audit-logs/repositories/slack-audit-log.repository';
import { ThenaPlatformApiProvider } from '../../../../../../src/external/provider/thena-platform-api.provider';
import { CUSTOM_LOGGER_TOKEN } from '../../../../../../src/utils';
import { SlackWebAPIService } from '../../../../../../src/slack/providers/slack-apis/slack-apis.service';
import { SlackChannelService } from '../../../../../../src/slack/services/slack-channel.service';
import { SlackChannelRenameHandler } from '../../../../../../src/slack/event-handlers/handlers/channel/channel-rename.handler';

describe('SlackChannelRenameHandler', () => {
  let handler: SlackChannelRenameHandler;
  let mockLogger: any;
  let mockTransactionService: any;
  let mockChannelsRepository: any;
  let mockSlackAuditLogRepository: any;
  let mockSlackChannelService: any;
  let mockSlackWebAPIService: any;
  let mockThenaPlatformApiProvider: any;
  let mockInstallation: any;
  let mockOrganization: any;

  beforeEach(() => {
    mockLogger = {
      log: vi.fn(),
      debug: vi.fn(),
      error: vi.fn(),
      warn: vi.fn(),
    };

    mockTransactionService = {
      runInTransaction: vi.fn().mockImplementation(async (callback) => {
        return callback({ manager: {} });
      }),
    };

    mockChannelsRepository = {
      findByCondition: vi.fn(),
      updateWithTxn: vi.fn(),
    };

    mockSlackAuditLogRepository = {
      recordAuditLog: vi.fn(),
    };

    mockSlackChannelService = {
      getAllCustomersFromChannel: vi.fn(),
    };

    mockSlackWebAPIService = {
      sendMessage: vi.fn(),
    };

    mockThenaPlatformApiProvider = {
      getAccountsByIds: vi.fn(),
      getCustomObjectRecordsByIds: vi.fn(),
      getCustomerContactsByIds: vi.fn(),
      updateAccount: vi.fn(),
      updateCustomObjectRecord: vi.fn(),
      updateCustomerContact: vi.fn(),
    };

    mockInstallation = {
      id: 'test-installation-id',
      name: 'Test Installation',
      teamId: 'T12345',
      botToken: 'xoxb-test-token',
      platformDump: {
        customObjects: {
          accountCustomObjectId: 'account-custom-object-id',
          contactCustomObjectId: 'contact-custom-object-id',
        },
        customFields: {
          slackChannelId: 'slack-channel-id-field',
          slackChannelName: 'slack-channel-name-field',
        },
      },
    };

    mockOrganization = {
      id: 'test-org-id',
    };

    handler = new SlackChannelRenameHandler(
      mockLogger,
      mockTransactionService,
      mockChannelsRepository,
      mockSlackAuditLogRepository,
      mockSlackChannelService,
      mockSlackWebAPIService,
      mockThenaPlatformApiProvider
    );
  });

  describe('canHandle', () => {
    it('should return true for channel_rename events', () => {
      const event = {
        event: {
          type: 'channel_rename',
          channel: {
            id: 'C12345',
            name: 'new-channel-name',
          },
        },
      } as any;

      expect(handler.canHandle(event)).toBe(true);
    });

    it('should return false for other event types', () => {
      const event = {
        event: {
          type: 'channel_created',
          channel: {
            id: 'C12345',
            name: 'new-channel-name',
          },
        },
      } as any;

      expect(handler.canHandle(event)).toBe(false);
    });
  });

  describe('handle', () => {
    it('should handle valid channel_rename events', async () => {
      const event = {
        context: {
          installation: mockInstallation,
          organization: mockOrganization,
        },
        event: {
          type: 'channel_rename',
          channel: {
            id: 'C12345',
            name: 'new-channel-name',
          },
          event_ts: '**********.123456',
        },
      } as any;

      const mockChannel = {
        id: 'db-channel-id',
        channelId: 'C12345',
        name: 'old-channel-name',
        isShared: false,
        installation: mockInstallation,
        platformDump: {
          accounts: [],
        },
      };

      mockChannelsRepository.findByCondition.mockResolvedValue(mockChannel);

      await handler.handle(event);

      expect(mockSlackAuditLogRepository.recordAuditLog).toHaveBeenCalledWith(
        expect.objectContaining({
          eventTs: '**********.123456',
          activity: expect.stringContaining('Channel rename event received'),
          op: AuditLogOp.INFO,
          visibility: AuditLogVisibility.ORGANIZATION,
          installation: mockInstallation,
          organization: mockOrganization,
        })
      );

      expect(mockChannelsRepository.updateWithTxn).toHaveBeenCalledWith(
        expect.anything(),
        { channelId: 'C12345', installation: { id: 'test-installation-id' } },
        { name: 'new-channel-name' }
      );

      expect(mockSlackAuditLogRepository.recordAuditLog).toHaveBeenCalledWith(
        expect.objectContaining({
          eventTs: '**********.123456',
          activity: expect.stringContaining('Channel renamed successfully'),
          op: AuditLogOp.UPDATED,
          visibility: AuditLogVisibility.ORGANIZATION,
          installation: mockInstallation,
          organization: mockOrganization,
        })
      );
    });

    it('should handle when channel is not found', async () => {
      const event = {
        context: {
          installation: mockInstallation,
          organization: mockOrganization,
        },
        event: {
          type: 'channel_rename',
          channel: {
            id: 'C12345',
            name: 'new-channel-name',
          },
          event_ts: '**********.123456',
        },
      } as any;

      mockChannelsRepository.findByCondition.mockResolvedValue(null);

      await handler.handle(event);

      expect(mockSlackAuditLogRepository.recordAuditLog).toHaveBeenCalledWith(
        expect.objectContaining({
          eventTs: '**********.123456',
          activity: expect.stringContaining('Channel rename event received'),
          op: AuditLogOp.INFO,
          visibility: AuditLogVisibility.ORGANIZATION,
          installation: mockInstallation,
          organization: mockOrganization,
        })
      );

      expect(mockLogger.debug).toHaveBeenCalledWith(
        expect.stringContaining('The channel C12345 was not found')
      );

      expect(mockChannelsRepository.updateWithTxn).not.toHaveBeenCalled();
    });

    it('should handle shared channels with accounts and contacts', async () => {
      const event = {
        context: {
          installation: mockInstallation,
          organization: mockOrganization,
        },
        event: {
          type: 'channel_rename',
          channel: {
            id: 'C12345',
            name: 'new-channel-name',
          },
          event_ts: '**********.123456',
        },
      } as any;

      const mockChannel = {
        id: 'db-channel-id',
        channelId: 'C12345',
        name: 'old-channel-name',
        isShared: true,
        installation: mockInstallation,
        platformDump: {
          accounts: [
            { accountId: 'account-1', customObjectRecordIds: ['account-record-1'] },
            { accountId: 'account-2', customObjectRecordIds: ['account-record-2'] },
          ],
        },
      };

      const mockCustomers = [
        {
          id: 'customer-1',
          platformDump: {
            customerContactId: 'contact-1',
            customObjectRecordIds: ['contact-record-1'],
          },
        },
        {
          id: 'customer-2',
          platformDump: {
            customerContactId: 'contact-2',
            customObjectRecordIds: ['contact-record-2'],
          },
        },
      ];

      const mockAccounts = [
        {
          id: 'account-1',
          metadata: {
            sinks: {
              slack: {
                channels: [
                  { channelId: 'C12345', channelName: 'old-channel-name' },
                ],
              },
            },
          },
        },
        {
          id: 'account-2',
          metadata: {
            sinks: {
              slack: {
                channels: [
                  { channelId: 'C12345', channelName: 'old-channel-name' },
                ],
              },
            },
          },
        },
      ];

      const mockAccountCustomObjectRecords = [
        {
          id: 'account-record-1',
          customFieldValues: [
            {
              customFieldId: 'slack-channel-id-field',
              data: [{ value: 'C12345' }],
            },
          ],
        },
        {
          id: 'account-record-2',
          customFieldValues: [
            {
              customFieldId: 'slack-channel-id-field',
              data: [{ value: 'C12345' }],
            },
          ],
        },
      ];

      const mockContacts = [
        {
          id: 'contact-1',
          metadata: {
            sinks: {
              slack: {
                channels: [
                  { channelId: 'C12345', channelName: 'old-channel-name' },
                ],
              },
            },
          },
        },
        {
          id: 'contact-2',
          metadata: {
            sinks: {
              slack: {
                channels: [
                  { channelId: 'C12345', channelName: 'old-channel-name' },
                ],
              },
            },
          },
        },
      ];

      const mockContactCustomObjectRecords = [
        {
          id: 'contact-record-1',
          customFieldValues: [
            {
              customFieldId: 'slack-channel-id-field',
              data: [{ value: 'C12345' }],
            },
          ],
        },
        {
          id: 'contact-record-2',
          customFieldValues: [
            {
              customFieldId: 'slack-channel-id-field',
              data: [{ value: 'C12345' }],
            },
          ],
        },
      ];

      mockChannelsRepository.findByCondition.mockResolvedValue(mockChannel);
      mockSlackChannelService.getAllCustomersFromChannel.mockResolvedValue(mockCustomers);
      mockThenaPlatformApiProvider.getAccountsByIds.mockResolvedValue(mockAccounts);
      mockThenaPlatformApiProvider.getCustomObjectRecordsByIds
        .mockResolvedValueOnce(mockAccountCustomObjectRecords)
        .mockResolvedValueOnce(mockContactCustomObjectRecords);
      mockThenaPlatformApiProvider.getCustomerContactsByIds.mockResolvedValue(mockContacts);

      await handler.handle(event);

      expect(mockChannelsRepository.updateWithTxn).toHaveBeenCalledWith(
        expect.anything(),
        { channelId: 'C12345', installation: { id: 'test-installation-id' } },
        { name: 'new-channel-name' }
      );

      expect(mockThenaPlatformApiProvider.updateAccount).toHaveBeenCalledTimes(2);
      expect(mockThenaPlatformApiProvider.updateAccount).toHaveBeenCalledWith(
        mockInstallation,
        'account-1',
        expect.objectContaining({
          metadata: expect.objectContaining({
            sinks: expect.objectContaining({
              slack: expect.objectContaining({
                channels: expect.arrayContaining([
                  expect.objectContaining({
                    channelId: 'C12345',
                    channelName: 'new-channel-name',
                  }),
                ]),
              }),
            }),
          }),
        })
      );

      expect(mockThenaPlatformApiProvider.updateCustomObjectRecord).toHaveBeenCalled();
      
      expect(mockThenaPlatformApiProvider.updateCustomObjectRecord.mock.calls.some(call => 
        call[1].customObjectId === 'account-custom-object-id'
      )).toBe(true);

      expect(mockThenaPlatformApiProvider.updateCustomerContact).toHaveBeenCalledTimes(2);
      expect(mockThenaPlatformApiProvider.updateCustomerContact).toHaveBeenCalledWith(
        mockInstallation,
        'contact-1',
        expect.objectContaining({
          metadata: expect.objectContaining({
            sinks: expect.objectContaining({
              slack: expect.objectContaining({
                channels: expect.arrayContaining([
                  expect.objectContaining({
                    channelId: 'C12345',
                    channelName: 'new-channel-name',
                  }),
                ]),
              }),
            }),
          }),
        })
      );

      expect(mockThenaPlatformApiProvider.updateCustomObjectRecord.mock.calls.some(call => 
        call[1].customObjectId === 'contact-custom-object-id'
      )).toBe(true);
    });

    it('should handle invalid events', async () => {
      const event = {
        context: {
          installation: mockInstallation,
          organization: mockOrganization,
        },
        event: {
          type: 'channel_rename',
          event_ts: '**********.123456',
        },
      } as any;

      await handler.handle(event);

      expect(mockLogger.error).toHaveBeenCalledWith(
        expect.stringContaining('Failed to handle channel rename event'),
        expect.any(String)
      );

      expect(mockSlackAuditLogRepository.recordAuditLog).toHaveBeenCalledWith(
        expect.objectContaining({
          op: AuditLogOp.ERROR,
          visibility: AuditLogVisibility.ORGANIZATION,
          installation: mockInstallation,
          organization: mockOrganization,
        })
      );
    });

    it('should handle errors when updating accounts', async () => {
      const event = {
        context: {
          installation: mockInstallation,
          organization: mockOrganization,
        },
        event: {
          type: 'channel_rename',
          channel: {
            id: 'C12345',
            name: 'new-channel-name',
          },
          event_ts: '**********.123456',
        },
      } as any;

      const mockChannel = {
        id: 'db-channel-id',
        channelId: 'C12345',
        name: 'old-channel-name',
        isShared: true,
        installation: mockInstallation,
        platformDump: {
          accounts: [
            { accountId: 'account-1', customObjectRecordIds: ['account-record-1'] },
          ],
        },
      };

      const mockCustomers = [
        {
          id: 'customer-1',
          platformDump: {
            customerContactId: 'contact-1',
            customObjectRecordIds: ['contact-record-1'],
          },
        },
      ];

      const mockAccounts = [
        {
          id: 'account-1',
          metadata: {
            sinks: {
              slack: {
                channels: [
                  { channelId: 'C12345', channelName: 'old-channel-name' },
                ],
              },
            },
          },
        },
      ];

      mockChannelsRepository.findByCondition.mockResolvedValue(mockChannel);
      mockSlackChannelService.getAllCustomersFromChannel.mockResolvedValue(mockCustomers);
      mockThenaPlatformApiProvider.getAccountsByIds.mockResolvedValue(mockAccounts);
      mockThenaPlatformApiProvider.getCustomObjectRecordsByIds.mockResolvedValue([]);
      mockThenaPlatformApiProvider.getCustomerContactsByIds.mockResolvedValue([]);
      mockThenaPlatformApiProvider.updateAccount.mockRejectedValue(new Error('Update account error'));

      await handler.handle(event);

      expect(mockChannelsRepository.updateWithTxn).toHaveBeenCalled();

      expect(mockLogger.error).toHaveBeenCalledWith(
        expect.stringContaining('[ChannelRenameHandler] Failed to update accounts')
      );
    });

    it('should handle errors when updating custom object records', async () => {
      const event = {
        context: {
          installation: mockInstallation,
          organization: mockOrganization,
        },
        event: {
          type: 'channel_rename',
          channel: {
            id: 'C12345',
            name: 'new-channel-name',
          },
          event_ts: '**********.123456',
        },
      } as any;

      const mockChannel = {
        id: 'db-channel-id',
        channelId: 'C12345',
        name: 'old-channel-name',
        isShared: true,
        installation: mockInstallation,
        platformDump: {
          accounts: [
            { accountId: 'account-1', customObjectRecordIds: ['account-record-1'] },
          ],
        },
      };

      const mockCustomers = [
        {
          id: 'customer-1',
          platformDump: {
            customerContactId: 'contact-1',
            customObjectRecordIds: ['contact-record-1'],
          },
        },
      ];

      const mockAccounts = [
        {
          id: 'account-1',
          metadata: {
            sinks: {
              slack: {
                channels: [
                  { channelId: 'C12345', channelName: 'old-channel-name' },
                ],
              },
            },
          },
        },
      ];

      const mockAccountCustomObjectRecords = [
        {
          id: 'account-record-1',
          customFieldValues: [
            {
              customFieldId: 'slack-channel-id-field',
              data: [{ value: 'C12345' }],
            },
          ],
        },
      ];

      mockChannelsRepository.findByCondition.mockResolvedValue(mockChannel);
      mockSlackChannelService.getAllCustomersFromChannel.mockResolvedValue(mockCustomers);
      mockThenaPlatformApiProvider.getAccountsByIds.mockResolvedValue(mockAccounts);
      mockThenaPlatformApiProvider.getCustomObjectRecordsByIds.mockResolvedValue(mockAccountCustomObjectRecords);
      mockThenaPlatformApiProvider.getCustomerContactsByIds.mockResolvedValue([]);
      mockThenaPlatformApiProvider.updateAccount.mockResolvedValue({});
      mockThenaPlatformApiProvider.updateCustomObjectRecord.mockRejectedValue(new Error('Update custom object error'));

      await handler.handle(event);

      expect(mockChannelsRepository.updateWithTxn).toHaveBeenCalled();

      expect(mockLogger.error).toHaveBeenCalledWith(
        expect.stringContaining('[ChannelRenameHandler] Failed to update account custom object records')
      );
    });

    it('should handle non-Error errors', async () => {
      const event = {
        context: {
          installation: mockInstallation,
          organization: mockOrganization,
        },
        event: {
          type: 'channel_rename',
          channel: {
            id: 'C12345',
            name: 'new-channel-name',
          },
          event_ts: '**********.123456',
        },
      } as any;

      mockChannelsRepository.findByCondition.mockRejectedValue('String error');

      const consoleErrorSpy = vi.spyOn(console, 'error').mockImplementation(() => {});

      await handler.handle(event);

      expect(consoleErrorSpy).toHaveBeenCalledWith(
        expect.stringContaining('[ChannelRenameHandler] Failed to handle channel rename event')
      );

      consoleErrorSpy.mockRestore();
    });
  });

  describe('processAccounts', () => {
    it('should process accounts correctly', async () => {
      const mockChannel = {
        id: 'db-channel-id',
        channelId: 'C12345',
        name: 'old-channel-name',
      };

      const mockAccounts = [
        {
          id: 'account-1',
          metadata: {
            sinks: {
              slack: {
                channels: [
                  { channelId: 'C12345', channelName: 'old-channel-name' },
                  { channelId: 'C67890', channelName: 'other-channel' },
                ],
              },
            },
          },
        },
        {
          id: 'account-2',
          metadata: {
            sinks: {
              slack: {
                channels: [
                  { channelId: 'C67890', channelName: 'other-channel' },
                ],
              },
            },
          },
        },
      ];

      const mockCustomObjectRecords = [
        {
          id: 'account-record-1',
          customFieldValues: [
            {
              customFieldId: 'slack-channel-id-field',
              data: [{ value: 'C12345' }],
            },
          ],
        },
        {
          id: 'account-record-2',
          customFieldValues: [
            {
              customFieldId: 'slack-channel-id-field',
              data: [{ value: 'C67890' }],
            },
          ],
        },
      ];

      mockThenaPlatformApiProvider.getAccountsByIds.mockResolvedValue(mockAccounts);
      mockThenaPlatformApiProvider.getCustomObjectRecordsByIds.mockResolvedValue(mockCustomObjectRecords);

      const result = await (handler as any).processAccounts(
        mockInstallation,
        mockChannel,
        'new-channel-name',
        ['account-1', 'account-2'],
        ['account-record-1', 'account-record-2']
      );

      expect(result.accountsToUpdate).toHaveLength(2);
      expect(result.accountsToUpdate[0].id).toBe('account-1');
      expect(result.accountsToUpdate[0].data.metadata.sinks.slack.channels).toEqual([
        { channelId: 'C12345', channelName: 'new-channel-name' },
        { channelId: 'C67890', channelName: 'other-channel' },
      ]);

      expect(result.customObjectRecordsToUpdate).toHaveLength(1);
      expect(result.customObjectRecordsToUpdate[0].customObjectId).toBe('account-custom-object-id');
      expect(result.customObjectRecordsToUpdate[0].customObjectRecordId).toBe('account-record-1');
      expect(result.customObjectRecordsToUpdate[0].customFieldValues).toEqual([
        {
          customFieldId: 'slack-channel-name-field',
          data: [{ value: 'new-channel-name' }],
        },
      ]);
    });

    it('should handle empty account IDs', async () => {
      const mockChannel = {
        id: 'db-channel-id',
        channelId: 'C12345',
        name: 'old-channel-name',
      };

      const result = await (handler as any).processAccounts(
        mockInstallation,
        mockChannel,
        'new-channel-name',
        [],
        []
      );

      expect(mockLogger.error).toHaveBeenCalledWith(
        expect.stringContaining('No accounts found for channel C12345')
      );

      expect(result.accountsToUpdate).toHaveLength(0);
      expect(result.customObjectRecordsToUpdate).toHaveLength(0);
    });

    it('should handle custom object records with invalid data', async () => {
      const mockChannel = {
        id: 'db-channel-id',
        channelId: 'C12345',
        name: 'old-channel-name',
      };

      const mockAccounts = [
        {
          id: 'account-1',
          metadata: {
            sinks: {
              slack: {
                channels: [
                  { channelId: 'C12345', channelName: 'old-channel-name' },
                ],
              },
            },
          },
        },
      ];

      const mockCustomObjectRecords = [
        {
          id: 'account-record-1',
          customFieldValues: [
            {
              customFieldId: 'slack-channel-id-field',
              data: null, // Invalid data
            },
          ],
        },
        {
          id: 'account-record-2',
          customFieldValues: [
            {
              customFieldId: 'slack-channel-id-field',
              data: [], // Empty array
            },
          ],
        },
        {
          id: 'account-record-3',
          customFieldValues: [
            {
              customFieldId: 'wrong-field-id',
              data: [{ value: 'C12345' }],
            },
          ],
        },
      ];

      mockThenaPlatformApiProvider.getAccountsByIds.mockResolvedValue(mockAccounts);
      mockThenaPlatformApiProvider.getCustomObjectRecordsByIds.mockResolvedValue(mockCustomObjectRecords);

      const result = await (handler as any).processAccounts(
        mockInstallation,
        mockChannel,
        'new-channel-name',
        ['account-1'],
        ['account-record-1', 'account-record-2', 'account-record-3']
      );

      expect(result.accountsToUpdate).toHaveLength(1);

      expect(result.customObjectRecordsToUpdate).toHaveLength(0);
    });
  });

  describe('processContacts', () => {
    it('should process contacts correctly', async () => {
      const mockChannel = {
        id: 'db-channel-id',
        channelId: 'C12345',
        name: 'old-channel-name',
      };

      const mockContacts = [
        {
          id: 'contact-1',
          metadata: {
            sinks: {
              slack: {
                channels: [
                  { channelId: 'C12345', channelName: 'old-channel-name' },
                  { channelId: 'C67890', channelName: 'other-channel' },
                ],
              },
            },
          },
        },
        {
          id: 'contact-2',
          metadata: {
            sinks: {
              slack: {
                channels: [
                  { channelId: 'C67890', channelName: 'other-channel' },
                ],
              },
            },
          },
        },
      ];

      const mockCustomObjectRecords = [
        {
          id: 'contact-record-1',
          customFieldValues: [
            {
              customFieldId: 'slack-channel-id-field',
              data: [{ value: 'C12345' }],
            },
          ],
        },
        {
          id: 'contact-record-2',
          customFieldValues: [
            {
              customFieldId: 'slack-channel-id-field',
              data: [{ value: 'C67890' }],
            },
          ],
        },
      ];

      mockThenaPlatformApiProvider.getCustomerContactsByIds.mockResolvedValue(mockContacts);
      mockThenaPlatformApiProvider.getCustomObjectRecordsByIds.mockResolvedValue(mockCustomObjectRecords);

      const result = await (handler as any).processContacts(
        mockInstallation,
        mockChannel,
        'new-channel-name',
        ['contact-1', 'contact-2'],
        ['contact-record-1', 'contact-record-2']
      );

      expect(result.contactUpdates).toHaveLength(2);
      expect(result.contactUpdates[0].id).toBe('contact-1');
      expect(result.contactUpdates[0].data.metadata.sinks.slack.channels).toEqual([
        { channelId: 'C12345', channelName: 'new-channel-name' },
        { channelId: 'C67890', channelName: 'other-channel' },
      ]);

      expect(result.customObjectRecordsToUpdate).toHaveLength(1);
      expect(result.customObjectRecordsToUpdate[0].customObjectId).toBe('contact-custom-object-id');
      expect(result.customObjectRecordsToUpdate[0].customObjectRecordId).toBe('contact-record-1');
      expect(result.customObjectRecordsToUpdate[0].customFieldValues).toEqual([
        {
          customFieldId: 'slack-channel-name-field',
          data: [{ value: 'new-channel-name' }],
        },
      ]);
    });

    it('should handle empty contact IDs', async () => {
      const mockChannel = {
        id: 'db-channel-id',
        channelId: 'C12345',
        name: 'old-channel-name',
      };

      const result = await (handler as any).processContacts(
        mockInstallation,
        mockChannel,
        'new-channel-name',
        [],
        []
      );

      expect(mockLogger.error).toHaveBeenCalledWith(
        expect.stringContaining('No contacts found for channel C12345')
      );

      expect(result.contactUpdates).toHaveLength(0);
      expect(result.customObjectRecordsToUpdate).toHaveLength(0);
    });

    it('should handle custom object records with invalid data', async () => {
      const mockChannel = {
        id: 'db-channel-id',
        channelId: 'C12345',
        name: 'old-channel-name',
      };

      const mockContacts = [
        {
          id: 'contact-1',
          metadata: {
            sinks: {
              slack: {
                channels: [
                  { channelId: 'C12345', channelName: 'old-channel-name' },
                ],
              },
            },
          },
        },
      ];

      const mockCustomObjectRecords = [
        {
          id: 'contact-record-1',
          customFieldValues: [
            {
              customFieldId: 'slack-channel-id-field',
              data: null, // Invalid data
            },
          ],
        },
        {
          id: 'contact-record-2',
          customFieldValues: [
            {
              customFieldId: 'slack-channel-id-field',
              data: [], // Empty array
            },
          ],
        },
        {
          id: 'contact-record-3',
          customFieldValues: [
            {
              customFieldId: 'wrong-field-id',
              data: [{ value: 'C12345' }],
            },
          ],
        },
      ];

      mockThenaPlatformApiProvider.getCustomerContactsByIds.mockResolvedValue(mockContacts);
      mockThenaPlatformApiProvider.getCustomObjectRecordsByIds.mockResolvedValue(mockCustomObjectRecords);

      const result = await (handler as any).processContacts(
        mockInstallation,
        mockChannel,
        'new-channel-name',
        ['contact-1'],
        ['contact-record-1', 'contact-record-2', 'contact-record-3']
      );

      expect(result.contactUpdates).toHaveLength(1);

      expect(result.customObjectRecordsToUpdate).toHaveLength(0);
    });
  });
});
