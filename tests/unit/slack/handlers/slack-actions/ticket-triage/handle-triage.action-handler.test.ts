import { describe, it, expect, beforeEach, vi, Mock } from 'vitest';
import { TriageAssignActionHandler } from '../../../../../../src/slack/handlers/slack-actions/ticket-triage/handle-triage.action-handler';
import { ChannelsRepository } from '../../../../../../src/database/entities/channels/repositories';
import { ILogger } from '../../../../../../src/utils/logger/logger.interface';
import { SelectAssigneeComposite } from '../../../../../../src/slack/blocks/components/composite/triage';
import { SlackActionMiddlewareArgs } from '@slack/bolt';
import { EnrichedSlackArgsContext } from '../../../../../../src/slack/services/slack-action-discovery.service';

describe('TriageAssignActionHandler', () => {
  let handler: TriageAssignActionHandler;
  let mockLogger: ILogger;
  let mockChannelsRepository: ChannelsRepository;
  let mockAssignTicketBlock: SelectAssigneeComposite;

  beforeEach(() => {
    vi.resetAllMocks();
    
    mockLogger = {
      log: vi.fn(),
      error: vi.fn(),
      warn: vi.fn(),
      debug: vi.fn(),
      verbose: vi.fn(),
    } as unknown as ILogger;

    mockChannelsRepository = {
      findByCondition: vi.fn(),
    } as unknown as ChannelsRepository;

    mockAssignTicketBlock = {
      build: vi.fn().mockReturnValue({ blocks: [] }),
    } as unknown as SelectAssigneeComposite;

    handler = new TriageAssignActionHandler(
      mockLogger,
      mockChannelsRepository,
      mockAssignTicketBlock,
    );
  });

  describe('handle', () => {
    it('should open a modal when called with valid parameters', async () => {
      const args = {
        body: {
          channel: {
            id: 'C12345',
          },
          trigger_id: 'trigger-123',
          message: {
            metadata: {
              ticket_id: 'ticket-123',
            },
          },
        },
        context: {
          client: {
            views: {
              open: vi.fn().mockResolvedValue({ ok: true }),
            },
          },
          installation: {
            id: 'installation-id',
            botToken: 'mock-bot-token',
          },
        },
      } as unknown as SlackActionMiddlewareArgs & { context: EnrichedSlackArgsContext };

      const mockChannel = {
        id: 'channel-db-id',
        channelId: 'C12345',
        name: 'triage-channel',
      };

      (mockChannelsRepository.findByCondition as Mock).mockResolvedValue(mockChannel);
      (mockAssignTicketBlock.build as Mock).mockReturnValue({ blocks: [{ type: 'section', text: { type: 'mrkdwn', text: 'Select assignee' } }] });

      await handler.handle(args);

      expect(mockChannelsRepository.findByCondition).toHaveBeenCalledWith({
        where: { channelId: 'C12345', installation: { id: 'installation-id' } },
      });
      expect(mockAssignTicketBlock.build).toHaveBeenCalled();
      expect(args.context.client.views.open).toHaveBeenCalledWith({
        token: 'mock-bot-token',
        trigger_id: 'trigger-123',
        view: expect.objectContaining({
          type: 'modal',
          callback_id: 'triage_assign_modal_submit',
          private_metadata: expect.any(String),
        }),
      });
    });

    it('should log an error when context is missing', async () => {
      const args = {
        body: {
          channel: {
            id: 'C12345',
          },
        },
      } as unknown as SlackActionMiddlewareArgs;

      await handler.handle(args);

      expect(mockLogger.error).toHaveBeenCalledWith('No context found in arguments');
    });

    it('should throw an error when channel is not found', async () => {
      const args = {
        body: {
          channel: {
            id: 'C12345',
          },
          trigger_id: 'trigger-123',
        },
        context: {
          client: {
            views: {
              open: vi.fn(),
            },
          },
          installation: {
            id: 'installation-id',
            botToken: 'mock-bot-token',
          },
        },
      } as unknown as SlackActionMiddlewareArgs & { context: EnrichedSlackArgsContext };

      (mockChannelsRepository.findByCondition as Mock).mockResolvedValue(null);

      await expect(handler.handle(args)).rejects.toThrow('Channel not found');
      expect(mockChannelsRepository.findByCondition).toHaveBeenCalledWith({
        where: { channelId: 'C12345', installation: { id: 'installation-id' } },
      });
      expect(args.context.client.views.open).not.toHaveBeenCalled();
    });

    it('should throw an error when channel ID is missing', async () => {
      const args = {
        body: {
          trigger_id: 'trigger-123',
        },
        context: {
          client: {
            views: {
              open: vi.fn(),
            },
          },
          installation: {
            id: 'installation-id',
            botToken: 'mock-bot-token',
          },
        },
      } as unknown as SlackActionMiddlewareArgs & { context: EnrichedSlackArgsContext };

      await expect(handler.handle(args)).rejects.toThrow('No channel id found in body');
      expect(mockChannelsRepository.findByCondition).not.toHaveBeenCalled();
      expect(args.context.client.views.open).not.toHaveBeenCalled();
    });

    it('should handle errors when opening the modal', async () => {
      const args = {
        body: {
          channel: {
            id: 'C12345',
          },
          trigger_id: 'trigger-123',
          message: {
            metadata: {
              ticket_id: 'ticket-123',
            },
          },
        },
        context: {
          client: {
            views: {
              open: vi.fn().mockRejectedValue(new Error('API error')),
            },
          },
          installation: {
            id: 'installation-id',
            botToken: 'mock-bot-token',
          },
        },
      } as unknown as SlackActionMiddlewareArgs & { context: EnrichedSlackArgsContext };

      const mockChannel = {
        id: 'channel-db-id',
        channelId: 'C12345',
        name: 'triage-channel',
      };

      (mockChannelsRepository.findByCondition as Mock).mockResolvedValue(mockChannel);
      (mockAssignTicketBlock.build as Mock).mockReturnValue({ blocks: [{ type: 'section', text: { type: 'mrkdwn', text: 'Select assignee' } }] });

      await expect(handler.handle(args)).rejects.toThrow('API error');
      expect(mockChannelsRepository.findByCondition).toHaveBeenCalled();
      expect(mockAssignTicketBlock.build).toHaveBeenCalled();
      expect(args.context.client.views.open).toHaveBeenCalled();
    });
  });
});
