import { describe, it, expect, beforeEach, vi, <PERSON>ck } from 'vitest';
import { UpdateTicketDetailsActionHandler } from '../../../../../../src/slack/handlers/slack-actions/ticket-triage/update-ticket-details.action-handler.ts';
import { Repository } from 'typeorm';
import { SlackMessages } from '../../../../../../src/database/entities';
import { ThenaPlatformApiProvider } from '../../../../../../src/external/provider/thena-platform-api.provider';
import { ILogger } from '../../../../../../src/utils/logger/logger.interface';
import { CoreTriageService } from '../../../../../../src/slack/core/messages';
import { SlackActionMiddlewareArgs } from '@slack/bolt';
import { EnrichedSlackArgsContext } from '../../../../../../src/slack/services/slack-action-discovery.service';
import { TriageMessageBlock } from '../../../../../../src/slack/blocks/components';

describe('UpdateTicketDetailsActionHandler', () => {
  let handler: UpdateTicketDetailsActionHandler;
  let mockLogger: ILogger;
  let mockThenaPlatformApiProvider: ThenaPlatformApiProvider;
  let mockSlackMessagesRepository: Repository<SlackMessages>;
  let mockCoreTriageService: CoreTriageService;

  beforeEach(() => {
    vi.resetAllMocks();
    
    mockLogger = {
      log: vi.fn(),
      error: vi.fn(),
      warn: vi.fn(),
      debug: vi.fn(),
      verbose: vi.fn(),
    } as unknown as ILogger;

    mockThenaPlatformApiProvider = {
      updateTicket: vi.fn(),
      closeTicket: vi.fn(),
      archiveTicket: vi.fn(),
    } as unknown as ThenaPlatformApiProvider;

    mockSlackMessagesRepository = {
      findOne: vi.fn(),
    } as unknown as Repository<SlackMessages>;

    mockCoreTriageService = {
      updateTriageMessagesForSlackMessage: vi.fn(),
    } as unknown as CoreTriageService;

    handler = new UpdateTicketDetailsActionHandler(
      mockLogger,
      mockThenaPlatformApiProvider,
      mockSlackMessagesRepository,
      mockCoreTriageService,
    );
  });

  describe('handle', () => {
    it('should update ticket status when status action is triggered', async () => {
      const args = {
        payload: {
          action_id: TriageMessageBlock.ACTION_IDS.STATUS,
          selected_option: {
            value: 'status-123',
          },
        },
        body: {
          message: {
            metadata: {
              event_payload: {
                ticket_id: 'ticket-123',
                ticket_team_id: 'team-123',
              },
            },
          },
        },
        context: {
          installation: {
            id: 'installation-id',
            botToken: 'mock-bot-token',
          },
        },
      } as unknown as SlackActionMiddlewareArgs & { context: EnrichedSlackArgsContext };

      const mockSlackMessage = {
        id: 'message-id',
        platformTicketId: 'ticket-123',
      };

      (mockSlackMessagesRepository.findOne as Mock).mockResolvedValue(mockSlackMessage);

      await handler.handle(args);

      expect(mockThenaPlatformApiProvider.updateTicket).toHaveBeenCalledWith(
        args.context.installation,
        'ticket-123',
        { statusId: 'status-123' },
      );
      expect(mockSlackMessagesRepository.findOne).toHaveBeenCalledWith({
        where: {
          platformTicketId: 'ticket-123',
          installation: { id: 'installation-id' },
        },
      });
      expect(mockCoreTriageService.updateTriageMessagesForSlackMessage).toHaveBeenCalledWith(
        args.context.installation,
        mockSlackMessage,
      );
    });

    it('should update ticket priority when priority action is triggered', async () => {
      const args = {
        payload: {
          action_id: TriageMessageBlock.ACTION_IDS.PRIORITY,
          selected_option: {
            value: 'priority-123',
          },
        },
        body: {
          message: {
            metadata: {
              event_payload: {
                ticket_id: 'ticket-123',
                ticket_team_id: 'team-123',
              },
            },
          },
        },
        context: {
          installation: {
            id: 'installation-id',
            botToken: 'mock-bot-token',
          },
        },
      } as unknown as SlackActionMiddlewareArgs & { context: EnrichedSlackArgsContext };

      const mockSlackMessage = {
        id: 'message-id',
        platformTicketId: 'ticket-123',
      };

      (mockSlackMessagesRepository.findOne as Mock).mockResolvedValue(mockSlackMessage);

      await handler.handle(args);

      expect(mockThenaPlatformApiProvider.updateTicket).toHaveBeenCalledWith(
        args.context.installation,
        'ticket-123',
        { priorityId: 'priority-123' },
      );
      expect(mockSlackMessagesRepository.findOne).toHaveBeenCalledWith({
        where: {
          platformTicketId: 'ticket-123',
          installation: { id: 'installation-id' },
        },
      });
      expect(mockCoreTriageService.updateTriageMessagesForSlackMessage).toHaveBeenCalledWith(
        args.context.installation,
        mockSlackMessage,
      );
    });

    it('should archive ticket when archive action is triggered', async () => {
      const args = {
        payload: {
          action_id: TriageMessageBlock.ACTION_IDS.ARCHIVE,
        },
        body: {
          message: {
            metadata: {
              event_payload: {
                ticket_id: 'ticket-123',
                ticket_team_id: 'team-123',
              },
            },
          },
        },
        context: {
          installation: {
            id: 'installation-id',
            botToken: 'mock-bot-token',
          },
        },
      } as unknown as SlackActionMiddlewareArgs & { context: EnrichedSlackArgsContext };

      const mockSlackMessage = {
        id: 'message-id',
        platformTicketId: 'ticket-123',
      };

      (mockSlackMessagesRepository.findOne as Mock).mockResolvedValue(mockSlackMessage);

      await handler.handle(args);

      expect(mockThenaPlatformApiProvider.archiveTicket).toHaveBeenCalledWith(
        args.context.installation,
        'ticket-123',
      );
      expect(mockSlackMessagesRepository.findOne).toHaveBeenCalledWith({
        where: {
          platformTicketId: 'ticket-123',
          installation: { id: 'installation-id' },
        },
      });
      expect(mockCoreTriageService.updateTriageMessagesForSlackMessage).toHaveBeenCalledWith(
        args.context.installation,
        mockSlackMessage,
      );
    });

    it('should close ticket when close action is triggered', async () => {
      const args = {
        payload: {
          action_id: TriageMessageBlock.ACTION_IDS.CLOSE,
        },
        body: {
          message: {
            metadata: {
              event_payload: {
                ticket_id: 'ticket-123',
                ticket_team_id: 'team-123',
              },
            },
          },
        },
        context: {
          installation: {
            id: 'installation-id',
            botToken: 'mock-bot-token',
          },
        },
      } as unknown as SlackActionMiddlewareArgs & { context: EnrichedSlackArgsContext };

      const mockSlackMessage = {
        id: 'message-id',
        platformTicketId: 'ticket-123',
      };

      (mockSlackMessagesRepository.findOne as Mock).mockResolvedValue(mockSlackMessage);

      await handler.handle(args);

      expect(mockThenaPlatformApiProvider.closeTicket).toHaveBeenCalledWith(
        args.context.installation,
        'ticket-123',
        'team-123',
      );
      expect(mockSlackMessagesRepository.findOne).toHaveBeenCalledWith({
        where: {
          platformTicketId: 'ticket-123',
          installation: { id: 'installation-id' },
        },
      });
      expect(mockCoreTriageService.updateTriageMessagesForSlackMessage).toHaveBeenCalledWith(
        args.context.installation,
        mockSlackMessage,
      );
    });

    it('should log an error when context is missing', async () => {
      const args = {
        payload: {
          action_id: TriageMessageBlock.ACTION_IDS.STATUS,
          selected_option: {
            value: 'status-123',
          },
        },
        body: {
          message: {
            metadata: {
              event_payload: {
                ticket_id: 'ticket-123',
              },
            },
          },
        },
      } as unknown as SlackActionMiddlewareArgs;

      await handler.handle(args);

      expect(mockLogger.error).toHaveBeenCalledWith('No context found in arguments');
      expect(mockThenaPlatformApiProvider.updateTicket).not.toHaveBeenCalled();
    });

    it('should throw an error when ticket ID is missing', async () => {
      const args = {
        payload: {
          action_id: TriageMessageBlock.ACTION_IDS.STATUS,
          selected_option: {
            value: 'status-123',
          },
        },
        body: {
          message: {
            metadata: {
              event_payload: {
                ticket_team_id: 'team-123',
              },
            },
          },
        },
        context: {
          installation: {
            id: 'installation-id',
            botToken: 'mock-bot-token',
          },
        },
      } as unknown as SlackActionMiddlewareArgs & { context: EnrichedSlackArgsContext };

      await expect(handler.handle(args)).rejects.toThrow('No ticket id found in message');
      expect(mockLogger.error).toHaveBeenCalledWith('No ticket id found in message');
      expect(mockThenaPlatformApiProvider.updateTicket).not.toHaveBeenCalled();
    });

    it('should throw an error for unsupported action ID', async () => {
      const args = {
        payload: {
          action_id: 'unsupported_action',
          selected_option: {
            value: 'some-value',
          },
        },
        body: {
          message: {
            metadata: {
              event_payload: {
                ticket_id: 'ticket-123',
                ticket_team_id: 'team-123',
              },
            },
          },
        },
        context: {
          installation: {
            id: 'installation-id',
            botToken: 'mock-bot-token',
          },
        },
      } as unknown as SlackActionMiddlewareArgs & { context: EnrichedSlackArgsContext };

      await expect(handler.handle(args)).rejects.toThrow('Unsupported action id: unsupported_action');
      expect(mockLogger.error).toHaveBeenCalledWith('Unsupported action id: unsupported_action');
      expect(mockThenaPlatformApiProvider.updateTicket).not.toHaveBeenCalled();
    });
  });
});
