import { beforeEach, describe, expect, it, vi } from 'vitest';
import { FormOptionsHandler } from '../../../../../src/slack/handlers/slack-actions/form-options.handler';
import { FormBuilderService } from '../../../../../src/slack/services/form-builder.service';
import { CUSTOM_LOGGER_TOKEN } from '../../../../../src/utils';

describe('FormOptionsHandler', () => {
  let handler: FormOptionsHandler;
  let mockLogger: any;
  let mockFormBuilderService: any;

  beforeEach(() => {
    mockLogger = {
      log: vi.fn(),
      debug: vi.fn(),
      error: vi.fn(),
      warn: vi.fn(),
    };

    mockFormBuilderService = {
      getFieldOptions: vi.fn().mockResolvedValue([
        { label: 'Option 1', value: 'option1' },
        { label: 'Option 2', value: 'option2' },
      ]),
    };

    handler = new FormOptionsHandler(
      mockLogger,
      mockFormBuilderService
    );
  });

  describe('handle', () => {
    it('should return options in Slack format for valid requests', async () => {
      const args = {
        body: {
          action_id: 'field_options_testField',
          value: 'test',
          view: {
            private_metadata: JSON.stringify({
              fields: [
                {
                  id: 'testField',
                  apiForOptions: '/api/options',
                },
              ],
              teamId: 'T12345',
            }),
          },
        },
        context: {
          installation: {
            id: 'test-installation-id',
          },
        },
      } as any;

      const result = await handler.handle(args);

      expect(result).toEqual({
        options: [
          {
            text: {
              type: 'plain_text',
              text: 'Option 1',
            },
            value: 'option1',
          },
          {
            text: {
              type: 'plain_text',
              text: 'Option 2',
            },
            value: 'option2',
          },
        ],
      });

      expect(mockFormBuilderService.getFieldOptions).toHaveBeenCalledWith(
        args.context.installation,
        '/api/options',
        'T12345',
        'test'
      );

      expect(mockLogger.debug).toHaveBeenCalledWith(
        'FormOptionsHandler Handling options load:',
        expect.any(String)
      );
    });

    it('should return empty options when no API endpoint is found for field', async () => {
      const args = {
        body: {
          action_id: 'field_options_testField',
          value: 'test',
          view: {
            private_metadata: JSON.stringify({
              fields: [
                {
                  id: 'testField',
                },
              ],
              teamId: 'T12345',
            }),
          },
        },
        context: {
          installation: {
            id: 'test-installation-id',
          },
        },
      } as any;

      const result = await handler.handle(args);

      expect(result).toEqual({
        options: [],
      });

      expect(mockFormBuilderService.getFieldOptions).not.toHaveBeenCalled();
      expect(mockLogger.error).toHaveBeenCalledWith(
        'FormOptionsHandler No API endpoint found for field testField'
      );
    });

    it('should return empty options when field is not found', async () => {
      const args = {
        body: {
          action_id: 'field_options_nonExistentField',
          value: 'test',
          view: {
            private_metadata: JSON.stringify({
              fields: [
                {
                  id: 'testField',
                  apiForOptions: '/api/options',
                },
              ],
              teamId: 'T12345',
            }),
          },
        },
        context: {
          installation: {
            id: 'test-installation-id',
          },
        },
      } as any;

      const result = await handler.handle(args);

      expect(result).toEqual({
        options: [],
      });

      expect(mockFormBuilderService.getFieldOptions).not.toHaveBeenCalled();
      expect(mockLogger.error).toHaveBeenCalledWith(
        'FormOptionsHandler No API endpoint found for field nonExistentField'
      );
    });

    it('should handle errors and return empty options', async () => {
      const args = {
        body: {
          action_id: 'field_options_testField',
          value: 'test',
          view: {
            private_metadata: JSON.stringify({
              fields: [
                {
                  id: 'testField',
                  apiForOptions: '/api/options',
                },
              ],
              teamId: 'T12345',
            }),
          },
        },
        context: {
          installation: {
            id: 'test-installation-id',
          },
        },
      } as any;

      const error = new Error('Test error');
      mockFormBuilderService.getFieldOptions.mockRejectedValue(error);

      const result = await handler.handle(args);

      expect(result).toEqual({
        options: [],
      });

      expect(mockLogger.error).toHaveBeenCalledWith(
        'FormOptionsHandler Error handling options load:',
        'Test error'
      );
    });

    it('should handle non-Error errors and return empty options', async () => {
      const args = {
        body: {
          action_id: 'field_options_testField',
          value: 'test',
          view: {
            private_metadata: JSON.stringify({
              fields: [
                {
                  id: 'testField',
                  apiForOptions: '/api/options',
                },
              ],
              teamId: 'T12345',
            }),
          },
        },
        context: {
          installation: {
            id: 'test-installation-id',
          },
        },
      } as any;

      mockFormBuilderService.getFieldOptions.mockRejectedValue('String error');

      const result = await handler.handle(args);

      expect(result).toEqual({
        options: [],
      });

      expect(mockLogger.error).toHaveBeenCalledWith(
        'FormOptionsHandler Error handling options load:',
        'Unknown error'
      );
    });

    it('should handle missing private_metadata and return empty options', async () => {
      const args = {
        body: {
          action_id: 'field_options_testField',
          value: 'test',
          view: {
          },
        },
        context: {
          installation: {
            id: 'test-installation-id',
          },
        },
      } as any;

      const result = await handler.handle(args);

      expect(result).toEqual({
        options: [],
      });

      expect(mockFormBuilderService.getFieldOptions).not.toHaveBeenCalled();
      expect(mockLogger.error).toHaveBeenCalledWith(
        'FormOptionsHandler No API endpoint found for field testField'
      );
    });
  });
});
