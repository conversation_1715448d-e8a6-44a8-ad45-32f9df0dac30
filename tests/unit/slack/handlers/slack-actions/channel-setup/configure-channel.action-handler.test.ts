import { describe, it, expect, beforeEach, vi, Mock } from 'vitest';
import { ConfigureChannelActionHandler } from '../../../../../../src/slack/handlers/slack-actions/channel-setup/configure-channel.action-handler';
import { ILogger } from '../../../../../../src/utils';
import { ChannelSetupService } from '../../../../../../src/slack/services/channel-setup.service';
import { DecoratedSlackActionMiddlewareArgs } from '../../../../../../src/slack/event-handlers';
import { ChannelData, Team } from '../../../../../../src/slack/types/channel-setup.types';

describe('ConfigureChannelActionHandler', () => {
  let handler: ConfigureChannelActionHandler;
  let mockLogger: ILogger;
  let mockChannelSetupService: ChannelSetupService;

  const mockInstallation = {
    id: 'installation-123',
    teamId: 'T12345',
    botToken: 'xoxb-token',
    organization: { id: 'org-123' }
  };

  const mockChannelData: ChannelData = {
    channelId: 'C12345',
    channelName: 'test-channel'
  };

  const mockTeams: Team[] = [
    { id: 'team-1', name: 'Engineering' },
    { id: 'team-2', name: 'Support' }
  ];

  beforeEach(() => {
    mockLogger = {
      debug: vi.fn(),
      log: vi.fn(),
      error: vi.fn(),
      warn: vi.fn(),
      verbose: vi.fn()
    } as unknown as ILogger;

    mockChannelSetupService = {
      fetchTeams: vi.fn(),
      openConfigurationModal: vi.fn(),
      checkChannelState: vi.fn()
    } as unknown as ChannelSetupService;

    handler = new ConfigureChannelActionHandler(
      mockLogger,
      mockChannelSetupService
    );

    global.fetch = vi.fn().mockResolvedValue({
      ok: true,
      json: vi.fn().mockResolvedValue({})
    });
  });

  describe('handle', () => {
    it('should log error if context is missing', async () => {
      const args = {
        body: {}
      } as unknown as DecoratedSlackActionMiddlewareArgs;

      const result = await handler.handle(args);

      expect(mockLogger.error).toHaveBeenCalledWith('No context found in arguments');
      expect(result).toBeUndefined();
    });

    it('should log error if no actions found in body', async () => {
      const args = {
        body: {
        },
        context: {
          client: {},
          installation: mockInstallation
        }
      } as unknown as DecoratedSlackActionMiddlewareArgs;

      const result = await handler.handle(args);

      expect(mockLogger.error).toHaveBeenCalledWith('No actions found in body');
      expect(result).toBeUndefined();
    });

    it('should log error if channel data parsing fails', async () => {
      const args = {
        body: {
          actions: [
            {
              value: 'invalid-json'
            }
          ],
          user: { id: 'U12345' }
        },
        context: {
          client: {},
          installation: mockInstallation
        }
      } as unknown as DecoratedSlackActionMiddlewareArgs;

      await handler.handle(args);

      expect(mockLogger.error).toHaveBeenCalledWith(expect.stringContaining('Error parsing channel data'));
    });

    it('should log error if no channel ID found in action value', async () => {
      const args = {
        body: {
          actions: [
            {
              value: JSON.stringify({})
            }
          ],
          user: { id: 'U12345' }
        },
        context: {
          client: {},
          installation: mockInstallation
        }
      } as unknown as DecoratedSlackActionMiddlewareArgs;

      const result = await handler.handle(args);

      expect(mockLogger.error).toHaveBeenCalledWith('No channel ID found in action value');
      expect(result).toBeUndefined();
    });

    it('should post ephemeral message if no teams found', async () => {
      const mockClient = {
        chat: {
          postEphemeral: vi.fn().mockResolvedValue({ ok: true })
        }
      };

      const args = {
        body: {
          actions: [
            {
              value: JSON.stringify(mockChannelData)
            }
          ],
          user: { id: 'U12345' }
        },
        context: {
          client: mockClient,
          installation: mockInstallation
        }
      } as unknown as DecoratedSlackActionMiddlewareArgs;

      (mockChannelSetupService.fetchTeams as Mock).mockResolvedValue([]);

      const result = await handler.handle(args);

      expect(mockChannelSetupService.fetchTeams).toHaveBeenCalledWith(mockInstallation);
      expect(mockClient.chat.postEphemeral).toHaveBeenCalledWith({
        token: mockInstallation.botToken,
        channel: mockChannelData.channelId,
        user: 'U12345',
        text: 'No teams were found in the system. Please contact your administrator to set up teams first.'
      });
      expect(result).toBeUndefined();
    });

    it('should post ephemeral message if error fetching teams', async () => {
      const mockClient = {
        chat: {
          postEphemeral: vi.fn().mockResolvedValue({ ok: true })
        }
      };

      const args = {
        body: {
          actions: [
            {
              value: JSON.stringify(mockChannelData)
            }
          ],
          user: { id: 'U12345' }
        },
        context: {
          client: mockClient,
          installation: mockInstallation
        }
      } as unknown as DecoratedSlackActionMiddlewareArgs;

      const error = new Error('Failed to fetch teams');
      (mockChannelSetupService.fetchTeams as Mock).mockRejectedValue(error);

      const result = await handler.handle(args);

      expect(mockChannelSetupService.fetchTeams).toHaveBeenCalledWith(mockInstallation);
      expect(mockLogger.error).toHaveBeenCalledWith(expect.stringContaining('Error fetching teams'));
      expect(mockClient.chat.postEphemeral).toHaveBeenCalledWith({
        token: mockInstallation.botToken,
        channel: mockChannelData.channelId,
        user: 'U12345',
        text: 'Failed to connect to the team management system. Please try again later or contact support.'
      });
      expect(result).toBeUndefined();
    });

    it('should open configuration modal if teams found', async () => {
      const mockClient = {
        chat: {
          postEphemeral: vi.fn()
        }
      };

      const args = {
        body: {
          actions: [
            {
              value: JSON.stringify(mockChannelData)
            }
          ],
          user: { id: 'U12345' },
          trigger_id: 'trigger-123'
        },
        context: {
          client: mockClient,
          installation: mockInstallation
        }
      } as unknown as DecoratedSlackActionMiddlewareArgs;

      (mockChannelSetupService.fetchTeams as Mock).mockResolvedValue(mockTeams);

      await handler.handle(args);

      expect(mockChannelSetupService.fetchTeams).toHaveBeenCalledWith(mockInstallation);
      expect(mockChannelSetupService.openConfigurationModal).toHaveBeenCalledWith(
        mockClient,
        mockInstallation,
        'trigger-123',
        mockChannelData,
        mockTeams
      );
      expect(mockChannelSetupService.checkChannelState).toHaveBeenCalledWith(
        mockChannelData.channelId,
        mockInstallation
      );
    });

    it('should handle errors during processing', async () => {
      const args = {
        body: {
          actions: [
            {
              value: JSON.stringify(mockChannelData)
            }
          ],
          user: { id: 'U12345' }
        },
        context: {
          client: {},
          installation: mockInstallation
        }
      } as unknown as DecoratedSlackActionMiddlewareArgs;

      const error = new Error('Test error');
      (mockChannelSetupService.fetchTeams as Mock).mockImplementation(() => {
        throw error;
      });

      await handler.handle(args);

      expect(mockLogger.error).toHaveBeenCalledWith(expect.stringContaining('Error handling configure channel action'));
    });
  });
});
