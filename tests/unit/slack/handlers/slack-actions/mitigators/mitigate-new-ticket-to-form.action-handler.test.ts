import { describe, it, expect, beforeEach, vi, Mock } from 'vitest';
import { MitigateNewTicketToFormActionHandler } from '../../../../../../src/slack/handlers/slack-actions/mitigators/mitigate-new-ticket-to-form.action-handler';
import { ILogger } from '../../../../../../src/utils';
import { ChannelsRepository } from '../../../../../../src/database/entities/channels/repositories';
import { TeamChannelMapsRepository } from '../../../../../../src/database/entities/mappings/repositories/team-channel-maps.repository';
import { CreateTicketsBlocksComposite } from '../../../../../../src/slack/blocks/components';
import { DecoratedSlackActionMiddlewareArgs } from '../../../../../../src/slack/event-handlers';
import { EnrichedSlackArgsContext } from '../../../../../../src/slack/services/slack-action-discovery.service';

describe('MitigateNewTicketToFormActionHandler', () => {
  let handler: MitigateNewTicketToFormActionHandler;
  let mockLogger: ILogger;
  let mockChannelsRepository: ChannelsRepository;
  let mockTeamChannelMapsRepo: TeamChannelMapsRepository;
  let mockCreateNewTicketBlock: CreateTicketsBlocksComposite;

  const mockInstallation = {
    id: 'installation-123',
    teamId: 'T12345',
    botToken: 'xoxb-token',
    organization: { id: 'org-123' }
  };

  const mockChannel = {
    id: 'channel-123',
    channelId: 'C12345',
    name: 'test-channel'
  };

  const mockTeamChannelMap = {
    id: 'map-123',
    channel: mockChannel,
    platformTeam: {
      id: 'team-123',
      uid: 'team-uid-123',
      name: 'Engineering'
    }
  };

  beforeEach(() => {
    mockLogger = {
      debug: vi.fn(),
      log: vi.fn(),
      error: vi.fn(),
      warn: vi.fn(),
      verbose: vi.fn()
    } as unknown as ILogger;

    mockChannelsRepository = {
      findByCondition: vi.fn()
    } as unknown as ChannelsRepository;

    mockTeamChannelMapsRepo = {
      findByCondition: vi.fn()
    } as unknown as TeamChannelMapsRepository;

    mockCreateNewTicketBlock = {
      build: vi.fn().mockReturnValue({ blocks: [] })
    } as unknown as CreateTicketsBlocksComposite;

    handler = new MitigateNewTicketToFormActionHandler(
      mockLogger,
      mockChannelsRepository,
      mockTeamChannelMapsRepo,
      mockCreateNewTicketBlock
    );
  });

  describe('handle', () => {
    it('should log error if context is missing', async () => {
      const args = {
        body: {}
      } as unknown as DecoratedSlackActionMiddlewareArgs;

      const result = await handler.handle(args);

      expect(mockLogger.error).toHaveBeenCalledWith('No context found in arguments');
      expect(result).toBeUndefined();
    });

    it('should throw error if channel ID is missing', async () => {
      const args = {
        body: {
          channel: null // No channel ID
        },
        context: {
          installation: mockInstallation
        }
      } as unknown as DecoratedSlackActionMiddlewareArgs;

      await expect(handler.handle(args)).rejects.toThrow('No channel id found in body');
    });

    it('should throw error if channel is not found', async () => {
      const args = {
        body: {
          channel: { id: 'C12345' }
        },
        context: {
          installation: mockInstallation
        }
      } as unknown as DecoratedSlackActionMiddlewareArgs;

      (mockChannelsRepository.findByCondition as Mock).mockResolvedValue(null);

      await expect(handler.handle(args)).rejects.toThrow('Channel not found');
    });

    it('should throw error if team channel map is not found', async () => {
      const args = {
        body: {
          channel: { id: 'C12345' },
          container: {
            is_ephemeral: true,
            thread_ts: 'thread-123'
          },
          response_url: 'https://slack.com/api/response_url'
        },
        context: {
          installation: mockInstallation,
          organization: { id: 'org-123' }
        }
      } as unknown as DecoratedSlackActionMiddlewareArgs;

      (mockChannelsRepository.findByCondition as Mock).mockResolvedValue(mockChannel);
      (mockTeamChannelMapsRepo.findByCondition as Mock).mockResolvedValue(null);

      await expect(handler.handle(args)).rejects.toThrow(
        'This channel was not found mapped to a team on platform.'
      );
      expect(mockLogger.debug).toHaveBeenCalledWith(
        `Team channel map not found for channel ${mockChannel.channelId}`
      );
    });

    it('should open ticket creation modal if channel and team map are found', async () => {
      const mockClient = {
        views: {
          open: vi.fn().mockResolvedValue({ ok: true })
        }
      };

      const args = {
        body: {
          channel: { id: 'C12345' },
          container: {
            is_ephemeral: true,
            thread_ts: 'thread-123'
          },
          response_url: 'https://slack.com/api/response_url',
          trigger_id: 'trigger-123'
        },
        context: {
          client: mockClient,
          installation: mockInstallation,
          organization: { id: 'org-123' }
        }
      } as unknown as DecoratedSlackActionMiddlewareArgs;

      (mockChannelsRepository.findByCondition as Mock).mockResolvedValue(mockChannel);
      (mockTeamChannelMapsRepo.findByCondition as Mock).mockResolvedValue(mockTeamChannelMap);
      (mockCreateNewTicketBlock.build as Mock).mockReturnValue({ blocks: [] });

      await handler.handle(args);

      expect(mockChannelsRepository.findByCondition).toHaveBeenCalledWith({
        where: { channelId: 'C12345', installation: { id: 'installation-123' } }
      });
      expect(mockTeamChannelMapsRepo.findByCondition).toHaveBeenCalledWith({
        where: {
          channel: { channelId: 'C12345' },
          installation: { id: 'installation-123' },
          organization: { id: 'org-123' }
        },
        relations: { channel: true, platformTeam: true }
      });
      expect(mockClient.views.open).toHaveBeenCalledWith({
        token: mockInstallation.botToken,
        trigger_id: 'trigger-123',
        view: expect.objectContaining({
          type: 'modal',
          private_metadata: expect.any(String),
          blocks: expect.any(Array)
        })
      });

      const privateMetadata = JSON.parse(
        mockClient.views.open.mock.calls[0][0].view.private_metadata
      );
      expect(privateMetadata).toEqual({
        channelId: 'C12345',
        platformTeamId: 'team-uid-123',
        responseUrl: 'https://slack.com/api/response_url',
        threadTs: 'thread-123',
        shouldLinkSlackMessage: true
      });
    });

    it('should handle errors during processing', async () => {
      const args = {
        body: {
          channel: { id: 'C12345' }
        },
        context: {
          installation: mockInstallation
        }
      } as unknown as DecoratedSlackActionMiddlewareArgs;

      const error = new Error('Test error');
      (mockChannelsRepository.findByCondition as Mock).mockImplementation(() => {
        throw error;
      });

      await expect(handler.handle(args)).rejects.toThrow('Test error');
    });
  });
});
