import { describe, it, expect, beforeEach, vi } from 'vitest';
import { AckNoOpActionHandler } from '../../../../../../src/slack/handlers/slack-actions/utility/ack-no-op.action-handler';
import { ILogger } from '../../../../../../src/utils/logger';
import { SlackActionMiddlewareArgs } from '@slack/bolt';

describe('AckNoOpActionHandler', () => {
  let handler: AckNoOpActionHandler;
  let mockLogger: ILogger;

  beforeEach(() => {
    mockLogger = {
      debug: vi.fn(),
      log: vi.fn(),
      error: vi.fn(),
      warn: vi.fn(),
      verbose: vi.fn()
    } as unknown as ILogger;

    handler = new AckNoOpActionHandler(mockLogger);
  });

  describe('handle', () => {
    it('should do nothing when called', async () => {
      const args = {} as SlackActionMiddlewareArgs;

      const result = await handler.handle(args);

      expect(result).toBeUndefined();
      expect(mockLogger.debug).not.toHaveBeenCalled();
      expect(mockLogger.log).not.toHaveBeenCalled();
      expect(mockLogger.error).not.toHaveBeenCalled();
      expect(mockLogger.warn).not.toHaveBeenCalled();
      expect(mockLogger.verbose).not.toHaveBeenCalled();
    });
  });
});
