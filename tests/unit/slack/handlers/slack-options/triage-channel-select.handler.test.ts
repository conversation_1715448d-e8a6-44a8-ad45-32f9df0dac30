import { describe, it, expect, beforeEach, vi, Mock } from 'vitest';
import { TriageChannelOptionsHandler } from '../../../../../src/slack/handlers/slack-options/triage-channel-select.handler.ts';
import { ChannelsRepository } from '../../../../../src/database/entities/channels/repositories/channels.repository';
import { SlackOptionsMiddlewareArgs } from '@slack/bolt';
import { ChannelType } from '../../../../../src/database/entities/channels/channels.entity';
import { Not, In, Like } from 'typeorm';

describe('TriageChannelOptionsHandler', () => {
  let handler: TriageChannelOptionsHandler;
  let mockChannelsRepository: ChannelsRepository;

  const mockInstallation = {
    id: 'installation-123',
    teamId: 'T12345',
    botToken: 'xoxb-token',
    organization: { id: 'org-123' }
  };

  const mockChannels = [
    { channelId: 'C12345', name: 'general' },
    { channelId: 'C67890', name: 'random' },
    { channelId: 'C54321', name: 'support' }
  ];

  beforeEach(() => {
    mockChannelsRepository = {
      findAll: vi.fn()
    } as unknown as ChannelsRepository;

    handler = new TriageChannelOptionsHandler(mockChannelsRepository);
  });

  describe('handle', () => {
    it('should fetch channels and format them as Slack options', async () => {
      const args = {
        context: {
          installation: mockInstallation,
          organization: { id: 'org-123' }
        },
        payload: {}
      } as unknown as SlackOptionsMiddlewareArgs;

      (mockChannelsRepository.findAll as Mock).mockResolvedValue(mockChannels);

      const result = await handler.handle(args);

      expect(mockChannelsRepository.findAll).toHaveBeenCalledWith({
        where: {
          isShared: false,
          isArchived: false,
          channelType: expect.objectContaining({
            _type: 'not',
            _value: expect.objectContaining({
              _type: 'in',
              _value: ['customer_channel']
            })
          }),
          installation: { id: 'installation-123' },
          organization: { id: 'org-123' }
        },
        take: 100
      });
      expect(result).toEqual({
        options: [
          {
            text: {
              type: 'plain_text',
              text: 'general'
            },
            value: 'C12345'
          },
          {
            text: {
              type: 'plain_text',
              text: 'random'
            },
            value: 'C67890'
          },
          {
            text: {
              type: 'plain_text',
              text: 'support'
            },
            value: 'C54321'
          }
        ]
      });
    });

    it('should filter channels by query if provided', async () => {
      const args = {
        context: {
          installation: mockInstallation,
          organization: { id: 'org-123' }
        },
        payload: {
          value: 'supp'
        }
      } as unknown as SlackOptionsMiddlewareArgs;

      const filteredChannels = [
        { channelId: 'C54321', name: 'support' }
      ];

      (mockChannelsRepository.findAll as Mock).mockResolvedValue(filteredChannels);

      const result = await handler.handle(args);

      expect(mockChannelsRepository.findAll).toHaveBeenCalledWith({
        where: {
          isShared: false,
          isArchived: false,
          channelType: expect.objectContaining({
            _type: 'not',
            _value: expect.objectContaining({
              _type: 'in',
              _value: ['customer_channel']
            })
          }),
          installation: { id: 'installation-123' },
          organization: { id: 'org-123' },
          name: expect.objectContaining({
            _type: 'like',
            _value: '%supp%'
          })
        },
        take: 100
      });
      expect(result).toEqual({
        options: [
          {
            text: {
              type: 'plain_text',
              text: 'support'
            },
            value: 'C54321'
          }
        ]
      });
    });

    it('should return empty options if no channels found', async () => {
      const args = {
        context: {
          installation: mockInstallation,
          organization: { id: 'org-123' }
        },
        payload: {}
      } as unknown as SlackOptionsMiddlewareArgs;

      (mockChannelsRepository.findAll as Mock).mockResolvedValue([]);

      const result = await handler.handle(args);

      expect(result).toEqual({ options: [] });
    });
  });
});
