import { SlackOptionsMiddlewareArgs } from '@slack/bolt';
import { Mock, beforeEach, describe, expect, it, vi } from 'vitest';
import { ThenaPlatformApiProvider } from '../../../../../src/external/provider/thena-platform-api.provider';
import { TicketStatusOptionsHandler } from '../../../../../src/slack/handlers/slack-options/ticket-status-select.handler';

describe('TicketStatusOptionsHandler', () => {
  let handler: TicketStatusOptionsHandler;
  let mockThenaPlatformApiProvider: ThenaPlatformApiProvider;

  const mockInstallation = {
    id: 'installation-123',
    teamId: 'T12345',
    botToken: 'xoxb-token',
    organization: { id: 'org-123' },
  };

  const mockStatuses = [
    { id: 'status-1', name: 'Open' },
    { id: 'status-2', name: 'In Progress' },
    { id: 'status-3', name: 'Closed' },
  ];

  const mockStatusesWithChildren = [
    {
      id: 'YYFRSPSJ10P6PG35D42W1SCS8FVAQ',
      name: 'Open',
      displayName: 'Open',
      isDefault: true,
    },
    {
      id: '2ZFRSPSJ108KXEH7YJGYMAN6QM5M7',
      name: 'In progress',
      displayName: 'In progress',
      isDefault: false,
    },
    {
      id: '2ZFRSPSJ10T5F5D03FTR2KSPJH89E',
      name: 'On hold',
      displayName: 'On hold',
      isDefault: false,
    },
    {
      id: '2ZFRSPSJ10BW7W3G5R21AH9ZVVX2N',
      name: 'Closed',
      displayName: 'Closed',
      isDefault: false,
    },
    {
      id: '21ZNE9VJ10N43YP7SG4QZFRVR5CEH',
      name: 'working on it',
      displayName: 'working on it',
      isDefault: true,
      parentStatusId: '2ZFRSPSJ108KXEH7YJGYMAN6QM5M7',
    },
    {
      id: 'QQ3PE9VJ10CHFTK2P47VSTNSD59PM',
      name: 'almost done',
      displayName: 'almost done',
      isDefault: false,
      parentStatusId: '2ZFRSPSJ108KXEH7YJGYMAN6QM5M7',
    },
  ];

  beforeEach(() => {
    mockThenaPlatformApiProvider = {
      getStatusesForTeam: vi.fn(),
    } as unknown as ThenaPlatformApiProvider;

    handler = new TicketStatusOptionsHandler(mockThenaPlatformApiProvider);
  });

  describe('handle', () => {
    it('should return empty options if message is not a ticket details message', async () => {
      const args = {
        context: {
          installation: mockInstallation,
        },
        payload: {
          message: {
            metadata: {
              event_type: 'not_ticket_details',
            },
          },
        },
      } as unknown as SlackOptionsMiddlewareArgs;

      const result = await handler.handle(args);

      expect(result).toEqual({ options: [] });
      expect(
        mockThenaPlatformApiProvider.getStatusesForTeam,
      ).not.toHaveBeenCalled();
    });

    it('should throw error if installation ID does not match', async () => {
      const args = {
        context: {
          installation: mockInstallation,
        },
        payload: {
          message: {
            metadata: {
              event_type: 'ticket_details',
              event_payload: {
                installation_id: 'different-installation-id',
                ticket_team_id: 'team-123',
              },
            },
          },
        },
      } as unknown as SlackOptionsMiddlewareArgs;

      await expect(handler.handle(args)).rejects.toThrow(
        "You're not allowed to interact with this entity!",
      );
    });

    it('should fetch and return statuses as options', async () => {
      const args = {
        context: {
          installation: mockInstallation,
        },
        payload: {
          message: {
            metadata: {
              event_type: 'ticket_details',
              event_payload: {
                installation_id: 'installation-123',
                ticket_team_id: 'team-123',
              },
            },
          },
        },
      } as unknown as SlackOptionsMiddlewareArgs;

      (
        mockThenaPlatformApiProvider.getStatusesForTeam as Mock
      ).mockResolvedValue(mockStatuses);

      const result = await handler.handle(args);

      expect(
        mockThenaPlatformApiProvider.getStatusesForTeam,
      ).toHaveBeenCalledWith(mockInstallation, 'team-123');
      expect(result).toEqual({
        options: [
          {
            text: {
              type: 'plain_text',
              text: 'Open',
            },
            value: 'status-1',
          },
          {
            text: {
              type: 'plain_text',
              text: 'In Progress',
            },
            value: 'status-2',
          },
          {
            text: {
              type: 'plain_text',
              text: 'Closed',
            },
            value: 'status-3',
          },
        ],
      });
    });

    it('should filter out parent statuses that have children', async () => {
      const args = {
        context: {
          installation: mockInstallation,
        },
        payload: {
          message: {
            metadata: {
              event_type: 'ticket_details',
              event_payload: {
                installation_id: 'installation-123',
                ticket_team_id: 'team-123',
              },
            },
          },
        },
      } as unknown as SlackOptionsMiddlewareArgs;

      (
        mockThenaPlatformApiProvider.getStatusesForTeam as Mock
      ).mockResolvedValue(mockStatusesWithChildren);

      const result = await handler.handle(args);

      expect(
        mockThenaPlatformApiProvider.getStatusesForTeam,
      ).toHaveBeenCalledWith(mockInstallation, 'team-123');

      // Should include all statuses except the 'In progress' parent status that has children
      expect(result.options).toHaveLength(5);

      // Verify the 'In progress' parent status is not included
      const inProgressParentOption = result.options.find(
        (option) => option.value === '2ZFRSPSJ108KXEH7YJGYMAN6QM5M7',
      );
      expect(inProgressParentOption).toBeUndefined();

      // Verify the child statuses are included
      const workingOnItOption = result.options.find(
        (option) => option.value === '21ZNE9VJ10N43YP7SG4QZFRVR5CEH',
      );
      expect(workingOnItOption).toBeDefined();
      expect(workingOnItOption?.text.text).toBe('working on it');

      const almostDoneOption = result.options.find(
        (option) => option.value === 'QQ3PE9VJ10CHFTK2P47VSTNSD59PM',
      );
      expect(almostDoneOption).toBeDefined();
      expect(almostDoneOption?.text.text).toBe('almost done');

      // Verify other parent statuses without children are included
      const openOption = result.options.find(
        (option) => option.value === 'YYFRSPSJ10P6PG35D42W1SCS8FVAQ',
      );
      expect(openOption).toBeDefined();
    });
  });
});
