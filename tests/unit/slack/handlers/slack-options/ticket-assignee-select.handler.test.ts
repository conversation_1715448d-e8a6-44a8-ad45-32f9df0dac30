import { describe, it, expect, beforeEach, vi, Mock } from 'vitest';
import { TicketAssigneeOptionsHandler } from '../../../../../src/slack/handlers/slack-options/ticket-assignee-select.handler';
import { ThenaPlatformApiProvider } from '../../../../../src/external/provider/thena-platform-api.provider';
import { SlackOptionsMiddlewareArgs } from '@slack/bolt';

describe('TicketAssigneeOptionsHandler', () => {
  let handler: TicketAssigneeOptionsHandler;
  let mockThenaPlatformApiProvider: ThenaPlatformApiProvider;

  const mockInstallation = {
    id: 'installation-123',
    teamId: 'T12345',
    botToken: 'xoxb-token',
    organization: { id: 'org-123' }
  };

  const mockTeamMembers = [
    { id: 'user-1', name: '<PERSON>' },
    { id: 'user-2', name: '<PERSON>' },
    { id: 'user-3', name: '<PERSON>' }
  ];

  beforeEach(() => {
    mockThenaPlatformApiProvider = {
      getTeamMembers: vi.fn()
    } as unknown as ThenaPlatformApiProvider;

    handler = new TicketAssigneeOptionsHandler(mockThenaPlatformApiProvider);
  });

  describe('handle', () => {
    it('should return empty options if metadata is not a ticket details event', async () => {
      const args = {
        context: {
          installation: mockInstallation
        },
        payload: {
          view: {
            private_metadata: JSON.stringify({
              metadata: {
                event_type: 'not_ticket_details'
              }
            })
          }
        }
      } as unknown as SlackOptionsMiddlewareArgs;

      const result = await handler.handle(args);

      expect(result).toEqual({ options: [] });
      expect(mockThenaPlatformApiProvider.getTeamMembers).not.toHaveBeenCalled();
    });

    it('should throw error if installation ID does not match', async () => {
      const args = {
        context: {
          installation: mockInstallation
        },
        payload: {
          view: {
            private_metadata: JSON.stringify({
              metadata: {
                event_type: 'ticket_details',
                event_payload: {
                  installation_id: 'different-installation-id',
                  ticket_team_id: 'team-123'
                }
              }
            })
          }
        }
      } as unknown as SlackOptionsMiddlewareArgs;

      await expect(handler.handle(args)).rejects.toThrow(
        "You're not allowed to interact with this entity!"
      );
    });

    it('should fetch and return team members as options', async () => {
      const args = {
        context: {
          installation: mockInstallation
        },
        payload: {
          view: {
            private_metadata: JSON.stringify({
              metadata: {
                event_type: 'ticket_details',
                event_payload: {
                  installation_id: 'installation-123',
                  ticket_team_id: 'team-123'
                }
              }
            })
          }
        }
      } as unknown as SlackOptionsMiddlewareArgs;

      (mockThenaPlatformApiProvider.getTeamMembers as Mock).mockResolvedValue(mockTeamMembers);

      const result = await handler.handle(args);

      expect(mockThenaPlatformApiProvider.getTeamMembers).toHaveBeenCalledWith(
        mockInstallation,
        'team-123'
      );
      expect(result).toEqual({
        options: [
          {
            text: {
              type: 'plain_text',
              text: 'John Doe'
            },
            value: 'user-1'
          },
          {
            text: {
              type: 'plain_text',
              text: 'Jane Smith'
            },
            value: 'user-2'
          },
          {
            text: {
              type: 'plain_text',
              text: 'Bob Johnson'
            },
            value: 'user-3'
          }
        ]
      });
    });

    it('should handle errors during processing', async () => {
      const args = {
        context: {
          installation: mockInstallation
        },
        payload: {
          view: {
            private_metadata: JSON.stringify({
              metadata: {
                event_type: 'ticket_details',
                event_payload: {
                  installation_id: 'installation-123',
                  ticket_team_id: 'team-123'
                }
              }
            })
          }
        }
      } as unknown as SlackOptionsMiddlewareArgs;

      const error = new Error('Test error');
      (mockThenaPlatformApiProvider.getTeamMembers as Mock).mockRejectedValue(error);

      await expect(handler.handle(args)).rejects.toThrow('Test error');
    });
  });
});
