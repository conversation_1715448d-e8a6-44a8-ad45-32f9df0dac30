import { describe, it, expect, beforeEach, vi, Mock } from 'vitest';
import { TicketPriorityOptionsHandler } from '../../../../../src/slack/handlers/slack-options/ticket-priority-select.handler';
import { ThenaPlatformApiProvider } from '../../../../../src/external/provider/thena-platform-api.provider';
import { ILogger } from '../../../../../src/utils/logger';
import { SlackOptionsMiddlewareArgs } from '@slack/bolt';

describe('TicketPriorityOptionsHandler', () => {
  let handler: TicketPriorityOptionsHandler;
  let mockThenaPlatformApiProvider: ThenaPlatformApiProvider;
  let mockLogger: ILogger;

  const mockInstallation = {
    id: 'installation-123',
    teamId: 'T12345',
    botToken: 'xoxb-token',
    organization: { id: 'org-123' }
  };

  const mockPriorities = [
    { id: 'priority-1', name: 'Low' },
    { id: 'priority-2', name: 'Medium' },
    { id: 'priority-3', name: 'High' }
  ];

  beforeEach(() => {
    mockLogger = {
      debug: vi.fn(),
      log: vi.fn(),
      error: vi.fn(),
      warn: vi.fn(),
      verbose: vi.fn()
    } as unknown as ILogger;

    mockThenaPlatformApiProvider = {
      getPrioritiesForTeam: vi.fn()
    } as unknown as ThenaPlatformApiProvider;

    handler = new TicketPriorityOptionsHandler(
      mockLogger,
      mockThenaPlatformApiProvider
    );

    vi.spyOn(console, 'error').mockImplementation(() => {});
  });

  describe('handle', () => {
    it('should return empty options if message is not a ticket details message', async () => {
      const args = {
        context: {
          installation: mockInstallation
        },
        payload: {
          message: {
            metadata: {
              event_type: 'not_ticket_details'
            }
          }
        }
      } as unknown as SlackOptionsMiddlewareArgs;

      const result = await handler.handle(args);

      expect(result).toEqual({ options: [] });
      expect(mockThenaPlatformApiProvider.getPrioritiesForTeam).not.toHaveBeenCalled();
    });

    it('should throw error if installation ID does not match', async () => {
      const args = {
        context: {
          installation: mockInstallation
        },
        payload: {
          message: {
            metadata: {
              event_type: 'ticket_details',
              event_payload: {
                installation_id: 'different-installation-id',
                ticket_team_id: 'team-123'
              }
            }
          }
        }
      } as unknown as SlackOptionsMiddlewareArgs;

      const result = await handler.handle(args);

      expect(result).toEqual({ options: [] });
      expect(mockLogger.error).toHaveBeenCalledWith(
        expect.stringContaining("Error getting ticket priorities. You're not allowed to interact with this entity!")
      );
    });

    it('should fetch and return priorities as options for message-based payload', async () => {
      const args = {
        context: {
          installation: mockInstallation
        },
        payload: {
          message: {
            metadata: {
              event_type: 'ticket_details',
              event_payload: {
                installation_id: 'installation-123',
                ticket_team_id: 'team-123'
              }
            }
          }
        }
      } as unknown as SlackOptionsMiddlewareArgs;

      (mockThenaPlatformApiProvider.getPrioritiesForTeam as Mock).mockResolvedValue(mockPriorities);

      const result = await handler.handle(args);

      expect(mockThenaPlatformApiProvider.getPrioritiesForTeam).toHaveBeenCalledWith(
        mockInstallation,
        'team-123'
      );
      expect(result).toEqual({
        options: [
          {
            text: {
              type: 'plain_text',
              text: 'Low'
            },
            value: 'priority-1'
          },
          {
            text: {
              type: 'plain_text',
              text: 'Medium'
            },
            value: 'priority-2'
          },
          {
            text: {
              type: 'plain_text',
              text: 'High'
            },
            value: 'priority-3'
          }
        ]
      });
    });

    it('should fetch and return priorities as options for view-based payload', async () => {
      const args = {
        context: {
          installation: mockInstallation
        },
        payload: {
          view: {
            private_metadata: JSON.stringify({
              channelId: 'C12345',
              platformTeamId: 'team-123'
            })
          }
        }
      } as unknown as SlackOptionsMiddlewareArgs;

      (mockThenaPlatformApiProvider.getPrioritiesForTeam as Mock).mockResolvedValue(mockPriorities);

      const result = await handler.handle(args);

      expect(mockThenaPlatformApiProvider.getPrioritiesForTeam).toHaveBeenCalledWith(
        mockInstallation,
        'team-123'
      );
      expect(result).toEqual({
        options: [
          {
            text: {
              type: 'plain_text',
              text: 'Low'
            },
            value: 'priority-1'
          },
          {
            text: {
              type: 'plain_text',
              text: 'Medium'
            },
            value: 'priority-2'
          },
          {
            text: {
              type: 'plain_text',
              text: 'High'
            },
            value: 'priority-3'
          }
        ]
      });
    });

    it('should handle errors during processing', async () => {
      const args = {
        context: {
          installation: mockInstallation
        },
        payload: {
          message: {
            metadata: {
              event_type: 'ticket_details',
              event_payload: {
                installation_id: 'installation-123',
                ticket_team_id: 'team-123'
              }
            }
          }
        }
      } as unknown as SlackOptionsMiddlewareArgs;

      const error = new Error('Test error');
      (mockThenaPlatformApiProvider.getPrioritiesForTeam as Mock).mockRejectedValue(error);

      const result = await handler.handle(args);

      expect(mockLogger.error).toHaveBeenCalledWith(
        'TicketPriorityOptionsHandler Error getting ticket priorities. Test error'
      );
      expect(result).toEqual({ options: [] });
    });

    it('should handle non-Error exceptions', async () => {
      const args = {
        context: {
          installation: mockInstallation
        },
        payload: {
          message: {
            metadata: {
              event_type: 'ticket_details',
              event_payload: {
                installation_id: 'installation-123',
                ticket_team_id: 'team-123'
              }
            }
          }
        }
      } as unknown as SlackOptionsMiddlewareArgs;

      (mockThenaPlatformApiProvider.getPrioritiesForTeam as Mock).mockRejectedValue('String error');

      const result = await handler.handle(args);

      expect(console.error).toHaveBeenCalledWith(
        'TicketPriorityOptionsHandler Error getting ticket priorities.',
        'String error'
      );
      expect(result).toEqual({ options: [] });
    });
  });
});
