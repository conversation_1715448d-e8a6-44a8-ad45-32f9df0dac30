import { describe, it, expect, beforeEach, vi, Mock } from 'vitest';
import { AssignTicketHandler } from '../../../../../src/slack/handlers/slack-views/assign-ticket.handler';
import { ILogger } from '../../../../../src/utils/logger';
import { ThenaPlatformApiProvider } from '../../../../../src/external/provider/thena-platform-api.provider';
import { SlackViewMiddlewareArgs } from '@slack/bolt';
import { SelectAssigneeComposite } from '../../../../../src/slack/blocks/components';

describe('AssignTicketHandler', () => {
  let handler: AssignTicketHandler;
  let mockLogger: ILogger;
  let mockThenaPlatformApiProvider: ThenaPlatformApiProvider;

  const mockInstallation = {
    id: 'installation-123',
    teamId: 'T12345',
    botToken: 'xoxb-token',
    organization: { id: 'org-123' }
  };

  beforeEach(() => {
    mockLogger = {
      debug: vi.fn(),
      log: vi.fn(),
      error: vi.fn(),
      warn: vi.fn(),
      verbose: vi.fn()
    } as unknown as ILogger;

    mockThenaPlatformApiProvider = {
      updateTicket: vi.fn()
    } as unknown as ThenaPlatformApiProvider;

    handler = new AssignTicketHandler(
      mockLogger,
      mockThenaPlatformApiProvider
    );

    vi.spyOn(console, 'error').mockImplementation(() => {});
  });

  describe('handle', () => {
    it('should process view submission and update ticket assignee', async () => {
      const args = {
        context: {
          installation: mockInstallation
        },
        view: {
          private_metadata: JSON.stringify({
            metadata: {
              event_type: 'ticket_details',
              event_payload: {
                ticket_id: 'ticket-123'
              }
            }
          }),
          state: {
            values: {
              [SelectAssigneeComposite.BLOCK_ID]: {
                [SelectAssigneeComposite.ACTION_IDS.ASSIGNEE_SELECT]: {
                  selected_option: {
                    value: 'user-123'
                  }
                }
              }
            }
          }
        }
      } as unknown as SlackViewMiddlewareArgs;

      (mockThenaPlatformApiProvider.updateTicket as Mock).mockResolvedValue({});

      await handler.handle(args);

      expect(mockThenaPlatformApiProvider.updateTicket).toHaveBeenCalledWith(
        mockInstallation,
        'ticket-123',
        {
          assignedAgentId: 'user-123'
        }
      );
    });

    it('should throw error when ticket id is missing', async () => {
      const args = {
        context: {
          installation: mockInstallation
        },
        view: {
          private_metadata: JSON.stringify({
            metadata: {
              event_type: 'ticket_details',
              event_payload: {
              }
            }
          }),
          state: {
            values: {
              [SelectAssigneeComposite.BLOCK_ID]: {
                [SelectAssigneeComposite.ACTION_IDS.ASSIGNEE_SELECT]: {
                  selected_option: {
                    value: 'user-123'
                  }
                }
              }
            }
          }
        }
      } as unknown as SlackViewMiddlewareArgs;

      await expect(handler.handle(args)).rejects.toThrow('Failed to get ticket id or selected user');
      expect(mockLogger.error).toHaveBeenCalledWith('AssignTicketHandler Failed to get ticket id or selected user');
    });

    it('should throw error when selected user is missing', async () => {
      const args = {
        context: {
          installation: mockInstallation
        },
        view: {
          private_metadata: JSON.stringify({
            metadata: {
              event_type: 'ticket_details',
              event_payload: {
                ticket_id: 'ticket-123'
              }
            }
          }),
          state: {
            values: {
            }
          }
        }
      } as unknown as SlackViewMiddlewareArgs;

      await expect(handler.handle(args)).rejects.toThrow('Failed to get ticket id or selected user');
      expect(mockLogger.error).toHaveBeenCalledWith('AssignTicketHandler Failed to get ticket id or selected user');
    });

    it('should handle error during ticket update', async () => {
      const args = {
        context: {
          installation: mockInstallation
        },
        view: {
          private_metadata: JSON.stringify({
            metadata: {
              event_type: 'ticket_details',
              event_payload: {
                ticket_id: 'ticket-123'
              }
            }
          }),
          state: {
            values: {
              [SelectAssigneeComposite.BLOCK_ID]: {
                [SelectAssigneeComposite.ACTION_IDS.ASSIGNEE_SELECT]: {
                  selected_option: {
                    value: 'user-123'
                  }
                }
              }
            }
          }
        }
      } as unknown as SlackViewMiddlewareArgs;

      const error = new Error('Platform API error');
      (mockThenaPlatformApiProvider.updateTicket as Mock).mockRejectedValue(error);

      await expect(handler.handle(args)).rejects.toThrow('Platform API error');
      expect(mockLogger.error).toHaveBeenCalledWith(
        'AssignTicketHandler Failed to update ticket, Platform API error'
      );
    });

    it('should handle non-Error exceptions during ticket update', async () => {
      const args = {
        context: {
          installation: mockInstallation
        },
        view: {
          private_metadata: JSON.stringify({
            metadata: {
              event_type: 'ticket_details',
              event_payload: {
                ticket_id: 'ticket-123'
              }
            }
          }),
          state: {
            values: {
              [SelectAssigneeComposite.BLOCK_ID]: {
                [SelectAssigneeComposite.ACTION_IDS.ASSIGNEE_SELECT]: {
                  selected_option: {
                    value: 'user-123'
                  }
                }
              }
            }
          }
        }
      } as unknown as SlackViewMiddlewareArgs;

      (mockThenaPlatformApiProvider.updateTicket as Mock).mockRejectedValue('String error');

      await expect(handler.handle(args)).rejects.toEqual('String error');
      expect(console.error).toHaveBeenCalledWith('String error');
    });

    it('should handle error during getSelectedUser', async () => {
      const args = {
        context: {
          installation: mockInstallation
        },
        view: {
          private_metadata: JSON.stringify({
            metadata: {
              event_type: 'ticket_details',
              event_payload: {
                ticket_id: 'ticket-123'
              }
            }
          }),
          state: {
            values: {
              [SelectAssigneeComposite.BLOCK_ID]: {
              }
            }
          }
        }
      } as unknown as SlackViewMiddlewareArgs;

      await expect(handler.handle(args)).rejects.toThrow('Failed to get ticket id or selected user');
      expect(mockLogger.error).toHaveBeenCalledWith(
        expect.stringContaining('AssignTicketHandler Failed to parse private metadata')
      );
    });
  });
});
