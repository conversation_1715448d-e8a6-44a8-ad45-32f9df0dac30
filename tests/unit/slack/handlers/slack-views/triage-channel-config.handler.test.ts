import { describe, it, expect, before<PERSON>ach, vi, <PERSON><PERSON> } from 'vitest';
import { TriageChannelConfigHandler } from '../../../../../src/slack/handlers/slack-views/triage-channel-config.handler';
import { ILogger } from '../../../../../src/utils/logger';
import { TransactionService } from '../../../../../src/database/common';
import { ChannelsRepository } from '../../../../../src/database/entities/channels/repositories/channels.repository';
import { TriageMapsRepository } from '../../../../../src/database/entities/mappings/repositories/triage-maps.repository';
import { SlackWebAPIService } from '../../../../../src/slack/providers/slack-apis/slack-apis.service';
import { SlackViewMiddlewareArgs } from '@slack/bolt';
import { ConfigureTriageChannelComposite } from '../../../../../src/slack/blocks/components/composite/channels';
import { ChannelType } from '../../../../../src/database/entities/channels/channels.entity';
import { BotCtx } from '../../../../../src/auth/interfaces';
import { ConfigureTriageChannelDTO } from '../../../../../src/slack/dtos';

describe('TriageChannelConfigHandler', () => {
  let handler: TriageChannelConfigHandler;
  let mockLogger: ILogger;
  let mockTransactionService: TransactionService;
  let mockChannelRepository: ChannelsRepository;
  let mockTriageMapsRepository: TriageMapsRepository;
  let mockSlackWebAPIService: SlackWebAPIService;

  const mockInstallation = {
    id: 'installation-123',
    teamId: 'T12345',
    botToken: 'xoxb-token',
    organization: { id: 'org-123' }
  };

  const mockOrganization = { id: 'org-123' };

  const mockCustomerChannel = {
    id: 'channel-123',
    channelId: 'C12345',
    name: 'customer-channel',
    channelType: ChannelType.CUSTOMER_CHANNEL,
    isArchived: false,
    isShared: true
  };

  const mockTriageChannel = {
    id: 'channel-456',
    channelId: 'C67890',
    name: 'triage-channel',
    channelType: ChannelType.NOT_SETUP,
    isArchived: false,
    isShared: false
  };

  beforeEach(() => {
    mockLogger = {
      debug: vi.fn(),
      log: vi.fn(),
      error: vi.fn(),
      warn: vi.fn(),
      verbose: vi.fn()
    } as unknown as ILogger;

    mockTransactionService = {
      runInTransaction: vi.fn()
    } as unknown as TransactionService;

    mockChannelRepository = {
      findAll: vi.fn(),
      updateWithTxn: vi.fn()
    } as unknown as ChannelsRepository;

    mockTriageMapsRepository = {
      saveWithTxn: vi.fn()
    } as unknown as TriageMapsRepository;

    mockSlackWebAPIService = {
      joinConversation: vi.fn()
    } as unknown as SlackWebAPIService;

    handler = new TriageChannelConfigHandler(
      mockLogger,
      mockTransactionService,
      mockChannelRepository,
      mockTriageMapsRepository,
      mockSlackWebAPIService
    );

    vi.spyOn(console, 'error').mockImplementation(() => {});
  });

  describe('handle', () => {
    it('should process view submission and configure triage channel', async () => {
      const args = {
        context: {
          installation: mockInstallation,
          organization: mockOrganization
        },
        view: {
          private_metadata: JSON.stringify({
            channelId: 'C12345'
          }),
          state: {
            values: {
              [ConfigureTriageChannelComposite.BLOCK_ID]: {
                [ConfigureTriageChannelComposite.ACTION_IDS.CHANNEL_SELECT]: {
                  selected_option: {
                    value: 'C67890'
                  }
                }
              }
            }
          }
        }
      } as unknown as SlackViewMiddlewareArgs;

      (mockChannelRepository.findAll as Mock).mockResolvedValue([
        mockCustomerChannel,
        mockTriageChannel
      ]);
      (mockSlackWebAPIService.joinConversation as Mock).mockResolvedValue({});
      (mockTransactionService.runInTransaction as Mock).mockImplementation(async (callback) => {
        return await callback({});
      });

      await handler.handle(args);

      expect(mockChannelRepository.findAll).toHaveBeenCalledWith({
        where: {
          channelId: expect.objectContaining({
            _type: 'in',
            _value: expect.arrayContaining(['C12345', 'C67890'])
          }),
          installation: { id: 'installation-123' },
          organization: { id: 'org-123' }
        }
      });
      expect(mockSlackWebAPIService.joinConversation).toHaveBeenCalledWith(
        'xoxb-token',
        { channel: 'C67890' }
      );
      expect(mockChannelRepository.updateWithTxn).toHaveBeenCalledWith(
        {},
        { id: 'channel-456' },
        { channelType: ChannelType.TRIAGE_CHANNEL }
      );
      expect(mockTriageMapsRepository.saveWithTxn).toHaveBeenCalledWith(
        {},
        {
          activeChannel: { id: 'channel-123' },
          triageChannel: { id: 'channel-456' },
          installation: { id: 'installation-123' },
          organization: { id: 'org-123' }
        }
      );
    });

    it('should handle API-based configuration', async () => {
      const dto: ConfigureTriageChannelDTO = {
        channelToTriage: 'C12345',
        triageChannel: 'C67890'
      };

      const botCtx = {
        installation: mockInstallation as any,
        organization: mockOrganization as any
      } as BotCtx;

      (mockChannelRepository.findAll as Mock).mockResolvedValue([
        mockCustomerChannel,
        mockTriageChannel
      ]);
      (mockSlackWebAPIService.joinConversation as Mock).mockResolvedValue({});
      (mockTransactionService.runInTransaction as Mock).mockImplementation(async (callback) => {
        return await callback({});
      });

      await handler.handleViaApi(dto, botCtx);

      expect(mockChannelRepository.findAll).toHaveBeenCalledWith({
        where: {
          channelId: expect.objectContaining({
            _type: 'in',
            _value: expect.arrayContaining(['C12345', 'C67890'])
          }),
          installation: { id: 'installation-123' },
          organization: { id: 'org-123' }
        }
      });
      expect(mockSlackWebAPIService.joinConversation).toHaveBeenCalledWith(
        'xoxb-token',
        { channel: 'C67890' }
      );
      expect(mockChannelRepository.updateWithTxn).toHaveBeenCalledWith(
        {},
        { id: 'channel-456' },
        { channelType: ChannelType.TRIAGE_CHANNEL }
      );
      expect(mockTriageMapsRepository.saveWithTxn).toHaveBeenCalledWith(
        {},
        {
          activeChannel: { id: 'channel-123' },
          triageChannel: { id: 'channel-456' },
          installation: { id: 'installation-123' },
          organization: { id: 'org-123' }
        }
      );
    });

    it('should throw error when channels are not found', async () => {
      const args = {
        context: {
          installation: mockInstallation,
          organization: mockOrganization
        },
        view: {
          private_metadata: JSON.stringify({
            channelId: 'C12345'
          }),
          state: {
            values: {
              [ConfigureTriageChannelComposite.BLOCK_ID]: {
                [ConfigureTriageChannelComposite.ACTION_IDS.CHANNEL_SELECT]: {
                  selected_option: {
                    value: 'C67890'
                  }
                }
              }
            }
          }
        }
      } as unknown as SlackViewMiddlewareArgs;

      (mockChannelRepository.findAll as Mock).mockResolvedValue([mockCustomerChannel]);

      await expect(handler.handle(args)).rejects.toThrow('Some of the provided channels were not found!');
    });

    it('should throw error when triage channel is invalid', async () => {
      const args = {
        context: {
          installation: mockInstallation,
          organization: mockOrganization
        },
        view: {
          private_metadata: JSON.stringify({
            channelId: 'C12345'
          }),
          state: {
            values: {
              [ConfigureTriageChannelComposite.BLOCK_ID]: {
                [ConfigureTriageChannelComposite.ACTION_IDS.CHANNEL_SELECT]: {
                  selected_option: {
                    value: 'C67890'
                  }
                }
              }
            }
          }
        }
      } as unknown as SlackViewMiddlewareArgs;

      const invalidTriageChannel = {
        ...mockTriageChannel,
        channelType: ChannelType.CUSTOMER_CHANNEL
      };

      (mockChannelRepository.findAll as Mock).mockResolvedValue([
        mockCustomerChannel,
        invalidTriageChannel
      ]);

      await expect(handler.handle(args)).rejects.toThrow('The selected channel is not a valid triage channel!');
    });

    it('should throw error when triage channel is archived', async () => {
      const args = {
        context: {
          installation: mockInstallation,
          organization: mockOrganization
        },
        view: {
          private_metadata: JSON.stringify({
            channelId: 'C12345'
          }),
          state: {
            values: {
              [ConfigureTriageChannelComposite.BLOCK_ID]: {
                [ConfigureTriageChannelComposite.ACTION_IDS.CHANNEL_SELECT]: {
                  selected_option: {
                    value: 'C67890'
                  }
                }
              }
            }
          }
        }
      } as unknown as SlackViewMiddlewareArgs;

      const archivedTriageChannel = {
        ...mockTriageChannel,
        isArchived: true
      };

      (mockChannelRepository.findAll as Mock).mockResolvedValue([
        mockCustomerChannel,
        archivedTriageChannel
      ]);

      await expect(handler.handle(args)).rejects.toThrow('The selected channel is archived!');
    });

    it('should throw error when triage channel is shared', async () => {
      const args = {
        context: {
          installation: mockInstallation,
          organization: mockOrganization
        },
        view: {
          private_metadata: JSON.stringify({
            channelId: 'C12345'
          }),
          state: {
            values: {
              [ConfigureTriageChannelComposite.BLOCK_ID]: {
                [ConfigureTriageChannelComposite.ACTION_IDS.CHANNEL_SELECT]: {
                  selected_option: {
                    value: 'C67890'
                  }
                }
              }
            }
          }
        }
      } as unknown as SlackViewMiddlewareArgs;

      const sharedTriageChannel = {
        ...mockTriageChannel,
        isShared: true
      };

      (mockChannelRepository.findAll as Mock).mockResolvedValue([
        mockCustomerChannel,
        sharedTriageChannel
      ]);

      await expect(handler.handle(args)).rejects.toThrow('The selected channel is shared!');
    });

    it('should throw error when customer channel is invalid', async () => {
      const args = {
        context: {
          installation: mockInstallation,
          organization: mockOrganization
        },
        view: {
          private_metadata: JSON.stringify({
            channelId: 'C12345'
          }),
          state: {
            values: {
              [ConfigureTriageChannelComposite.BLOCK_ID]: {
                [ConfigureTriageChannelComposite.ACTION_IDS.CHANNEL_SELECT]: {
                  selected_option: {
                    value: 'C67890'
                  }
                }
              }
            }
          }
        }
      } as unknown as SlackViewMiddlewareArgs;

      const invalidCustomerChannel = {
        ...mockCustomerChannel,
        channelType: ChannelType.NOT_SETUP
      };

      (mockChannelRepository.findAll as Mock).mockResolvedValue([
        invalidCustomerChannel,
        mockTriageChannel
      ]);

      await expect(handler.handle(args)).rejects.toThrow('The selected channel is not a valid customer channel!');
    });

    it('should throw error when customer channel is archived', async () => {
      const args = {
        context: {
          installation: mockInstallation,
          organization: mockOrganization
        },
        view: {
          private_metadata: JSON.stringify({
            channelId: 'C12345'
          }),
          state: {
            values: {
              [ConfigureTriageChannelComposite.BLOCK_ID]: {
                [ConfigureTriageChannelComposite.ACTION_IDS.CHANNEL_SELECT]: {
                  selected_option: {
                    value: 'C67890'
                  }
                }
              }
            }
          }
        }
      } as unknown as SlackViewMiddlewareArgs;

      const archivedCustomerChannel = {
        ...mockCustomerChannel,
        isArchived: true
      };

      (mockChannelRepository.findAll as Mock).mockResolvedValue([
        archivedCustomerChannel,
        mockTriageChannel
      ]);

      await expect(handler.handle(args)).rejects.toThrow('The selected channel is archived!');
    });

    it('should throw error when customer channel is not shared', async () => {
      const args = {
        context: {
          installation: mockInstallation,
          organization: mockOrganization
        },
        view: {
          private_metadata: JSON.stringify({
            channelId: 'C12345'
          }),
          state: {
            values: {
              [ConfigureTriageChannelComposite.BLOCK_ID]: {
                [ConfigureTriageChannelComposite.ACTION_IDS.CHANNEL_SELECT]: {
                  selected_option: {
                    value: 'C67890'
                  }
                }
              }
            }
          }
        }
      } as unknown as SlackViewMiddlewareArgs;

      const nonSharedCustomerChannel = {
        ...mockCustomerChannel,
        isShared: false
      };

      (mockChannelRepository.findAll as Mock).mockResolvedValue([
        nonSharedCustomerChannel,
        mockTriageChannel
      ]);

      await expect(handler.handle(args)).rejects.toThrow('The selected channel is not shared!');
    });

    it('should handle error during join conversation', async () => {
      const args = {
        context: {
          installation: mockInstallation,
          organization: mockOrganization
        },
        view: {
          private_metadata: JSON.stringify({
            channelId: 'C12345'
          }),
          state: {
            values: {
              [ConfigureTriageChannelComposite.BLOCK_ID]: {
                [ConfigureTriageChannelComposite.ACTION_IDS.CHANNEL_SELECT]: {
                  selected_option: {
                    value: 'C67890'
                  }
                }
              }
            }
          }
        }
      } as unknown as SlackViewMiddlewareArgs;

      (mockChannelRepository.findAll as Mock).mockResolvedValue([
        mockCustomerChannel,
        mockTriageChannel
      ]);
      
      const error = new Error('Failed to join conversation');
      (mockSlackWebAPIService.joinConversation as Mock).mockRejectedValue(error);
      (mockTransactionService.runInTransaction as Mock).mockImplementation(async (callback) => {
        return await callback({});
      });

      await handler.handle(args);

      expect(mockLogger.error).toHaveBeenCalledWith(
        'Failed to join the triage channel: Failed to join conversation'
      );
      expect(mockChannelRepository.updateWithTxn).toHaveBeenCalled();
      expect(mockTriageMapsRepository.saveWithTxn).toHaveBeenCalled();
    });

    it('should handle non-Error exceptions during join conversation', async () => {
      const args = {
        context: {
          installation: mockInstallation,
          organization: mockOrganization
        },
        view: {
          private_metadata: JSON.stringify({
            channelId: 'C12345'
          }),
          state: {
            values: {
              [ConfigureTriageChannelComposite.BLOCK_ID]: {
                [ConfigureTriageChannelComposite.ACTION_IDS.CHANNEL_SELECT]: {
                  selected_option: {
                    value: 'C67890'
                  }
                }
              }
            }
          }
        }
      } as unknown as SlackViewMiddlewareArgs;

      (mockChannelRepository.findAll as Mock).mockResolvedValue([
        mockCustomerChannel,
        mockTriageChannel
      ]);
      
      (mockSlackWebAPIService.joinConversation as Mock).mockRejectedValue('String error');
      (mockTransactionService.runInTransaction as Mock).mockImplementation(async (callback) => {
        return await callback({});
      });

      await handler.handle(args);

      expect(console.error).toHaveBeenCalledWith('String error');
      expect(mockChannelRepository.updateWithTxn).toHaveBeenCalled();
      expect(mockTriageMapsRepository.saveWithTxn).toHaveBeenCalled();
    });
  });
});
