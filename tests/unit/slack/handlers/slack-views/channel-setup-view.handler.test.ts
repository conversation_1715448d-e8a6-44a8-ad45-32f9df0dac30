import { describe, it, expect, beforeEach, vi, <PERSON>ck } from 'vitest';
import { ChannelSetupViewHandler } from '../../../../../src/slack/handlers/slack-views/channel-setup-view.handler';
import { ILogger } from '../../../../../src/utils/logger';
import { ChannelsRepository } from '../../../../../src/database/entities/channels/repositories/channels.repository';
import { Repository } from 'typeorm';
import { PlatformTeamsToChannelMappings } from '../../../../../src/database/entities/mappings/platform-teams-to-channel-mappings.entity';
import { TeamsRepository } from '../../../../../src/database/entities/teams';
import { DecoratedSlackViewMiddlewareArgs } from '../../../../../src/slack/event-handlers';
import { ChannelSetupBlocks } from '../../../../../src/slack/blocks/components';
import { ChannelType } from '../../../../../src/database/entities/channels/channels.entity';

describe('ChannelSetupViewHandler', () => {
  let handler: ChannelSetupViewHandler;
  let mockLogger: ILogger;
  let mockChannelsRepository: ChannelsRepository;
  let mockPlatformTeamsToChannelMappingsRepository: Repository<PlatformTeamsToChannelMappings>;
  let mockTeamsRepository: TeamsRepository;

  const mockInstallation = {
    id: 'installation-123',
    teamId: 'T12345',
    botToken: 'xoxb-token',
    organization: { id: 'org-123' }
  };

  const mockChannel = {
    id: 'channel-123',
    channelId: 'C12345',
    name: 'test-channel',
    installation: mockInstallation,
    organization: { id: 'org-123' },
    channelDump: {
      shared_team_ids: ['T67890', 'T54321']
    },
    isShared: true,
    sharedTeamIds: ['T67890', 'T54321']
  };

  const mockPlatformTeam = {
    id: 'team-123',
    uid: 'team-uid-123',
    name: 'Engineering'
  };

  beforeEach(() => {
    mockLogger = {
      debug: vi.fn(),
      log: vi.fn(),
      error: vi.fn(),
      warn: vi.fn(),
      verbose: vi.fn()
    } as unknown as ILogger;

    mockChannelsRepository = {
      findByCondition: vi.fn(),
      update: vi.fn()
    } as unknown as ChannelsRepository;

    mockPlatformTeamsToChannelMappingsRepository = {
      upsert: vi.fn()
    } as unknown as Repository<PlatformTeamsToChannelMappings>;

    mockTeamsRepository = {
      findByCondition: vi.fn()
    } as unknown as TeamsRepository;

    handler = new ChannelSetupViewHandler(
      mockLogger,
      mockChannelsRepository,
      mockTeamsRepository,
      mockPlatformTeamsToChannelMappingsRepository
    );

    vi.spyOn(console, 'error').mockImplementation(() => {});
  });

  describe('handle', () => {
    it('should process view submission and update channel config with team', async () => {
      const mockClient = {
        chat: {
          postEphemeral: vi.fn().mockResolvedValue({ ok: true }),
          postMessage: vi.fn().mockResolvedValue({ ok: true })
        }
      };

      const args = {
        context: {
          installation: mockInstallation,
          client: mockClient,
          user: { id: 'U12345' },
          channelId: 'C12345'
        },
        view: {
          private_metadata: JSON.stringify({
            channelId: 'C12345'
          }),
          state: {
            values: {
              [`${ChannelSetupBlocks.BLOCK_ID}_team`]: {
                [ChannelSetupBlocks.ACTION_IDS.TEAM_SELECT]: {
                  selected_option: {
                    value: 'team-uid-123'
                  }
                }
              },
              [`${ChannelSetupBlocks.BLOCK_ID}_type`]: {
                [ChannelSetupBlocks.ACTION_IDS.CHANNEL_TYPE_SELECT]: {
                  selected_option: {
                    value: ChannelType.CUSTOMER_CHANNEL
                  }
                }
              }
            }
          }
        },
        body: {
          user: { id: 'U12345' }
        }
      } as unknown as DecoratedSlackViewMiddlewareArgs;

      (mockChannelsRepository.findByCondition as Mock).mockResolvedValue(mockChannel);
      (mockTeamsRepository.findByCondition as Mock).mockResolvedValue(mockPlatformTeam);
      (mockChannelsRepository.update as Mock).mockResolvedValue({ affected: 1 });
      (mockPlatformTeamsToChannelMappingsRepository.upsert as Mock).mockResolvedValue({});

      await handler.handle(args);

      expect(mockChannelsRepository.findByCondition).toHaveBeenCalledWith({
        where: { channelId: 'C12345', installation: { id: 'installation-123' } },
        relations: { installation: true, organization: true }
      });
      expect(mockTeamsRepository.findByCondition).toHaveBeenCalledWith({
        where: {
          uid: 'team-uid-123',
          installation: { id: 'installation-123' }
        }
      });
      expect(mockChannelsRepository.update).toHaveBeenCalledWith('channel-123', {
        isBotActive: true,
        isBotJoined: true,
        channelType: ChannelType.CUSTOMER_CHANNEL,
        sharedTeamIds: ['T67890', 'T54321']
      });
      expect(mockPlatformTeamsToChannelMappingsRepository.upsert).toHaveBeenCalledWith(
        expect.objectContaining({
          relationshipType: expect.any(String),
          platformTeam: { id: 'team-123' },
          channel: { id: 'channel-123' },
          installation: mockInstallation,
          organization: { id: 'org-123' }
        }),
        expect.any(Object)
      );
      expect(mockClient.chat.postEphemeral).toHaveBeenCalledWith(expect.objectContaining({
        channel: 'C12345',
        user: 'U12345',
        text: expect.stringContaining('Customer Support')
      }));
      expect(mockClient.chat.postMessage).toHaveBeenCalledWith(expect.objectContaining({
        channel: 'U12345',
        text: expect.stringContaining('Customer Support')
      }));
    });

    it('should process view submission without team selection', async () => {
      const mockClient = {
        chat: {
          postEphemeral: vi.fn().mockResolvedValue({ ok: true }),
          postMessage: vi.fn().mockResolvedValue({ ok: true })
        }
      };

      const args = {
        context: {
          installation: mockInstallation,
          client: mockClient,
          user: { id: 'U12345' },
          channelId: 'C12345'
        },
        view: {
          private_metadata: JSON.stringify({
            channelId: 'C12345'
          }),
          state: {
            values: {
              [`${ChannelSetupBlocks.BLOCK_ID}_team`]: {
                [ChannelSetupBlocks.ACTION_IDS.TEAM_SELECT]: {
                }
              },
              [`${ChannelSetupBlocks.BLOCK_ID}_type`]: {
                [ChannelSetupBlocks.ACTION_IDS.CHANNEL_TYPE_SELECT]: {
                  selected_option: {
                    value: ChannelType.TRIAGE_CHANNEL
                  }
                }
              }
            }
          }
        },
        body: {
          user: { id: 'U12345' }
        }
      } as unknown as DecoratedSlackViewMiddlewareArgs;

      (mockChannelsRepository.findByCondition as Mock).mockResolvedValue(mockChannel);
      (mockChannelsRepository.update as Mock).mockResolvedValue({ affected: 1 });

      await handler.handle(args);

      expect(mockChannelsRepository.findByCondition).toHaveBeenCalled();
      expect(mockChannelsRepository.update).toHaveBeenCalledWith('channel-123', {
        isBotActive: true,
        isBotJoined: true,
        channelType: ChannelType.TRIAGE_CHANNEL,
        sharedTeamIds: ['T67890', 'T54321']
      });
      expect(mockTeamsRepository.findByCondition).not.toHaveBeenCalled();
      expect(mockPlatformTeamsToChannelMappingsRepository.upsert).not.toHaveBeenCalled();
      expect(mockClient.chat.postEphemeral).toHaveBeenCalledWith(expect.objectContaining({
        channel: 'C12345',
        user: 'U12345',
        text: expect.stringContaining('Triage')
      }));
    });

    it('should handle invalid private metadata', async () => {
      const args = {
        context: {
          installation: mockInstallation,
          client: {
            chat: {
              postEphemeral: vi.fn().mockResolvedValue({ ok: true })
            }
          }
        },
        view: {
          private_metadata: 'invalid-json'
        }
      } as unknown as DecoratedSlackViewMiddlewareArgs;

      await handler.handle(args);

      expect(mockLogger.error).toHaveBeenCalledWith(
        expect.stringContaining('Error parsing private metadata:')
      );
      expect(mockChannelsRepository.findByCondition).not.toHaveBeenCalled();
    });

    it('should handle missing channelId in private metadata', async () => {
      const args = {
        context: {
          installation: mockInstallation,
          client: {
            chat: {
              postEphemeral: vi.fn().mockResolvedValue({ ok: true })
            }
          }
        },
        view: {
          private_metadata: JSON.stringify({
          })
        }
      } as unknown as DecoratedSlackViewMiddlewareArgs;

      await handler.handle(args);

      expect(mockLogger.error).toHaveBeenCalledWith('No channel ID found in private metadata');
      expect(mockChannelsRepository.findByCondition).not.toHaveBeenCalled();
    });

    it('should handle channel not found', async () => {
      const args = {
        context: {
          installation: mockInstallation,
          client: {
            chat: {
              postEphemeral: vi.fn().mockResolvedValue({ ok: true })
            }
          }
        },
        view: {
          private_metadata: JSON.stringify({
            channelId: 'C12345'
          }),
          state: {
            values: {}
          }
        }
      } as unknown as DecoratedSlackViewMiddlewareArgs;

      (mockChannelsRepository.findByCondition as Mock).mockResolvedValue(null);

      await handler.handle(args);

      expect(mockLogger.error).toHaveBeenCalledWith('Channel C12345 not found');
      expect(mockChannelsRepository.update).not.toHaveBeenCalled();
    });

    it('should handle platform team not found', async () => {
      const mockClient = {
        chat: {
          postEphemeral: vi.fn().mockResolvedValue({ ok: true }),
          postMessage: vi.fn().mockResolvedValue({ ok: true })
        }
      };

      const args = {
        context: {
          installation: mockInstallation,
          client: mockClient,
          user: { id: 'U12345' },
          channelId: 'C12345'
        },
        view: {
          private_metadata: JSON.stringify({
            channelId: 'C12345'
          }),
          state: {
            values: {
              [`${ChannelSetupBlocks.BLOCK_ID}_team`]: {
                [ChannelSetupBlocks.ACTION_IDS.TEAM_SELECT]: {
                  selected_option: {
                    value: 'team-uid-123'
                  }
                }
              },
              [`${ChannelSetupBlocks.BLOCK_ID}_type`]: {
                [ChannelSetupBlocks.ACTION_IDS.CHANNEL_TYPE_SELECT]: {
                  selected_option: {
                    value: ChannelType.CUSTOMER_CHANNEL
                  }
                }
              }
            }
          }
        },
        body: {
          user: { id: 'U12345' }
        }
      } as unknown as DecoratedSlackViewMiddlewareArgs;

      (mockChannelsRepository.findByCondition as Mock).mockResolvedValue(mockChannel);
      (mockTeamsRepository.findByCondition as Mock).mockResolvedValue(null);
      (mockChannelsRepository.update as Mock).mockResolvedValue({ affected: 1 });

      await handler.handle(args);

      expect(mockLogger.error).toHaveBeenCalledWith('Platform team team-uid-123 not found');
      expect(mockChannelsRepository.update).toHaveBeenCalled();
      expect(mockPlatformTeamsToChannelMappingsRepository.upsert).not.toHaveBeenCalled();
      expect(mockClient.chat.postEphemeral).toHaveBeenCalled();
      expect(mockClient.chat.postMessage).toHaveBeenCalled();
    });

    it('should handle error during DM sending', async () => {
      const mockClient = {
        chat: {
          postEphemeral: vi.fn().mockResolvedValue({ ok: true }),
          postMessage: vi.fn().mockRejectedValue(new Error('DM error'))
        }
      };

      const args = {
        context: {
          installation: mockInstallation,
          client: mockClient,
          user: { id: 'U12345' },
          channelId: 'C12345'
        },
        view: {
          private_metadata: JSON.stringify({
            channelId: 'C12345'
          }),
          state: {
            values: {
              [`${ChannelSetupBlocks.BLOCK_ID}_type`]: {
                [ChannelSetupBlocks.ACTION_IDS.CHANNEL_TYPE_SELECT]: {
                  selected_option: {
                    value: ChannelType.INTERNAL_HELPDESK
                  }
                }
              }
            }
          }
        },
        body: {
          user: { id: 'U12345' }
        }
      } as unknown as DecoratedSlackViewMiddlewareArgs;

      (mockChannelsRepository.findByCondition as Mock).mockResolvedValue(mockChannel);
      (mockChannelsRepository.update as Mock).mockResolvedValue({ affected: 1 });

      await handler.handle(args);

      expect(mockLogger.error).toHaveBeenCalledWith(
        expect.stringContaining('Failed to send confirmation DM:')
      );
      expect(mockClient.chat.postEphemeral).toHaveBeenCalled();
      expect(mockClient.chat.postMessage).toHaveBeenCalled();
    });

    it('should handle general errors during processing', async () => {
      const args = {
        context: {
          installation: mockInstallation,
          client: {
            chat: {
              postEphemeral: vi.fn().mockResolvedValue({ ok: true })
            }
          }
        },
        view: {
          private_metadata: JSON.stringify({
            channelId: 'C12345'
          }),
          state: {
            values: {}
          }
        }
      } as unknown as DecoratedSlackViewMiddlewareArgs;

      const error = new Error('Test error');
      (mockChannelsRepository.findByCondition as Mock).mockRejectedValue(error);

      await handler.handle(args);

      expect(mockLogger.error).toHaveBeenCalledWith(
        expect.stringContaining('Error handling channel setup view submission:')
      );
    });
  });
});
