import { describe, it, expect, beforeEach, vi, <PERSON><PERSON> } from 'vitest';
import { CreateTicketViewHandler } from '../../../../../src/slack/handlers/slack-views/create-ticket-view.handler';
import { ILogger } from '../../../../../src/utils/logger';
import { SlackMessagesRepository } from '../../../../../src/database/entities/slack-messages/repositories/slack-messages.repository';
import { TeamChannelMapsRepository } from '../../../../../src/database/entities/mappings/repositories/team-channel-maps.repository';
import { TeamsRepository } from '../../../../../src/database/entities/teams';
import { TransactionService } from '../../../../../src/database/common/transactions.service';
import { SlackAppManagementService } from '../../../../../src/slack/core';
import { ThenaPlatformApiProvider } from '../../../../../src/external/provider/thena-platform-api.provider';
import { SlackWebAPIService } from '../../../../../src/slack/providers/slack-apis/slack-apis.service';
import { DecoratedSlackViewMiddlewareArgs } from '../../../../../src/slack/event-handlers';
import { CustomerContacts } from '../../../../../src/database/entities';

describe('CreateTicketViewHandler', () => {
  let handler: CreateTicketViewHandler;
  let mockLogger: ILogger;
  let mockSlackMessagesRepository: SlackMessagesRepository;
  let mockTeamChannelMapsRepo: TeamChannelMapsRepository;
  let mockPlatformTeamsRepo: TeamsRepository;
  let mockTransactionService: TransactionService;
  let mockSlackAppManagementService: SlackAppManagementService;
  let mockThenaPlatformApiProvider: ThenaPlatformApiProvider;
  let mockSlackApiProvider: SlackWebAPIService;

  const mockInstallation = {
    id: 'installation-123',
    teamId: 'T12345',
    botToken: 'xoxb-token',
    organization: { id: 'org-123' }
  };

  const mockTeamChannelMap = {
    channel: {
      id: 'channel-123',
      channelId: 'C12345',
      name: 'test-channel'
    },
    platformTeam: {
      id: 'team-123',
      uid: 'team-uid-123',
      name: 'Engineering'
    }
  };

  const mockTicket = {
    id: 'ticket-123',
    ticketId: 'TKT-123',
    status: 'Open',
    statusId: 'status-123',
    priority: 'Medium',
    priorityId: 'priority-123'
  };

  beforeEach(() => {
    mockLogger = {
      debug: vi.fn(),
      log: vi.fn(),
      error: vi.fn(),
      warn: vi.fn(),
      verbose: vi.fn()
    } as unknown as ILogger;

    mockSlackMessagesRepository = {
      saveWithTxn: vi.fn(),
      updateWithTxn: vi.fn()
    } as unknown as SlackMessagesRepository;

    mockTeamChannelMapsRepo = {
      findByCondition: vi.fn()
    } as unknown as TeamChannelMapsRepository;

    mockPlatformTeamsRepo = {
      findByCondition: vi.fn()
    } as unknown as TeamsRepository;

    mockTransactionService = {
      runInTransaction: vi.fn()
    } as unknown as TransactionService;

    mockSlackAppManagementService = {
      upsertPersonWithIdentification: vi.fn()
    } as unknown as SlackAppManagementService;

    mockThenaPlatformApiProvider = {
      createNewTicket: vi.fn(),
      createNewComment: vi.fn()
    } as unknown as ThenaPlatformApiProvider;

    mockSlackApiProvider = {
      getConversationHistory: vi.fn(),
      getPermalink: vi.fn(),
      sendMessage: vi.fn()
    } as unknown as SlackWebAPIService;

    handler = new CreateTicketViewHandler(
      mockLogger,
      mockSlackMessagesRepository,
      mockTeamChannelMapsRepo,
      mockPlatformTeamsRepo,
      mockTransactionService,
      mockSlackAppManagementService,
      mockThenaPlatformApiProvider,
      mockSlackApiProvider
    );

    global.fetch = vi.fn().mockResolvedValue({
      ok: true,
      json: vi.fn().mockResolvedValue({})
    });

    vi.spyOn(console, 'error').mockImplementation(() => {});
  });

  describe('handle', () => {
    it('should process view submission and create a ticket with team channel map', async () => {
      const mockClient = {
        chat: {
          postMessage: vi.fn().mockResolvedValue({ ok: true })
        }
      };

      const args = {
        context: {
          installation: mockInstallation,
          organization: { id: 'org-123' },
          client: mockClient
        },
        body: {
          user: { id: 'U12345' }
        },
        view: {
          private_metadata: JSON.stringify({
            channelId: 'C12345',
            responseUrl: 'https://slack.com/api/response_url'
          }),
          state: {
            values: {
              title_block: {
                title_input: {
                  value: 'Test Ticket'
                }
              },
              requestor_email_block: {
                requestor_email_input: {
                  value: '<EMAIL>'
                }
              },
              description_block: {
                description_input: {
                  value: 'This is a test ticket'
                }
              }
            }
          }
        }
      } as unknown as DecoratedSlackViewMiddlewareArgs;

      (mockTeamChannelMapsRepo.findByCondition as Mock).mockResolvedValue(mockTeamChannelMap);
      (mockThenaPlatformApiProvider.createNewTicket as Mock).mockResolvedValue(mockTicket);

      await handler.handle(args);

      expect(mockTeamChannelMapsRepo.findByCondition).toHaveBeenCalledWith({
        where: {
          channel: { channelId: 'C12345' },
          installation: { id: 'installation-123' },
          organization: { id: 'org-123' }
        },
        relations: { channel: true, platformTeam: true }
      });
      expect(mockThenaPlatformApiProvider.createNewTicket).toHaveBeenCalledWith(
        mockInstallation,
        {
          title: 'Test Ticket',
          requestorEmail: '<EMAIL>',
          teamId: 'team-uid-123',
          text: 'This is a test ticket',
          metadata: {
            slack: {
              channel: 'C12345',
              ts: 'SLASH_TICKET',
              user: 'U12345'
            }
          }
        }
      );
      expect(global.fetch).toHaveBeenCalledWith('https://slack.com/api/response_url', expect.any(Object));
      expect(mockClient.chat.postMessage).toHaveBeenCalledWith({
        token: 'xoxb-token',
        channel: 'C12345',
        text: 'Ticket created successfully. Ticket ID: TKT-123'
      });
    });

    it('should process view submission and create a ticket with selected team', async () => {
      const mockClient = {
        chat: {
          postMessage: vi.fn().mockResolvedValue({ ok: true })
        }
      };

      const args = {
        context: {
          installation: mockInstallation,
          organization: { id: 'org-123' },
          client: mockClient
        },
        body: {
          user: { id: 'U12345' }
        },
        view: {
          private_metadata: JSON.stringify({
            channelId: 'C12345',
            responseUrl: 'https://slack.com/api/response_url'
          }),
          state: {
            values: {
              title_block: {
                title_input: {
                  value: 'Test Ticket'
                }
              },
              team_select_block: {
                team_select: {
                  selected_option: {
                    value: 'team-uid-123'
                  }
                }
              },
              requestor_email_block: {
                requestor_email_input: {
                  value: '<EMAIL>'
                }
              },
              description_block: {
                description_input: {
                  value: 'This is a test ticket'
                }
              }
            }
          }
        }
      } as unknown as DecoratedSlackViewMiddlewareArgs;

      (mockTeamChannelMapsRepo.findByCondition as Mock).mockResolvedValue(null);
      (mockPlatformTeamsRepo.findByCondition as Mock).mockResolvedValue(mockTeamChannelMap.platformTeam);
      (mockThenaPlatformApiProvider.createNewTicket as Mock).mockResolvedValue(mockTicket);

      await handler.handle(args);

      expect(mockTeamChannelMapsRepo.findByCondition).toHaveBeenCalled();
      expect(mockPlatformTeamsRepo.findByCondition).toHaveBeenCalledWith({
        where: {
          uid: 'team-uid-123',
          installation: { id: 'installation-123' }
        }
      });
      expect(mockThenaPlatformApiProvider.createNewTicket).toHaveBeenCalledWith(
        mockInstallation,
        expect.objectContaining({
          title: 'Test Ticket',
          teamId: 'team-uid-123'
        })
      );
    });

    it('should link slack message when shouldLinkSlackMessage is true', async () => {
      const mockClient = {
        chat: {
          postMessage: vi.fn().mockResolvedValue({ ok: true })
        }
      };

      const args = {
        context: {
          installation: mockInstallation,
          organization: { id: 'org-123' },
          client: mockClient
        },
        body: {
          user: { id: 'U12345' }
        },
        view: {
          private_metadata: JSON.stringify({
            channelId: 'C12345',
            responseUrl: 'https://slack.com/api/response_url',
            threadTs: '**********.123456',
            shouldLinkSlackMessage: true
          }),
          state: {
            values: {
              title_block: {
                title_input: {
                  value: 'Test Ticket'
                }
              },
              requestor_email_block: {
                requestor_email_input: {
                  value: '<EMAIL>'
                }
              },
              description_block: {
                description_input: {
                  value: 'This is a test ticket'
                }
              }
            }
          }
        }
      } as unknown as DecoratedSlackViewMiddlewareArgs;

      const mockSlackMessage = {
        ts: '**********.123456',
        thread_ts: '**********.123456',
        user: 'U12345',
        text: 'This is a test message'
      };

      const mockUser = {
        displayName: 'Test User',
        realName: 'Test User',
        slackProfileEmail: '<EMAIL>',
        getUserAvatar: vi.fn().mockReturnValue('https://example.com/avatar.jpg')
      };

      const mockComment = {
        data: {
          id: 'comment-123'
        }
      };

      const mockSlackMessageRecord = {
        id: 'slack-message-123'
      };

      (mockTeamChannelMapsRepo.findByCondition as Mock).mockResolvedValue(mockTeamChannelMap);
      (mockThenaPlatformApiProvider.createNewTicket as Mock).mockResolvedValue(mockTicket);
      (mockSlackApiProvider.getConversationHistory as Mock).mockResolvedValue({
        ok: true,
        messages: [mockSlackMessage]
      });
      (mockSlackAppManagementService.upsertPersonWithIdentification as Mock).mockResolvedValue(mockUser);
      (mockSlackApiProvider.getPermalink as Mock).mockResolvedValue({
        ok: true,
        permalink: 'https://slack.com/archives/C12345/p**********123456'
      });
      (mockSlackMessagesRepository.saveWithTxn as Mock).mockResolvedValue(mockSlackMessageRecord);
      (mockThenaPlatformApiProvider.createNewComment as Mock).mockResolvedValue(mockComment);
      (mockTransactionService.runInTransaction as Mock).mockImplementation(async (callback) => {
        return await callback({});
      });

      await handler.handle(args);

      expect(mockTeamChannelMapsRepo.findByCondition).toHaveBeenCalled();
      expect(mockThenaPlatformApiProvider.createNewTicket).toHaveBeenCalled();
      expect(mockSlackApiProvider.getConversationHistory).toHaveBeenCalledWith(
        'xoxb-token',
        {
          oldest: '**********.123456',
          inclusive: true,
          channel: 'C12345',
          limit: 1
        }
      );
      expect(mockSlackAppManagementService.upsertPersonWithIdentification).toHaveBeenCalledWith(
        'U12345',
        mockInstallation,
        mockTeamChannelMap.channel
      );
      expect(mockSlackApiProvider.getPermalink).toHaveBeenCalledWith(
        'xoxb-token',
        {
          channel: 'C12345',
          message_ts: '**********.123456'
        }
      );
      expect(mockSlackMessagesRepository.saveWithTxn).toHaveBeenCalled();
      expect(mockThenaPlatformApiProvider.createNewComment).toHaveBeenCalled();
      expect(mockSlackMessagesRepository.updateWithTxn).toHaveBeenCalledWith(
        {},
        'slack-message-123',
        { platformCommentId: 'comment-123' }
      );
      expect(mockSlackApiProvider.sendMessage).toHaveBeenCalledWith(
        'xoxb-token',
        expect.objectContaining({
          channel: 'C12345',
          thread_ts: '**********.123456'
        })
      );
    });

    it('should handle customer contact when linking slack message', async () => {
      const mockClient = {
        chat: {
          postMessage: vi.fn().mockResolvedValue({ ok: true })
        }
      };

      const args = {
        context: {
          installation: mockInstallation,
          organization: { id: 'org-123' },
          client: mockClient
        },
        body: {
          user: { id: 'U12345' }
        },
        view: {
          private_metadata: JSON.stringify({
            channelId: 'C12345',
            responseUrl: 'https://slack.com/api/response_url',
            threadTs: '**********.123456',
            shouldLinkSlackMessage: true
          }),
          state: {
            values: {
              title_block: {
                title_input: {
                  value: 'Test Ticket'
                }
              },
              requestor_email_block: {
                requestor_email_input: {
                  value: '<EMAIL>'
                }
              },
              description_block: {
                description_input: {
                  value: 'This is a test ticket'
                }
              }
            }
          }
        }
      } as unknown as DecoratedSlackViewMiddlewareArgs;

      const mockSlackMessage = {
        ts: '**********.123456',
        thread_ts: '**********.123456',
        user: 'U12345',
        text: 'This is a test message'
      };

      const mockCustomerContact = new CustomerContacts();
      mockCustomerContact.displayName = 'Test Customer';
      mockCustomerContact.realName = 'Test Customer';
      mockCustomerContact.slackProfileEmail = '<EMAIL>';
      mockCustomerContact.getUserAvatar = vi.fn().mockReturnValue('https://example.com/avatar.jpg');

      const mockComment = {
        data: {
          id: 'comment-123'
        }
      };

      const mockSlackMessageRecord = {
        id: 'slack-message-123'
      };

      (mockTeamChannelMapsRepo.findByCondition as Mock).mockResolvedValue(mockTeamChannelMap);
      (mockThenaPlatformApiProvider.createNewTicket as Mock).mockResolvedValue(mockTicket);
      (mockSlackApiProvider.getConversationHistory as Mock).mockResolvedValue({
        ok: true,
        messages: [mockSlackMessage]
      });
      (mockSlackAppManagementService.upsertPersonWithIdentification as Mock).mockResolvedValue(mockCustomerContact);
      (mockSlackApiProvider.getPermalink as Mock).mockResolvedValue({
        ok: true,
        permalink: 'https://slack.com/archives/C12345/p**********123456'
      });
      (mockSlackMessagesRepository.saveWithTxn as Mock).mockResolvedValue(mockSlackMessageRecord);
      (mockThenaPlatformApiProvider.createNewComment as Mock).mockResolvedValue(mockComment);
      (mockTransactionService.runInTransaction as Mock).mockImplementation(async (callback) => {
        return await callback({});
      });

      await handler.handle(args);

      expect(mockThenaPlatformApiProvider.createNewComment).toHaveBeenCalledWith(
        mockInstallation,
        expect.objectContaining({
          customerEmail: '<EMAIL>'
        })
      );
    });

    it('should handle error when team channel map not found and no team selected', async () => {
      const mockClient = {
        chat: {
          postMessage: vi.fn().mockResolvedValue({ ok: true })
        }
      };

      const args = {
        context: {
          installation: mockInstallation,
          organization: { id: 'org-123' },
          client: mockClient
        },
        body: {
          user: { id: 'U12345' }
        },
        view: {
          private_metadata: JSON.stringify({
            channelId: 'C12345',
            responseUrl: 'https://slack.com/api/response_url'
          }),
          state: {
            values: {
              title_block: {
                title_input: {
                  value: 'Test Ticket'
                }
              },
              requestor_email_block: {
                requestor_email_input: {
                  value: '<EMAIL>'
                }
              },
              description_block: {
                description_input: {
                  value: 'This is a test ticket'
                }
              }
            }
          }
        }
      } as unknown as DecoratedSlackViewMiddlewareArgs;

      (mockTeamChannelMapsRepo.findByCondition as Mock).mockResolvedValue(null);

      await handler.handle(args);

      expect(mockLogger.debug).toHaveBeenCalledWith(
        expect.stringContaining('Team channel map not found for channel C12345')
      );
      expect(mockClient.chat.postMessage).toHaveBeenCalledWith({
        token: 'xoxb-token',
        channel: 'U12345',
        text: expect.stringContaining('An error occurred while creating a ticket')
      });
    });

    it('should handle error when platform team not found', async () => {
      const mockClient = {
        chat: {
          postMessage: vi.fn().mockResolvedValue({ ok: true })
        }
      };

      const args = {
        context: {
          installation: mockInstallation,
          organization: { id: 'org-123' },
          client: mockClient
        },
        body: {
          user: { id: 'U12345' }
        },
        view: {
          private_metadata: JSON.stringify({
            channelId: 'C12345',
            responseUrl: 'https://slack.com/api/response_url'
          }),
          state: {
            values: {
              title_block: {
                title_input: {
                  value: 'Test Ticket'
                }
              },
              team_select_block: {
                team_select: {
                  selected_option: {
                    value: 'team-uid-123'
                  }
                }
              },
              requestor_email_block: {
                requestor_email_input: {
                  value: '<EMAIL>'
                }
              },
              description_block: {
                description_input: {
                  value: 'This is a test ticket'
                }
              }
            }
          }
        }
      } as unknown as DecoratedSlackViewMiddlewareArgs;

      (mockTeamChannelMapsRepo.findByCondition as Mock).mockResolvedValue(null);
      (mockPlatformTeamsRepo.findByCondition as Mock).mockResolvedValue(null);

      await handler.handle(args);

      expect(mockClient.chat.postMessage).toHaveBeenCalledWith({
        token: 'xoxb-token',
        channel: 'U12345',
        text: expect.stringContaining('An error occurred while creating a ticket')
      });
    });

    it('should handle error during slack message history retrieval', async () => {
      const mockClient = {
        chat: {
          postMessage: vi.fn().mockResolvedValue({ ok: true })
        }
      };

      const args = {
        context: {
          installation: mockInstallation,
          organization: { id: 'org-123' },
          client: mockClient
        },
        body: {
          user: { id: 'U12345' }
        },
        view: {
          private_metadata: JSON.stringify({
            channelId: 'C12345',
            responseUrl: 'https://slack.com/api/response_url',
            threadTs: '**********.123456',
            shouldLinkSlackMessage: true
          }),
          state: {
            values: {
              title_block: {
                title_input: {
                  value: 'Test Ticket'
                }
              },
              requestor_email_block: {
                requestor_email_input: {
                  value: '<EMAIL>'
                }
              },
              description_block: {
                description_input: {
                  value: 'This is a test ticket'
                }
              }
            }
          }
        }
      } as unknown as DecoratedSlackViewMiddlewareArgs;

      (mockTeamChannelMapsRepo.findByCondition as Mock).mockResolvedValue(mockTeamChannelMap);
      (mockThenaPlatformApiProvider.createNewTicket as Mock).mockResolvedValue(mockTicket);
      (mockSlackApiProvider.getConversationHistory as Mock).mockResolvedValue({
        ok: false,
        error: 'channel_not_found'
      });

      await handler.handle(args);

      expect(mockLogger.error).toHaveBeenCalledWith(
        expect.stringContaining('Error getting slack message history'),
        'channel_not_found'
      );
    });

    it('should handle error during permalink retrieval', async () => {
      const mockClient = {
        chat: {
          postMessage: vi.fn().mockResolvedValue({ ok: true })
        }
      };

      const args = {
        context: {
          installation: mockInstallation,
          organization: { id: 'org-123' },
          client: mockClient
        },
        body: {
          user: { id: 'U12345' }
        },
        view: {
          private_metadata: JSON.stringify({
            channelId: 'C12345',
            responseUrl: 'https://slack.com/api/response_url',
            threadTs: '**********.123456',
            shouldLinkSlackMessage: true
          }),
          state: {
            values: {
              title_block: {
                title_input: {
                  value: 'Test Ticket'
                }
              },
              requestor_email_block: {
                requestor_email_input: {
                  value: '<EMAIL>'
                }
              },
              description_block: {
                description_input: {
                  value: 'This is a test ticket'
                }
              }
            }
          }
        }
      } as unknown as DecoratedSlackViewMiddlewareArgs;

      const mockSlackMessage = {
        ts: '**********.123456',
        thread_ts: '**********.123456',
        user: 'U12345',
        text: 'This is a test message'
      };

      const mockUser = {
        displayName: 'Test User',
        realName: 'Test User',
        slackProfileEmail: '<EMAIL>',
        getUserAvatar: vi.fn().mockReturnValue('https://example.com/avatar.jpg')
      };

      (mockTeamChannelMapsRepo.findByCondition as Mock).mockResolvedValue(mockTeamChannelMap);
      (mockThenaPlatformApiProvider.createNewTicket as Mock).mockResolvedValue(mockTicket);
      (mockSlackApiProvider.getConversationHistory as Mock).mockResolvedValue({
        ok: true,
        messages: [mockSlackMessage]
      });
      (mockSlackAppManagementService.upsertPersonWithIdentification as Mock).mockResolvedValue(mockUser);
      (mockSlackApiProvider.getPermalink as Mock).mockResolvedValue({
        ok: false,
        error: 'channel_not_found'
      });
      (mockTransactionService.runInTransaction as Mock).mockImplementation(async (callback) => {
        return await callback({});
      });

      await handler.handle(args);

      expect(mockLogger.error).toHaveBeenCalledWith(
        expect.stringContaining('Error getting slack message permalink'),
        'channel_not_found'
      );
    });

    it('should handle error during private metadata parsing', async () => {
      const mockClient = {
        chat: {
          postMessage: vi.fn().mockResolvedValue({ ok: true })
        }
      };

      const args = {
        context: {
          installation: mockInstallation,
          organization: { id: 'org-123' },
          client: mockClient
        },
        body: {
          user: { id: 'U12345' }
        },
        view: {
          private_metadata: 'invalid-json',
          state: {
            values: {}
          }
        }
      } as unknown as DecoratedSlackViewMiddlewareArgs;

      await handler.handle(args);

      expect(mockLogger.error).toHaveBeenCalledWith(
        expect.stringContaining('Error handling create ticket view submission'),
        expect.any(String)
      );
      expect(mockClient.chat.postMessage).toHaveBeenCalledWith({
        token: 'xoxb-token',
        channel: 'U12345',
        text: expect.stringContaining('An error occurred while creating a ticket')
      });
    });

    it('should handle error during message sending', async () => {
      const mockClient = {
        chat: {
          postMessage: vi.fn().mockResolvedValue({ ok: true })
        }
      };

      const args = {
        context: {
          installation: mockInstallation,
          organization: { id: 'org-123' },
          client: mockClient
        },
        body: {
          user: { id: 'U12345' }
        },
        view: {
          private_metadata: JSON.stringify({
            channelId: 'C12345',
            responseUrl: 'https://slack.com/api/response_url',
            threadTs: '**********.123456',
            shouldLinkSlackMessage: true
          }),
          state: {
            values: {
              title_block: {
                title_input: {
                  value: 'Test Ticket'
                }
              },
              requestor_email_block: {
                requestor_email_input: {
                  value: '<EMAIL>'
                }
              },
              description_block: {
                description_input: {
                  value: 'This is a test ticket'
                }
              }
            }
          }
        }
      } as unknown as DecoratedSlackViewMiddlewareArgs;

      const mockSlackMessage = {
        ts: '**********.123456',
        thread_ts: '**********.123456',
        user: 'U12345',
        text: 'This is a test message'
      };

      const mockUser = {
        displayName: 'Test User',
        realName: 'Test User',
        slackProfileEmail: '<EMAIL>',
        getUserAvatar: vi.fn().mockReturnValue('https://example.com/avatar.jpg')
      };

      const mockComment = {
        data: {
          id: 'comment-123'
        }
      };

      const mockSlackMessageRecord = {
        id: 'slack-message-123'
      };

      (mockTeamChannelMapsRepo.findByCondition as Mock).mockResolvedValue(mockTeamChannelMap);
      (mockThenaPlatformApiProvider.createNewTicket as Mock).mockResolvedValue(mockTicket);
      (mockSlackApiProvider.getConversationHistory as Mock).mockResolvedValue({
        ok: true,
        messages: [mockSlackMessage]
      });
      (mockSlackAppManagementService.upsertPersonWithIdentification as Mock).mockResolvedValue(mockUser);
      (mockSlackApiProvider.getPermalink as Mock).mockResolvedValue({
        ok: true,
        permalink: 'https://slack.com/archives/C12345/p**********123456'
      });
      (mockSlackMessagesRepository.saveWithTxn as Mock).mockResolvedValue(mockSlackMessageRecord);
      (mockThenaPlatformApiProvider.createNewComment as Mock).mockResolvedValue(mockComment);
      (mockTransactionService.runInTransaction as Mock).mockImplementation(async (callback) => {
        return await callback({});
      });

      const sendMessageError = new Error('Failed to send message');
      (mockSlackApiProvider.sendMessage as Mock).mockRejectedValue(sendMessageError);

      await handler.handle(args);

      expect(mockLogger.error).toHaveBeenCalledWith(
        expect.stringContaining('Error sending message in the thread informing about the ticket creation'),
        expect.any(String)
      );
    });
  });
});
