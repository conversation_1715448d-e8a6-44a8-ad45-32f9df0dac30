import { describe, it, expect, beforeEach, vi, Mock } from 'vitest';
import { TeamOptionsLoadHandler } from '../../../../../src/slack/handlers/channels/channel-team-select.handler';
import { ThenaPlatformApiProvider } from '../../../../../src/external/provider/thena-platform-api.provider';
import { EnrichedSlackArgsContext } from '../../../../../src/slack/services/slack-action-discovery.service';

describe('TeamOptionsLoadHandler', () => {
  let handler: TeamOptionsLoadHandler;
  let mockThenaPlatformApiProvider: ThenaPlatformApiProvider;

  const mockInstallation = {
    id: 'installation-123',
    teamId: 'T12345',
    botToken: 'xoxb-token',
    organization: { id: 'org-123' }
  };

  const mockTeams = [
    { id: 'team-1', name: 'Engineering' },
    { id: 'team-2', name: 'Support' },
    { id: 'team-3', name: 'Sales' }
  ];

  beforeEach(() => {
    mockThenaPlatformApiProvider = {
      getTeams: vi.fn()
    } as unknown as ThenaPlatformApiProvider;

    handler = new TeamOptionsLoadHandler(mockThenaPlatformApiProvider);
  });

  describe('handle', () => {
    it('should fetch teams and format them as Slack options', async () => {
      const args = {
        context: {
          installation: mockInstallation
        }
      } as unknown as EnrichedSlackArgsContext;

      (mockThenaPlatformApiProvider.getTeams as Mock).mockResolvedValue(mockTeams);

      const result = await handler.handle(args);

      expect(mockThenaPlatformApiProvider.getTeams).toHaveBeenCalledWith(mockInstallation);
      expect(result).toEqual({
        options: [
          {
            text: {
              type: 'plain_text',
              text: 'Engineering'
            },
            value: 'team-1'
          },
          {
            text: {
              type: 'plain_text',
              text: 'Support'
            },
            value: 'team-2'
          },
          {
            text: {
              type: 'plain_text',
              text: 'Sales'
            },
            value: 'team-3'
          }
        ]
      });
    });

    it('should handle empty teams array', async () => {
      const args = {
        context: {
          installation: mockInstallation
        }
      } as unknown as EnrichedSlackArgsContext;

      (mockThenaPlatformApiProvider.getTeams as Mock).mockResolvedValue([]);

      const result = await handler.handle(args);

      expect(mockThenaPlatformApiProvider.getTeams).toHaveBeenCalledWith(mockInstallation);
      expect(result).toEqual({
        options: []
      });
    });

    it('should handle errors during team fetching', async () => {
      const args = {
        context: {
          installation: mockInstallation
        }
      } as unknown as EnrichedSlackArgsContext;

      const error = new Error('Failed to fetch teams');
      (mockThenaPlatformApiProvider.getTeams as Mock).mockRejectedValue(error);

      await expect(handler.handle(args)).rejects.toThrow('Failed to fetch teams');
    });
  });
});
