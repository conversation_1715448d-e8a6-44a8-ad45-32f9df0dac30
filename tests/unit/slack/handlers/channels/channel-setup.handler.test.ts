import { describe, it, expect, beforeEach, vi, <PERSON><PERSON> } from 'vitest';
import { ChannelSetupHandler } from '../../../../../src/slack/handlers/channels/channel-setup.handler';
import { SlackAppManagementService } from '../../../../../src/slack/core';
import { SelectChannelTeamComposite } from '../../../../../src/slack/blocks/components/composite';
import { ILogger } from '../../../../../src/utils/logger';
import { ChannelsRepository } from '../../../../../src/database/entities/channels/repositories/channels.repository';
import { ChannelType } from '../../../../../src/database/entities/channels/channels.entity';
import { EnrichedSlackArgsContext } from '../../../../../src/slack/services/slack-action-discovery.service';
import { ChannelSetupBlocks } from '../../../../../src/slack/blocks/components/composite';
import { SlackActionMiddlewareArgs } from '@slack/bolt';

describe('ChannelSetupHandler', () => {
  let handler: ChannelSetupHandler;
  let mockSlackAppManagementService: SlackAppManagementService;
  let mockTeamConfigBlock: SelectChannelTeamComposite;
  let mockLogger: ILogger;
  let mockChannelsRepository: ChannelsRepository;
  
  const mockChannel = {
    id: 'channel-123',
    channelId: 'C12345',
    isBotJoined: true,
    channelType: null
  };

  const mockInstallation = {
    id: 'installation-123',
    teamId: 'T12345',
    botToken: 'xoxb-token',
    organization: { id: 'org-123' }
  };

  beforeEach(() => {
    mockLogger = {
      debug: vi.fn(),
      log: vi.fn(),
      error: vi.fn(),
      warn: vi.fn(),
      verbose: vi.fn()
    } as unknown as ILogger;

    mockSlackAppManagementService = {
      getAndUpsertChannel: vi.fn()
    } as unknown as SlackAppManagementService;

    mockTeamConfigBlock = {
      build: vi.fn().mockReturnValue({ blocks: [] })
    } as unknown as SelectChannelTeamComposite;

    mockChannelsRepository = {
      update: vi.fn()
    } as unknown as ChannelsRepository;

    handler = new ChannelSetupHandler(
      mockSlackAppManagementService,
      mockTeamConfigBlock,
      mockLogger,
      mockChannelsRepository
    );

    global.fetch = vi.fn().mockResolvedValue({
      ok: true,
      json: vi.fn().mockResolvedValue({})
    });
  });

  describe('handle', () => {
    it('should log error if context is missing', async () => {
      const args = {
        action: {},
        body: {}
      } as any;

      const result = await handler.handle(args);

      expect(mockLogger.error).toHaveBeenCalledWith('No context found in arguments');
      expect(result).toBeUndefined();
    });

    it('should handle channel type selection for customer channel', async () => {
      const args = {
        action: {
          action_id: ChannelSetupBlocks.ACTION_IDS.CHANNEL_TYPE_SELECT,
          selected_option: {
            value: ChannelType.CUSTOMER_CHANNEL
          }
        },
        body: {
          channel: { id: 'C12345' },
          trigger_id: 'trigger-123',
          response_url: 'https://slack.com/api/response_url'
        },
        context: {
          botToken: 'xoxb-token',
          installation: mockInstallation,
          organization: { id: 'org-123' },
          client: {
            views: {
              open: vi.fn().mockResolvedValue({ ok: true })
            }
          }
        },
        respond: vi.fn()
      } as unknown as any;

      (mockSlackAppManagementService.getAndUpsertChannel as Mock).mockResolvedValue(mockChannel);

      const result = await handler.handle(args);

      expect(mockSlackAppManagementService.getAndUpsertChannel).toHaveBeenCalledWith({
        channelId: 'C12345',
        token: 'xoxb-token',
        installationId: 'installation-123',
        organizationId: 'org-123'
      });
      expect(args.context.client.views.open).toHaveBeenCalled();
      expect(result).toEqual({
        response_type: 'ephemeral',
        text: 'Channel setup complete',
        replace_original: true,
        delete_original: true
      });
    });

    it('should handle channel type selection for internal helpdesk', async () => {
      const args = {
        action: {
          action_id: ChannelSetupBlocks.ACTION_IDS.CHANNEL_TYPE_SELECT,
          selected_option: {
            value: ChannelType.INTERNAL_HELPDESK
          }
        },
        body: {
          channel: { id: 'C12345' },
          response_url: 'https://slack.com/api/response_url'
        },
        context: {
          botToken: 'xoxb-token',
          installation: mockInstallation,
          organization: { id: 'org-123' }
        },
        respond: vi.fn()
      } as unknown as any;

      (mockSlackAppManagementService.getAndUpsertChannel as Mock).mockResolvedValue(mockChannel);

      const result = await handler.handle(args);

      expect(mockSlackAppManagementService.getAndUpsertChannel).toHaveBeenCalledWith({
        channelId: 'C12345',
        token: 'xoxb-token',
        installationId: 'installation-123',
        organizationId: 'org-123'
      });
      expect(mockChannelsRepository.update).toHaveBeenCalledWith('channel-123', {
        isBotActive: true,
        channelType: ChannelType.INTERNAL_HELPDESK
      });
      expect(result).toEqual({
        response_type: 'ephemeral',
        text: 'Channel setup complete',
        replace_original: true,
        delete_original: true
      });
    });

    it('should handle channel type selection for triage channel', async () => {
      const args = {
        action: {
          action_id: ChannelSetupBlocks.ACTION_IDS.CHANNEL_TYPE_SELECT,
          selected_option: {
            value: ChannelType.TRIAGE_CHANNEL
          }
        },
        body: {
          channel: { id: 'C12345' },
          response_url: 'https://slack.com/api/response_url'
        },
        context: {
          botToken: 'xoxb-token',
          installation: mockInstallation,
          organization: { id: 'org-123' }
        },
        respond: vi.fn()
      } as unknown as any;

      (mockSlackAppManagementService.getAndUpsertChannel as Mock).mockResolvedValue(mockChannel);

      const result = await handler.handle(args);

      expect(mockSlackAppManagementService.getAndUpsertChannel).toHaveBeenCalledWith({
        channelId: 'C12345',
        token: 'xoxb-token',
        installationId: 'installation-123',
        organizationId: 'org-123'
      });
      expect(mockChannelsRepository.update).toHaveBeenCalledWith('channel-123', {
        isBotActive: true,
        channelType: ChannelType.TRIAGE_CHANNEL
      });
      expect(result).toEqual({
        response_type: 'ephemeral',
        text: 'Channel setup complete',
        replace_original: true,
        delete_original: true
      });
    });

    it('should throw error if bot is not joined to the channel', async () => {
      const args = {
        action: {
          action_id: ChannelSetupBlocks.ACTION_IDS.CHANNEL_TYPE_SELECT,
          selected_option: {
            value: ChannelType.TRIAGE_CHANNEL
          }
        },
        body: {
          channel: { id: 'C12345' },
          response_url: 'https://slack.com/api/response_url'
        },
        context: {
          botToken: 'xoxb-token',
          installation: mockInstallation,
          organization: { id: 'org-123' }
        },
        respond: vi.fn()
      } as unknown as any;

      const channelWithoutBot = {
        ...mockChannel,
        isBotJoined: false
      };

      (mockSlackAppManagementService.getAndUpsertChannel as Mock).mockResolvedValue(channelWithoutBot);

      await handler.handle(args);

      expect(args.respond).toHaveBeenCalledWith({
        text: 'Bot is not present in this channel, please invite the bot first and try again.'
      });
    });

    it('should handle unknown action id', async () => {
      const args = {
        action: {
          action_id: 'unknown_action_id'
        },
        body: {
          channel: { id: 'C12345' },
          response_url: 'https://slack.com/api/response_url'
        },
        context: {
          botToken: 'xoxb-token',
          installation: mockInstallation,
          organization: { id: 'org-123' }
        },
        respond: vi.fn()
      } as unknown as any;

      (mockSlackAppManagementService.getAndUpsertChannel as Mock).mockResolvedValue(mockChannel);

      const result = await handler.handle(args);

      expect(mockLogger.error).toHaveBeenCalledWith('Unknown action id: unknown_action_id');
      expect(result).toEqual({
        response_type: 'ephemeral',
        text: 'Channel setup complete',
        replace_original: true,
        delete_original: true
      });
    });

    it.todo('should handle errors during processing', async () => {
      const respondMock = vi.fn();
      
      const args = {
        action: {
          action_id: ChannelSetupBlocks.ACTION_IDS.CHANNEL_TYPE_SELECT,
          selected_option: {
            value: ChannelType.TRIAGE_CHANNEL
          }
        },
        body: {
          channel: { id: 'C12345' },
          response_url: 'https://slack.com/api/response_url'
        },
        context: {
          botToken: 'xoxb-token',
          installation: mockInstallation,
          organization: { id: 'org-123' }
        },
        respond: respondMock
      } as unknown as SlackActionMiddlewareArgs;
      
      (mockSlackAppManagementService.getAndUpsertChannel as Mock).mockImplementation(() => {
        return Promise.reject(new Error('Test error'));
      });
      
      vi.spyOn(handler as any, 'removeEphemeralResponse').mockResolvedValue({});

      const result = await handler.handle(args);
      
      expect(mockLogger.error).toHaveBeenCalledWith('Error handling channel setup: Error: Test error');
      
      expect(respondMock).toHaveBeenCalledWith({ text: 'Test error' });
      
      expect(result).toEqual({
        response_type: 'ephemeral',
        text: 'Channel setup complete',
        replace_original: true,
        delete_original: true
      });
    });

    it.todo('should handle unknown errors during processing', async () => {
      const respondMock = vi.fn();
      
      const args = {
        action: {
          action_id: ChannelSetupBlocks.ACTION_IDS.CHANNEL_TYPE_SELECT,
          selected_option: {
            value: ChannelType.TRIAGE_CHANNEL
          }
        },
        body: {
          channel: { id: 'C12345' },
          response_url: 'https://slack.com/api/response_url'
        },
        context: {
          botToken: 'xoxb-token',
          installation: mockInstallation,
          organization: { id: 'org-123' }
        },
        respond: respondMock
      } as unknown as SlackActionMiddlewareArgs;

      (mockSlackAppManagementService.getAndUpsertChannel as Mock).mockImplementation(() => {
        return Promise.reject('Unknown error');
      });
      
      vi.spyOn(handler as any, 'removeEphemeralResponse').mockResolvedValue({});

      const result = await handler.handle(args);
      
      expect(mockLogger.error).toHaveBeenCalledWith('Error handling channel setup: Unknown error');
      
      expect(respondMock).toHaveBeenCalledWith({
        text: 'An unknown error occurred while handling the channel setup, please try again.'
      });
      
      expect(result).toEqual({
        response_type: 'ephemeral',
        text: 'Channel setup complete',
        replace_original: true,
        delete_original: true
      });
    });
  });
});
