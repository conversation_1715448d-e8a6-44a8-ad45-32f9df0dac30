import { Test, TestingModule } from '@nestjs/testing';
import { beforeEach, describe, expect, it, vi } from 'vitest';
import { Channels, Installations } from '../../../../../src/database/entities';
import { Person } from '../../../../../src/database/interfaces/person.interface';
import { Ticket } from '../../../../../src/platform/interfaces';
import { ForMessageFactory } from '../../../../../src/slack/core/factories/for-message.factory';

describe('ForMessageFactory', () => {
  let factory: ForMessageFactory;

  beforeEach(async () => {
    const module: TestingModule = await Test.createTestingModule({
      providers: [
        ForMessageFactory,
      ],
    }).compile();

    factory = module.get<ForMessageFactory>(ForMessageFactory);
  });

  describe('constructSlackMessage', () => {
    it('should construct a slack message with the provided data', () => {
      const mockTicket: Ticket = {
        id: 'ticket-123',
        status: 'Open',
        statusId: 'status-1',
        priority: 'High',
        priorityId: 'priority-1',
      } as Ticket;

      const mockChannel: Channels = {
        id: 'channel-id',
      } as Channels;

      const mockInstallation: Installations = {
        id: 'installation-id',
        organization: { id: 'org-id' },
      } as Installations;

      const mockCustomer: Person = {
        slackProfileEmail: '<EMAIL>',
        displayName: 'Customer Name',
        realName: 'Real Customer Name',
      } as Person;

      const mockSlackDetails = {
        eventTs: '**********.123456',
        ts: '**********.123456',
        threadTs: '**********.123456',
        messagePermalink: 'https://slack.com/archives/C12345/p**********123456',
        user: 'U12345',
      };

      const result = factory.constructSlackMessage({
        ticket: mockTicket,
        channel: mockChannel,
        installation: mockInstallation,
        customer: mockCustomer,
        slackDetails: mockSlackDetails,
      });

      expect(result).toEqual({
        platformTicketId: 'ticket-123',
        slackEventCreatedAt: expect.any(String),
        slackMessageTs: '**********.123456',
        slackMessageThreadTs: '**********.123456',
        slackPermalink: 'https://slack.com/archives/C12345/p**********123456',
        slackUserId: 'U12345',
        metadata: {
          customer: {
            email: '<EMAIL>',
            name: 'Customer Name',
          },
          ticket_details: {
            status: 'Open',
            statusId: 'status-1',
            priority: 'High',
            priorityId: 'priority-1',
          },
        },
        channel: { id: 'channel-id' },
        installation: { id: 'installation-id' },
        organization: { id: 'org-id' },
      });
    });

    it('should use realName if displayName is not available', () => {
      const mockTicket: Ticket = {
        id: 'ticket-123',
        status: 'Open',
        statusId: 'status-1',
        priority: 'High',
        priorityId: 'priority-1',
      } as Ticket;

      const mockChannel: Channels = {
        id: 'channel-id',
      } as Channels;

      const mockInstallation: Installations = {
        id: 'installation-id',
        organization: { id: 'org-id' },
      } as Installations;

      const mockCustomer: Person = {
        slackProfileEmail: '<EMAIL>',
        displayName: null,
        realName: 'Real Customer Name',
      } as Person;

      const mockSlackDetails = {
        eventTs: '**********.123456',
        ts: '**********.123456',
        threadTs: '**********.123456',
        messagePermalink: 'https://slack.com/archives/C12345/p**********123456',
        user: 'U12345',
      };

      const result = factory.constructSlackMessage({
        ticket: mockTicket,
        channel: mockChannel,
        installation: mockInstallation,
        customer: mockCustomer,
        slackDetails: mockSlackDetails,
      });

      expect(result.metadata.customer.name).toBe('Real Customer Name');
    });

    it.todo('should convert eventTs to ISO string date', () => {
      const mockTicket: Ticket = {
        id: 'ticket-123',
        status: 'Open',
        statusId: 'status-1',
        priority: 'High',
        priorityId: 'priority-1',
      } as Ticket;

      const mockChannel: Channels = {
        id: 'channel-id',
      } as Channels;

      const mockInstallation: Installations = {
        id: 'installation-id',
        organization: { id: 'org-id' },
      } as Installations;

      const mockCustomer: Person = {
        slackProfileEmail: '<EMAIL>',
        displayName: 'Customer Name',
        realName: 'Real Customer Name',
      } as Person;

      const mockSlackDetails = {
        eventTs: '**********.123456',
        ts: '**********.123456',
        threadTs: '**********.123456',
        messagePermalink: 'https://slack.com/archives/C12345/p**********123456',
        user: 'U12345',
      };

      const result = factory.constructSlackMessage({
        ticket: mockTicket,
        channel: mockChannel,
        installation: mockInstallation,
        customer: mockCustomer,
        slackDetails: mockSlackDetails,
      });

      const expectedDate = new Date(********** * 1000);
      
      expect(result.slackEventCreatedAt).toBe(expectedDate.toISOString());
    });
  });
});
