import { Test, TestingModule } from '@nestjs/testing';
import { beforeEach, describe, expect, it, vi } from 'vitest';
import { OnPlatformEvent } from '../../../../../src/slack/core/on-message/on-platform-message';
import { CUSTOM_LOGGER_TOKEN } from '../../../../../src/utils';

describe('OnPlatformEvent', () => {
  let service: OnPlatformEvent;
  let mockLogger: any;

  beforeEach(async () => {
    mockLogger = {
      log: vi.fn(),
      debug: vi.fn(),
      error: vi.fn(),
      warn: vi.fn(),
    };

    const module: TestingModule = await Test.createTestingModule({
      providers: [
        OnPlatformEvent,
        {
          provide: CUSTOM_LOGGER_TOKEN,
          useValue: mockLogger,
        },
      ],
    }).compile();

    service = module.get<OnPlatformEvent>(OnPlatformEvent);
  });

  describe('onMessage', () => {
    it('should call onMessage method without errors', async () => {
      const mockEvent = { 
        type: 'platform_message',
        data: {
          ticketId: '123',
          comment: {
            id: 'comment-123',
            content: 'Test comment'
          }
        }
      };

      await expect(service.onMessage(mockEvent)).resolves.not.toThrow();
    });
  });
});
