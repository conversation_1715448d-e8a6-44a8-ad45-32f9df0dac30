import { describe, expect, it } from 'vitest';
import * as OnMessageModule from '../../../../../src/slack/core/on-message';
import { CoreSlackMessage } from '../../../../../src/slack/core/on-message/core-slack-message';
import { OnMessageHandler } from '../../../../../src/slack/core/on-message/on-slack-message';
import { OnThreadMessageHandler } from '../../../../../src/slack/core/on-message/on-thread-message';

describe('OnMessage Module Exports', () => {
  it('should export OnMessageHandler', () => {
    expect(OnMessageModule.OnMessageHandler).toBeDefined();
    expect(OnMessageModule.OnMessageHandler).toBe(OnMessageHandler);
  });

  it('should export OnThreadMessageHandler', () => {
    expect(OnMessageModule.OnThreadMessageHandler).toBeDefined();
    expect(OnMessageModule.OnThreadMessageHandler).toBe(OnThreadMessageHandler);
  });

  it('should export CoreSlackMessage', () => {
    expect(OnMessageModule.CoreSlackMessage).toBeDefined();
    expect(OnMessageModule.CoreSlackMessage).toBe(CoreSlackMessage);
  });
});
