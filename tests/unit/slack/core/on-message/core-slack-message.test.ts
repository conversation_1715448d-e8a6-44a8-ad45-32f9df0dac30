import { Repository } from 'typeorm';
import { Mock, beforeEach, describe, expect, it, vi } from 'vitest';
import { Channels, CustomerContacts, Installations, PlatformTeams, Users } from '../../../../../src/database/entities';
import { ChannelsRepository } from '../../../../../src/database/entities/channels/repositories';
import { CustomerContactsRepository } from '../../../../../src/database/entities/customer-contacts/repositories/customer-contacts.repository';
import { TeamsRepository } from '../../../../../src/database/entities/teams/repositories/teams.repository';
import { UsersRepository } from '../../../../../src/database/entities/users/repositories/users.repository';
import { ThenaPlatformApiProvider } from '../../../../../src/external/provider/thena-platform-api.provider';
import { AiService } from '../../../../../src/ai/ai.service';
import { ILogger } from '../../../../../src/utils';
import { CoreSlackMessage } from '../../../../../src/slack/core/on-message/core-slack-message';
import { SettingsCore } from '../../../../../src/slack/core/management';
import { ChannelType } from '../../../../../src/database/entities/channels/channels.entity';

describe('CoreSlackMessage', () => {
  let service: CoreSlackMessage;
  let mockLogger: ILogger;
  let mockChannelsRepository: ChannelsRepository;
  let mockUsersRepository: Repository<Users>;
  let mockCustomerContactsRepository: Repository<CustomerContacts>;
  let mockTeamsRepository: TeamsRepository;
  let mockPlatformApiProvider: ThenaPlatformApiProvider;
  let mockAiService: AiService;
  let mockSettingsCore: SettingsCore;

  beforeEach(() => {
    // Reset mocks before each test
    vi.resetAllMocks();

    // Create mock implementations
    mockLogger = {
      log: vi.fn(),
      error: vi.fn(),
      warn: vi.fn(),
      debug: vi.fn(),
      verbose: vi.fn(),
    } as unknown as ILogger;

    mockUsersRepository = {
      findOne: vi.fn(),
      find: vi.fn(),
    } as unknown as Repository<Users>;

    mockCustomerContactsRepository = {
      findOne: vi.fn(),
    } as unknown as Repository<CustomerContacts>;

    mockChannelsRepository = {
      findOne: vi.fn(),
      findByCondition: vi.fn(),
      find: vi.fn(),
      save: vi.fn(),
      create: vi.fn(),
      update: vi.fn(),
      count: vi.fn(),
      exists: vi.fn(),
      remove: vi.fn(),
      softDelete: vi.fn(),
      findOneById: vi.fn(),
      findWithRelations: vi.fn(),
      findAll: vi.fn(),
    } as unknown as ChannelsRepository;

    const mockGroupedSlackMessagesRepository = {
      save: vi.fn(),
      findOne: vi.fn(),
    } as unknown as any;

    const mockSubGroupsMapsRepository = {
      findByCondition: vi.fn(),
    } as unknown as any;

    mockTeamsRepository = {
      findOne: vi.fn(),
      findAll: vi.fn(),
    } as unknown as TeamsRepository;

    const mockConversationGroupingService = {
      shouldGroupWithExistingConversation: vi.fn().mockResolvedValue({
        isGrouped: false,
      }),
    } as unknown as any;

    mockSettingsCore = {
      getValue: vi.fn(),
      getValueOrDefault: vi.fn(),
      setValue: vi.fn(),
      deleteValue: vi.fn(),
    } as unknown as SettingsCore;

    const mockSlackAppManagementService = {
      upsertPersonWithIdentification: vi.fn(),
    } as unknown as any;

    mockPlatformApiProvider = {
      createNewTicket: vi.fn(),
      getTeam: vi.fn(),
      getPrioritiesForTeam: vi.fn(),
    } as unknown as ThenaPlatformApiProvider;

    const mockSlackWebAPIService = {
      getPermalink: vi.fn(),
    } as unknown as any;

    mockAiService = {
      isValidTicket: vi.fn(),
      detectUrgency: vi.fn(),
      generateTicketTitle: vi.fn(),
      generateTicketDescription: vi.fn(),
      loadTeamPrompts: vi.fn(),
      setActiveModel: vi.fn(),
      setActiveProvider: vi.fn(),
    } as unknown as AiService;

    // Create the service instance
    service = new CoreSlackMessage(
      mockLogger,
      mockUsersRepository,
      mockCustomerContactsRepository,
      mockChannelsRepository,
      mockGroupedSlackMessagesRepository,
      mockSubGroupsMapsRepository,
      mockTeamsRepository,
      mockConversationGroupingService,
      mockSettingsCore,
      mockSlackAppManagementService,
      mockPlatformApiProvider,
      mockSlackWebAPIService,
      mockAiService,
    );
  });

  describe('checkAndGetSlackMessageDetails', () => {
    it('should return channel, user, and flags for a valid message', async () => {
      // Arrange
      const mockInstallation = {
        id: 'installation-id',
        teamId: 'T12345',
      } as unknown as Installations;

      const event = {
        type: 'message',
        user: 'U12345',
        channel: 'C12345',
        team: 'T12345',
        ts: '**********.123456',
        text: 'This is a test message',
      };

      const mockChannel = {
        id: 'channel-id',
        channelId: 'C12345',
        channelType: ChannelType.CUSTOMER_CHANNEL,
        isBotActive: true,
        platformTeamsToChannelMappings: [],
      } as unknown as Channels;

      const mockUser = {
        id: 'user-id',
        slackId: 'U12345',
        slackProfileEmail: '<EMAIL>',
      } as unknown as Users;

      (mockChannelsRepository.findByCondition as Mock).mockResolvedValue(mockChannel);
      (mockUsersRepository.findOne as Mock).mockResolvedValue(null);
      (mockCustomerContactsRepository.findOne as Mock).mockResolvedValue(mockUser);
      
      // Add mock for the shouldGroupWithExistingConversation method
      const mockConversationGroupingService = {
        shouldGroupWithExistingConversation: vi.fn().mockResolvedValue({
          isGrouped: false,
        }),
      };
      
      // Use property accessor to avoid readonly property error
      Object.defineProperty(service, 'conversationGroupingService', {
        value: mockConversationGroupingService
      });

      // Act
      const result = await service.checkAndGetSlackMessageDetails(
        mockInstallation,
        event as any,
      );

      // Assert
      expect(mockChannelsRepository.findByCondition).toHaveBeenCalledWith({
        where: {
          channelId: 'C12345',
          installation: { id: 'installation-id' },
        },
        relations: {
          platformTeamsToChannelMappings: {
            platformTeam: true,
          },
        },
      });
      expect(mockUsersRepository.findOne).toHaveBeenCalledWith({
        where: {
          slackId: 'U12345',
          installation: { id: 'installation-id' },
        },
      });
      expect(mockCustomerContactsRepository.findOne).toHaveBeenCalledWith({
        where: {
          slackId: 'U12345',
          installation: { id: 'installation-id' },
        },
      });
      expect(result).toEqual({
        channel: mockChannel,
        user: mockUser,
        isCustomer: true,
        isGrouped: false,
      });
    });

    it('should throw an error if message is not a top level message', async () => {
      // Arrange
      const mockInstallation = {
        id: 'installation-id',
        teamId: 'T12345',
      } as unknown as Installations;

      const event = {
        type: 'message',
        // Missing text property
        user: 'U12345',
        channel: 'C12345',
      };

      // Act & Assert
      await expect(
        service.checkAndGetSlackMessageDetails(mockInstallation, event as any),
      ).rejects.toThrow('Message is not a top level message');
    });

    it('should throw an error if message has no team', async () => {
      // Arrange
      const mockInstallation = {
        id: 'installation-id',
        teamId: 'T12345',
      } as unknown as Installations;

      const event = {
        type: 'message',
        text: 'This is a test message',
        user: 'U12345',
        channel: 'C12345',
        // Missing team property
      };

      // Act & Assert
      await expect(
        service.checkAndGetSlackMessageDetails(mockInstallation, event as any),
      ).rejects.toThrow('Slack workspace not attached to the event message!');
    });

    it('should throw an error if channel is not found', async () => {
      // Arrange
      const mockInstallation = {
        id: 'installation-id',
        teamId: 'T12345',
      } as unknown as Installations;

      const event = {
        type: 'message',
        text: 'This is a test message',
        user: 'U12345',
        channel: 'C12345',
        team: 'T12345',
        ts: '**********.123456',
      };

      (mockChannelsRepository.findByCondition as Mock).mockResolvedValue(null);

      // Act & Assert
      await expect(
        service.checkAndGetSlackMessageDetails(mockInstallation, event as any),
      ).rejects.toThrow('Channel not found!');
    });

    it('should skip ticket creation for non-customer in non-helpdesk channel', async () => {
      // Arrange
      const mockInstallation = {
        id: 'installation-id',
        teamId: 'T12345',
      } as unknown as Installations;

      const event = {
        type: 'message',
        text: 'This is a test message',
        user: 'U12345',
        channel: 'C12345',
        team: 'T12345',
        ts: '**********.123456',
      };

      const mockChannel = {
        id: 'channel-id',
        channelId: 'C12345',
        channelType: ChannelType.CUSTOMER_CHANNEL,
        isBotActive: true,
        platformTeamsToChannelMappings: [],
      } as unknown as Channels;

      const mockUser = {
        id: 'user-id',
        slackId: 'U12345',
        slackProfileEmail: '<EMAIL>',
      } as unknown as Users;

      (mockChannelsRepository.findByCondition as Mock).mockResolvedValue(mockChannel);
      (mockUsersRepository.findOne as Mock).mockResolvedValue(mockUser);
      (mockCustomerContactsRepository.findOne as Mock).mockResolvedValue(null);

      // Add mock for the shouldGroupWithExistingConversation method
      const mockConversationGroupingService = {
        shouldGroupWithExistingConversation: vi.fn().mockResolvedValue({
          isGrouped: false,
        }),
      };
      
      // Use property accessor to avoid readonly property error
      Object.defineProperty(service, 'conversationGroupingService', {
        value: mockConversationGroupingService
      });

      // Act
      const result = await service.checkAndGetSlackMessageDetails(
        mockInstallation,
        event as any,
      );

      // Assert
      expect(mockLogger.debug).toHaveBeenCalledWith(
        expect.stringContaining('User U12345 is not a customer! Skipping ticket creation.'),
      );
      expect(result).toEqual({
        channel: mockChannel,
        user: mockUser,
        isCustomer: false,
        isGrouped: false,
      });
    });
  });

  describe('aiCheckIsValidTicket', () => {
    it('should return true when AI detects a valid ticket', async () => {
      // Arrange
      const mockInstallation = {
        id: 'installation-id',
        teamId: 'T12345',
      } as unknown as Installations;

      const mockPlatformTeam = {
        id: 'team-id',
        uid: 'team-uid',
      } as unknown as PlatformTeams;

      const text = 'This is a valid ticket request';
      
      // Mock settings and AI service
      (mockSettingsCore.getValue as Mock).mockImplementation((key) => {
        if (key === 'ai_model') return Promise.resolve('gpt-4');
        return Promise.resolve(null);
      });
      (mockAiService.setActiveProvider as Mock).mockReturnValue(mockAiService);
      (mockAiService.setActiveModel as Mock).mockReturnValue(mockAiService);
      (mockAiService.isValidTicket as Mock).mockResolvedValue(true);

      // Act
      const result = await service.aiCheckIsValidTicket(
        text,
        mockPlatformTeam,
        mockInstallation,
      );

      // Assert
      expect(mockSettingsCore.getValue).toHaveBeenCalledWith('ai_model', {
        platformTeam: mockPlatformTeam,
        workspace: mockInstallation,
      });
      expect(mockAiService.setActiveProvider).toHaveBeenCalledWith('openai');
      expect(mockAiService.setActiveModel).toHaveBeenCalledWith('o3-mini-2025-01-31');
      expect(mockAiService.isValidTicket).toHaveBeenCalledWith(text, mockPlatformTeam.id);
      expect(result).toBe(true);
    });

    it('should return false when AI detects an invalid ticket', async () => {
      // Arrange
      const mockInstallation = {
        id: 'installation-id',
        teamId: 'T12345',
      } as unknown as Installations;

      const mockPlatformTeam = {
        id: 'team-id',
        uid: 'team-uid',
      } as unknown as PlatformTeams;

      const text = 'This is not a valid ticket request';
      
      // Mock settings and AI service
      (mockSettingsCore.getValue as Mock).mockImplementation((key) => {
        if (key === 'ai_model') return Promise.resolve('gpt-4');
        return Promise.resolve(null);
      });
      (mockAiService.setActiveProvider as Mock).mockReturnValue(mockAiService);
      (mockAiService.setActiveModel as Mock).mockReturnValue(mockAiService);
      (mockAiService.isValidTicket as Mock).mockResolvedValue(false);

      // Act
      const result = await service.aiCheckIsValidTicket(
        text,
        mockPlatformTeam,
        mockInstallation,
      );

      // Assert
      expect(mockSettingsCore.getValue).toHaveBeenCalledWith('ai_model', {
        platformTeam: mockPlatformTeam,
        workspace: mockInstallation,
      });
      expect(mockAiService.setActiveProvider).toHaveBeenCalledWith('openai');
      expect(mockAiService.setActiveModel).toHaveBeenCalledWith('o3-mini-2025-01-31');
      expect(mockAiService.isValidTicket).toHaveBeenCalledWith(text, mockPlatformTeam.id);
      expect(result).toBe(false);
    });
  });
});
