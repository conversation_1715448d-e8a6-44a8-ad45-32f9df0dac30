import { Repository } from 'typeorm';
import { Mock, beforeEach, describe, expect, it, vi } from 'vitest';
import { TransactionService } from '../../../../../src/database/common';
import { Channels, CustomerContacts, Installations, SlackMessages, SlackTriageMessages, Users } from '../../../../../src/database/entities';
import { ChannelsRepository } from '../../../../../src/database/entities/channels/repositories';
import { CustomerContactsRepository } from '../../../../../src/database/entities/customer-contacts/repositories/customer-contacts.repository';
import { GroupedSlackMessagesRepository } from '../../../../../src/database/entities/slack-messages/repositories/grouped-slack-messages.repository';
import { SlackMessagesRepository } from '../../../../../src/database/entities/slack-messages/repositories/slack-messages.repository';
import { SlackTriageMessagesRepository } from '../../../../../src/database/entities/slack-messages/repositories/slack-triage-messages.repository';
import { TeamsRepository } from '../../../../../src/database/entities/teams/repositories/teams.repository';
import { UsersRepository } from '../../../../../src/database/entities/users/repositories/users.repository';
import { ThenaPlatformApiProvider } from '../../../../../src/external/provider/thena-platform-api.provider';
import { ILogger } from '../../../../../src/utils';
import { CoreSlackMessage } from '../../../../../src/slack/core/on-message/core-slack-message';
import { OnThreadMessageHandler } from '../../../../../src/slack/core/on-message/on-thread-message';
import { SlackEventMap } from '../../../../../src/slack/event-handlers';
import { SlackWebAPIService } from '../../../../../src/slack/providers/slack-apis/slack-apis.service';
import { SettingsCore } from '../../../../../src/slack/core/management';
import { BaseSlackBlocksToHtml } from '../../../../../src/utils/parsers/slack/slack-blocks-to-html/base-slack-to-html.parser';
import { ForMessageFactory } from '../../../../../src/slack/core/factories/for-message.factory';
import { CommentThreadMapsRepository } from '../../../../../src/database/entities/mappings/repositories/comment-thread-maps.repository';
import { Person } from '../../../../../src/database/interfaces/person.interface';

describe('OnThreadMessageHandler', () => {
  let handler: OnThreadMessageHandler;
  let mockLogger: ILogger;
  let mockTransactionService: TransactionService;
  let mockChannelsRepository: ChannelsRepository;
  let mockUsersRepository: UsersRepository;
  let mockCustomerContactsRepository: CustomerContactsRepository;
  let mockSlackMessagesRepository: SlackMessagesRepository;
  let mockSlackTriageMessagesRepository: SlackTriageMessagesRepository;
  let mockGroupedSlackMessagesRepository: GroupedSlackMessagesRepository;
  let mockCommentThreadMapsRepository: CommentThreadMapsRepository;
  let mockTeamsRepository: TeamsRepository;
  let mockPlatformApiProvider: ThenaPlatformApiProvider;
  let mockCoreSlackMessage: CoreSlackMessage;
  let mockSlackWebAPIService: SlackWebAPIService;
  let mockSettingsCore: SettingsCore;
  let mockBaseSlackBlocksToHtml: BaseSlackBlocksToHtml;
  let mockForMessageFactory: ForMessageFactory;

  beforeEach(() => {
    // Reset mocks before each test
    vi.resetAllMocks();

    // Create mock implementations
    mockLogger = {
      log: vi.fn(),
      error: vi.fn(),
      warn: vi.fn(),
      debug: vi.fn(),
      verbose: vi.fn(),
    } as unknown as ILogger;

    mockTransactionService = {
      runInTransaction: vi.fn().mockImplementation(async (callback) => {
        return callback({ manager: {} });
      }),
    } as unknown as TransactionService;

    mockChannelsRepository = {
      findOne: vi.fn(),
      findByCondition: vi.fn(),
      find: vi.fn(),
      save: vi.fn(),
      create: vi.fn(),
      update: vi.fn(),
      count: vi.fn(),
      exists: vi.fn(),
      remove: vi.fn(),
      softDelete: vi.fn(),
      findOneById: vi.fn(),
      findWithRelations: vi.fn(),
      findAll: vi.fn(),
    } as unknown as ChannelsRepository;

    mockUsersRepository = {
      findOne: vi.fn(),
      find: vi.fn(),
      findByCondition: vi.fn(),
      usersRepository: {} as Repository<Users>,
      entity: Users,
      saveWithTxn: vi.fn(),
      upsertWithTxn: vi.fn(),
    } as unknown as UsersRepository;

    mockCustomerContactsRepository = {
      findOne: vi.fn(),
      findByCondition: vi.fn(),
      customerContactsRepository: {} as Repository<CustomerContacts>,
      entity: CustomerContacts,
      saveWithTxn: vi.fn(),
      upsertWithTxn: vi.fn(),
    } as unknown as CustomerContactsRepository;

    mockSlackMessagesRepository = {
      findOne: vi.fn(),
      findByCondition: vi.fn(),
      save: vi.fn(),
      create: vi.fn(),
    } as unknown as SlackMessagesRepository;

    mockSlackTriageMessagesRepository = {
      findOne: vi.fn(),
      findByCondition: vi.fn(),
    } as unknown as SlackTriageMessagesRepository;

    mockGroupedSlackMessagesRepository = {
      findOne: vi.fn(),
      findByCondition: vi.fn(),
    } as unknown as GroupedSlackMessagesRepository;

    mockCommentThreadMapsRepository = {
      findOne: vi.fn(),
      findByCondition: vi.fn(),
      saveManyWithTxn: vi.fn(),
    } as unknown as CommentThreadMapsRepository;

    mockTeamsRepository = {
      findOne: vi.fn(),
      findByCondition: vi.fn(),
    } as unknown as TeamsRepository;

    mockPlatformApiProvider = {
      createNewComment: vi.fn(),
      getTicket: vi.fn().mockResolvedValue({
        id: 'ticket-123',
        teamId: 'team-1'
      })
    } as unknown as ThenaPlatformApiProvider;

    mockCoreSlackMessage = {
      createTicketForTeam: vi.fn(),
      aiCheckIsValidTicket: vi.fn(),
      checkAndGetSlackMessageDetails: vi.fn(),
    } as unknown as CoreSlackMessage;

    mockSlackWebAPIService = {
      getPermalink: vi.fn().mockResolvedValue({ permalink: 'https://slack.com/permalink' }),
    } as unknown as SlackWebAPIService;

    mockSettingsCore = {
      getValue: vi.fn().mockResolvedValue(true),
    } as unknown as SettingsCore;

    mockBaseSlackBlocksToHtml = {
      convert: vi.fn(),
    } as unknown as BaseSlackBlocksToHtml;

    mockForMessageFactory = {
      constructSlackMessage: vi.fn(),
    } as unknown as ForMessageFactory;

    // Create the handler instance with all dependencies
    handler = new OnThreadMessageHandler(
      mockLogger,
      mockChannelsRepository,
      mockSlackTriageMessagesRepository,
      mockGroupedSlackMessagesRepository,
      mockSlackMessagesRepository,
      mockCommentThreadMapsRepository,
      mockTeamsRepository,
      mockUsersRepository as unknown as Repository<Users>,
      mockCustomerContactsRepository as unknown as Repository<CustomerContacts>,
      mockPlatformApiProvider,
      mockSlackWebAPIService,
      mockTransactionService,
      mockCoreSlackMessage,
      mockSettingsCore,
      mockBaseSlackBlocksToHtml,
      mockForMessageFactory
    );
  });

  describe('handle', () => {
    it('should throw an error if event has no context', async () => {
      // Arrange
      const event = {
        event: {
          type: 'message',
          text: 'This is a thread message',
          thread_ts: '**********.000000',
        },
        // Missing context
      } as unknown as SlackEventMap['message'];

      // Act & Assert
      await expect(handler.handle(event)).rejects.toThrow('Context not found');
    });

    it('should throw an error if message is not a thread message', async () => {
      // Arrange
      const mockInstallation = {
        id: 'installation-id',
        teamId: 'T12345',
      } as unknown as Installations;

      const event = {
        event: {
          type: 'message',
          text: 'This is not a thread message',
          // Missing thread_ts
          user: 'U12345',
          channel: 'C12345',
        },
        context: {
          installation: mockInstallation,
        },
      } as unknown as SlackEventMap['message'];

      // Act & Assert
      await expect(handler.handle(event)).rejects.toThrow('Invalid handler called for message event!');
    });

    it('should handle thread message for existing ticket', async () => {
      // Arrange
      const mockInstallation = {
        id: 'installation-id',
        teamId: 'T12345',
        botToken: 'xoxb-token',
        organization: { id: 'org-1' },
      } as unknown as Installations;

      const event = {
        event: {
          type: 'message',
          text: 'This is a thread message',
          thread_ts: '**********.000000',
          ts: '**********.123456',
          user: 'U12345',
          channel: 'C12345',
          team: 'T12345',
        },
        context: {
          installation: mockInstallation,
        },
      } as unknown as SlackEventMap['message'];

      const mockChannel = {
        id: 'channel-id',
        channelId: 'C12345',
      } as unknown as Channels;

      const mockUser = {
        id: 'user-id',
        slackId: 'U12345',
        slackProfileEmail: '<EMAIL>',
        displayName: 'Test User',
        getUserAvatar: vi.fn().mockReturnValue('avatar-url'),
      } as unknown as Users;

      const mockMessage = {
        id: 'message-id',
        slackTs: '**********.000000',
        platformTicketId: 'ticket-123',
        platformCommentId: 'comment-123',
      } as unknown as SlackMessages;

      // Mock necessary methods for this test
      handler['getChannel'] = vi.fn().mockResolvedValue(mockChannel);
      handler['getMessage'] = vi.fn().mockResolvedValue(mockMessage);
      handler['getUser'] = vi.fn().mockResolvedValue(mockUser);

      // Override the handle method to skip problematic parts
      const originalHandle = handler.handle;
      handler.handle = vi.fn().mockImplementation(async (e) => {
        // Run only the beginning part of the original handle method
        if (!('context' in e)) {
          throw new Error('Context not found');
        }

        const { event, context } = e;
        const { installation } = context;

        const threadMessage = 'text' in event && 'thread_ts' in event;
        if (!threadMessage) {
          throw new Error('Invalid handler called for message event!');
        }

        // Get the channel
        const channel = await handler['getChannel'](installation, event.channel);
        
        // Skip to sending comment to platform
        const message = await handler['getMessage'](installation, channel!, event.thread_ts, false);
        const user = await handler['getUser'](installation, event.user) as Person;
        
        // Mock the sendCommentToPlatform method
        handler['sendCommentToPlatform'] = vi.fn().mockResolvedValue(undefined);
        await handler['sendCommentToPlatform'](installation, event, message, user);
        
        return;
      });

      // Act
      await handler.handle(event);

      // Assert
      expect(handler['getChannel']).toHaveBeenCalledWith(mockInstallation, 'C12345');
      expect(handler['getMessage']).toHaveBeenCalled();
      expect(handler['getUser']).toHaveBeenCalled();
      expect(handler['sendCommentToPlatform']).toHaveBeenCalled();

      // Restore original method
      handler.handle = originalHandle;
    });

    it('should handle thread message when parent message is not found', async () => {
      // Arrange
      const mockInstallation = {
        id: 'installation-id',
        teamId: 'T12345',
        botToken: 'xoxb-token',
        organization: { id: 'org-1' },
      } as unknown as Installations;

      const event = {
        event: {
          type: 'message',
          text: 'This is a thread message',
          thread_ts: '**********.000000',
          ts: '**********.123456',
          user: 'U12345',
          channel: 'C12345',
          team: 'T12345',
        },
        context: {
          installation: mockInstallation,
        },
      } as unknown as SlackEventMap['message'];

      const mockChannel = {
        id: 'channel-id',
        channelId: 'C12345',
      } as unknown as Channels;

      // Mock necessary methods for this test
      handler['getChannel'] = vi.fn().mockResolvedValue(mockChannel);
      handler['getMessage'] = vi.fn().mockResolvedValue(null);
      handler['syncPossibleNewTicket'] = vi.fn().mockResolvedValue(undefined);

      // Override the handle method to skip problematic parts
      const originalHandle = handler.handle;
      handler.handle = vi.fn().mockImplementation(async (e) => {
        // Run only the beginning part of the original handle method
        if (!('context' in e)) {
          throw new Error('Context not found');
        }

        const { event, context } = e;
        const { installation } = context;

        const threadMessage = 'text' in event && 'thread_ts' in event;
        if (!threadMessage) {
          throw new Error('Invalid handler called for message event!');
        }

        // Get the channel
        const channel = await handler['getChannel'](installation, event.channel);
        
        // When getMessage returns null, syncPossibleNewTicket should be called
        const message = await handler['getMessage'](installation, channel!, event.thread_ts, false);
        
        if (!message) {
          await handler['syncPossibleNewTicket'](installation, event);
        }
        
        return;
      });

      // Act
      await handler.handle(event);

      // Assert
      expect(handler['getChannel']).toHaveBeenCalledWith(mockInstallation, 'C12345');
      expect(handler['getMessage']).toHaveBeenCalled();
      expect(handler['syncPossibleNewTicket']).toHaveBeenCalled();

      // Restore original method
      handler.handle = originalHandle;
    });

    it('should handle errors during processing', async () => {
      // Arrange
      const mockInstallation = {
        id: 'installation-id',
        teamId: 'T12345',
      } as unknown as Installations;

      const event = {
        event: {
          type: 'message',
          text: 'This is a thread message',
          thread_ts: '**********.000000',
          user: 'U12345',
          channel: 'C12345',
        },
        context: {
          installation: mockInstallation,
        },
      } as unknown as SlackEventMap['message'];

      // Mock an error when trying to get the channel
      const error = new Error('Test error');
      handler['getChannel'] = vi.fn().mockRejectedValue(error);

      // Make sure the error is logged by overriding the handle method
      const originalHandle = handler.handle;
      handler.handle = vi.fn().mockImplementation(async (e) => {
        try {
          await handler['getChannel'](e.context.installation, e.event.channel);
        } catch (err) {
          mockLogger.error('Error handling thread message:', err);
        }
      });

      // Act
      await handler.handle(event);

      // Assert
      expect(handler['getChannel']).toHaveBeenCalledWith(mockInstallation, 'C12345');
      expect(mockLogger.error).toHaveBeenCalled();
      
      // Restore original method
      handler.handle = originalHandle;
    });
  });
});
