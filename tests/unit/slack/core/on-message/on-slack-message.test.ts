import { Repository } from 'typeorm';
import { Mock, beforeEach, describe, expect, it, vi } from 'vitest';
import { CommentThreadMappings, Installations, SlackMessages } from '../../../../../src/database/entities';
import { ThenaPlatformApiProvider } from '../../../../../src/external/provider/thena-platform-api.provider';
import { ILogger } from '../../../../../src/utils';
import { BaseSlackBlocksToHtml } from '../../../../../src/utils/parsers/slack/slack-blocks-to-html/base-slack-to-html.parser';
import { SlackEventMap } from '../../../../../src/slack/event-handlers';
import { CoreSlackMessage } from '../../../../../src/slack/core/on-message/core-slack-message';
import { OnMessageHandler } from '../../../../../src/slack/core/on-message/on-slack-message';
import { OnThreadMessageHandler } from '../../../../../src/slack/core/on-message/on-thread-message';
import { SettingsCore } from '../../../../../src/slack/core/management';
import { TeamRelationshipType } from '../../../../../src/database/entities/mappings/platform-teams-to-channel-mappings.entity';
import * as CommonUtils from '../../../../../src/utils/common';

describe('OnMessageHandler', () => {
  let handler: OnMessageHandler;
  let mockLogger: ILogger;
  let mockSlackMessagesRepository: Repository<SlackMessages>;
  let mockCommentThreadMappingsRepository: Repository<CommentThreadMappings>;
  let mockPlatformApiProvider: ThenaPlatformApiProvider;
  let mockCoreSlackMessage: CoreSlackMessage;
  let mockBaseSlackBlocksToHtml: BaseSlackBlocksToHtml;
  let mockOnThreadMessageHandler: OnThreadMessageHandler;
  let mockSettingsCore: SettingsCore;

  beforeEach(() => {
    // Reset mocks before each test
    vi.resetAllMocks();

    // Create mock implementations
    mockLogger = {
      log: vi.fn(),
      error: vi.fn(),
      warn: vi.fn(),
      debug: vi.fn(),
      verbose: vi.fn(),
    } as unknown as ILogger;

    mockSlackMessagesRepository = {
      findOne: vi.fn(),
      save: vi.fn(),
      create: vi.fn(),
      update: vi.fn(),
    } as unknown as Repository<SlackMessages>;

    mockCommentThreadMappingsRepository = {
      findOne: vi.fn(),
      save: vi.fn(),
      create: vi.fn(),
    } as unknown as Repository<CommentThreadMappings>;

    mockPlatformApiProvider = {
      createNewTicket: vi.fn(),
      createNewComment: vi.fn(),
    } as unknown as ThenaPlatformApiProvider;

    mockCoreSlackMessage = {
      checkAndGetSlackMessageDetails: vi.fn(),
      aiCheckIsValidTicket: vi.fn(),
      ticketFromSourceSlack: vi.fn(),
      createTicketForTeam: vi.fn(),
      getChannel: vi.fn(),
      getUser: vi.fn(),
      shouldGroupWithExistingConversation: vi.fn(),
    } as unknown as CoreSlackMessage;

    mockBaseSlackBlocksToHtml = {
      initialize: vi.fn(),
      convert: vi.fn(),
    } as unknown as BaseSlackBlocksToHtml;

    mockOnThreadMessageHandler = {
      handle: vi.fn(),
    } as unknown as OnThreadMessageHandler;

    mockSettingsCore = {
      getValue: vi.fn(),
    } as unknown as SettingsCore;

    // Mock CommonUtils.getSlackMentions function
    vi.spyOn(CommonUtils, 'getSlackMentions').mockImplementation(() => ({
      mentionedUsers: [],
      mentionedUserGroups: [],
      botMentioned: false,
    }));

    // Create the handler instance
    handler = new OnMessageHandler(
      mockLogger,
      mockSlackMessagesRepository,
      mockCommentThreadMappingsRepository,
      mockPlatformApiProvider,
      mockCoreSlackMessage,
      mockBaseSlackBlocksToHtml,
      mockOnThreadMessageHandler,
      mockSettingsCore,
    );
  });

  describe('onMessage', () => {
    it('should handle thread messages by delegating to onThreadMessageHandler', async () => {
      // Arrange
      const mockInstallation = {
        id: 'installation-id',
        teamId: 'T12345',
        botToken: 'xoxb-token',
      } as unknown as Installations;

      const event = {
        event: {
          type: 'message',
          user: 'U12345',
          channel: 'C12345',
          ts: '**********.123456',
          text: 'This is a thread message',
          thread_ts: '**********.000000', // This makes it a thread message
        },
        context: {
          installation: mockInstallation,
        },
      } as unknown as SlackEventMap['message'];

      // Act
      await handler.onMessage(event);

      // Assert
      expect(mockOnThreadMessageHandler.handle).toHaveBeenCalledWith(event);
      expect(mockCoreSlackMessage.checkAndGetSlackMessageDetails).not.toHaveBeenCalled();
    });

    it('should handle top-level messages by calling handleNewMessage', async () => {
      // Arrange
      const mockInstallation = {
        id: 'installation-id',
        teamId: 'T12345',
        botToken: 'xoxb-token',
        organization: { id: 'org-1' },
      } as unknown as Installations;

      const event = {
        event: {
          type: 'message',
          user: 'U12345',
          channel: 'C12345',
          team: 'T12345',
          ts: '**********.123456',
          text: 'This is a top-level message',
          // No thread_ts makes it a top-level message
        },
        context: {
          installation: mockInstallation,
        },
      } as unknown as SlackEventMap['message'];

      const channelWithTeam = {
        id: 'channel-id',
        channelId: 'C12345',
        platformTeamsToChannelMappings: [
          {
            relationshipType: TeamRelationshipType.PRIMARY,
            platformTeam: { id: 'team-1' }
          }
        ]
      };

      // Mock the checkAndGetSlackMessageDetails method
      (mockCoreSlackMessage.checkAndGetSlackMessageDetails as Mock).mockResolvedValue({
        channel: channelWithTeam,
        user: { id: 'user-id', slackId: 'U12345' },
        isCustomer: true,
        isGrouped: false,
      });

      // Mock getSlackMentions to return no bot mentions
      vi.spyOn(CommonUtils, 'getSlackMentions').mockReturnValue({
        mentionedUsers: [],
        mentionedUserGroups: [],
        botMentioned: false,
      });

      // Mock the getValue method for settings
      (mockSettingsCore.getValue as Mock).mockImplementation((key) => {
        if (key === 'thena_bot_tagging_enabled') return Promise.resolve(false);
        if (key === 'automatic_tickets') return Promise.resolve(true);
        if (key === 'ai_model') return Promise.resolve('gpt-4');
        return Promise.resolve(null);
      });

      // Mock the aiCheckIsValidTicket method
      (mockCoreSlackMessage.aiCheckIsValidTicket as Mock).mockResolvedValue(true);

      // Setup the ticketFromSourceSlack mock
      handler['ticketFromSourceSlack'] = vi.fn().mockResolvedValue({
        ticket: { id: 'ticket-id', title: 'Test Ticket' },
        comment: { id: 'comment-id' },
      });

      // Act
      await handler.onMessage(event);

      // Assert - just verify that the message details were fetched
      expect(mockOnThreadMessageHandler.handle).not.toHaveBeenCalled();
      expect(mockCoreSlackMessage.checkAndGetSlackMessageDetails).toHaveBeenCalledWith(
        mockInstallation,
        event.event,
      );
    });

    it('should skip processing for bot messages with ignoreSelf metadata', async () => {
      // Arrange
      const mockInstallation = {
        id: 'installation-id',
        teamId: 'T12345',
        botToken: 'xoxb-token',
      } as unknown as Installations;

      const event = {
        event: {
          type: 'message',
          user: 'U12345',
          channel: 'C12345',
          team: 'T12345',
          ts: '**********.123456',
          text: 'This is a bot message',
          bot_profile: {
            id: 'B12345',
            name: 'Test Bot',
          },
          metadata: {
            event_payload: {
              ignoreSelf: true,
            },
          },
        },
        context: {
          installation: mockInstallation,
        },
      } as unknown as SlackEventMap['message'];

      // Act
      await handler.onMessage(event);

      // Assert
      expect(mockLogger.debug).toHaveBeenCalledWith(
        expect.stringContaining('Bot is mentioned in the message! Skipping ticket creation.'),
      );
      expect(mockOnThreadMessageHandler.handle).not.toHaveBeenCalled();
      expect(mockCoreSlackMessage.checkAndGetSlackMessageDetails).not.toHaveBeenCalled();
    });

    it('should skip ticket creation for direct message channels', async () => {
      // Arrange
      const mockInstallation = {
        id: 'installation-id',
        teamId: 'T12345',
        botToken: 'xoxb-token',
      } as unknown as Installations;

      const event = {
        event: {
          type: 'message',
          user: 'U12345',
          channel: 'D12345', // Direct message channel starts with D
          team: 'T12345',
          ts: '**********.123456',
          text: 'This is a direct message',
        },
        context: {
          installation: mockInstallation,
        },
      } as unknown as SlackEventMap['message'];

      // Act
      await handler.onMessage(event);

      // Assert
      expect(mockLogger.debug).toHaveBeenCalledWith(
        expect.stringContaining('Message received in a direct message channel'),
      );
      expect(mockCoreSlackMessage.checkAndGetSlackMessageDetails).not.toHaveBeenCalled();
    });

    it('should create a ticket when bot is mentioned and setting is enabled', async () => {
      // Arrange
      const mockInstallation = {
        id: 'installation-id',
        teamId: 'T12345',
        botToken: 'xoxb-token',
        botId: 'B12345',
        organization: { id: 'org-1' },
      } as unknown as Installations;

      const event = {
        event: {
          type: 'message',
          user: 'U12345',
          channel: 'C12345',
          team: 'T12345',
          ts: '**********.123456',
          text: 'Hey <@B12345> I need help!', // Mention the bot
          // No thread_ts makes it a top-level message
        },
        context: {
          installation: mockInstallation,
        },
      } as unknown as SlackEventMap['message'];

      const channelWithTeam = {
        id: 'channel-id',
        channelId: 'C12345',
        platformTeamsToChannelMappings: [
          {
            relationshipType: TeamRelationshipType.PRIMARY,
            platformTeam: { id: 'team-1' }
          }
        ]
      };

      // Mock the checkAndGetSlackMessageDetails method
      (mockCoreSlackMessage.checkAndGetSlackMessageDetails as Mock).mockResolvedValue({
        channel: channelWithTeam,
        user: { id: 'user-id', slackId: 'U12345' },
        isCustomer: true,
        isGrouped: false,
      });

      // Mock getSlackMentions to return bot mentioned
      vi.spyOn(CommonUtils, 'getSlackMentions').mockReturnValue({
        mentionedUsers: ['B12345'],
        mentionedUserGroups: [],
        botMentioned: true,
      });

      // Mock the getValue method for bot mention setting
      (mockSettingsCore.getValue as Mock).mockImplementation((key) => {
        if (key === 'thena_bot_tagging_enabled') return Promise.resolve(true);
        return Promise.resolve(null);
      });

      // Setup the ticketFromSourceSlack mock correctly
      handler['ticketFromSourceSlack'] = vi.fn().mockResolvedValue({
        ticket: { id: 'ticket-id', title: 'Test Ticket' },
        comment: { id: 'comment-id' },
      });

      // Act
      await handler.onMessage(event);

      // Assert
      expect(mockSettingsCore.getValue).toHaveBeenCalledWith('thena_bot_tagging_enabled', {
        platformTeam: { id: 'team-1' },
        workspace: mockInstallation,
        recursivelyLookup: false,
      });
      expect(handler['ticketFromSourceSlack']).toHaveBeenCalledWith(
        mockInstallation,
        channelWithTeam,
        { id: 'user-id', slackId: 'U12345' },
        event.event,
      );
    });

    it('should handle errors during processing', async () => {
      // Arrange
      const mockInstallation = {
        id: 'installation-id',
        teamId: 'T12345',
        botToken: 'xoxb-token',
      } as unknown as Installations;

      const event = {
        event: {
          type: 'message',
          user: 'U12345',
          channel: 'C12345',
          team: 'T12345',
          ts: '**********.123456',
          text: 'This is a message that will cause an error',
        },
        context: {
          installation: mockInstallation,
        },
      } as unknown as SlackEventMap['message'];

      // Mock the checkAndGetSlackMessageDetails method to throw an error
      const error = new Error('Test error');
      (mockCoreSlackMessage.checkAndGetSlackMessageDetails as Mock).mockRejectedValue(error);

      // Act
      await handler.onMessage(event);

      // Assert
      expect(mockLogger.error).toHaveBeenCalledWith(
        expect.stringContaining('Error handling new message:'),
        error.stack,
      );
    });
  });
});
