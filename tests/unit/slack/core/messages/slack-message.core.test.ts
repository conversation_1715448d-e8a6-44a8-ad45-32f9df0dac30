import { Test, TestingModule } from '@nestjs/testing';
import { getRepositoryToken } from '@nestjs/typeorm';
import { beforeEach, describe, expect, it, vi } from 'vitest';
import { InstallationWithOrganization } from '../../../../../src/common/interfaces/common.interface';
import { SlackMessages } from '../../../../../src/database/entities';
import { SlackMessageCore } from '../../../../../src/slack/core/messages/slack-message.core';
import { CUSTOM_LOGGER_TOKEN } from '../../../../../src/utils';

describe('SlackMessageCore', () => {
  let service: SlackMessageCore;
  let mockLogger: any;
  let mockSlackMessageRepository: any;

  beforeEach(async () => {
    mockLogger = {
      log: vi.fn(),
      debug: vi.fn(),
      error: vi.fn(),
      warn: vi.fn(),
    };

    mockSlackMessageRepository = {
      findOne: vi.fn(),
      save: vi.fn(),
    };

    const module: TestingModule = await Test.createTestingModule({
      providers: [
        SlackMessageCore,
        {
          provide: CUSTOM_LOGGER_TOKEN,
          useValue: mockLogger,
        },
        {
          provide: getRepositoryToken(SlackMessages),
          useValue: mockSlackMessageRepository,
        },
      ],
    }).compile();

    service = module.get<SlackMessageCore>(SlackMessageCore);
    
    (service as any).logger = mockLogger;
    (service as any).slackMessageRepository = mockSlackMessageRepository;
  });

  describe('getSlackMessageByPlatformTicketId', () => {
    it('should return slack message when found', async () => {
      const mockInstallation: InstallationWithOrganization = {
        id: 'installation-id',
        organization: { id: 'org-id' },
      } as InstallationWithOrganization;
      
      const ticketId = 'ticket-123';
      const mockMessage = { id: 'message-id', platformTicketId: ticketId };
      
      mockSlackMessageRepository.findOne.mockResolvedValue(mockMessage);
      
      const result = await service.getSlackMessageByPlatformTicketId(
        mockInstallation,
        ticketId,
        { createIndependentIfNotFound: false }
      );
      
      expect(result).toEqual(mockMessage);
      expect(mockSlackMessageRepository.findOne).toHaveBeenCalledWith({
        where: {
          platformTicketId: ticketId,
          installation: { id: mockInstallation.id },
          organization: { id: mockInstallation.organization.id },
        },
      });
      expect(mockSlackMessageRepository.save).not.toHaveBeenCalled();
    });

    it('should create independent message when not found and createIndependentIfNotFound is true', async () => {
      const mockInstallation: InstallationWithOrganization = {
        id: 'installation-id',
        organization: { id: 'org-id' },
      } as InstallationWithOrganization;
      
      const ticketId = 'ticket-123';
      const mockMessage = { 
        id: 'message-id', 
        platformTicketId: ticketId,
        isIndependent: true,
      };
      
      mockSlackMessageRepository.findOne.mockResolvedValue(null);
      mockSlackMessageRepository.save.mockResolvedValue(mockMessage);
      
      const result = await service.getSlackMessageByPlatformTicketId(
        mockInstallation,
        ticketId,
        { createIndependentIfNotFound: true }
      );
      
      expect(result).toEqual(mockMessage);
      expect(mockSlackMessageRepository.findOne).toHaveBeenCalledWith({
        where: {
          platformTicketId: ticketId,
          installation: { id: mockInstallation.id },
          organization: { id: mockInstallation.organization.id },
        },
      });
      expect(mockSlackMessageRepository.save).toHaveBeenCalledWith({
        platformTicketId: ticketId,
        isIndependent: true,
        installation: { id: mockInstallation.id },
        organization: { id: mockInstallation.organization.id },
      });
    });

    it('should not create independent message when not found and createIndependentIfNotFound is false', async () => {
      const mockInstallation: InstallationWithOrganization = {
        id: 'installation-id',
        organization: { id: 'org-id' },
      } as InstallationWithOrganization;
      
      const ticketId = 'ticket-123';
      
      mockSlackMessageRepository.findOne.mockResolvedValue(null);
      
      const result = await service.getSlackMessageByPlatformTicketId(
        mockInstallation,
        ticketId,
        { createIndependentIfNotFound: false }
      );
      
      expect(result).toBeNull();
      expect(mockSlackMessageRepository.findOne).toHaveBeenCalledWith({
        where: {
          platformTicketId: ticketId,
          installation: { id: mockInstallation.id },
          organization: { id: mockInstallation.organization.id },
        },
      });
      expect(mockSlackMessageRepository.save).not.toHaveBeenCalled();
    });

    it('should use default value for createIndependentIfNotFound when not provided', async () => {
      const mockInstallation: InstallationWithOrganization = {
        id: 'installation-id',
        organization: { id: 'org-id' },
      } as InstallationWithOrganization;
      
      const ticketId = 'ticket-123';
      
      mockSlackMessageRepository.findOne.mockResolvedValue(null);
      
      const result = await service.getSlackMessageByPlatformTicketId(
        mockInstallation,
        ticketId,
        {}
      );
      
      expect(result).toBeNull();
      expect(mockSlackMessageRepository.findOne).toHaveBeenCalledWith({
        where: {
          platformTicketId: ticketId,
          installation: { id: mockInstallation.id },
          organization: { id: mockInstallation.organization.id },
        },
      });
      expect(mockSlackMessageRepository.save).not.toHaveBeenCalled();
    });
  });
});
