import { Repository } from 'typeorm';
import { Mock, beforeEach, describe, expect, it, vi } from 'vitest';
import { InstallationWithOrganization } from '../../../../../src/common/interfaces/common.interface';
import { ConfigService } from '../../../../../src/config/config.service';
import { TransactionService } from '../../../../../src/database/common';
import { Installations, Users } from '../../../../../src/database/entities';
import {
  ChannelType,
  Channels,
} from '../../../../../src/database/entities/channels/channels.entity';
import { ChannelsRepository } from '../../../../../src/database/entities/channels/repositories';
import { TriageMapsRepository } from '../../../../../src/database/entities/mappings/repositories/triage-maps.repository';
import { SlackTriageMessagesRepository } from '../../../../../src/database/entities/slack-messages/repositories/slack-triage-messages.repository';
import { SlackMessages } from '../../../../../src/database/entities/slack-messages/slack-messages.entity';
import { ThenaPlatformApiProvider } from '../../../../../src/external/provider/thena-platform-api.provider';
import { TriageMessageBlock } from '../../../../../src/slack/blocks/components';
import { CoreTriageService } from '../../../../../src/slack/core/messages/triage.core';
import { SlackLinkSharedHandler } from '../../../../../src/slack/event-handlers/handlers/links/link-shared.handler';
import { SlackWebAPIService } from '../../../../../src/slack/providers/slack-apis/slack-apis.service';
import { ILogger } from '../../../../../src/utils/logger/logger.interface';
import { parseWithMentions } from '../../../../../src/utils/parsers/slack/mentions.parser';

describe('CoreTriageService', () => {
  let service: CoreTriageService;
  let mockLogger: ILogger;
  let mockThenaPlatformApi: ThenaPlatformApiProvider;
  let mockSlackWebAPIService: SlackWebAPIService;
  let mockConfigService: ConfigService;
  let mockTransactionService: TransactionService;
  let mockLinkSharedHandler: SlackLinkSharedHandler;
  let mockSlackUsersRepository: Repository<Users>;
  let mockTriageMapsRepository: TriageMapsRepository;
  let mockSlackTriageMessagesRepository: SlackTriageMessagesRepository;
  let mockChannelsRepository: ChannelsRepository;
  let mockTriageMessageBlock: TriageMessageBlock;

  beforeEach(() => {
    vi.resetAllMocks();

    mockLogger = {
      log: vi.fn(),
      error: vi.fn(),
      warn: vi.fn(),
      debug: vi.fn(),
      verbose: vi.fn(),
    } as unknown as ILogger;

    mockThenaPlatformApi = {
      createNewComment: vi.fn(),
      getTicket: vi.fn(),
    } as unknown as ThenaPlatformApiProvider;

    mockSlackWebAPIService = {
      sendMessage: vi
        .fn()
        .mockResolvedValue({ ok: true, ts: '**********.123456' }),
      getTeamInfo: vi.fn(),
      unfurlLink: vi.fn(),
      joinConversation: vi.fn(),
    } as unknown as SlackWebAPIService;

    mockConfigService = {
      get: vi.fn(),
    } as unknown as ConfigService;

    mockTransactionService = {
      runInTransaction: vi.fn(async (callback) => callback({})),
    } as unknown as TransactionService;

    mockLinkSharedHandler = {
      generateUnfurlContent: vi.fn(),
    } as unknown as SlackLinkSharedHandler;

    mockSlackUsersRepository = {
      findOne: vi.fn(),
    } as unknown as Repository<Users>;

    mockTriageMapsRepository = {
      findAll: vi.fn(),
      saveWithTxn: vi.fn(),
    } as unknown as TriageMapsRepository;

    mockSlackTriageMessagesRepository = {
      saveWithTxn: vi.fn(),
      saveManyWithTxn: vi.fn(),
      findByCondition: vi.fn().mockResolvedValue([]),
    } as unknown as SlackTriageMessagesRepository;

    mockChannelsRepository = {
      findByCondition: vi.fn().mockResolvedValue([]),
      findAll: vi.fn(),
      updateWithTxn: vi.fn(),
    } as unknown as ChannelsRepository;

    mockTriageMessageBlock = {
      build: vi.fn().mockReturnValue({ blocks: [] }),
    } as unknown as TriageMessageBlock;

    service = new CoreTriageService(
      mockLogger,
      mockThenaPlatformApi,
      mockSlackWebAPIService,
      mockConfigService,
      mockTransactionService,
      mockLinkSharedHandler,
      mockSlackUsersRepository,
      mockTriageMapsRepository,
      mockSlackTriageMessagesRepository,
      mockChannelsRepository,
      mockTriageMessageBlock,
    );
  });

  describe('sendTriageMessageToChannel', () => {
    it('should send triage messages to matching channels', async () => {
      const ticket = {
        id: 'ticket-123',
        title: 'Test Ticket',
        description: 'Test Description',
        priority: 'High',
        priorityId: 'high',
        status: 'Open',
        statusId: 'open',
        teamId: 'team-123',
        ticketId: 'T-123',
        createdAt: new Date().toISOString(),
        customerContactEmail: '<EMAIL>',
        customerContactFirstName: 'Test',
        customerContactLastName: 'Customer',
      };

      // Mock the ticketDetails that would be returned from platform API
      const ticketDetails = {
        requestorEmail: '<EMAIL>',
        customerContactEmail: '<EMAIL>',
        customerContactFirstName: 'Test',
        customerContactLastName: 'Customer',
      };

      // Set up the platform API to return the ticket details
      (mockThenaPlatformApi.getTicket as Mock).mockResolvedValue(ticketDetails);

      const installation = {
        id: 'installation-id',
        name: 'Test Workspace',
        teamId: 'T12345',
        teamName: 'Test Team',
        botToken: 'mock-bot-token',
        botSlackId: 'B12345',
        botSlackUserId: 'U12345',
        installingUserSlackId: 'U67890',
        installingUserName: 'Test User',
        organization: { id: 'org-id' },
      } as unknown as Installations;

      const mockChannel = {
        id: 'channel-1',
        channelId: 'C12345',
        channelName: 'triage-channel-1',
        channelType: ChannelType.TRIAGE_CHANNEL,
        name: 'triage-channel-1',
        channelDump: {},
        slackCreatedAt: new Date(),
        isBotActive: true,
        isArchived: false,
        isPrivate: false,
        isShared: false,
        memberCount: 10,
        purpose: 'Test purpose',
        topic: 'Test topic',
        installation: { id: installation.id },
        organization: { id: 'org-id' },
      } as unknown as Channels;

      const mockSlackMessage = {
        id: 'message-id',
        platformTicketId: ticket.id,
        platformCommentId: null,
        slackMessageTs: '**********.123456',
        slackMessageThreadTs: null,
        slackChannelId: 'C12345',
        slackUserId: 'U12345',
        slackTeamId: 'T12345',
        slackPermalink: 'https://slack.com/archives/C12345/p**********123456',
        messageText: 'Test message',
        isDeleted: false,
        isThreadParent: false,
        isThreadMessage: false,
        installation: { id: installation.id },
        organization: { id: 'org-id' },
      } as unknown as SlackMessages;

      await service.sendTriageMessageToChannel(installation, {
        channel: mockChannel,
        ticket,
        slackMessage: mockSlackMessage,
      });

      expect(mockTriageMessageBlock.build).toHaveBeenCalled();
      expect(mockSlackWebAPIService.sendMessage).toHaveBeenCalled();
      expect(mockSlackTriageMessagesRepository.saveWithTxn).toHaveBeenCalled();
    });
  });

  describe('createTriageMapping', () => {
    it('should create a triage mapping successfully', async () => {
      const installation = {
        id: 'installation-id',
        teamId: 'T12345',
        organization: { id: 'org-id' },
      } as unknown as InstallationWithOrganization;

      const triageChannel = {
        id: 'channel-id',
        channelId: 'C12345',
        channelName: 'triage-channel',
        installation: { id: installation.id },
        organization: { id: 'org-id' },
      } as unknown as Channels;

      const customerChannel = {
        id: 'customer-channel-id',
        channelId: 'C67890',
        channelName: 'customer-channel',
        installation: { id: installation.id },
        organization: { id: 'org-id' },
      } as unknown as Channels;

      const triageMapping = {
        id: 'mapping-id',
        triageChannel: { id: triageChannel.id },
        activeChannel: { id: customerChannel.id },
        installation: { id: installation.id },
        organization: { id: 'org-id' },
      };

      (mockChannelsRepository.findByCondition as Mock)
        .mockResolvedValueOnce(triageChannel)
        .mockResolvedValueOnce(customerChannel);
      (mockTriageMapsRepository.saveWithTxn as Mock).mockResolvedValue(
        triageMapping,
      );

      const result = await service.createTriageMapping(installation, {
        channelId: 'C12345',
        triageForChannelId: 'C67890',
      });

      expect(mockChannelsRepository.findByCondition).toHaveBeenCalledTimes(2);
      expect(mockChannelsRepository.updateWithTxn).toHaveBeenCalled();
      expect(mockTriageMapsRepository.saveWithTxn).toHaveBeenCalled();
      expect(result).toBe(triageMapping);
    });

    it('should throw an error if channel is not found', async () => {
      const installation = {
        id: 'installation-id',
        teamId: 'T12345',
        organization: { id: 'org-id' },
      } as unknown as InstallationWithOrganization;

      (mockChannelsRepository.findByCondition as Mock).mockResolvedValue(null);

      await expect(
        service.createTriageMapping(installation, {
          channelId: 'C12345',
        }),
      ).rejects.toThrow('Triage channel not found');

      expect(mockChannelsRepository.findByCondition).toHaveBeenCalledTimes(1);
      expect(mockChannelsRepository.updateWithTxn).not.toHaveBeenCalled();
      expect(mockTriageMapsRepository.saveWithTxn).not.toHaveBeenCalled();
    });
  });
});

describe('parseWithMentions', () => {
  let userRepository: Repository<Users>;
  let installation: Installations;

  beforeEach(() => {
    userRepository = {
      findOne: vi.fn(),
    } as unknown as Repository<Users>;

    installation = {
      id: 'test-installation-id',
    } as unknown as Installations;
  });

  it('should return empty string for empty input', async () => {
    const result = await parseWithMentions('', userRepository, installation);
    expect(result).toBe('');
  });

  it('should return the original text if no mentions are found', async () => {
    const text = 'This is a test message with no mentions';
    const result = await parseWithMentions(text, userRepository, installation);
    expect(result).toBe(text);
  });

  it('should replace mentions with <@userId> when user is found', async () => {
    const text = 'Hello <@U123|<EMAIL>>, how are you?';

    const user = {
      slackId: 'U456',
    } as unknown as Users;

    (userRepository.findOne as Mock).mockResolvedValueOnce(user);

    const result = await parseWithMentions(text, userRepository, installation);
    expect(result).toBe('Hello <@U456>, how are you?');
    expect(userRepository.findOne).toHaveBeenCalledWith({
      where: {
        installation: { id: installation.id },
        slackProfileEmail: '<EMAIL>',
      },
    });
  });

  it('should replace mentions with @label when user is not found', async () => {
    const text = 'Hello <@U123|<EMAIL>>, how are you?';

    (userRepository.findOne as Mock).mockResolvedValueOnce(null);

    const result = await parseWithMentions(text, userRepository, installation);
    expect(result).toBe('Hello @<EMAIL>, how are you?');
  });

  it('should handle multiple mentions correctly', async () => {
    const text =
      'Hello <@U123|<EMAIL>> and <@U456|<EMAIL>>';

    const user1 = {
      slackId: 'U789',
    } as unknown as Users;

    (userRepository.findOne as Mock)
      .mockResolvedValueOnce(user1)
      .mockResolvedValueOnce(null);

    const result = await parseWithMentions(text, userRepository, installation);
    expect(result).toBe('Hello <@U789> and @<EMAIL>');
  });
});
