import { Test, TestingModule } from '@nestjs/testing';
import { beforeEach, describe, expect, it, vi } from 'vitest';
import { Channels, Installations } from '../../../../../src/database/entities';
import { ChannelType } from '../../../../../src/database/entities/channels/channels.entity';
import { SlackWebAPIService } from '../../../../../src/slack/providers/slack-apis/slack-apis.service';
import { ChannelsManagementService } from '../../../../../src/slack/core/management/channels.management.ts';
import { CUSTOM_LOGGER_TOKEN } from '../../../../../src/utils';

describe('ChannelsManagementService', () => {
  let service: ChannelsManagementService;
  let mockLogger: any;
  let mockSlackWebAPIService: any;

  beforeEach(async () => {
    mockLogger = {
      log: vi.fn(),
      warn: vi.fn(),
      error: vi.fn(),
    };

    mockSlackWebAPIService = {
      joinConversation: vi.fn(),
    };

    const module: TestingModule = await Test.createTestingModule({
      providers: [
        ChannelsManagementService,
        {
          provide: CUSTOM_LOGGER_TOKEN,
          useValue: mockLogger,
        },
        {
          provide: SlackWebAPIService,
          useValue: mockSlackWebAPIService,
        },
      ],
    }).compile();

    service = module.get<ChannelsManagementService>(ChannelsManagementService);
    
    (service as any).logger = mockLogger;
    (service as any).slackWebAPIService = mockSlackWebAPIService;
  });

  describe('joinChannels', () => {
    it('should log a warning and return if no channels are provided', async () => {
      const mockInstallation = { botToken: 'xoxb-test-token' } as Installations;
      const channels: Channels[] = [];

      await service.joinChannels(mockInstallation, channels);

      expect(mockLogger.warn).toHaveBeenCalledWith('No channels to join');
      expect(mockSlackWebAPIService.joinConversation).not.toHaveBeenCalled();
    });

    it('should join each channel in the provided list', async () => {
      const mockInstallation = { botToken: 'xoxb-test-token' } as Installations;
      const channels: Channels[] = [
        { channelId: 'C12345', name: 'channel-1' } as Channels,
        { channelId: 'C67890', name: 'channel-2' } as Channels,
      ];

      mockSlackWebAPIService.joinConversation.mockResolvedValue({ ok: true });

      await service.joinChannels(mockInstallation, channels);

      expect(mockLogger.log).toHaveBeenCalledWith(`Joining ${channels.length} channels`);
      expect(mockSlackWebAPIService.joinConversation).toHaveBeenCalledTimes(2);
      expect(mockSlackWebAPIService.joinConversation).toHaveBeenCalledWith(
        mockInstallation.botToken,
        { channel: 'C12345' }
      );
      expect(mockSlackWebAPIService.joinConversation).toHaveBeenCalledWith(
        mockInstallation.botToken,
        { channel: 'C67890' }
      );
    });

    it('should handle errors when joining channels', async () => {
      const mockInstallation = { botToken: 'xoxb-test-token' } as Installations;
      const channels: Channels[] = [
        { channelId: 'C12345', name: 'channel-1' } as Channels,
      ];

      const error = new Error('Failed to join channel');
      mockSlackWebAPIService.joinConversation.mockRejectedValue(error);

      await service.joinChannels(mockInstallation, channels);

      expect(mockLogger.error).toHaveBeenCalledWith(
        `Error joining channels: ${error.message}`,
        error.stack
      );
    });

    it('should handle non-Error errors when joining channels', async () => {
      const mockInstallation = { botToken: 'xoxb-test-token' } as Installations;
      const channels: Channels[] = [
        { channelId: 'C12345', name: 'channel-1' } as Channels,
      ];

      mockSlackWebAPIService.joinConversation.mockRejectedValue('String error');

      const consoleErrorSpy = vi.spyOn(console, 'error').mockImplementation(() => {});

      await service.joinChannels(mockInstallation, channels);

      expect(consoleErrorSpy).toHaveBeenCalledWith('Error joining channels:', 'String error');

      consoleErrorSpy.mockRestore();
    });

    it('should accept and use channelType option', async () => {
      const mockInstallation = { botToken: 'xoxb-test-token' } as Installations;
      const channels: Channels[] = [
        { channelId: 'C12345', name: 'channel-1', channelType: ChannelType.INTERNAL_HELPDESK } as Channels,
      ];

      mockSlackWebAPIService.joinConversation.mockResolvedValue({ ok: true });

      await service.joinChannels(mockInstallation, channels, { channelType: ChannelType.INTERNAL_HELPDESK });

      expect(mockSlackWebAPIService.joinConversation).toHaveBeenCalledWith(
        mockInstallation.botToken,
        { channel: 'C12345' }
      );
    });
  });
});
