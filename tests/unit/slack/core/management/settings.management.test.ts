import { describe, it, expect, beforeEach, vi, Mock } from 'vitest';
import { Settings<PERSON>ore, SettingsKey } from '../../../../../src/slack/core/management/settings.management';
import { SettingsRepository } from '../../../../../src/database/entities/settings/repositories/settings.repository';
import { Installations, Organizations, PlatformTeams } from '../../../../../src/database/entities';
import { SlackAppSettings } from '../../../../../src/database/entities/settings/interfaces/slack-app-settings.interface';

describe('SettingsCore', () => {
  let settingsCore: SettingsCore;
  let mockSettingsRepository: SettingsRepository;
  let mockQueryBuilder: any;

  const mockInstallation = {
    id: 'installation-123',
    organization: { id: 'org-123' }
  } as unknown as Installations;

  const mockPlatformTeam = {
    id: 'team-123',
    name: 'Test Team'
  } as unknown as PlatformTeams;

  const mockOrganization = {
    id: 'org-123',
    name: 'Test Organization'
  } as unknown as Organizations;

  beforeEach(() => {
    mockQueryBuilder = {
      select: vi.fn().mockReturnThis(),
      where: vi.fn().mockReturnThis(),
      setParameters: vi.fn().mockReturnThis(),
      getRawOne: vi.fn(),
    };

    mockSettingsRepository = {
      createQueryBuilder: vi.fn().mockReturnValue(mockQueryBuilder),
      findByCondition: vi.fn(),
      update: vi.fn(),
      save: vi.fn(),
    } as unknown as SettingsRepository;
    
    mockSettingsRepository.findByCondition = vi.fn();
    mockSettingsRepository.update = vi.fn();
    mockSettingsRepository.save = vi.fn();

    settingsCore = new SettingsCore(mockSettingsRepository);
  });

  describe('getValue', () => {
    it('should get a boolean setting value with non-recursive lookup', async () => {
      const key: SettingsKey = 'automatic_tickets';
      const options = {
        workspace: mockInstallation,
        platformTeam: mockPlatformTeam,
        recursivelyLookup: false,
      };

      mockQueryBuilder.getRawOne.mockResolvedValue({ value: 'true' });

      const result = await settingsCore.getValue(key, options);

      expect(mockSettingsRepository.createQueryBuilder).toHaveBeenCalledWith('settings');
      expect(mockQueryBuilder.select).toHaveBeenCalledWith(`settings.settings->>'${key}' as value`);
      expect(mockQueryBuilder.where).toHaveBeenCalledWith(
        'settings.installation_id = :installationId AND settings.platform_team_id = :teamId',
        {
          installationId: mockInstallation.id,
          teamId: mockPlatformTeam.id,
        }
      );
      expect(result).toBe(true); // String 'true' should be coerced to boolean true
    });

    it('should get a number setting value with non-recursive lookup', async () => {
      const key: SettingsKey = 'conversation_window';
      const options = {
        workspace: mockInstallation,
        platformTeam: mockPlatformTeam,
        recursivelyLookup: false,
      };

      mockQueryBuilder.getRawOne.mockResolvedValue({ value: '30' });

      const result = await settingsCore.getValue(key, options);

      expect(result).toBe(30); // String '30' should be coerced to number 30
    });

    it('should get a string setting value with non-recursive lookup', async () => {
      const key: SettingsKey = 'ai_model';
      const options = {
        workspace: mockInstallation,
        platformTeam: mockPlatformTeam,
        recursivelyLookup: false,
      };

      mockQueryBuilder.getRawOne.mockResolvedValue({ value: 'gpt-4' });

      const result = await settingsCore.getValue(key, options);

      expect(result).toBe('gpt-4');
    });

    it('should use default value when setting is not found in database', async () => {
      const key: SettingsKey = 'automatic_tickets';
      const options = {
        workspace: mockInstallation,
        platformTeam: mockPlatformTeam,
        recursivelyLookup: false,
      };

      mockQueryBuilder.getRawOne.mockResolvedValue({ value: null });
      (mockSettingsRepository.save as Mock).mockResolvedValue({});

      const result = await settingsCore.getValue(key, options);

      expect(result).toBe(false); // Default value for automatic_tickets is false
      expect(mockSettingsRepository.save).toHaveBeenCalledWith(
        expect.objectContaining({
          installation: { id: mockInstallation.id },
          platformTeam: { id: mockPlatformTeam.id },
          settings: {
            automatic_tickets: false,
          },
        })
      );
    });

    it('should get a setting value with recursive lookup', async () => {
      const key: SettingsKey = 'automatic_tickets';
      const options = {
        workspace: mockInstallation,
        platformTeam: mockPlatformTeam,
        organization: mockOrganization,
        recursivelyLookup: true,
      };

      mockQueryBuilder.getRawOne.mockResolvedValue({ value: 'true' });

      const result = await settingsCore.getValue(key, options);

      expect(mockSettingsRepository.createQueryBuilder).toHaveBeenCalledWith('settings');
      expect(mockQueryBuilder.select).toHaveBeenCalledWith(expect.stringContaining('COALESCE'));
      expect(mockQueryBuilder.setParameters).toHaveBeenCalledWith({
        orgId: mockOrganization.id,
        teamId: mockPlatformTeam.id,
        workspaceId: mockInstallation.id,
      });
      expect(result).toBe(true);
    });

    it('should use default value when setting is not found with recursive lookup', async () => {
      const key: SettingsKey = 'conversation_window';
      const options = {
        workspace: mockInstallation,
        platformTeam: mockPlatformTeam,
        organization: mockOrganization,
        recursivelyLookup: true,
      };

      mockQueryBuilder.getRawOne.mockResolvedValue({ value: null });
      (mockSettingsRepository.save as Mock).mockResolvedValue({});

      const result = await settingsCore.getValue(key, options);

      expect(result).toBe(24); // Default value for conversation_window is 24
      expect(mockSettingsRepository.save).toHaveBeenCalledWith(
        expect.objectContaining({
          installation: { id: mockInstallation.id },
          platformTeam: { id: mockPlatformTeam.id },
          organization: { id: mockOrganization.id },
          settings: {
            conversation_window: 24,
          },
        })
      );
    });
  });

  describe('getMultipleValues', () => {
    it('should get multiple setting values with non-recursive lookup', async () => {
      const keys: SettingsKey[] = ['automatic_tickets', 'conversation_window', 'ai_model'];
      const options = {
        workspace: mockInstallation,
        platformTeam: mockPlatformTeam,
        recursivelyLookup: false,
      };

      mockQueryBuilder.getRawOne.mockResolvedValue({
        automatic_tickets: 'true',
        conversation_window: '30',
        ai_model: 'gpt-4',
      });

      const result = await settingsCore.getMultipleValues(keys, options);

      expect(mockSettingsRepository.createQueryBuilder).toHaveBeenCalledWith('settings');
      expect(mockQueryBuilder.select).toHaveBeenCalledWith(
        keys.map((k) => `settings.settings->>'${k}' as "${k}"`).join(', ')
      );
      expect(mockQueryBuilder.where).toHaveBeenCalledWith(
        'settings.installation_id = :installationId AND settings.platform_team_id = :teamId',
        {
          installationId: mockInstallation.id,
          teamId: mockPlatformTeam.id,
        }
      );
      expect(result).toEqual({
        automatic_tickets: true,
        conversation_window: 30,
        ai_model: 'gpt-4',
      });
    });

    it('should use default values for missing settings in multiple values query', async () => {
      const keys: SettingsKey[] = ['automatic_tickets', 'conversation_window', 'ai_model'];
      const options = {
        workspace: mockInstallation,
        platformTeam: mockPlatformTeam,
        recursivelyLookup: false,
      };

      mockQueryBuilder.getRawOne.mockResolvedValue({
        automatic_tickets: null,
        conversation_window: '30',
        ai_model: null,
      });

      const result = await settingsCore.getMultipleValues(keys, options);

      expect(result).toEqual({
        automatic_tickets: false, // Default value
        conversation_window: 30,
        ai_model: 'o3-mini-2025-01-31', // Default value
      });
    });

    it('should return empty object when no keys are provided', async () => {
      const keys: SettingsKey[] = [];
      const options = {
        workspace: mockInstallation,
        platformTeam: mockPlatformTeam,
        recursivelyLookup: false,
      };

      const result = await settingsCore.getMultipleValues(keys, options);

      expect(result).toEqual({});
      expect(mockSettingsRepository.createQueryBuilder).not.toHaveBeenCalled();
    });

    it('should get multiple setting values with recursive lookup', async () => {
      const keys: SettingsKey[] = ['automatic_tickets', 'conversation_window'];
      const options = {
        workspace: mockInstallation,
        platformTeam: mockPlatformTeam,
        organization: mockOrganization,
        recursivelyLookup: true,
      };

      mockQueryBuilder.getRawOne.mockResolvedValue({
        automatic_tickets: 'true',
        conversation_window: '30',
      });

      const result = await settingsCore.getMultipleValues(keys, options);

      expect(mockSettingsRepository.createQueryBuilder).toHaveBeenCalledWith('settings');
      expect(mockQueryBuilder.select).toHaveBeenCalledWith(expect.stringContaining('COALESCE'));
      expect(mockQueryBuilder.setParameters).toHaveBeenCalledWith({
        orgId: mockOrganization.id,
        teamId: mockPlatformTeam.id,
        workspaceId: mockInstallation.id,
      });
      expect(result).toEqual({
        automatic_tickets: true,
        conversation_window: 30,
      });
    });
  });

  describe('private methods', () => {
    describe('coerceValue', () => {
      it('should coerce string "true" to boolean true', async () => {
        mockQueryBuilder.getRawOne.mockResolvedValue({ value: 'true' });
        
        const result = await settingsCore.getValue('automatic_tickets', {
          workspace: mockInstallation,
          platformTeam: mockPlatformTeam,
        });
        
        expect(result).toBe(true);
      });

      it('should coerce string "1" to boolean true', async () => {
        mockQueryBuilder.getRawOne.mockResolvedValue({ value: '1' });
        
        const result = await settingsCore.getValue('automatic_tickets', {
          workspace: mockInstallation,
          platformTeam: mockPlatformTeam,
        });
        
        expect(result).toBe(true);
      });

      it('should coerce string "false" to boolean false', async () => {
        mockQueryBuilder.getRawOne.mockResolvedValue({ value: 'false' });
        
        const result = await settingsCore.getValue('automatic_tickets', {
          workspace: mockInstallation,
          platformTeam: mockPlatformTeam,
        });
        
        expect(result).toBe(false);
      });

      it('should coerce string to number for number settings', async () => {
        mockQueryBuilder.getRawOne.mockResolvedValue({ value: '42' });
        
        const result = await settingsCore.getValue('conversation_window', {
          workspace: mockInstallation,
          platformTeam: mockPlatformTeam,
        });
        
        expect(result).toBe(42);
      });

      it('should handle null values', async () => {
        mockQueryBuilder.getRawOne.mockResolvedValue({ value: null });
        (mockSettingsRepository.save as Mock).mockResolvedValue({});
        
        const result1 = await settingsCore.getValue('conversation_window', {
          workspace: mockInstallation,
          platformTeam: mockPlatformTeam,
        });
        
        expect(result1).toBe(24); // Default value
      });
    });

    describe('upsertSetting', () => {
      it('should update existing setting', async () => {
        (mockSettingsRepository.findByCondition as Mock).mockResolvedValue({
          id: 'setting-123',
          settings: {
            automatic_tickets: false,
          },
        });

        mockQueryBuilder.getRawOne.mockResolvedValue({ value: null });
        await settingsCore.getValue('automatic_tickets', {
          workspace: mockInstallation,
          platformTeam: mockPlatformTeam,
        });

        expect(mockSettingsRepository.update).toHaveBeenCalledWith(
          { id: 'setting-123' },
          {
            settings: {
              automatic_tickets: false,
            },
          }
        );
      });

      it('should create new setting when none exists', async () => {
        (mockSettingsRepository.findByCondition as Mock).mockResolvedValue(null);

        mockQueryBuilder.getRawOne.mockResolvedValue({ value: null });
        await settingsCore.getValue('automatic_tickets', {
          workspace: mockInstallation,
          platformTeam: mockPlatformTeam,
        });

        expect(mockSettingsRepository.save).toHaveBeenCalledWith(
          expect.objectContaining({
            installation: { id: mockInstallation.id },
            platformTeam: { id: mockPlatformTeam.id },
            settings: {
              automatic_tickets: false,
            },
          })
        );
      });
    });
  });
});
