import { Test, TestingModule } from '@nestjs/testing';
import { getRepositoryToken } from '@nestjs/typeorm';
import { beforeEach, describe, expect, it, vi } from 'vitest';
import { CustomerContacts, Installations, Users } from '../../../../../src/database/entities';
import { ChannelType } from '../../../../../src/database/entities/channels/channels.entity';
import { Channels } from '../../../../../src/database/entities/channels/channels.entity';
import { ChannelsRepository } from '../../../../../src/database/entities/channels/repositories/channels.repository';
import { Person } from '../../../../../src/database/interfaces/person.interface';
import { SlackAppManagementService } from '../../../../../src/slack/core/management/slack-data.management';
import { SlackWebAPIService } from '../../../../../src/slack/providers/slack-apis/slack-apis.service';
import { CUSTOM_LOGGER_TOKEN } from '../../../../../src/utils';

describe('SlackAppManagementService', () => {
  let service: SlackAppManagementService;
  let mockLogger: any;
  let mockUsersRepository: any;
  let mockCustomerContactsRepository: any;
  let mockChannelsRepository: any;
  let mockSlackWebAPIService: any;

  beforeEach(async () => {
    mockLogger = {
      log: vi.fn(),
      debug: vi.fn(),
      error: vi.fn(),
      warn: vi.fn(),
    };

    mockUsersRepository = {
      findOne: vi.fn(),
      upsert: vi.fn(),
      save: vi.fn(),
    };

    mockCustomerContactsRepository = {
      findOne: vi.fn(),
      upsert: vi.fn(),
      save: vi.fn(),
    };

    mockChannelsRepository = {
      findByCondition: vi.fn(),
      upsert: vi.fn(),
    };

    mockSlackWebAPIService = {
      getUserInfo: vi.fn(),
    };

    const module: TestingModule = await Test.createTestingModule({
      providers: [
        SlackAppManagementService,
        {
          provide: CUSTOM_LOGGER_TOKEN,
          useValue: mockLogger,
        },
        {
          provide: getRepositoryToken(Users),
          useValue: mockUsersRepository,
        },
        {
          provide: getRepositoryToken(CustomerContacts),
          useValue: mockCustomerContactsRepository,
        },
        {
          provide: ChannelsRepository,
          useValue: mockChannelsRepository,
        },
        {
          provide: SlackWebAPIService,
          useValue: mockSlackWebAPIService,
        },
      ],
    }).compile();

    service = module.get<SlackAppManagementService>(SlackAppManagementService);
    
    (service as any).logger = mockLogger;
    (service as any).usersRepository = mockUsersRepository;
    (service as any).customerContactsRepository = mockCustomerContactsRepository;
    (service as any).channelsRepository = mockChannelsRepository;
    (service as any).slackWebAPIService = mockSlackWebAPIService;
  });

  describe('upsertPersonWithIdentification', () => {
    it('should upsert a person with identification', async () => {
      const userId = 'U12345';
      const mockInstallation = {
        id: 'installation-id',
        botToken: 'xoxb-test-token',
        organization: { id: 'org-id' },
        teamId: 'T12345',
        domains: 'example.com',
      } as Installations;
      const mockChannel = {
        id: 'channel-id',
        channelId: 'C12345',
        name: 'test-channel',
        guestAreCustomers: false,
      } as Channels;

      const mockUserInfo = {
        ok: true,
        user: {
          id: userId,
          team_id: 'T12345',
          profile: {
            email: '<EMAIL>',
            real_name: 'Test User',
            display_name: 'testuser',
            image_72: 'https://example.com/avatar.jpg',
          },
          is_restricted: false,
          is_ultra_restricted: false,
        },
      };

      mockSlackWebAPIService.getUserInfo.mockResolvedValue(mockUserInfo);

      vi.spyOn(service, 'isCustomer').mockReturnValue(false);

      const mockUser = { id: 'user-db-id', slackId: userId } as Users;
      vi.spyOn(service, 'upsertSlackUser').mockResolvedValue(mockUser);

      const result = await service.upsertPersonWithIdentification(userId, mockInstallation, mockChannel);

      expect(mockSlackWebAPIService.getUserInfo).toHaveBeenCalledWith(
        mockInstallation.botToken,
        { user: userId }
      );

      expect(service.isCustomer).toHaveBeenCalledWith(
        mockInstallation,
        expect.objectContaining({
          slackId: userId,
          slackProfileEmail: '<EMAIL>',
          userDump: mockUserInfo.user,
        }),
        mockChannel
      );

      expect(service.upsertSlackUser).toHaveBeenCalledWith({
        installationId: mockInstallation.id,
        organizationId: mockInstallation.organization.id,
        token: mockInstallation.botToken,
        userId,
        isCustomer: false,
      });

      expect(result).toEqual(mockUser);
    });

    it('should handle errors when getting user info', async () => {
      const userId = 'U12345';
      const mockInstallation = {
        id: 'installation-id',
        botToken: 'xoxb-test-token',
        organization: { id: 'org-id' },
      } as Installations;
      const mockChannel = {
        id: 'channel-id',
        channelId: 'C12345',
        name: 'test-channel',
      } as Channels;

      mockSlackWebAPIService.getUserInfo.mockResolvedValue({
        ok: false,
        error: 'user_not_found',
      });

      await expect(service.upsertPersonWithIdentification(userId, mockInstallation, mockChannel))
        .rejects.toThrow('user_not_found');

      expect(mockLogger.error).toHaveBeenCalledWith(
        expect.stringContaining(`Failed to get user info for user ${userId}`)
      );
    });
  });

  describe('isCustomer', () => {
    it('should identify a user from an external workspace as a customer', () => {
      const mockInstallation = {
        id: 'installation-id',
        teamId: 'T12345',
        domains: 'example.com',
      } as Installations;
      
      const mockUser = {
        slackId: 'U12345',
        slackProfileEmail: '<EMAIL>',
        userDump: { team_id: 'T67890' }, // Different team ID
        isRestricted: false,
        isUltraRestricted: false,
        userHasEmail: () => true,
        getUserAvatar: () => 'https://example.com/avatar.jpg',
      };
      
      const mockChannel = {
        id: 'channel-id',
        channelId: 'C12345',
        name: 'test-channel',
        guestAreCustomers: false,
      } as Channels;

      const result = service.isCustomer(mockInstallation, mockUser, mockChannel);

      expect(result).toBe(true);
      expect(mockLogger.debug).toHaveBeenCalledWith(
        expect.stringContaining('is from an external workspace')
      );
    });

    it('should identify a guest user as a customer when channel marks guests as customers', () => {
      const mockInstallation = {
        id: 'installation-id',
        teamId: 'T12345',
        domains: 'example.com',
      } as Installations;
      
      const mockUser = {
        slackId: 'U12345',
        slackProfileEmail: '<EMAIL>',
        userDump: { team_id: 'T12345' }, // Same team ID
        isRestricted: true, // Guest user
        isUltraRestricted: false,
        userHasEmail: () => true,
        getUserAvatar: () => 'https://example.com/avatar.jpg',
      };
      
      const mockChannel = {
        id: 'channel-id',
        channelId: 'C12345',
        name: 'test-channel',
        guestAreCustomers: true, // Channel marks guests as customers
      } as Channels;

      const result = service.isCustomer(mockInstallation, mockUser, mockChannel);

      expect(result).toBe(true);
      expect(mockLogger.debug).toHaveBeenCalledWith(
        expect.stringContaining('is a guest user and the channel')
      );
    });

    it('should identify a user with non-domain email as a customer', () => {
      const mockInstallation = {
        id: 'installation-id',
        teamId: 'T12345',
        domains: 'example.com',
      } as Installations;
      
      const mockUser = {
        slackId: 'U12345',
        slackProfileEmail: '<EMAIL>', // Different domain
        userDump: { team_id: 'T12345' }, // Same team ID
        isRestricted: false,
        isUltraRestricted: false,
        userHasEmail: () => true,
        getUserAvatar: () => 'https://example.com/avatar.jpg',
      };
      
      const mockChannel = {
        id: 'channel-id',
        channelId: 'C12345',
        name: 'test-channel',
        guestAreCustomers: false,
      } as Channels;

      const result = service.isCustomer(mockInstallation, mockUser, mockChannel);

      expect(result).toBe(true);
      expect(mockLogger.debug).toHaveBeenCalledWith(
        expect.stringContaining('is our domain user? false')
      );
    });

    it('should identify a user with domain email as not a customer', () => {
      const mockInstallation = {
        id: 'installation-id',
        teamId: 'T12345',
        domains: 'example.com',
      } as Installations;
      
      const mockUser = {
        slackId: 'U12345',
        slackProfileEmail: '<EMAIL>', // Same domain
        userDump: { team_id: 'T12345' }, // Same team ID
        isRestricted: false,
        isUltraRestricted: false,
        userHasEmail: () => true,
        getUserAvatar: () => 'https://example.com/avatar.jpg',
      };
      
      const mockChannel = {
        id: 'channel-id',
        channelId: 'C12345',
        name: 'test-channel',
        guestAreCustomers: false,
      } as Channels;

      const result = service.isCustomer(mockInstallation, mockUser, mockChannel);

      expect(result).toBe(false);
      expect(mockLogger.debug).toHaveBeenCalledWith(
        expect.stringContaining('is our domain user? true')
      );
    });

    it('should return false if user dump is not available', () => {
      const mockInstallation = {
        id: 'installation-id',
        teamId: 'T12345',
      } as Installations;
      
      const mockUser = {
        slackId: 'U12345',
        slackProfileEmail: '<EMAIL>',
        userDump: null, // No user dump
        isRestricted: false,
        isUltraRestricted: false,
        userHasEmail: () => true,
        getUserAvatar: () => 'https://example.com/avatar.jpg',
      } as unknown as Person;
      
      const mockChannel = {
        id: 'channel-id',
        channelId: 'C12345',
        name: 'test-channel',
        guestAreCustomers: false,
      } as Channels;

      const result = service.isCustomer(mockInstallation, mockUser, mockChannel);

      expect(result).toBe(false);
      expect(mockLogger.error).toHaveBeenCalledWith(
        expect.stringContaining('does not have a user dump')
      );
    });
  });

});
