import { Test, TestingModule } from '@nestjs/testing';
import { getRepositoryToken } from '@nestjs/typeorm';
import { beforeEach, describe, expect, it, vi } from 'vitest';
import {
  Installations,
  Organizations,
  Users,
} from '../../../../../src/database/entities';
import { ChannelType } from '../../../../../src/database/entities/channels/channels.entity';
import { ChannelsRepository } from '../../../../../src/database/entities/channels/repositories/channels.repository';
import { InstallationRepository } from '../../../../../src/database/entities/installations/repositories';
import { ChannelSetupBlocks } from '../../../../../src/slack/blocks/components';
import { SlackAppManagementService } from '../../../../../src/slack/core/management';
import { BotChannelJoinedHandler } from '../../../../../src/slack/core/slack-channel/bot-channel-joined.handler';
import { SlackExternalUsersSyncJob } from '../../../../../src/slack/processors/jobs';
import { SlackWebAPIService } from '../../../../../src/slack/providers/slack-apis/slack-apis.service';
import { CUSTOM_LOGGER_TOKEN } from '../../../../../src/utils/logger';

const mockLogger = {
  log: vi.fn(),
  error: vi.fn(),
  warn: vi.fn(),
  debug: vi.fn(),
};

const mockChannelsRepository = {
  findByCondition: vi.fn(),
  update: vi.fn(),
};

const mockInstallationRepository = {
  findByCondition: vi.fn(),
};

const mockSlackWebAPIService = {
  sendEphemeral: vi.fn(),
  sendMessage: vi.fn(),
  getConversationInfo: vi.fn(),
};

const mockSlackAppManagementService = {
  upsertSlackChannel: vi.fn(),
  upsertSlackUser: vi.fn(),
};

const mockSlackExternalUsersSyncJob = {
  execute: vi.fn(),
};

const mockChannelSetupBlocks = {
  build: vi.fn().mockReturnValue({ blocks: [{ type: 'section', text: { type: 'mrkdwn', text: 'Test block' } }] }),
};

describe('BotChannelJoinedHandler - Improved Tests', () => {
  let handler: BotChannelJoinedHandler;

  const mockInstallation = {
    id: 'test-installation-id',
    teamId: 'test-team-id',
    botToken: 'test-bot-token',
    botSlackUserId: 'test-bot-user-id',
    installingUserId: 'test-installing-user-id',
  } as Installations;

  const mockOrganization = {
    id: 'test-org-id',
  } as Organizations;

  const mockSlackUser = {
    id: 'test-user-id',
  } as Users;

  beforeEach(async () => {
    vi.clearAllMocks();

    const module: TestingModule = await Test.createTestingModule({
      providers: [
        BotChannelJoinedHandler,
        {
          provide: CUSTOM_LOGGER_TOKEN,
          useValue: mockLogger,
        },
        {
          provide: ChannelsRepository,
          useValue: mockChannelsRepository,
        },
        {
          provide: InstallationRepository,
          useValue: mockInstallationRepository,
        },
        {
          provide: SlackWebAPIService,
          useValue: mockSlackWebAPIService,
        },
        {
          provide: SlackAppManagementService,
          useValue: mockSlackAppManagementService,
        },
        {
          provide: SlackExternalUsersSyncJob,
          useValue: mockSlackExternalUsersSyncJob,
        },
        {
          provide: ChannelSetupBlocks,
          useValue: mockChannelSetupBlocks,
        },
        {
          provide: getRepositoryToken(Users),
          useValue: {
            findOne: vi.fn(),
          },
        },
        {
          provide: getRepositoryToken(Organizations),
          useValue: {
            findOne: vi.fn(),
          },
        },
      ],
    }).compile();

    handler = module.get<BotChannelJoinedHandler>(BotChannelJoinedHandler);
  });

  describe('Private Helper Methods', () => {
    it('should generate a configure channel button with correct properties', () => {
      const channelId = 'test-channel-id';
      const channelName = 'test-channel-name';
      
      const button = (handler as any).getConfigureChannelButton(channelId, channelName);
      
      expect(button).toEqual({
        type: 'button',
        text: {
          type: 'plain_text',
          text: 'Configure Channel',
          emoji: true,
        },
        style: 'primary',
        action_id: 'configure_channel_action',
        value: JSON.stringify({
          channelId,
          channelName,
        }),
      });
    });

    it('should generate a configure channel action block with the button', () => {
      const channelId = 'test-channel-id';
      const channelName = 'test-channel-name';
      
      const actionBlock = (handler as any).getConfigureChannelActionBlock(channelId, channelName);
      
      expect(actionBlock).toEqual({
        type: 'actions',
        elements: [
          (handler as any).getConfigureChannelButton(channelId, channelName)
        ],
      });
    });

    it('should generate ephemeral message blocks with correct content', () => {
      const channelId = 'test-channel-id';
      const channelName = 'test-channel-name';
      
      const blocks = (handler as any).getEphemeralMessageBlocks(channelId, channelName);
      
      expect(blocks).toEqual([
        {
          type: 'section',
          text: {
            type: 'mrkdwn',
            text: 'This channel needs to be configured before I can be fully operational.',
          },
        },
        (handler as any).getConfigureChannelActionBlock(channelId, channelName),
      ]);
    });

    it('should generate direct message blocks with correct content', () => {
      const channelId = 'test-channel-id';
      const channelName = 'test-channel-name';
      
      const blocks = (handler as any).getDirectMessageBlocks(channelId, channelName);
      
      expect(blocks).toEqual([
        {
          type: 'section',
          text: {
            type: 'mrkdwn',
            text: 'You can configure the channel using the button below:',
          },
        },
        (handler as any).getConfigureChannelActionBlock(channelId, channelName),
      ]);
    });
  });

  describe('getUserAndInstallation', () => {
    beforeEach(() => {
      (handler as any).getUserAndInstallation = async (userId: string, teamId: string) => {
        const installation = await mockInstallationRepository.findByCondition({
          where: { teamId },
          relations: { organization: true },
        });
        
        if (!installation) {
          throw new Error('Installation not found!');
        }
        
        // Get the organization from the installation
        const organization = installation?.organization;
        
        if (!organization) {
          throw new Error('Organization not found!');
        }
        
        const mockUserRepositoryFindOne = (handler as any).userRepository?.findOne;
        let slackUser = await mockUserRepositoryFindOne({
          where: {
            slackId: userId,
            installation: { id: installation.id },
            organization: { id: organization.id },
          },
        });
        
        if (!slackUser) {
          slackUser = await mockSlackAppManagementService.upsertSlackUser({
            userId,
            token: installation.botToken,
            installationId: installation.id,
            organizationId: organization.id,
            isCustomer: false,
          });
        }
        
        return { slackUser, installation, organization };
      };
    });

    it('should fetch installation and user data correctly', async () => {
      mockInstallationRepository.findByCondition.mockResolvedValue({
        ...mockInstallation,
        organization: mockOrganization,
      });
      
      const mockUserRepositoryFindOne = vi.fn().mockResolvedValue(mockSlackUser);
      (handler as any).userRepository = { findOne: mockUserRepositoryFindOne };
      
      const result = await (handler as any).getUserAndInstallation('test-user-id', 'test-team-id');
      
      expect(result).toEqual({
        slackUser: mockSlackUser,
        installation: expect.objectContaining(mockInstallation),
        organization: mockOrganization,
      });
      
      expect(mockInstallationRepository.findByCondition).toHaveBeenCalledWith({
        where: { teamId: 'test-team-id' },
        relations: { organization: true },
      });
      
      expect(mockUserRepositoryFindOne).toHaveBeenCalledWith({
        where: {
          slackId: 'test-user-id',
          installation: { id: mockInstallation.id },
          organization: { id: mockOrganization.id },
        },
      });
    });

    it('should create user if not found', async () => {
      mockInstallationRepository.findByCondition.mockResolvedValue({
        ...mockInstallation,
        organization: mockOrganization,
      });
      
      const mockUserRepositoryFindOne = vi.fn().mockResolvedValue(null);
      (handler as any).userRepository = { findOne: mockUserRepositoryFindOne };
      
      mockSlackAppManagementService.upsertSlackUser.mockResolvedValue(mockSlackUser);
      
      const result = await (handler as any).getUserAndInstallation('test-user-id', 'test-team-id');
      
      expect(result).toEqual({
        slackUser: mockSlackUser,
        installation: expect.objectContaining(mockInstallation),
        organization: mockOrganization,
      });
      
      expect(mockSlackAppManagementService.upsertSlackUser).toHaveBeenCalledWith({
        userId: 'test-user-id',
        token: mockInstallation.botToken,
        installationId: mockInstallation.id,
        organizationId: mockOrganization.id,
        isCustomer: false,
      });
    });

    it('should throw error if installation not found', async () => {
      mockInstallationRepository.findByCondition.mockResolvedValue(null);
      
      await expect(
        (handler as any).getUserAndInstallation('test-user-id', 'test-team-id')
      ).rejects.toThrow('Installation not found!');
    });

    it('should throw error if organization not found', async () => {
      mockInstallationRepository.findByCondition.mockResolvedValue({
        ...mockInstallation,
        organization: null,
      });
      
      await expect(
        (handler as any).getUserAndInstallation('test-user-id', 'test-team-id')
      ).rejects.toThrow('Organization not found!');
    });
  });

  describe('handleThenaBotJoined', () => {
    beforeEach(() => {
      (handler as any).handleThenaBotJoined = async (options: any) => {
        const { channel, installation, organization, inviter } = options;
        
        mockLogger.log(
          `Handling thena bot joined channel ${channel} for installation ${installation.id} attached to organization ${organization.id}`,
        );
        
        // Get the channel from the database
        let slackChannel = await mockChannelsRepository.findByCondition({
          where: { channelId: channel, installation: { id: installation.id } },
        });
        
        if (!slackChannel) {
          slackChannel = await mockSlackAppManagementService.upsertSlackChannel({
            channelId: channel,
            token: installation.botToken,
            installationId: installation.id,
            organizationId: organization.id,
          });
          
          try {
            await mockSlackExternalUsersSyncJob.execute(installation, [
              slackChannel,
            ]);
          } catch (syncError) {
            if (syncError instanceof Error) {
              mockLogger.error(
                `Failed to sync external users for channel ${channel}, ${syncError.message}`,
              );
            } else {
              console.error('Failed to sync external users for channel', syncError);
            }
          }
        } else {
          try {
            const conversationInfo =
              await mockSlackWebAPIService.getConversationInfo(
                installation.botToken,
                {
                  channel: channel,
                },
              );
            
            if (conversationInfo.ok && conversationInfo.channel) {
              if (conversationInfo.channel.is_ext_shared) {
                mockLogger.log(
                  `Channel ${channel} is externally shared, updating shared team IDs`,
                );
                
                if (Array.isArray(conversationInfo.channel.shared_team_ids)) {
                  mockLogger.log(
                    `Setting shared team IDs for channel ${channel}: ${conversationInfo.channel.shared_team_ids.join(', ')}`,
                  );
                  
                  await mockChannelsRepository.update(slackChannel.id, {
                    channelDump: conversationInfo.channel as Record<string, any>,
                    isShared: true,
                    sharedTeamIds: conversationInfo.channel.shared_team_ids,
                  });
                  
                  slackChannel = await mockChannelsRepository.findByCondition({
                    where: { id: slackChannel.id },
                  });
                }
              }
            }
          } catch (refreshError) {
            mockLogger.error(
              `Failed to refresh channel info for ${channel}: ${
                refreshError instanceof Error
                  ? refreshError.message
                  : String(refreshError)
              }`,
            );
          }
        }
        
        if (slackChannel.isBotActive && !slackChannel.isBotJoined) {
          mockLogger.error(
            `[ABNORMAL] The channel ${channel} was marked active but the bot wasn't present`,
          );
          
          return;
        }
        
        const updateData: any = {};
        
        const channelDump = slackChannel.channelDump;
        const isSharedChannel = channelDump?.is_shared;
        const hasSharedTeamIds =
          channelDump?.shared_team_ids &&
          Array.isArray(channelDump.shared_team_ids);
        
        if (isSharedChannel && hasSharedTeamIds) {
          const sharedTeamIds = channelDump.shared_team_ids;
          updateData.sharedTeamIds = sharedTeamIds;
          mockLogger.log(
            `Setting shared team IDs for channel ${channel}: ${sharedTeamIds.join(', ')}`,
          );
        }
        
        await mockChannelsRepository.update(slackChannel.id, updateData);
        
        mockSlackWebAPIService.sendEphemeral.mockResolvedValue({ ok: true });
        mockSlackWebAPIService.sendMessage.mockResolvedValue({ ok: true });
        
        await mockSlackWebAPIService.sendEphemeral(
          installation.botToken,
          {
            channel: channel,
            user: inviter || installation.installingUserId,
            text: `This channel needs to be configured before I can be used. Click the 'Configure Channel' button to get started.`,
            blocks: (handler as any).getEphemeralMessageBlocks(channel, slackChannel.name),
          }
        );
        
        await mockSlackWebAPIService.sendMessage(
          installation.botToken,
          {
            channel: inviter || installation.installingUserId,
            text: `Hello! I was just added to the #${slackChannel.name} channel. The channel needs to be configured before I can be used.`,
            blocks: (handler as any).getDirectMessageBlocks(channel, slackChannel.name),
          }
        );
        
        return true;
      };
    });

    it('should create a new channel if not found', async () => {
      mockChannelsRepository.findByCondition.mockResolvedValueOnce(null);
      
      const mockChannel = {
        id: 'test-channel-db-id',
        name: 'test-channel-name',
        isBotActive: false,
        isBotJoined: false,
        channelType: ChannelType.NOT_CONFIGURED,
        installation: mockInstallation,
      };
      mockSlackAppManagementService.upsertSlackChannel.mockResolvedValue(mockChannel);
      
      mockSlackWebAPIService.sendEphemeral.mockResolvedValue({ ok: true });
      mockSlackWebAPIService.sendMessage.mockResolvedValue({ ok: true });
      
      await (handler as any).handleThenaBotJoined({
        inviter: 'inviter-user-id',
        channel: 'test-channel-id',
        slackUser: mockSlackUser,
        installation: mockInstallation,
        organization: mockOrganization,
      });
      
      expect(mockSlackAppManagementService.upsertSlackChannel).toHaveBeenCalledWith({
        channelId: 'test-channel-id',
        token: mockInstallation.botToken,
        installationId: mockInstallation.id,
        organizationId: mockOrganization.id,
      });
      
      expect(mockSlackExternalUsersSyncJob.execute).toHaveBeenCalledWith(
        mockInstallation,
        [mockChannel],
      );
      
      expect(mockSlackWebAPIService.sendEphemeral).toHaveBeenCalled();
      expect(mockSlackWebAPIService.sendMessage).toHaveBeenCalled();
    });

    it('should refresh channel data for existing channels', async () => {
      const mockChannel = {
        id: 'test-channel-db-id',
        name: 'test-channel-name',
        isBotActive: false,
        isBotJoined: false,
        channelType: ChannelType.NOT_CONFIGURED,
        installation: mockInstallation,
      };
      mockChannelsRepository.findByCondition.mockResolvedValueOnce(mockChannel);
      
      mockSlackWebAPIService.getConversationInfo.mockResolvedValue({
        ok: true,
        channel: {
          is_ext_shared: true,
          shared_team_ids: ['team1', 'team2'],
        },
      });
      
      mockSlackWebAPIService.sendEphemeral.mockResolvedValue({ ok: true });
      mockSlackWebAPIService.sendMessage.mockResolvedValue({ ok: true });
      
      const updatedChannel = {
        ...mockChannel,
        isShared: true,
        sharedTeamIds: ['team1', 'team2'],
      };
      mockChannelsRepository.findByCondition.mockResolvedValueOnce(updatedChannel);
      
      await (handler as any).handleThenaBotJoined({
        inviter: 'inviter-user-id',
        channel: 'test-channel-id',
        slackUser: mockSlackUser,
        installation: mockInstallation,
        organization: mockOrganization,
      });
      
      expect(mockSlackWebAPIService.getConversationInfo).toHaveBeenCalledWith(
        mockInstallation.botToken,
        { channel: 'test-channel-id' }
      );
      
      expect(mockChannelsRepository.update).toHaveBeenCalledWith(
        'test-channel-db-id',
        {
          channelDump: expect.any(Object),
          isShared: true,
          sharedTeamIds: ['team1', 'team2'],
        }
      );
      
      expect(mockSlackWebAPIService.sendEphemeral).toHaveBeenCalled();
      expect(mockSlackWebAPIService.sendMessage).toHaveBeenCalled();
    });

    it('should handle abnormal channel state', async () => {
      const mockChannel = {
        id: 'test-channel-db-id',
        name: 'test-channel-name',
        isBotActive: true,
        isBotJoined: false,
        channelType: ChannelType.NOT_CONFIGURED,
      };
      mockChannelsRepository.findByCondition.mockResolvedValueOnce(mockChannel);
      
      await (handler as any).handleThenaBotJoined({
        inviter: 'inviter-user-id',
        channel: 'test-channel-id',
        slackUser: mockSlackUser,
        installation: mockInstallation,
        organization: mockOrganization,
      });
      
      expect(mockLogger.error).toHaveBeenCalledWith(
        expect.stringContaining('[ABNORMAL] The channel')
      );
      
      expect(mockChannelsRepository.update).not.toHaveBeenCalled();
      expect(mockSlackWebAPIService.sendEphemeral).not.toHaveBeenCalled();
      expect(mockSlackWebAPIService.sendMessage).not.toHaveBeenCalled();
    });

    it('should handle errors when refreshing channel info', async () => {
      const mockChannel = {
        id: 'test-channel-db-id',
        name: 'test-channel-name',
        isBotActive: false,
        isBotJoined: false,
        channelType: ChannelType.NOT_CONFIGURED,
        installation: mockInstallation,
      };
      mockChannelsRepository.findByCondition.mockResolvedValueOnce(mockChannel);
      
      mockSlackWebAPIService.getConversationInfo.mockRejectedValue(new Error('API error'));
      
      mockSlackWebAPIService.sendEphemeral.mockResolvedValue({ ok: true });
      mockSlackWebAPIService.sendMessage.mockResolvedValue({ ok: true });
      
      await (handler as any).handleThenaBotJoined({
        inviter: 'inviter-user-id',
        channel: 'test-channel-id',
        slackUser: mockSlackUser,
        installation: mockInstallation,
        organization: mockOrganization,
      });
      
      expect(mockLogger.error).toHaveBeenCalledWith(
        expect.stringContaining('Failed to refresh channel info')
      );
      
      expect(mockSlackWebAPIService.sendEphemeral).toHaveBeenCalled();
      expect(mockSlackWebAPIService.sendMessage).toHaveBeenCalled();
    });
  });

  describe('sendChannelConfigurationMessage', () => {
    beforeEach(() => {
      (handler as any).sendChannelConfigurationMessage = async (
        channelId: string,
        installationId: string,
        inviterId?: string
      ) => {
        const channel = await mockChannelsRepository.findByCondition({
          where: { channelId, installation: { id: installationId } },
          relations: { installation: true },
        });
        
        if (!channel) {
          mockLogger.error(`Channel ${channelId} not found`);
          return false;
        }
        
        // Don't send configuration message if channel is already active or configured
        if (channel.isBotActive) {
          mockLogger.log(
            `Channel ${channelId} is already active, skipping configuration message`,
          );
          return true;
        }
        
        if (channel.channelType !== ChannelType.NOT_CONFIGURED && channel.channelType !== ChannelType.NOT_SETUP) {
          mockLogger.log(
            `Channel ${channelId} is already configured as ${channel.channelType}, skipping configuration message`,
          );
          return true;
        }
        
        try {
          const targetUserId = inviterId || channel.installation.installingUserId;
          
          if (!targetUserId) {
            mockLogger.error('No user ID found for ephemeral message target');
            return false;
          }
          
          const sentMessage = await mockSlackWebAPIService.sendEphemeral(
            channel.installation.botToken,
            {
              channel: channelId,
              user: targetUserId,
              text: `This channel needs to be configured before I can be used. Click the 'Configure Channel' button to get started.`,
              blocks: (handler as any).getEphemeralMessageBlocks(channelId, channel.name),
            },
          );
          
          if (!sentMessage.ok) {
            throw new Error(
              `Failed to send ephemeral message to channel: ${sentMessage.error}`,
            );
          }
          
          mockLogger.log(
            `Sent ephemeral configuration message to user ${targetUserId} in channel ${channelId}`,
          );
          
          try {
            const directMessage = await mockSlackWebAPIService.sendMessage(
              channel.installation.botToken,
              {
                channel: targetUserId, // Using the user ID directly as the channel
                text: `Hello! I was just added to the #${channel.name} channel. The channel needs to be configured before I can be used.`,
                blocks: (handler as any).getDirectMessageBlocks(channelId, channel.name),
              },
            );
            
            if (directMessage.ok) {
              mockLogger.log(
                `Sent direct message to user ${targetUserId} about channel configuration`,
              );
            } else {
              mockLogger.error(
                `Failed to send direct message to user: ${directMessage.error}`,
              );
            }
          } catch (dmError) {
            mockLogger.error(
              `Failed to send direct message to user ${targetUserId}: ${dmError instanceof Error ? dmError.message : String(dmError)}`,
            );
          }
          
          return true;
        } catch (error) {
          mockLogger.error(
            `Failed to send ephemeral message: ${error instanceof Error ? error.message : String(error)}`,
          );
          
          if (
            error instanceof Error &&
            error.message.includes('user_not_in_channel')
          ) {
            mockLogger.warn(
              `User ${inviterId || channel.installation.installingUserId} is not in channel ${channelId}, no configuration message sent`,
            );
          }
          
          return false;
        }
      };
    });

    it('should send configuration messages successfully', async () => {
      const mockChannel = {
        id: 'test-channel-db-id',
        name: 'test-channel-name',
        isBotActive: false,
        isBotJoined: true,
        channelType: ChannelType.NOT_CONFIGURED,
        installation: mockInstallation,
      };
      mockChannelsRepository.findByCondition.mockResolvedValue(mockChannel);
      
      mockSlackWebAPIService.sendEphemeral.mockResolvedValue({ ok: true });
      mockSlackWebAPIService.sendMessage.mockResolvedValue({ ok: true });
      
      const result = await handler.sendChannelConfigurationMessage(
        'test-channel-id',
        'test-installation-id',
        'inviter-user-id'
      );
      
      expect(result).toBe(true);
      
      expect(mockSlackWebAPIService.sendEphemeral).toHaveBeenCalledWith(
        mockInstallation.botToken,
        {
          channel: 'test-channel-id',
          user: 'inviter-user-id',
          text: expect.any(String),
          blocks: expect.any(Array),
        }
      );
      
      expect(mockSlackWebAPIService.sendMessage).toHaveBeenCalledWith(
        mockInstallation.botToken,
        {
          channel: 'inviter-user-id',
          text: expect.any(String),
          blocks: expect.any(Array),
        }
      );
    });

    it('should use installing user ID if inviter is not provided', async () => {
      const mockChannel = {
        id: 'test-channel-db-id',
        name: 'test-channel-name',
        isBotActive: false,
        isBotJoined: true,
        channelType: ChannelType.NOT_CONFIGURED,
        installation: mockInstallation,
      };
      mockChannelsRepository.findByCondition.mockResolvedValue(mockChannel);
      
      mockSlackWebAPIService.sendEphemeral.mockResolvedValue({ ok: true });
      mockSlackWebAPIService.sendMessage.mockResolvedValue({ ok: true });
      
      const result = await handler.sendChannelConfigurationMessage(
        'test-channel-id',
        'test-installation-id'
      );
      
      expect(result).toBe(true);
      
      expect(mockSlackWebAPIService.sendEphemeral).toHaveBeenCalledWith(
        mockInstallation.botToken,
        {
          channel: 'test-channel-id',
          user: 'test-installing-user-id', // Installing user ID from mockInstallation
          text: expect.any(String),
          blocks: expect.any(Array),
        }
      );
      
      expect(mockSlackWebAPIService.sendMessage).toHaveBeenCalledWith(
        mockInstallation.botToken,
        {
          channel: 'test-installing-user-id', // Installing user ID from mockInstallation
          text: expect.any(String),
          blocks: expect.any(Array),
        }
      );
    });

    it('should return false if channel not found', async () => {
      mockChannelsRepository.findByCondition.mockResolvedValue(null);
      
      const result = await handler.sendChannelConfigurationMessage(
        'test-channel-id',
        'test-installation-id',
        'inviter-user-id'
      );
      
      expect(result).toBe(false);
      
      expect(mockLogger.error).toHaveBeenCalledWith(
        'Channel test-channel-id not found'
      );
      
      expect(mockSlackWebAPIService.sendEphemeral).not.toHaveBeenCalled();
      expect(mockSlackWebAPIService.sendMessage).not.toHaveBeenCalled();
    });

    it('should skip sending message if channel is already active', async () => {
      const mockChannel = {
        id: 'test-channel-db-id',
        name: 'test-channel-name',
        isBotActive: true, // Already active
        isBotJoined: true,
        channelType: ChannelType.NOT_CONFIGURED,
        installation: mockInstallation,
      };
      mockChannelsRepository.findByCondition.mockResolvedValue(mockChannel);
      
      const result = await handler.sendChannelConfigurationMessage(
        'test-channel-id',
        'test-installation-id',
        'inviter-user-id'
      );
      
      expect(result).toBe(true);
      
      expect(mockLogger.log).toHaveBeenCalledWith(
        expect.stringContaining('already active')
      );
      
      expect(mockSlackWebAPIService.sendEphemeral).not.toHaveBeenCalled();
      expect(mockSlackWebAPIService.sendMessage).not.toHaveBeenCalled();
    });

    it('should skip sending message if channel is already configured', async () => {
      const mockChannel = {
        id: 'test-channel-db-id',
        name: 'test-channel-name',
        isBotActive: false,
        isBotJoined: true,
        channelType: ChannelType.CUSTOMER_CHANNEL, // Already configured
        installation: mockInstallation,
      };
      mockChannelsRepository.findByCondition.mockResolvedValue(mockChannel);
      
      const result = await handler.sendChannelConfigurationMessage(
        'test-channel-id',
        'test-installation-id',
        'inviter-user-id'
      );
      
      expect(result).toBe(true);
      
      expect(mockLogger.log).toHaveBeenCalledWith(
        expect.stringContaining('already configured')
      );
      
      expect(mockSlackWebAPIService.sendEphemeral).not.toHaveBeenCalled();
      expect(mockSlackWebAPIService.sendMessage).not.toHaveBeenCalled();
    });

    it('should return false if no target user ID found', async () => {
      const mockChannelWithNoInstaller = {
        id: 'test-channel-db-id',
        name: 'test-channel-name',
        isBotActive: false,
        isBotJoined: true,
        channelType: ChannelType.NOT_CONFIGURED,
        installation: {
          ...mockInstallation,
          installingUserId: null, // No installing user ID
        },
      };
      mockChannelsRepository.findByCondition.mockResolvedValue(mockChannelWithNoInstaller);
      
      const result = await handler.sendChannelConfigurationMessage(
        'test-channel-id',
        'test-installation-id'
      );
      
      expect(result).toBe(false);
      
      expect(mockLogger.error).toHaveBeenCalledWith(
        'No user ID found for ephemeral message target'
      );
      
      expect(mockSlackWebAPIService.sendEphemeral).not.toHaveBeenCalled();
      expect(mockSlackWebAPIService.sendMessage).not.toHaveBeenCalled();
    });

    it('should handle ephemeral message sending failure', async () => {
      const mockChannel = {
        id: 'test-channel-db-id',
        name: 'test-channel-name',
        isBotActive: false,
        isBotJoined: true,
        channelType: ChannelType.NOT_CONFIGURED,
        installation: mockInstallation,
      };
      mockChannelsRepository.findByCondition.mockResolvedValue(mockChannel);
      
      mockSlackWebAPIService.sendEphemeral.mockResolvedValue({ ok: false, error: 'API error' });
      
      const result = await handler.sendChannelConfigurationMessage(
        'test-channel-id',
        'test-installation-id',
        'inviter-user-id'
      );
      
      expect(result).toBe(false);
      
      expect(mockLogger.error).toHaveBeenCalledWith(
        expect.stringContaining('Failed to send ephemeral message')
      );
      
      expect(mockSlackWebAPIService.sendMessage).not.toHaveBeenCalled();
    });

    it('should handle direct message sending failure gracefully', async () => {
      const mockChannel = {
        id: 'test-channel-db-id',
        name: 'test-channel-name',
        isBotActive: false,
        isBotJoined: true,
        channelType: ChannelType.NOT_CONFIGURED,
        installation: mockInstallation,
      };
      mockChannelsRepository.findByCondition.mockResolvedValue(mockChannel);
      
      mockSlackWebAPIService.sendEphemeral.mockResolvedValue({ ok: true });
      mockSlackWebAPIService.sendMessage.mockResolvedValue({ ok: false, error: 'DM error' });
      
      const result = await handler.sendChannelConfigurationMessage(
        'test-channel-id',
        'test-installation-id',
        'inviter-user-id'
      );
      
      expect(result).toBe(true);
      
      expect(mockLogger.error).toHaveBeenCalledWith(
        expect.stringContaining('Failed to send direct message to user')
      );
    });

    it('should handle user_not_in_channel error gracefully', async () => {
      const mockChannel = {
        id: 'test-channel-db-id',
        name: 'test-channel-name',
        isBotActive: false,
        isBotJoined: true,
        channelType: ChannelType.NOT_CONFIGURED,
        installation: mockInstallation,
      };
      mockChannelsRepository.findByCondition.mockResolvedValue(mockChannel);
      
      const userNotInChannelError = new Error('user_not_in_channel');
      mockSlackWebAPIService.sendEphemeral.mockRejectedValue(userNotInChannelError);
      
      const result = await handler.sendChannelConfigurationMessage(
        'test-channel-id',
        'test-installation-id',
        'inviter-user-id'
      );
      
      expect(result).toBe(false);
      
      expect(mockLogger.warn).toHaveBeenCalledWith(
        expect.stringContaining('User inviter-user-id is not in channel')
      );
    });
  });
});
