import { Test, TestingModule } from '@nestjs/testing';
import { getRepositoryToken } from '@nestjs/typeorm';
import { beforeEach, describe, expect, it, vi } from 'vitest';
import {
  Installations,
  Organizations,
  Users,
} from '../../../../../src/database/entities';
import { ChannelType } from '../../../../../src/database/entities/channels/channels.entity';
import { ChannelsRepository } from '../../../../../src/database/entities/channels/repositories/channels.repository';
import { InstallationRepository } from '../../../../../src/database/entities/installations/repositories';
import { ChannelSetupBlocks } from '../../../../../src/slack/blocks/components';
import { SlackAppManagementService } from '../../../../../src/slack/core/management';
import { BotChannelJoinedHandler } from '../../../../../src/slack/core/slack-channel/bot-channel-joined.handler';
import { SlackExternalUsersSyncJob } from '../../../../../src/slack/processors/jobs';
import { SlackWebAPIService } from '../../../../../src/slack/providers/slack-apis/slack-apis.service';
import { CUSTOM_LOGGER_TOKEN } from '../../../../../src/utils/logger';

// Mock dependencies
const mockLogger = {
  log: vi.fn(),
  error: vi.fn(),
  warn: vi.fn(),
  debug: vi.fn(),
};

const mockChannelsRepository = {
  findByCondition: vi.fn(),
  update: vi.fn(),
};

const mockInstallationRepository = {
  findByCondition: vi.fn(),
};

const mockSlackWebAPIService = {
  sendEphemeral: vi.fn(),
};

const mockSlackAppManagementService = {
  upsertSlackChannel: vi.fn(),
};

const mockSlackExternalUsersSyncJob = {
  execute: vi.fn(),
};

const mockChannelSetupBlocks = {
  build: vi.fn().mockReturnValue({ blocks: [{ type: 'section', text: { type: 'mrkdwn', text: 'Test block' } }] }),
};

describe('BotChannelJoinedHandler', () => {
  let handler: BotChannelJoinedHandler;

  // Test data
  const mockInstallation = {
    id: 'test-installation-id',
    teamId: 'test-team-id',
    botToken: 'test-bot-token',
    botSlackUserId: 'test-bot-user-id',
  } as Installations;

  const mockOrganization = {
    id: 'test-org-id',
  } as Organizations;

  const mockSlackUser = {
    id: 'test-user-id',
  } as Users;

  beforeEach(async () => {
    // Reset mocks
    vi.clearAllMocks();

    // Create a testing module with our mocked dependencies
    const module: TestingModule = await Test.createTestingModule({
      providers: [
        BotChannelJoinedHandler,
        {
          provide: CUSTOM_LOGGER_TOKEN,
          useValue: mockLogger,
        },
        {
          provide: ChannelsRepository,
          useValue: mockChannelsRepository,
        },
        {
          provide: InstallationRepository,
          useValue: mockInstallationRepository,
        },
        {
          provide: SlackWebAPIService,
          useValue: mockSlackWebAPIService,
        },
        {
          provide: SlackAppManagementService,
          useValue: mockSlackAppManagementService,
        },
        {
          provide: SlackExternalUsersSyncJob,
          useValue: mockSlackExternalUsersSyncJob,
        },
        {
          provide: ChannelSetupBlocks,
          useValue: mockChannelSetupBlocks,
        },
        {
          provide: getRepositoryToken(Users),
          useValue: {
            findOne: vi.fn(),
          },
        },
        {
          provide: getRepositoryToken(Organizations),
          useValue: {
            findOne: vi.fn(),
          },
        },
      ],
    }).compile();

    handler = module.get<BotChannelJoinedHandler>(BotChannelJoinedHandler);

    // Mock the internal methods
    (handler as any).getUserAndInstallation = vi.fn().mockResolvedValue({
      slackUser: mockSlackUser,
      installation: mockInstallation,
      organization: mockOrganization,
    });

    // Spy on the handleThenaBotJoined method
    vi.spyOn(handler as any, 'handleThenaBotJoined');
  });

  describe('handleEvent', () => {
    it('should correctly identify when the bot joins a channel', async () => {
      const handleThenaBotJoinedSpy = vi.spyOn(handler as any, 'handleThenaBotJoined')
        .mockResolvedValue(undefined);
      
      vi.spyOn(handler as any, 'getUserAndInstallation').mockResolvedValue({
        slackUser: mockSlackUser,
        installation: mockInstallation,
        organization: mockOrganization,
      });
      
      // Mock event data
      const mockEvent = {
        event: {
          channel: 'test-channel-id',
          user: mockInstallation.botSlackUserId, // Same as the bot's ID
          team: 'test-team-id',
          event_ts: '123456789.000001',
        },
      };

      // Execute the actual method (not mocked)
      const result = await handler.handleEvent(mockEvent as any);

      // Assert result is true (because the bot joined)
      expect(result).toBe(true);

      // Verify handleThenaBotJoined was called with the correct parameters
      expect(handleThenaBotJoinedSpy).toHaveBeenCalledWith(expect.objectContaining({
        channel: 'test-channel-id',
        slackUser: mockSlackUser,
        installation: mockInstallation,
        organization: mockOrganization,
      }));
    });

    it('should handle when a regular user joins a channel', async () => {
      // Spy on the handleThenaBotJoined method to verify it's not called
      const handleThenaBotJoinedSpy = vi.spyOn(handler as any, 'handleThenaBotJoined');
      
      // Mock event data with a different user
      const mockEvent = {
        event: {
          channel: 'test-channel-id',
          user: 'regular-user-id', // Different from bot ID
          team: 'test-team-id',
          event_ts: '123456789.000001',
        },
      };

      // Execute the actual method
      const result = await handler.handleEvent(mockEvent as any);

      // Assert result is false (because it wasn't the bot that joined)
      expect(result).toBe(false);

      // Verify handleThenaBotJoined was not called
      expect(handleThenaBotJoinedSpy).not.toHaveBeenCalled();
    });

    it('should handle inviter information when provided', async () => {
      // Spy on the handleThenaBotJoined method to verify it's called with the correct parameters
      const handleThenaBotJoinedSpy = vi.spyOn(handler as any, 'handleThenaBotJoined');
      
      // Mock event data with inviter
      const mockEvent = {
        event: {
          channel: 'test-channel-id',
          user: mockInstallation.botSlackUserId, // Same as the bot's ID
          team: 'test-team-id',
          event_ts: '123456789.000001',
          inviter: 'inviter-user-id',
        },
      };

      // Execute the actual method
      await handler.handleEvent(mockEvent as any);

      // Verify handleThenaBotJoined was called with the correct inviter
      expect(handleThenaBotJoinedSpy).toHaveBeenCalledWith(expect.objectContaining({
        inviter: 'inviter-user-id',
        channel: 'test-channel-id',
        slackUser: mockSlackUser,
        installation: mockInstallation,
        organization: mockOrganization,
      }));
    });

    it('should handle errors gracefully', async () => {
      // Mock an error for getUserAndInstallation
      (handler as any).getUserAndInstallation = vi
        .fn()
        .mockRejectedValue(new Error('Test error'));

      // Mock event data
      const mockEvent = {
        event: {
          channel: 'test-channel-id',
          user: 'test-bot-user-id',
          team: 'test-team-id',
          event_ts: '123456789.000001',
        },
      };

      // Execute the actual method
      const result = await handler.handleEvent(mockEvent as any);

      // Assert logger.error was called
      expect(mockLogger.error).toHaveBeenCalled();

      // Assert result is false due to error
      expect(result).toBe(false);
    });
  });

  // For testing the private method directly, we need to create direct tests
  // that don't rely on calling the private method through the handler
  describe('handleThenaBotJoined (manual implementation)', () => {
    it('should test channel creation and ephemeral messaging flow', async () => {
      // Create mock channel data
      const mockChannel = {
        id: 'test-channel-db-id',
        name: 'test-channel-name',
        isBotActive: false,
        isBotJoined: false,
      };

      // Setup test conditions
      mockChannelsRepository.findByCondition.mockResolvedValue(null);
      mockSlackAppManagementService.upsertSlackChannel.mockResolvedValue(
        mockChannel,
      );
      mockSlackExternalUsersSyncJob.execute.mockResolvedValue(undefined);
      mockSlackWebAPIService.sendEphemeral.mockResolvedValue({ ok: true });

      // Create a direct implementation for the handler to ensure it runs our mocked methods
      (handler as any).handleThenaBotJoined = async (options: any) => {
        const { channel, installation, organization, inviter } = options;

        // Get the channel from the database
        let slackChannel = await mockChannelsRepository.findByCondition({
          where: { channelId: channel, installation: { id: installation.id } },
        });

        // If the channel is not found, create it and sync it
        if (!slackChannel) {
          slackChannel = await mockSlackAppManagementService.upsertSlackChannel(
            {
              channelId: channel,
              token: installation.botToken,
              installationId: installation.id,
              organizationId: organization.id,
            },
          );

          try {
            // Execute the external users sync job
            await mockSlackExternalUsersSyncJob.execute(installation, [
              slackChannel,
            ]);
          } catch (syncError) {
            mockLogger.error(
              `Failed to sync external users for channel ${channel}, ${syncError instanceof Error ? syncError.message : syncError}`,
            );
          }
        }

        // If the channel is already active, then we don't need to do anything
        if (slackChannel.isBotActive && !slackChannel.isBotJoined) {
          mockLogger.error(
            `[ABNORMAL] The channel ${channel} was marked active but the bot wasn't present`,
          );
          return;
        }

        // If the inviter is present, send an ephemeral message to the inviter
        if (inviter) {
          // Mark the channel as active and joined
          await mockChannelsRepository.update(slackChannel.id, {
            isBotActive: false,
            isBotJoined: true,
            channelType: ChannelType.NOT_CONFIGURED,
          });

          // Send an ephemeral message to the inviter
          const sentEphemeral = await mockSlackWebAPIService.sendEphemeral(
            installation.botToken,
            {
              channel,
              user: inviter,
              blocks: [{ type: 'section', text: { type: 'mrkdwn', text: 'Test block' } }],
              text: `Configure channel #${slackChannel?.name} to get started!`,
            },
          );

          // If the ephemeral message failed to send, throw an error
          if (!sentEphemeral.ok) {
            mockLogger.error(
              `Failed to send ephemeral message to user ${inviter} in channel ${channel}`,
            );
            throw new Error('Failed to send ephemeral message');
          }
        }
      };

      // Execute
      await (handler as any).handleThenaBotJoined({
        inviter: 'inviter-user-id',
        channel: 'test-channel-id',
        slackUser: mockSlackUser,
        installation: mockInstallation,
        organization: mockOrganization,
      });

      // Verify channel was created
      expect(
        mockSlackAppManagementService.upsertSlackChannel,
      ).toHaveBeenCalledWith({
        channelId: 'test-channel-id',
        token: 'test-bot-token',
        installationId: 'test-installation-id',
        organizationId: 'test-org-id',
      });

      // Verify external user sync was triggered
      expect(mockSlackExternalUsersSyncJob.execute).toHaveBeenCalledWith(
        mockInstallation,
        [mockChannel],
      );

      // Verify channel was updated
      expect(mockChannelsRepository.update).toHaveBeenCalledWith(
        'test-channel-db-id',
        {
          isBotActive: false,
          isBotJoined: true,
          channelType: ChannelType.NOT_CONFIGURED,
        },
      );

      // Verify ephemeral message was sent
      expect(mockSlackWebAPIService.sendEphemeral).toHaveBeenCalled();
    });

    it('should handle external user sync errors', async () => {
      // Setup test conditions
      mockChannelsRepository.findByCondition.mockResolvedValue(null);
      const mockChannel = {
        id: 'test-channel-db-id',
        name: 'test-channel-name',
        isBotActive: false,
        isBotJoined: false,
      };
      mockSlackAppManagementService.upsertSlackChannel.mockResolvedValue(
        mockChannel,
      );
      mockSlackExternalUsersSyncJob.execute.mockRejectedValue(
        new Error('Sync error'),
      );
      mockSlackWebAPIService.sendEphemeral.mockResolvedValue({ ok: true });

      // Create a direct implementation for the handler to ensure it runs our mocked methods
      (handler as any).handleThenaBotJoined = async (options: any) => {
        const { channel, installation, organization, inviter } = options;

        // Get the channel from the database
        let slackChannel = await mockChannelsRepository.findByCondition({
          where: { channelId: channel, installation: { id: installation.id } },
        });

        // If the channel is not found, create it and sync it
        if (!slackChannel) {
          slackChannel = await mockSlackAppManagementService.upsertSlackChannel(
            {
              channelId: channel,
              token: installation.botToken,
              installationId: installation.id,
              organizationId: organization.id,
            },
          );

          try {
            // Execute the external users sync job
            await mockSlackExternalUsersSyncJob.execute(installation, [
              slackChannel,
            ]);
          } catch (syncError) {
            mockLogger.error(
              `Failed to sync external users for channel ${channel}, ${syncError instanceof Error ? syncError.message : syncError}`,
            );
          }
        }

        // Continue with the flow even if sync failed
        if (inviter) {
          await mockChannelsRepository.update(slackChannel.id, {
            isBotActive: false,
            isBotJoined: true,
            channelType: ChannelType.NOT_CONFIGURED,
          });

          const sentEphemeral = await mockSlackWebAPIService.sendEphemeral(
            installation.botToken,
            {
              channel,
              user: inviter,
              blocks: [{ type: 'section', text: { type: 'mrkdwn', text: 'Test block' } }],
              text: `Configure channel #${slackChannel?.name} to get started!`,
            },
          );

          if (!sentEphemeral.ok) {
            mockLogger.error(
              `Failed to send ephemeral message to user ${inviter} in channel ${channel}`,
            );
            throw new Error('Failed to send ephemeral message');
          }
        }
      };

      // Execute
      await (handler as any).handleThenaBotJoined({
        inviter: 'inviter-user-id',
        channel: 'test-channel-id',
        slackUser: mockSlackUser,
        installation: mockInstallation,
        organization: mockOrganization,
      });

      // Verify error was logged
      expect(mockLogger.error).toHaveBeenCalledWith(
        'Failed to sync external users for channel test-channel-id, Sync error',
      );

      // Verify the process continued despite the error
      expect(mockChannelsRepository.update).toHaveBeenCalled();
      expect(mockSlackWebAPIService.sendEphemeral).toHaveBeenCalled();
    });

    it('should handle ephemeral message sending failures', async () => {
      // Setup test conditions
      mockChannelsRepository.findByCondition.mockResolvedValue(null);
      const mockChannel = {
        id: 'test-channel-db-id',
        name: 'test-channel-name',
        isBotActive: false,
        isBotJoined: false,
      };
      mockSlackAppManagementService.upsertSlackChannel.mockResolvedValue(
        mockChannel,
      );
      mockSlackExternalUsersSyncJob.execute.mockResolvedValue(undefined);
      mockSlackWebAPIService.sendEphemeral.mockResolvedValue({ ok: false });

      // Create a direct implementation for the handler to ensure it runs our mocked methods
      (handler as any).handleThenaBotJoined = async (options: any) => {
        const { channel, installation, organization, inviter } = options;

        // Get the channel from the database
        let slackChannel = await mockChannelsRepository.findByCondition({
          where: { channelId: channel, installation: { id: installation.id } },
        });

        // If the channel is not found, create it and sync it
        if (!slackChannel) {
          slackChannel = await mockSlackAppManagementService.upsertSlackChannel(
            {
              channelId: channel,
              token: installation.botToken,
              installationId: installation.id,
              organizationId: organization.id,
            },
          );

          try {
            // Execute the external users sync job
            await mockSlackExternalUsersSyncJob.execute(installation, [
              slackChannel,
            ]);
          } catch (syncError) {
            mockLogger.error(
              `Failed to sync external users for channel ${channel}, ${syncError instanceof Error ? syncError.message : syncError}`,
            );
          }
        }

        // If the inviter is present, send an ephemeral message to the inviter
        if (inviter) {
          // Mark the channel as active and joined
          await mockChannelsRepository.update(slackChannel.id, {
            isBotActive: false,
            isBotJoined: true,
            channelType: ChannelType.NOT_CONFIGURED,
          });

          // Send an ephemeral message to the inviter
          const sentEphemeral = await mockSlackWebAPIService.sendEphemeral(
            installation.botToken,
            {
              channel,
              user: inviter,
              blocks: [{ type: 'section', text: { type: 'mrkdwn', text: 'Test block' } }],
              text: `Configure channel #${slackChannel?.name} to get started!`,
            },
          );

          // If the ephemeral message failed to send, throw an error
          if (!sentEphemeral.ok) {
            mockLogger.error(
              `Failed to send ephemeral message to user ${inviter} in channel ${channel}`,
            );
            throw new Error('Failed to send ephemeral message');
          }
        }
      };

      // Execute and expect error
      await expect(
        (handler as any).handleThenaBotJoined({
          inviter: 'inviter-user-id',
          channel: 'test-channel-id',
          slackUser: mockSlackUser,
          installation: mockInstallation,
          organization: mockOrganization,
        }),
      ).rejects.toThrow('Failed to send ephemeral message');

      // Verify error was logged
      expect(mockLogger.error).toHaveBeenCalledWith(
        expect.stringContaining('Failed to send ephemeral message to user'),
      );
    });

    it('should not send ephemeral message when there is no inviter', async () => {
      // Setup test conditions
      mockChannelsRepository.findByCondition.mockResolvedValue(null);
      const mockChannel = {
        id: 'test-channel-db-id',
        name: 'test-channel-name',
        isBotActive: false,
        isBotJoined: false,
      };
      mockSlackAppManagementService.upsertSlackChannel.mockResolvedValue(
        mockChannel,
      );

      // Create a direct implementation for the handler to ensure it runs our mocked methods
      (handler as any).handleThenaBotJoined = async (options: any) => {
        const { channel, installation, organization, inviter } = options;

        // Get the channel from the database
        let slackChannel = await mockChannelsRepository.findByCondition({
          where: { channelId: channel, installation: { id: installation.id } },
        });

        // If the channel is not found, create it and sync it
        if (!slackChannel) {
          slackChannel = await mockSlackAppManagementService.upsertSlackChannel(
            {
              channelId: channel,
              token: installation.botToken,
              installationId: installation.id,
              organizationId: organization.id,
            },
          );
        }

        // If there is no inviter, skip the ephemeral message
        if (!inviter) {
          return;
        }
      };

      // Execute
      await (handler as any).handleThenaBotJoined({
        inviter: undefined,
        channel: 'test-channel-id',
        slackUser: mockSlackUser,
        installation: mockInstallation,
        organization: mockOrganization,
      });

      // Verify no ephemeral message was sent
      expect(mockSlackWebAPIService.sendEphemeral).not.toHaveBeenCalled();
    });

    it('should handle abnormal channel state', async () => {
      // Setup test conditions - abnormal channel state
      const mockChannel = {
        id: 'test-channel-db-id',
        isBotActive: true,
        isBotJoined: false,
      };
      mockChannelsRepository.findByCondition.mockResolvedValue(mockChannel);

      // Create a direct implementation for the handler to ensure it runs our mocked methods
      (handler as any).handleThenaBotJoined = async (options: any) => {
        const { channel, installation } = options;

        // Get the channel from the database
        const slackChannel = await mockChannelsRepository.findByCondition({
          where: { channelId: channel, installation: { id: installation.id } },
        });

        // If the channel is already active, then we don't need to do anything
        if (slackChannel.isBotActive && !slackChannel.isBotJoined) {
          mockLogger.error(
            `[ABNORMAL] The channel ${channel} was marked active but the bot wasn't present`,
          );
          return;
        }
      };

      // Execute
      await (handler as any).handleThenaBotJoined({
        inviter: 'inviter-user-id',
        channel: 'test-channel-id',
        slackUser: mockSlackUser,
        installation: mockInstallation,
        organization: mockOrganization,
      });

      // Verify warning was logged
      expect(mockLogger.error).toHaveBeenCalledWith(
        expect.stringContaining('[ABNORMAL] The channel'),
      );

      // Verify no further actions were taken
      expect(mockChannelsRepository.update).not.toHaveBeenCalled();
      expect(mockSlackWebAPIService.sendEphemeral).not.toHaveBeenCalled();
    });
  });
});
