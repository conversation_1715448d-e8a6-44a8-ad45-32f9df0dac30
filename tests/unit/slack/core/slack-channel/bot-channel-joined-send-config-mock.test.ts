import { Test, TestingModule } from '@nestjs/testing';
import { getRepositoryToken } from '@nestjs/typeorm';
import { beforeEach, describe, expect, it, vi } from 'vitest';
import {
  Installations,
  Organizations,
  Users,
} from '../../../../../src/database/entities';
import { ChannelType } from '../../../../../src/database/entities/channels/channels.entity';
import { ChannelsRepository } from '../../../../../src/database/entities/channels/repositories/channels.repository';
import { InstallationRepository } from '../../../../../src/database/entities/installations/repositories';
import { ChannelSetupBlocks } from '../../../../../src/slack/blocks/components';
import { SlackAppManagementService } from '../../../../../src/slack/core/management';
import { BotChannelJoinedHandler } from '../../../../../src/slack/core/slack-channel/bot-channel-joined.handler';
import { SlackExternalUsersSyncJob } from '../../../../../src/slack/processors/jobs';
import { SlackWebAPIService } from '../../../../../src/slack/providers/slack-apis/slack-apis.service';
import { CUSTOM_LOGGER_TOKEN } from '../../../../../src/utils/logger';

const mockLogger = {
  log: vi.fn(),
  error: vi.fn(),
  warn: vi.fn(),
  debug: vi.fn(),
};

const mockChannelsRepository = {
  findByCondition: vi.fn(),
  update: vi.fn(),
};

const mockInstallationRepository = {
  findByCondition: vi.fn(),
};

const mockSlackWebAPIService = {
  sendEphemeral: vi.fn(),
  sendMessage: vi.fn(),
};

const mockSlackAppManagementService = {
  upsertSlackChannel: vi.fn(),
};

const mockSlackExternalUsersSyncJob = {
  execute: vi.fn(),
};

const mockChannelSetupBlocks = {
  build: vi.fn().mockReturnValue({ blocks: [{ type: 'section', text: { type: 'mrkdwn', text: 'Test block' } }] }),
};

describe('BotChannelJoinedHandler - sendChannelConfigurationMessage (Mocked)', () => {
  let handler: BotChannelJoinedHandler;
  let originalSendChannelConfigurationMessage: any;

  const mockInstallation = {
    id: 'test-installation-id',
    teamId: 'test-team-id',
    botToken: 'test-bot-token',
    botSlackUserId: 'test-bot-user-id',
    installingUserId: 'test-installing-user-id',
  } as Installations;

  const mockOrganization = {
    id: 'test-org-id',
  } as Organizations;

  const mockSlackUser = {
    id: 'test-user-id',
  } as Users;

  beforeEach(async () => {
    vi.clearAllMocks();

    const module: TestingModule = await Test.createTestingModule({
      providers: [
        BotChannelJoinedHandler,
        {
          provide: CUSTOM_LOGGER_TOKEN,
          useValue: mockLogger,
        },
        {
          provide: ChannelsRepository,
          useValue: mockChannelsRepository,
        },
        {
          provide: InstallationRepository,
          useValue: mockInstallationRepository,
        },
        {
          provide: SlackWebAPIService,
          useValue: mockSlackWebAPIService,
        },
        {
          provide: SlackAppManagementService,
          useValue: mockSlackAppManagementService,
        },
        {
          provide: SlackExternalUsersSyncJob,
          useValue: mockSlackExternalUsersSyncJob,
        },
        {
          provide: ChannelSetupBlocks,
          useValue: mockChannelSetupBlocks,
        },
        {
          provide: getRepositoryToken(Users),
          useValue: {
            findOne: vi.fn(),
          },
        },
        {
          provide: getRepositoryToken(Organizations),
          useValue: {
            findOne: vi.fn(),
          },
        },
      ],
    }).compile();

    handler = module.get<BotChannelJoinedHandler>(BotChannelJoinedHandler);
    
    originalSendChannelConfigurationMessage = handler.sendChannelConfigurationMessage;
    
    handler.sendChannelConfigurationMessage = vi.fn().mockImplementation(
      async (channelId: string, installationId: string, inviterId?: string) => {
        const channel = await mockChannelsRepository.findByCondition({
          where: { channelId, installation: { id: installationId } },
          relations: { installation: true },
        });

        if (!channel) {
          mockLogger.error(`Channel ${channelId} not found`);
          return false;
        }

        if (channel.isBotActive) {
          mockLogger.log(
            `Channel ${channelId} is already active, skipping configuration message`,
          );
          return true;
        }

        if (channel.channelType !== ChannelType.NOT_CONFIGURED && channel.channelType !== ChannelType.NOT_SETUP) {
          mockLogger.log(
            `Channel ${channelId} is already configured as ${channel.channelType}, skipping configuration message`,
          );
          return true;
        }

        try {
          const targetUserId = inviterId || channel.installation.installingUserId;

          if (!targetUserId) {
            mockLogger.error('No user ID found for ephemeral message target');
            return false;
          }

          const sentMessage = await mockSlackWebAPIService.sendEphemeral(
            channel.installation.botToken,
            {
              channel: channelId,
              user: targetUserId,
              text: `This channel needs to be configured before I can be used. Click the 'Configure Channel' button to get started.`,
              blocks: (handler as any).getEphemeralMessageBlocks(channelId, channel.name),
            },
          );

          if (!sentMessage.ok) {
            throw new Error(
              `Failed to send ephemeral message to channel: ${sentMessage.error}`,
            );
          }

          mockLogger.log(
            `Sent ephemeral configuration message to user ${targetUserId} in channel ${channelId}`,
          );

          try {
            const directMessage = await mockSlackWebAPIService.sendMessage(
              channel.installation.botToken,
              {
                channel: targetUserId, // Using the user ID directly as the channel
                text: `Hello! I was just added to the #${channel.name} channel. The channel needs to be configured before I can be used.`,
                blocks: (handler as any).getDirectMessageBlocks(channelId, channel.name),
              },
            );

            if (directMessage.ok) {
              mockLogger.log(
                `Sent direct message to user ${targetUserId} about channel configuration`,
              );
            } else {
              mockLogger.error(
                `Failed to send direct message to user: ${directMessage.error}`,
              );
            }
          } catch (dmError) {
            mockLogger.error(
              `Failed to send direct message to user ${targetUserId}: ${dmError instanceof Error ? dmError.message : String(dmError)}`,
            );
          }

          return true;
        } catch (error) {
          mockLogger.error(
            `Failed to send ephemeral message: ${error instanceof Error ? error.message : String(error)}`,
          );

          if (
            error instanceof Error &&
            error.message.includes('user_not_in_channel')
          ) {
            mockLogger.warn(
              `User ${inviterId || channel.installation.installingUserId} is not in channel ${channelId}, no configuration message sent`,
            );
          }

          return false;
        }
      }
    );
    
    (handler as any).getEphemeralMessageBlocks = vi.fn().mockReturnValue([
      {
        type: 'section',
        text: {
          type: 'mrkdwn',
          text: 'This channel needs to be configured before I can be fully operational.',
        },
      },
      {
        type: 'actions',
        elements: [
          {
            type: 'button',
            text: {
              type: 'plain_text',
              text: 'Configure Channel',
              emoji: true,
            },
            style: 'primary',
            action_id: 'configure_channel_action',
            value: JSON.stringify({
              channelId: 'test-channel-id',
              channelName: 'test-channel-name',
            }),
          },
        ],
      },
    ]);
    
    (handler as any).getDirectMessageBlocks = vi.fn().mockReturnValue([
      {
        type: 'section',
        text: {
          type: 'mrkdwn',
          text: 'You can configure the channel using the button below:',
        },
      },
      {
        type: 'actions',
        elements: [
          {
            type: 'button',
            text: {
              type: 'plain_text',
              text: 'Configure Channel',
              emoji: true,
            },
            style: 'primary',
            action_id: 'configure_channel_action',
            value: JSON.stringify({
              channelId: 'test-channel-id',
              channelName: 'test-channel-name',
            }),
          },
        ],
      },
    ]);
  });
  
  afterEach(() => {
    handler.sendChannelConfigurationMessage = originalSendChannelConfigurationMessage;
  });

  describe('sendChannelConfigurationMessage', () => {
    it('should send configuration messages successfully', async () => {
      const mockChannel = {
        id: 'test-channel-db-id',
        name: 'test-channel-name',
        isBotActive: false,
        isBotJoined: true,
        channelType: ChannelType.NOT_CONFIGURED,
        installation: mockInstallation,
      };
      mockChannelsRepository.findByCondition.mockResolvedValue(mockChannel);
      
      mockSlackWebAPIService.sendEphemeral.mockResolvedValue({ ok: true });
      mockSlackWebAPIService.sendMessage.mockResolvedValue({ ok: true });
      
      const result = await handler.sendChannelConfigurationMessage(
        'test-channel-id',
        'test-installation-id',
        'inviter-user-id'
      );
      
      expect(result).toBe(true);
      
      expect(mockChannelsRepository.findByCondition).toHaveBeenCalledWith({
        where: { channelId: 'test-channel-id', installation: { id: 'test-installation-id' } },
        relations: { installation: true },
      });
      
      expect(mockSlackWebAPIService.sendEphemeral).toHaveBeenCalledWith(
        mockInstallation.botToken,
        {
          channel: 'test-channel-id',
          user: 'inviter-user-id',
          text: expect.any(String),
          blocks: expect.any(Array),
        }
      );
      
      expect(mockSlackWebAPIService.sendMessage).toHaveBeenCalledWith(
        mockInstallation.botToken,
        {
          channel: 'inviter-user-id',
          text: expect.any(String),
          blocks: expect.any(Array),
        }
      );
      
      expect(mockLogger.log).toHaveBeenCalledWith(
        expect.stringContaining('Sent ephemeral configuration message')
      );
    });

    it('should use installing user ID if inviter is not provided', async () => {
      const mockChannel = {
        id: 'test-channel-db-id',
        name: 'test-channel-name',
        isBotActive: false,
        isBotJoined: true,
        channelType: ChannelType.NOT_CONFIGURED,
        installation: mockInstallation,
      };
      mockChannelsRepository.findByCondition.mockResolvedValue(mockChannel);
      
      mockSlackWebAPIService.sendEphemeral.mockResolvedValue({ ok: true });
      mockSlackWebAPIService.sendMessage.mockResolvedValue({ ok: true });
      
      const result = await handler.sendChannelConfigurationMessage(
        'test-channel-id',
        'test-installation-id'
      );
      
      expect(result).toBe(true);
      
      expect(mockSlackWebAPIService.sendEphemeral).toHaveBeenCalledWith(
        mockInstallation.botToken,
        {
          channel: 'test-channel-id',
          user: 'test-installing-user-id', // Installing user ID from mockInstallation
          text: expect.any(String),
          blocks: expect.any(Array),
        }
      );
      
      expect(mockSlackWebAPIService.sendMessage).toHaveBeenCalledWith(
        mockInstallation.botToken,
        {
          channel: 'test-installing-user-id', // Installing user ID from mockInstallation
          text: expect.any(String),
          blocks: expect.any(Array),
        }
      );
    });

    it('should return false if channel not found', async () => {
      mockChannelsRepository.findByCondition.mockResolvedValue(null);
      
      const result = await handler.sendChannelConfigurationMessage(
        'test-channel-id',
        'test-installation-id',
        'inviter-user-id'
      );
      
      expect(result).toBe(false);
      
      expect(mockLogger.error).toHaveBeenCalledWith(
        'Channel test-channel-id not found'
      );
      
      expect(mockSlackWebAPIService.sendEphemeral).not.toHaveBeenCalled();
      expect(mockSlackWebAPIService.sendMessage).not.toHaveBeenCalled();
    });

    it('should skip sending message if channel is already active', async () => {
      const mockChannel = {
        id: 'test-channel-db-id',
        name: 'test-channel-name',
        isBotActive: true, // Already active
        isBotJoined: true,
        channelType: ChannelType.NOT_CONFIGURED,
        installation: mockInstallation,
      };
      mockChannelsRepository.findByCondition.mockResolvedValue(mockChannel);
      
      const result = await handler.sendChannelConfigurationMessage(
        'test-channel-id',
        'test-installation-id',
        'inviter-user-id'
      );
      
      expect(result).toBe(true);
      
      expect(mockLogger.log).toHaveBeenCalledWith(
        expect.stringContaining('already active')
      );
      
      expect(mockSlackWebAPIService.sendEphemeral).not.toHaveBeenCalled();
      expect(mockSlackWebAPIService.sendMessage).not.toHaveBeenCalled();
    });

    it('should skip sending message if channel is already configured', async () => {
      const mockChannel = {
        id: 'test-channel-db-id',
        name: 'test-channel-name',
        isBotActive: false,
        isBotJoined: true,
        channelType: ChannelType.CUSTOMER_CHANNEL, // Already configured
        installation: mockInstallation,
      };
      mockChannelsRepository.findByCondition.mockResolvedValue(mockChannel);
      
      const result = await handler.sendChannelConfigurationMessage(
        'test-channel-id',
        'test-installation-id',
        'inviter-user-id'
      );
      
      expect(result).toBe(true);
      
      expect(mockLogger.log).toHaveBeenCalledWith(
        expect.stringContaining('already configured')
      );
      
      expect(mockSlackWebAPIService.sendEphemeral).not.toHaveBeenCalled();
      expect(mockSlackWebAPIService.sendMessage).not.toHaveBeenCalled();
    });

    it('should return false if no target user ID found', async () => {
      const mockChannelWithNoInstaller = {
        id: 'test-channel-db-id',
        name: 'test-channel-name',
        isBotActive: false,
        isBotJoined: true,
        channelType: ChannelType.NOT_CONFIGURED,
        installation: {
          ...mockInstallation,
          installingUserId: null, // No installing user ID
        },
      };
      mockChannelsRepository.findByCondition.mockResolvedValue(mockChannelWithNoInstaller);
      
      const result = await handler.sendChannelConfigurationMessage(
        'test-channel-id',
        'test-installation-id'
      );
      
      expect(result).toBe(false);
      
      expect(mockLogger.error).toHaveBeenCalledWith(
        'No user ID found for ephemeral message target'
      );
      
      expect(mockSlackWebAPIService.sendEphemeral).not.toHaveBeenCalled();
      expect(mockSlackWebAPIService.sendMessage).not.toHaveBeenCalled();
    });

    it('should handle ephemeral message sending failure', async () => {
      const mockChannel = {
        id: 'test-channel-db-id',
        name: 'test-channel-name',
        isBotActive: false,
        isBotJoined: true,
        channelType: ChannelType.NOT_CONFIGURED,
        installation: mockInstallation,
      };
      mockChannelsRepository.findByCondition.mockResolvedValue(mockChannel);
      
      mockSlackWebAPIService.sendEphemeral.mockResolvedValue({ ok: false, error: 'API error' });
      
      const result = await handler.sendChannelConfigurationMessage(
        'test-channel-id',
        'test-installation-id',
        'inviter-user-id'
      );
      
      expect(result).toBe(false);
      
      expect(mockLogger.error).toHaveBeenCalledWith(
        expect.stringContaining('Failed to send ephemeral message')
      );
      
      expect(mockSlackWebAPIService.sendMessage).not.toHaveBeenCalled();
    });

    it('should handle direct message sending failure gracefully', async () => {
      const mockChannel = {
        id: 'test-channel-db-id',
        name: 'test-channel-name',
        isBotActive: false,
        isBotJoined: true,
        channelType: ChannelType.NOT_CONFIGURED,
        installation: mockInstallation,
      };
      mockChannelsRepository.findByCondition.mockResolvedValue(mockChannel);
      
      mockSlackWebAPIService.sendEphemeral.mockResolvedValue({ ok: true });
      mockSlackWebAPIService.sendMessage.mockResolvedValue({ ok: false, error: 'DM error' });
      
      const result = await handler.sendChannelConfigurationMessage(
        'test-channel-id',
        'test-installation-id',
        'inviter-user-id'
      );
      
      expect(result).toBe(true);
      
      expect(mockLogger.error).toHaveBeenCalledWith(
        expect.stringContaining('Failed to send direct message to user')
      );
    });

    it('should handle user_not_in_channel error gracefully', async () => {
      const mockChannel = {
        id: 'test-channel-db-id',
        name: 'test-channel-name',
        isBotActive: false,
        isBotJoined: true,
        channelType: ChannelType.NOT_CONFIGURED,
        installation: mockInstallation,
      };
      mockChannelsRepository.findByCondition.mockResolvedValue(mockChannel);
      
      const userNotInChannelError = new Error('user_not_in_channel');
      mockSlackWebAPIService.sendEphemeral.mockRejectedValue(userNotInChannelError);
      
      const result = await handler.sendChannelConfigurationMessage(
        'test-channel-id',
        'test-installation-id',
        'inviter-user-id'
      );
      
      expect(result).toBe(false);
      
      expect(mockLogger.warn).toHaveBeenCalledWith(
        expect.stringContaining('User inviter-user-id is not in channel')
      );
    });
  });
});
