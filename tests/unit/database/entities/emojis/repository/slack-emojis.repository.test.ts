import { describe, it, expect, beforeEach, vi, Mock } from 'vitest';
import { SlackEmojisRepository } from '../../../../../../src/database/entities/emojis/repository/slack-emojis.repository';
import { Repository } from 'typeorm';
import { SlackEmojis } from '../../../../../../src/database/entities/emojis/slack-emojis.entity';

describe('SlackEmojisRepository', () => {
  let slackEmojisRepository: SlackEmojisRepository;
  let mockTypeOrmRepository: Repository<SlackEmojis>;

  beforeEach(() => {
    mockTypeOrmRepository = {
      find: vi.fn(),
      findOneBy: vi.fn(),
      findOne: vi.fn(),
      save: vi.fn(),
      create: vi.fn(),
      remove: vi.fn(),
      update: vi.fn(),
      findAndCount: vi.fn(),
    } as unknown as Repository<SlackEmojis>;

    slackEmojisRepository = new SlackEmojisRepository(mockTypeOrmRepository);
  });

  describe('inheritance', () => {
    it('should extend BaseAbstractRepository', () => {
      expect(slackEmojisRepository.findAll).toBeDefined();
      expect(slackEmojisRepository.findOneById).toBeDefined();
      expect(slackEmojisRepository.findByCondition).toBeDefined();
      expect(slackEmojisRepository.create).toBeDefined();
      expect(slackEmojisRepository.createMany).toBeDefined();
      expect(slackEmojisRepository.update).toBeDefined();
      expect(slackEmojisRepository.remove).toBeDefined();
    });
  });

  describe('findAll', () => {
    it('should call the repository find method with the provided options', async () => {
      const options = { where: { id: '123' } };
      const expectedResult = [{ id: '123', name: 'smile' }] as SlackEmojis[];
      (mockTypeOrmRepository.find as Mock).mockResolvedValue(expectedResult);

      const result = await slackEmojisRepository.findAll(options);

      expect(mockTypeOrmRepository.find).toHaveBeenCalledWith(options);
      expect(result).toEqual(expectedResult);
    });
  });

  describe('findOneById', () => {
    it('should call the repository findOneBy method with the provided id', async () => {
      const id = '123';
      const expectedResult = { id: '123', name: 'smile' } as SlackEmojis;
      (mockTypeOrmRepository.findOneBy as Mock).mockResolvedValue(expectedResult);

      const result = await slackEmojisRepository.findOneById(id);

      expect(mockTypeOrmRepository.findOneBy).toHaveBeenCalledWith({ id });
      expect(result).toEqual(expectedResult);
    });
  });

  describe('findByCondition', () => {
    it('should call the repository findOne method with the provided options', async () => {
      const options = { where: { id: '123' } };
      const expectedResult = { id: '123', name: 'smile' } as SlackEmojis;
      (mockTypeOrmRepository.findOne as Mock).mockResolvedValue(expectedResult);

      const result = await slackEmojisRepository.findByCondition(options);

      expect(mockTypeOrmRepository.findOne).toHaveBeenCalledWith(options);
      expect(result).toEqual(expectedResult);
    });
  });

  describe('create', () => {
    it('should call the repository create method with the provided data', () => {
      const createData = { name: 'smile' };
      const createdEntity = { id: '123', ...createData } as SlackEmojis;
      (mockTypeOrmRepository.create as Mock).mockReturnValue(createdEntity);

      const result = slackEmojisRepository.create(createData);

      expect(mockTypeOrmRepository.create).toHaveBeenCalledWith(createData);
      expect(result).toEqual(createdEntity);
    });
  });

  describe('save', () => {
    it('should call the repository save method with the provided data', async () => {
      const saveData = { id: '123', name: 'smile' } as SlackEmojis;
      (mockTypeOrmRepository.save as Mock).mockResolvedValue(saveData);

      const result = await slackEmojisRepository.save(saveData);

      expect(mockTypeOrmRepository.save).toHaveBeenCalledWith(saveData);
      expect(result).toEqual(saveData);
    });
  });

  describe('update', () => {
    it('should call the repository update method with the provided id and data', async () => {
      const id = '123';
      const updateData = { name: 'grin' };
      const updateResult = { affected: 1 };
      (mockTypeOrmRepository.update as Mock).mockResolvedValue(updateResult);

      const result = await slackEmojisRepository.update(id, updateData);

      expect(mockTypeOrmRepository.update).toHaveBeenCalledWith(id, updateData);
      expect(result).toEqual(updateResult);
    });
  });

  describe('remove', () => {
    it('should call the repository remove method with the provided entity', async () => {
      const entity = { id: '123', name: 'smile' } as SlackEmojis;
      (mockTypeOrmRepository.remove as Mock).mockResolvedValue(entity);

      const result = await slackEmojisRepository.remove(entity);

      expect(mockTypeOrmRepository.remove).toHaveBeenCalledWith(entity);
      expect(result).toEqual(entity);
    });
  });
});
