import { Test, TestingModule } from '@nestjs/testing';
import { getRepositoryToken } from '@nestjs/typeorm';
import { Repository } from 'typeorm';
import { InstallationRepository } from '../../../../../../src/database/entities/installations/repositories/installation.repository';
import { Installations, InstallationStatus } from '../../../../../../src/database/entities/installations/installations.entity';
import { beforeEach, describe, expect, it, vi } from 'vitest';

describe('InstallationRepository', () => {
  let repository: InstallationRepository;
  let mockTypeOrmRepository: Repository<Installations>;

  // Mock installation data for testing
  const mockInstallation: Partial<Installations> = {
    id: '1',
    name: 'Test Workspace',
    teamId: 'T12345',
    teamName: 'Test Team',
    enterpriseId: null,
    teamInfo: { id: 'T12345', name: 'Test Team', domain: 'test-team' },
    status: InstallationStatus.SYNCED,
    installingUserId: 'U12345',
    installingUserSlackId: 'U12345',
    installingUserName: 'Test User',
    botToken: 'xoxb-123456789',
    botSlackId: 'B12345',
    botSlackUserId: 'U67890',
    slackAppAuthToken: 'xoxa-123456789',
    disconnected: false
  };

  const mockInstallations: Partial<Installations>[] = [
    mockInstallation,
    {
      ...mockInstallation,
      id: '2',
      teamId: 'T67890',
      teamName: 'Another Team'
    },
    {
      ...mockInstallation,
      id: '3',
      teamId: 'T11111',
      teamName: 'Disconnected Team',
      disconnected: true,
      disconnectedOn: new Date()
    }
  ];

  beforeEach(async () => {
    // Create mock implementation for TypeORM repository methods
    mockTypeOrmRepository = {
      save: vi.fn(),
      upsert: vi.fn(),
      insert: vi.fn(),
      update: vi.fn(),
      count: vi.fn(),
      create: vi.fn(),
      createQueryBuilder: vi.fn(),
      findOneBy: vi.fn(),
      findOne: vi.fn(),
      find: vi.fn(),
      remove: vi.fn(),
      softDelete: vi.fn(),
    } as unknown as Repository<Installations>;

    // Create a NestJS testing module
    const module: TestingModule = await Test.createTestingModule({
      providers: [
        InstallationRepository,
        {
          provide: getRepositoryToken(Installations),
          useValue: mockTypeOrmRepository,
        },
      ],
    }).compile();

    repository = module.get<InstallationRepository>(InstallationRepository);
  });

  describe('CRUD operations', () => {
    describe('Create operations', () => {
      it('should create a new installation entity', () => {
        // Arrange
        (mockTypeOrmRepository.create as any).mockReturnValue(mockInstallation);

        // Act
        const result = repository.create(mockInstallation);

        // Assert
        expect(result).toEqual(mockInstallation);
        expect(mockTypeOrmRepository.create).toHaveBeenCalledWith(mockInstallation);
      });

      it('should save a new installation to the database', async () => {
        // Arrange
        (mockTypeOrmRepository.save as any).mockResolvedValue(mockInstallation);

        // Act
        const result = await repository.save(mockInstallation);

        // Assert
        expect(result).toEqual(mockInstallation);
        expect(mockTypeOrmRepository.save).toHaveBeenCalledWith(mockInstallation);
      });

      it('should insert a new installation without returning the entity', async () => {
        // Arrange
        const insertResult = { identifiers: [{ id: '1' }], generatedMaps: [], raw: {} };
        (mockTypeOrmRepository.insert as any).mockResolvedValue(insertResult);

        // Act
        const result = await repository.insert(mockInstallation);

        // Assert
        expect(result).toEqual(insertResult);
        expect(mockTypeOrmRepository.insert).toHaveBeenCalledWith(mockInstallation);
      });

      it('should upsert an installation (create if not exists, update if exists)', async () => {
        // Arrange
        const upsertResult = { identifiers: [{ id: '1' }], generatedMaps: [], raw: {} };
        (mockTypeOrmRepository.upsert as any).mockResolvedValue(upsertResult);
        
        // Act
        const result = await repository.upsert(mockInstallation, { conflictPaths: ['teamId'] });

        // Assert
        expect(result).toEqual(upsertResult);
        expect(mockTypeOrmRepository.upsert).toHaveBeenCalledWith(
          mockInstallation, 
          { conflictPaths: ['teamId'] }
        );
      });

      it('should create multiple installation entities', () => {
        // Arrange
        (mockTypeOrmRepository.create as any).mockReturnValue(mockInstallations);

        // Act
        const result = repository.createMany(mockInstallations);

        // Assert
        expect(result).toEqual(mockInstallations);
        expect(mockTypeOrmRepository.create).toHaveBeenCalledWith(mockInstallations);
      });

      it('should save multiple installations to the database', async () => {
        // Arrange
        (mockTypeOrmRepository.save as any).mockResolvedValue(mockInstallations);

        // Act
        const result = await repository.saveMany(mockInstallations as Installations[]);

        // Assert
        expect(result).toEqual(mockInstallations);
        expect(mockTypeOrmRepository.save).toHaveBeenCalledWith(mockInstallations);
      });
    });

    describe('Read operations', () => {
      it('should find one installation by id', async () => {
        // Arrange
        (mockTypeOrmRepository.findOneBy as any).mockResolvedValue(mockInstallation);

        // Act
        const result = await repository.findOneById('1');

        // Assert
        expect(result).toEqual(mockInstallation);
        expect(mockTypeOrmRepository.findOneBy).toHaveBeenCalledWith({ id: '1' });
      });

      it('should return null when installation is not found by id', async () => {
        // Arrange
        (mockTypeOrmRepository.findOneBy as any).mockResolvedValue(null);

        // Act
        const result = await repository.findOneById('999');

        // Assert
        expect(result).toBeNull();
        expect(mockTypeOrmRepository.findOneBy).toHaveBeenCalledWith({ id: '999' });
      });

      it('should find one installation by condition', async () => {
        // Arrange
        (mockTypeOrmRepository.findOne as any).mockResolvedValue(mockInstallation);
        const condition = { where: { teamId: 'T12345' } };

        // Act
        const result = await repository.findByCondition(condition);

        // Assert
        expect(result).toEqual(mockInstallation);
        expect(mockTypeOrmRepository.findOne).toHaveBeenCalledWith(condition);
      });

      it('should find all installations', async () => {
        // Arrange
        (mockTypeOrmRepository.find as any).mockResolvedValue(mockInstallations);

        // Act
        const result = await repository.findAll();

        // Assert
        expect(result).toEqual(mockInstallations);
        expect(mockTypeOrmRepository.find).toHaveBeenCalled();
      });

      it('should find installations with specific options', async () => {
        // Arrange
        (mockTypeOrmRepository.find as any).mockResolvedValue([mockInstallation]);
        const options = { where: { disconnected: false } };

        // Act
        const result = await repository.findAll(options);

        // Assert
        expect(result).toEqual([mockInstallation]);
        expect(mockTypeOrmRepository.find).toHaveBeenCalledWith(options);
      });

      it('should find installations with relations', async () => {
        // Arrange
        const withRelations = {
          ...mockInstallation,
          organization: { id: 'org1', name: 'Test Org' }
        };
        (mockTypeOrmRepository.find as any).mockResolvedValue([withRelations]);
        const options = { relations: ['organization'] };

        // Act
        const result = await repository.findWithRelations(options);

        // Assert
        expect(result).toEqual([withRelations]);
        expect(mockTypeOrmRepository.find).toHaveBeenCalledWith(options);
      });

      it('should count installations with specific options', async () => {
        // Arrange
        (mockTypeOrmRepository.count as any).mockResolvedValue(3);
        const options = { where: { status: InstallationStatus.SYNCED } };

        // Act
        const result = await repository.count(options);

        // Assert
        expect(result).toBe(3);
        expect(mockTypeOrmRepository.count).toHaveBeenCalledWith(options);
      });
    });

    describe('Update operations', () => {
      it('should update an installation by id', async () => {
        // Arrange
        const updateResult = { affected: 1, raw: {} };
        (mockTypeOrmRepository.update as any).mockResolvedValue(updateResult);
        
        const updates = { 
          disconnected: true, 
          disconnectedOn: new Date(),
          status: InstallationStatus.ERROR 
        };

        // Act
        const result = await repository.update({ id: '1' }, updates);

        // Assert
        expect(result).toEqual(updateResult);
        expect(mockTypeOrmRepository.update).toHaveBeenCalledWith({ id: '1' }, updates);
      });

      it('should update multiple installations by condition', async () => {
        // Arrange
        const updateResult = { affected: 2, raw: {} };
        (mockTypeOrmRepository.update as any).mockResolvedValue(updateResult);
        
        const updates = { status: InstallationStatus.SYNCED };

        // Act
        const result = await repository.update(
          { status: InstallationStatus.SYNCING }, 
          updates
        );

        // Assert
        expect(result).toEqual(updateResult);
        expect(mockTypeOrmRepository.update).toHaveBeenCalledWith(
          { status: InstallationStatus.SYNCING },
          updates
        );
      });
    });

    describe('Delete operations', () => {
      it('should remove an installation from the database', async () => {
        // Arrange
        (mockTypeOrmRepository.remove as any).mockResolvedValue(mockInstallation);

        // Act
        const result = await repository.remove(mockInstallation as Installations);

        // Assert
        expect(result).toEqual(mockInstallation);
        expect(mockTypeOrmRepository.remove).toHaveBeenCalledWith(mockInstallation);
      });

      it('should soft delete an installation', async () => {
        // Arrange
        const deleteResult = { affected: 1, raw: {} };
        (mockTypeOrmRepository.softDelete as any).mockResolvedValue(deleteResult);

        // Act
        const result = await repository.softDelete({
            id: '1',
            organizationId: ''
        });

        // Assert
        expect(result).toEqual(deleteResult);
        expect(mockTypeOrmRepository.softDelete).toHaveBeenCalledWith({ id: '1', organizationId: '' });
      });
    });
  });
});
