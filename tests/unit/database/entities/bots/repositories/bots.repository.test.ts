import { describe, it, expect, beforeEach, vi, Mock } from 'vitest';
import { BotsRepository } from '../../../../../../src/database/entities/bots/repositories/bots.repository';
import { Repository } from 'typeorm';
import { Bots } from '../../../../../../src/database/entities/bots/bots.entity';

describe('BotsRepository', () => {
  let botsRepository: BotsRepository;
  let mockTypeOrmRepository: Repository<Bots>;

  beforeEach(() => {
    mockTypeOrmRepository = {
      find: vi.fn(),
      findOneBy: vi.fn(),
      findOne: vi.fn(),
      save: vi.fn(),
      create: vi.fn(),
      remove: vi.fn(),
      update: vi.fn(),
      findAndCount: vi.fn(),
    } as unknown as Repository<Bots>;

    botsRepository = new BotsRepository(mockTypeOrmRepository);
  });

  describe('inheritance', () => {
    it('should extend BaseAbstractRepository', () => {
      expect(botsRepository.findAll).toBeDefined();
      expect(botsRepository.findOneById).toBeDefined();
      expect(botsRepository.findByCondition).toBeDefined();
      expect(botsRepository.create).toBeDefined();
      expect(botsRepository.createMany).toBeDefined();
      expect(botsRepository.update).toBeDefined();
      expect(botsRepository.remove).toBeDefined();
    });
  });

  describe('findAll', () => {
    it('should call the repository find method with the provided options', async () => {
      const options = { where: { id: '123' } };
      const expectedResult = [{ id: '123', name: 'Test Bot' }] as Bots[];
      (mockTypeOrmRepository.find as Mock).mockResolvedValue(expectedResult);

      const result = await botsRepository.findAll(options);

      expect(mockTypeOrmRepository.find).toHaveBeenCalledWith(options);
      expect(result).toEqual(expectedResult);
    });
  });

  describe('findOneById', () => {
    it('should call the repository findOneBy method with the provided id', async () => {
      const id = '123';
      const expectedResult = { id: '123', name: 'Test Bot' } as Bots;
      (mockTypeOrmRepository.findOneBy as Mock).mockResolvedValue(expectedResult);

      const result = await botsRepository.findOneById(id);

      expect(mockTypeOrmRepository.findOneBy).toHaveBeenCalledWith({ id });
      expect(result).toEqual(expectedResult);
    });
  });

  describe('findByCondition', () => {
    it('should call the repository findOne method with the provided options', async () => {
      const options = { where: { id: '123' } };
      const expectedResult = { id: '123', name: 'Test Bot' } as Bots;
      (mockTypeOrmRepository.findOne as Mock).mockResolvedValue(expectedResult);

      const result = await botsRepository.findByCondition(options);

      expect(mockTypeOrmRepository.findOne).toHaveBeenCalledWith(options);
      expect(result).toEqual(expectedResult);
    });
  });

  describe('create', () => {
    it('should call the repository create method with the provided data', () => {
      const createData = { name: 'New Bot' };
      const createdEntity = { id: '123', ...createData } as Bots;
      (mockTypeOrmRepository.create as Mock).mockReturnValue(createdEntity);

      const result = botsRepository.create(createData);

      expect(mockTypeOrmRepository.create).toHaveBeenCalledWith(createData);
      expect(result).toEqual(createdEntity);
    });
  });

  describe('save', () => {
    it('should call the repository save method with the provided data', async () => {
      const saveData = { id: '123', name: 'Test Bot' } as Bots;
      (mockTypeOrmRepository.save as Mock).mockResolvedValue(saveData);

      const result = await botsRepository.save(saveData);

      expect(mockTypeOrmRepository.save).toHaveBeenCalledWith(saveData);
      expect(result).toEqual(saveData);
    });
  });

  describe('update', () => {
    it('should call the repository update method with the provided id and data', async () => {
      const id = '123';
      const updateData = { name: 'Updated Bot' };
      const updateResult = { affected: 1 };
      (mockTypeOrmRepository.update as Mock).mockResolvedValue(updateResult);

      const result = await botsRepository.update(id, updateData);

      expect(mockTypeOrmRepository.update).toHaveBeenCalledWith(id, updateData);
      expect(result).toEqual(updateResult);
    });
  });

  describe('remove', () => {
    it('should call the repository remove method with the provided entity', async () => {
      const entity = { id: '123', name: 'Test Bot' } as Bots;
      (mockTypeOrmRepository.remove as Mock).mockResolvedValue(entity);

      const result = await botsRepository.remove(entity);

      expect(mockTypeOrmRepository.remove).toHaveBeenCalledWith(entity);
      expect(result).toEqual(entity);
    });
  });
});
