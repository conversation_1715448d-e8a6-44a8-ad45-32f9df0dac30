import { Test, TestingModule } from '@nestjs/testing';
import { getRepositoryToken } from '@nestjs/typeorm';
import { Repository } from 'typeorm';
import { Mock, beforeEach, describe, expect, it, vi } from 'vitest';
import { CommentThreadMappings } from '../../../../../../src/database/entities/mappings/comment-thread-mappings.entity';
import { CommentThreadMapsRepository } from '../../../../../../src/database/entities/mappings/repositories/comment-thread-maps.repository';
import { TransactionContext } from '../../../../../../src/database/common/transaction-interface';

describe('CommentThreadMapsRepository', () => {
  let repository: CommentThreadMapsRepository;
  let mockTypeOrmRepository: Repository<CommentThreadMappings>;

  beforeEach(async () => {
    // Create mock implementation for TypeORM repository methods
    mockTypeOrmRepository = {
      save: vi.fn(),
      create: vi.fn(),
      findOne: vi.fn(),
      findOneBy: vi.fn(),
      find: vi.fn(),
      update: vi.fn(),
      softDelete: vi.fn(),
      count: vi.fn(),
      exists: vi.fn(),
      remove: vi.fn(),
      createQueryBuilder: vi.fn(),
      upsert: vi.fn(),
    } as unknown as Repository<CommentThreadMappings>;

    // Create a NestJS testing module
    const module: TestingModule = await Test.createTestingModule({
      providers: [
        CommentThreadMapsRepository,
        {
          provide: getRepositoryToken(CommentThreadMappings),
          useValue: mockTypeOrmRepository,
        },
      ],
    }).compile();

    repository = module.get<CommentThreadMapsRepository>(CommentThreadMapsRepository);
  });

  describe('Inherited CRUD operations', () => {
    it('should save a comment thread mapping', async () => {
      // Arrange
      const mapping = {
        platformCommentThreadId: 'thread-123',
        platformCommentTicketId: 'ticket-123',
        slackThreadId: '**********.123456',
        slackChannelId: 'C12345',
      };

      const savedMapping = {
        id: 'mapping-id',
        ...mapping,
      };

      (mockTypeOrmRepository.save as Mock).mockResolvedValue(savedMapping);

      // Act
      const result = await repository.save(mapping);

      // Assert
      expect(result).toEqual(savedMapping);
      expect(mockTypeOrmRepository.save).toHaveBeenCalledWith(mapping);
    });

    it('should create a new comment thread mapping entity', () => {
      // Arrange
      const mapping = {
        platformCommentThreadId: 'thread-123',
        platformCommentTicketId: 'ticket-123',
        slackThreadId: '**********.123456',
        slackChannelId: 'C12345',
      };

      const createdMapping = {
        id: 'mapping-id',
        ...mapping,
      };

      (mockTypeOrmRepository.create as Mock).mockReturnValue(createdMapping);

      // Act
      const result = repository.create(mapping);

      // Assert
      expect(result).toEqual(createdMapping);
      expect(mockTypeOrmRepository.create).toHaveBeenCalledWith(mapping);
    });

    it('should find one comment thread mapping by id', async () => {
      // Arrange
      const mappingId = 'mapping-id';
      const mapping = {
        id: mappingId,
        platformCommentThreadId: 'thread-123',
        platformCommentTicketId: 'ticket-123',
        slackThreadId: '**********.123456',
        slackChannelId: 'C12345',
      };

      (mockTypeOrmRepository.findOneBy as Mock).mockResolvedValue(mapping);

      // Act
      const result = await repository.findOneById(mappingId);

      // Assert
      expect(result).toEqual(mapping);
      expect(mockTypeOrmRepository.findOneBy).toHaveBeenCalledWith({
        id: mappingId,
      });
    });

    it('should find comment thread mappings by condition', async () => {
      // Arrange
      const condition = {
        where: { slackThreadId: '**********.123456' },
      };

      const mapping = {
        id: 'mapping-id',
        platformCommentThreadId: 'thread-123',
        platformCommentTicketId: 'ticket-123',
        slackThreadId: '**********.123456',
        slackChannelId: 'C12345',
      };

      (mockTypeOrmRepository.findOne as Mock).mockResolvedValue(mapping);

      // Act
      const result = await repository.findByCondition(condition);

      // Assert
      expect(result).toEqual(mapping);
      expect(mockTypeOrmRepository.findOne).toHaveBeenCalledWith(condition);
    });

    it('should find all comment thread mappings', async () => {
      // Arrange
      const mappings = [
        {
          id: 'mapping-id-1',
          platformCommentThreadId: 'thread-123',
          platformCommentTicketId: 'ticket-123',
          slackThreadId: '**********.123456',
          slackChannelId: 'C12345',
        },
        {
          id: 'mapping-id-2',
          platformCommentThreadId: 'thread-456',
          platformCommentTicketId: 'ticket-456',
          slackThreadId: '**********.654321',
          slackChannelId: 'C67890',
        },
      ];

      (mockTypeOrmRepository.find as Mock).mockResolvedValue(mappings);

      // Act
      const result = await repository.findAll();

      // Assert
      expect(result).toEqual(mappings);
      expect(mockTypeOrmRepository.find).toHaveBeenCalled();
    });

    it('should update a comment thread mapping', async () => {
      // Arrange
      const criteria = { id: 'mapping-id' };
      const partialEntity = { slackThreadId: '**********.999999' };
      const updateResult = { affected: 1 };

      (mockTypeOrmRepository.update as Mock).mockResolvedValue(updateResult);

      // Act
      const result = await repository.update(criteria, partialEntity);

      // Assert
      expect(result).toEqual(updateResult);
      expect(mockTypeOrmRepository.update).toHaveBeenCalledWith(criteria, partialEntity);
    });

    it('should remove a comment thread mapping', async () => {
      // Arrange
      const mapping = {
        id: 'mapping-id',
        platformCommentThreadId: 'thread-123',
        platformCommentTicketId: 'ticket-123',
        slackThreadId: '**********.123456',
        slackChannelId: 'C12345',
      };

      (mockTypeOrmRepository.remove as Mock).mockResolvedValue(mapping);

      // Act
      const result = await repository.remove(mapping);

      // Assert
      expect(result).toEqual(mapping);
      expect(mockTypeOrmRepository.remove).toHaveBeenCalledWith(mapping);
    });

    it('should soft delete a comment thread mapping', async () => {
      // Arrange
      const criteria = { id: 'mapping-id' };
      const deleteResult = { affected: 1 };

      (mockTypeOrmRepository.softDelete as Mock).mockResolvedValue(deleteResult);

      // Act
      const result = await repository.softDelete(criteria);

      // Assert
      expect(result).toEqual(deleteResult);
      expect(mockTypeOrmRepository.softDelete).toHaveBeenCalledWith(criteria);
    });

    it('should check if a comment thread mapping exists', async () => {
      // Arrange
      const options = {
        where: { slackThreadId: '**********.123456' },
      };

      (mockTypeOrmRepository.exists as Mock).mockResolvedValue(true);

      // Act
      const result = await repository.exists(options);

      // Assert
      expect(result).toBe(true);
      expect(mockTypeOrmRepository.exists).toHaveBeenCalledWith(options);
    });
  });

  describe('Transaction operations', () => {
    it('should save a comment thread mapping with transaction', async () => {
      // Arrange
      const mapping = {
        platformCommentThreadId: 'thread-123',
        platformCommentTicketId: 'ticket-123',
        slackThreadId: '**********.123456',
        slackChannelId: 'C12345',
      };

      const savedMapping = {
        id: 'mapping-id',
        ...mapping,
      };

      const mockTxnContext = {
        manager: {
          save: vi.fn().mockResolvedValue(savedMapping),
        },
      } as unknown as TransactionContext;

      // Act
      const result = await repository.saveWithTxn(mockTxnContext, mapping);

      // Assert
      expect(result).toEqual(savedMapping);
      expect(mockTxnContext.manager.save).toHaveBeenCalled();
    });

    it('should remove a comment thread mapping with transaction', async () => {
      // Arrange
      const mapping = {
        id: 'mapping-id',
        platformCommentThreadId: 'thread-123',
        platformCommentTicketId: 'ticket-123',
        slackThreadId: '**********.123456',
        slackChannelId: 'C12345',
      };

      const mockTxnContext = {
        manager: {
          remove: vi.fn().mockResolvedValue(mapping),
        },
      } as unknown as TransactionContext;

      // Act
      const result = await repository.removeWithTxn(mockTxnContext, mapping);

      // Assert
      expect(result).toEqual(mapping);
      expect(mockTxnContext.manager.remove).toHaveBeenCalled();
    });

    it('should soft delete a comment thread mapping with transaction', async () => {
      // Arrange
      const criteria = { id: 'mapping-id' };
      const deleteResult = { affected: 1 };

      const mockTxnContext = {
        manager: {
          softDelete: vi.fn().mockResolvedValue(deleteResult),
        },
      } as unknown as TransactionContext;

      // Act
      const result = await repository.softDeleteWithTxn(mockTxnContext, criteria);

      // Assert
      expect(result).toEqual(deleteResult);
      expect(mockTxnContext.manager.softDelete).toHaveBeenCalled();
    });
  });
});
