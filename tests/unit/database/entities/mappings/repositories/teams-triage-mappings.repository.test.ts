import { DataSource, Repository } from 'typeorm';
import { Mock, beforeEach, describe, expect, it, vi } from 'vitest';
import { BotCtx } from '../../../../../../src/auth/interfaces/context.interface';
import { TeamTriageRuleMapping } from '../../../../../../src/database/entities/mappings/teams-triage-mappings.entity';
import { TeamTriageRuleMappingRepository } from '../../../../../../src/database/entities/mappings/repositories/teams-triage-mappings.repository';

describe('TeamTriageRuleMappingRepository', () => {
  let repository: TeamTriageRuleMappingRepository;
  let mockDataSource: DataSource;
  let mockEntityManager: any;

  beforeEach(() => {
    // Reset mocks before each test
    vi.resetAllMocks();

    // Create mock implementations
    mockEntityManager = {
      find: vi.fn(),
      findOne: vi.fn(),
      save: vi.fn(),
      update: vi.fn(),
      delete: vi.fn(),
      softDelete: vi.fn(),
    };

    mockDataSource = {
      createEntityManager: vi.fn().mockReturnValue(mockEntityManager),
    } as unknown as DataSource;

    // Create repository instance
    repository = new TeamTriageRuleMappingRepository(mockDataSource);

    // Mock the repository methods
    repository.find = vi.fn();
  });

  describe('findTeamMappings', () => {
    it('should find team mappings for a platform team', async () => {
      // Arrange
      const platformTeamId = 'team-id';
      const botCtx: BotCtx = {
        installation: { id: 'installation-id' },
        organization: { id: 'organization-id' },
      };

      const mappings = [
        {
          id: 'mapping-id-1',
          platformTeam: { id: platformTeamId },
          installation: { id: 'installation-id' },
          organization: { id: 'organization-id' },
          triageChannels: [{ id: 'channel-id-1' }],
        },
        {
          id: 'mapping-id-2',
          platformTeam: { id: platformTeamId },
          installation: { id: 'installation-id' },
          organization: { id: 'organization-id' },
          triageChannels: [{ id: 'channel-id-2' }],
        },
      ];

      (repository.find as Mock).mockResolvedValue(mappings);

      // Act
      const result = await repository.findTeamMappings(platformTeamId, botCtx);

      // Assert
      expect(result).toEqual(mappings);
      expect(repository.find).toHaveBeenCalledWith({
        where: {
          platformTeam: { id: platformTeamId },
          installation: { id: 'installation-id' },
          organization: { id: 'organization-id' },
        },
        relations: ['triageChannels'],
      });
    });

    it('should return an empty array when no mappings are found', async () => {
      // Arrange
      const platformTeamId = 'team-id';
      const botCtx: BotCtx = {
        installation: { id: 'installation-id' },
        organization: { id: 'organization-id' },
      };

      (repository.find as Mock).mockResolvedValue([]);

      // Act
      const result = await repository.findTeamMappings(platformTeamId, botCtx);

      // Assert
      expect(result).toEqual([]);
      expect(repository.find).toHaveBeenCalled();
    });
  });

  describe('findActiveRulesForTeam', () => {
    it('should find active triage rules for a platform team', async () => {
      // Arrange
      const platformTeamId = 'team-id';
      const botCtx: BotCtx = {
        installation: { id: 'installation-id' },
        organization: { id: 'organization-id' },
      };

      const mappings = [
        {
          id: 'mapping-id-1',
          platformTeam: { id: platformTeamId },
          installation: { id: 'installation-id' },
          organization: { id: 'organization-id' },
          isEnabled: true,
          triageChannels: [{ id: 'channel-id-1' }],
        },
      ];

      (repository.find as Mock).mockResolvedValue(mappings);

      // Act
      const result = await repository.findActiveRulesForTeam(platformTeamId, botCtx);

      // Assert
      expect(result).toEqual(mappings);
      expect(repository.find).toHaveBeenCalledWith({
        where: {
          platformTeam: { id: platformTeamId },
          installation: { id: 'installation-id' },
          organization: { id: 'organization-id' },
          isEnabled: true,
        },
        relations: ['triageChannels'],
      });
    });

    it('should return an empty array when no active rules are found', async () => {
      // Arrange
      const platformTeamId = 'team-id';
      const botCtx: BotCtx = {
        installation: { id: 'installation-id' },
        organization: { id: 'organization-id' },
      };

      (repository.find as Mock).mockResolvedValue([]);

      // Act
      const result = await repository.findActiveRulesForTeam(platformTeamId, botCtx);

      // Assert
      expect(result).toEqual([]);
      expect(repository.find).toHaveBeenCalled();
    });
  });

  describe('Inherited Repository methods', () => {
    it('should inherit methods from TypeORM Repository', () => {
      // Assert
      expect(repository).toBeInstanceOf(Repository);
      expect(repository.find).toBeDefined();
      expect(repository.findOne).toBeDefined();
      expect(repository.save).toBeDefined();
      expect(repository.update).toBeDefined();
      expect(repository.delete).toBeDefined();
      expect(repository.softDelete).toBeDefined();
    });
  });
});
