import { Test, TestingModule } from '@nestjs/testing';
import { getRepositoryToken } from '@nestjs/typeorm';
import { Repository } from 'typeorm';
import { Mock, beforeEach, describe, expect, it, vi } from 'vitest';
import { PlatformTeamsToChannelMappings, TeamRelationshipType } from '../../../../../../src/database/entities/mappings/platform-teams-to-channel-mappings.entity';
import { TeamChannelMapsRepository } from '../../../../../../src/database/entities/mappings/repositories/team-channel-maps.repository';
import { TransactionContext } from '../../../../../../src/database/common/transaction-interface';

describe('TeamChannelMapsRepository', () => {
  let repository: TeamChannelMapsRepository;
  let mockTypeOrmRepository: Repository<PlatformTeamsToChannelMappings>;

  beforeEach(async () => {
    // Create mock implementation for TypeORM repository methods
    mockTypeOrmRepository = {
      save: vi.fn(),
      create: vi.fn(),
      findOne: vi.fn(),
      findOneBy: vi.fn(),
      find: vi.fn(),
      update: vi.fn(),
      softDelete: vi.fn(),
      count: vi.fn(),
      exists: vi.fn(),
      remove: vi.fn(),
      createQueryBuilder: vi.fn(),
      upsert: vi.fn(),
    } as unknown as Repository<PlatformTeamsToChannelMappings>;

    // Create a NestJS testing module
    const module: TestingModule = await Test.createTestingModule({
      providers: [
        TeamChannelMapsRepository,
        {
          provide: getRepositoryToken(PlatformTeamsToChannelMappings),
          useValue: mockTypeOrmRepository,
        },
      ],
    }).compile();

    repository = module.get<TeamChannelMapsRepository>(TeamChannelMapsRepository);
  });

  describe('Inherited CRUD operations', () => {
    it('should save a team channel mapping', async () => {
      // Arrange
      const mapping = {
        relationshipType: TeamRelationshipType.PRIMARY,
        platformTeam: { id: 'team-id' },
        channel: { id: 'channel-id' },
        installation: { id: 'installation-id' },
      };

      const savedMapping = {
        id: 'mapping-id',
        ...mapping,
      };

      (mockTypeOrmRepository.save as Mock).mockResolvedValue(savedMapping);

      // Act
      const result = await repository.save(mapping);

      // Assert
      expect(result).toEqual(savedMapping);
      expect(mockTypeOrmRepository.save).toHaveBeenCalledWith(mapping);
    });

    it('should create a new team channel mapping entity', () => {
      // Arrange
      const mapping = {
        relationshipType: TeamRelationshipType.SECONDARY,
        platformTeam: { id: 'team-id' },
        channel: { id: 'channel-id' },
        installation: { id: 'installation-id' },
      };

      const createdMapping = {
        id: 'mapping-id',
        ...mapping,
      };

      (mockTypeOrmRepository.create as Mock).mockReturnValue(createdMapping);

      // Act
      const result = repository.create(mapping);

      // Assert
      expect(result).toEqual(createdMapping);
      expect(mockTypeOrmRepository.create).toHaveBeenCalledWith(mapping);
    });

    it('should find one team channel mapping by id', async () => {
      // Arrange
      const mappingId = 'mapping-id';
      const mapping = {
        id: mappingId,
        relationshipType: TeamRelationshipType.PRIMARY,
        platformTeam: { id: 'team-id' },
        channel: { id: 'channel-id' },
        installation: { id: 'installation-id' },
      };

      (mockTypeOrmRepository.findOneBy as Mock).mockResolvedValue(mapping);

      // Act
      const result = await repository.findOneById(mappingId);

      // Assert
      expect(result).toEqual(mapping);
      expect(mockTypeOrmRepository.findOneBy).toHaveBeenCalledWith({
        id: mappingId,
      });
    });

    it('should find team channel mappings by condition', async () => {
      // Arrange
      const condition = {
        where: { 
          platformTeam: { id: 'team-id' },
          channel: { id: 'channel-id' },
        },
        relations: ['platformTeam', 'channel'],
      };

      const mapping = {
        id: 'mapping-id',
        relationshipType: TeamRelationshipType.PRIMARY,
        platformTeam: { id: 'team-id' },
        channel: { id: 'channel-id' },
        installation: { id: 'installation-id' },
      };

      (mockTypeOrmRepository.findOne as Mock).mockResolvedValue(mapping);

      // Act
      const result = await repository.findByCondition(condition);

      // Assert
      expect(result).toEqual(mapping);
      expect(mockTypeOrmRepository.findOne).toHaveBeenCalledWith(condition);
    });

    it('should find all team channel mappings', async () => {
      // Arrange
      const mappings = [
        {
          id: 'mapping-id-1',
          relationshipType: TeamRelationshipType.PRIMARY,
          platformTeam: { id: 'team-id-1' },
          channel: { id: 'channel-id-1' },
          installation: { id: 'installation-id' },
        },
        {
          id: 'mapping-id-2',
          relationshipType: TeamRelationshipType.SECONDARY,
          platformTeam: { id: 'team-id-2' },
          channel: { id: 'channel-id-2' },
          installation: { id: 'installation-id' },
        },
      ];

      (mockTypeOrmRepository.find as Mock).mockResolvedValue(mappings);

      // Act
      const result = await repository.findAll();

      // Assert
      expect(result).toEqual(mappings);
      expect(mockTypeOrmRepository.find).toHaveBeenCalled();
    });

    it('should update a team channel mapping', async () => {
      // Arrange
      const criteria = { id: 'mapping-id' };
      const partialEntity = { relationshipType: TeamRelationshipType.SECONDARY };
      const updateResult = { affected: 1 };

      (mockTypeOrmRepository.update as Mock).mockResolvedValue(updateResult);

      // Act
      const result = await repository.update(criteria, partialEntity);

      // Assert
      expect(result).toEqual(updateResult);
      expect(mockTypeOrmRepository.update).toHaveBeenCalledWith(criteria, partialEntity);
    });

    it('should remove a team channel mapping', async () => {
      // Arrange
      const mapping = {
        id: 'mapping-id',
        relationshipType: TeamRelationshipType.PRIMARY,
        platformTeam: { id: 'team-id' },
        channel: { id: 'channel-id' },
        installation: { id: 'installation-id' },
      };

      (mockTypeOrmRepository.remove as Mock).mockResolvedValue(mapping);

      // Act
      const result = await repository.remove(mapping);

      // Assert
      expect(result).toEqual(mapping);
      expect(mockTypeOrmRepository.remove).toHaveBeenCalledWith(mapping);
    });

    it('should soft delete a team channel mapping', async () => {
      // Arrange
      const criteria = { id: 'mapping-id' };
      const deleteResult = { affected: 1 };

      (mockTypeOrmRepository.softDelete as Mock).mockResolvedValue(deleteResult);

      // Act
      const result = await repository.softDelete(criteria);

      // Assert
      expect(result).toEqual(deleteResult);
      expect(mockTypeOrmRepository.softDelete).toHaveBeenCalledWith(criteria);
    });

    it('should check if a team channel mapping exists', async () => {
      // Arrange
      const options = {
        where: { 
          platformTeam: { id: 'team-id' },
          channel: { id: 'channel-id' },
        },
      };

      (mockTypeOrmRepository.exists as Mock).mockResolvedValue(true);

      // Act
      const result = await repository.exists(options);

      // Assert
      expect(result).toBe(true);
      expect(mockTypeOrmRepository.exists).toHaveBeenCalledWith(options);
    });
  });

  describe('Transaction operations', () => {
    it('should save a team channel mapping with transaction', async () => {
      // Arrange
      const mapping = {
        relationshipType: TeamRelationshipType.PRIMARY,
        platformTeam: { id: 'team-id' },
        channel: { id: 'channel-id' },
        installation: { id: 'installation-id' },
      };

      const savedMapping = {
        id: 'mapping-id',
        ...mapping,
      };

      const mockTxnContext = {
        manager: {
          save: vi.fn().mockResolvedValue(savedMapping),
        },
      } as unknown as TransactionContext;

      // Act
      const result = await repository.saveWithTxn(mockTxnContext, mapping);

      // Assert
      expect(result).toEqual(savedMapping);
      expect(mockTxnContext.manager.save).toHaveBeenCalled();
    });

    it('should remove a team channel mapping with transaction', async () => {
      // Arrange
      const mapping = {
        id: 'mapping-id',
        relationshipType: TeamRelationshipType.PRIMARY,
        platformTeam: { id: 'team-id' },
        channel: { id: 'channel-id' },
        installation: { id: 'installation-id' },
      };

      const mockTxnContext = {
        manager: {
          remove: vi.fn().mockResolvedValue(mapping),
        },
      } as unknown as TransactionContext;

      // Act
      const result = await repository.removeWithTxn(mockTxnContext, mapping);

      // Assert
      expect(result).toEqual(mapping);
      expect(mockTxnContext.manager.remove).toHaveBeenCalled();
    });

    it('should soft delete a team channel mapping with transaction', async () => {
      // Arrange
      const criteria = { id: 'mapping-id' };
      const deleteResult = { affected: 1 };

      const mockTxnContext = {
        manager: {
          softDelete: vi.fn().mockResolvedValue(deleteResult),
        },
      } as unknown as TransactionContext;

      // Act
      const result = await repository.softDeleteWithTxn(mockTxnContext, criteria);

      // Assert
      expect(result).toEqual(deleteResult);
      expect(mockTxnContext.manager.softDelete).toHaveBeenCalled();
    });
  });
});
