import { Test, TestingModule } from '@nestjs/testing';
import { getRepositoryToken } from '@nestjs/typeorm';
import { Repository } from 'typeorm';
import { Mock, beforeEach, describe, expect, it, vi } from 'vitest';
import { TransactionContext } from '../../../../../../src/database/common';
import { SettingsSchemas } from '../../../../../../src/database/entities/settings/settings-schemas.entity';
import { SettingsSchemasRepository } from '../../../../../../src/database/entities/settings/repositories/setting-schemas.repository';

describe('SettingsSchemasRepository', () => {
  let repository: SettingsSchemasRepository;
  let mockTypeOrmRepository: Repository<SettingsSchemas>;

  beforeEach(async () => {
    // Create mock implementation for TypeORM repository methods
    mockTypeOrmRepository = {
      save: vi.fn(),
      create: vi.fn(),
      findOne: vi.fn(),
      findOneBy: vi.fn(),
      find: vi.fn(),
      update: vi.fn(),
      softDelete: vi.fn(),
      count: vi.fn(),
      exists: vi.fn(),
    } as unknown as Repository<SettingsSchemas>;

    // Create a NestJS testing module
    const module: TestingModule = await Test.createTestingModule({
      providers: [
        SettingsSchemasRepository,
        {
          provide: getRepositoryToken(SettingsSchemas),
          useValue: mockTypeOrmRepository,
        },
      ],
    }).compile();

    repository = module.get<SettingsSchemasRepository>(SettingsSchemasRepository);
  });

  describe('Inherited CRUD operations', () => {
    it('should create a new settings schema entity', () => {
      // Arrange
      const schema = {
        key: 'test_schema',
        schema: JSON.stringify({
          type: 'object',
          properties: {
            setting: { type: 'string' },
          },
        }),
        scope: 'organization',
      };

      (mockTypeOrmRepository.create as Mock).mockReturnValue({
        id: 'schema-id',
        ...schema,
      });

      // Act
      const result = repository.create(schema);

      // Assert
      expect(result).toEqual({
        id: 'schema-id',
        key: 'test_schema',
        schema: JSON.stringify({
          type: 'object',
          properties: {
            setting: { type: 'string' },
          },
        }),
        scope: 'organization',
      });
      expect(mockTypeOrmRepository.create).toHaveBeenCalledWith(schema);
    });

    it('should save settings schema', async () => {
      // Arrange
      const schema = {
        key: 'test_schema',
        schema: JSON.stringify({
          type: 'object',
          properties: {
            setting: { type: 'string' },
          },
        }),
        scope: 'organization',
      };

      (mockTypeOrmRepository.save as Mock).mockResolvedValue({
        id: 'schema-id',
        ...schema,
      });

      // Act
      const result = await repository.save(schema);

      // Assert
      expect(result).toEqual({
        id: 'schema-id',
        key: 'test_schema',
        schema: JSON.stringify({
          type: 'object',
          properties: {
            setting: { type: 'string' },
          },
        }),
        scope: 'organization',
      });
      expect(mockTypeOrmRepository.save).toHaveBeenCalledWith(schema);
    });

    it('should find one settings schema by id', async () => {
      // Arrange
      const schema = {
        id: 'schema-id',
        key: 'test_schema',
        schema: JSON.stringify({
          type: 'object',
          properties: {
            setting: { type: 'string' },
          },
        }),
        scope: 'organization',
      };

      (mockTypeOrmRepository.findOneBy as Mock).mockResolvedValue(schema);

      // Act
      const result = await repository.findOneById('schema-id');

      // Assert
      expect(result).toEqual(schema);
      expect(mockTypeOrmRepository.findOneBy).toHaveBeenCalledWith({
        id: 'schema-id',
      });
    });

    it('should find settings schema by condition', async () => {
      // Arrange
      const schema = {
        id: 'schema-id',
        key: 'test_schema',
        schema: JSON.stringify({
          type: 'object',
          properties: {
            setting: { type: 'string' },
          },
        }),
        scope: 'organization',
      };

      (mockTypeOrmRepository.findOne as Mock).mockResolvedValue(schema);

      // Act
      const result = await repository.findByCondition({
        where: { key: 'test_schema' },
      });

      // Assert
      expect(result).toEqual(schema);
      expect(mockTypeOrmRepository.findOne).toHaveBeenCalledWith({
        where: { key: 'test_schema' },
      });
    });

    it('should find all settings schemas', async () => {
      // Arrange
      const schemas = [
        {
          id: 'schema-id-1',
          key: 'test_schema_1',
          schema: JSON.stringify({
            type: 'object',
            properties: {
              setting1: { type: 'string' },
            },
          }),
          scope: 'organization',
        },
        {
          id: 'schema-id-2',
          key: 'test_schema_2',
          schema: JSON.stringify({
            type: 'object',
            properties: {
              setting2: { type: 'number' },
            },
          }),
          scope: 'installation',
        },
      ];

      (mockTypeOrmRepository.find as Mock).mockResolvedValue(schemas);

      // Act
      const result = await repository.findAll();

      // Assert
      expect(result).toEqual(schemas);
      expect(mockTypeOrmRepository.find).toHaveBeenCalled();
    });

    it.todo('should find settings schemas with options');

    it('should update settings schema', async () => {
      // Arrange
      const updateResult = { affected: 1 };
      (mockTypeOrmRepository.update as Mock).mockResolvedValue(updateResult);

      // Act
      const result = await repository.update('schema-id', {
        schema: JSON.stringify({
          type: 'object',
          properties: {
            setting: { type: 'string' },
            newSetting: { type: 'boolean' },
          },
        }),
      });

      // Assert
      expect(result).toEqual(updateResult);
      expect(mockTypeOrmRepository.update).toHaveBeenCalledWith(
        'schema-id',
        {
          schema: JSON.stringify({
            type: 'object',
            properties: {
              setting: { type: 'string' },
              newSetting: { type: 'boolean' },
            },
          }),
        }
      );
    });

    it.todo('should update settings schema with transaction');

    it('should soft delete settings schema', async () => {
      // Arrange
      const deleteResult = { affected: 1 };
      (mockTypeOrmRepository.softDelete as Mock).mockResolvedValue(deleteResult);

      // Act
      const result = await repository.softDelete({ id: 'schema-id' });

      // Assert
      expect(result).toEqual(deleteResult);
      expect(mockTypeOrmRepository.softDelete).toHaveBeenCalledWith({
        id: 'schema-id',
      });
    });

    it('should count settings schemas', async () => {
      // Arrange
      (mockTypeOrmRepository.count as Mock).mockResolvedValue(5);

      // Act
      const result = await repository.count();

      // Assert
      expect(result).toEqual(5);
      expect(mockTypeOrmRepository.count).toHaveBeenCalled();
    });

    it('should check if settings schema exists', async () => {
      // Arrange
      (mockTypeOrmRepository.exists as Mock).mockResolvedValue(true);

      // Act
      const result = await repository.exists({
        where: { key: 'test_schema' },
      });

      // Assert
      expect(result).toBe(true);
      expect(mockTypeOrmRepository.exists).toHaveBeenCalledWith({
        where: { key: 'test_schema' },
      });
    });
  });
});
