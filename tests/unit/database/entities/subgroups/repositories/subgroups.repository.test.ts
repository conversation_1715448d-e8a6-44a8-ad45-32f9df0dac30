import { describe, it, expect, beforeEach, vi, Mock } from 'vitest';
import { SlackSubgroupsRepository } from '../../../../../../src/database/entities/subgroups/repositories/subgroups.repository';
import { Repository } from 'typeorm';
import { SlackSubgroups } from '../../../../../../src/database/entities/subgroups/subgroups.entity';
import { Installations } from '../../../../../../src/database/entities/installations/installations.entity';
import { Organizations } from '../../../../../../src/database/entities/organizations/organizations.entity';

describe('SlackSubgroupsRepository', () => {
  let slackSubgroupsRepository: SlackSubgroupsRepository;
  let mockTypeOrmRepository: Repository<SlackSubgroups>;

  const mockInstallation = { id: 'inst-123' } as Installations;
  const mockOrganization = { id: 'org-123' } as Organizations;

  const createMockSubgroup = (id: string, handle: string) => ({
    id,
    slackGroupId: `G${id}`,
    slackHandle: handle,
    description: `Description for ${handle}`,
    isExternal: false,
    usersCount: 10,
    users: ['U123', 'U456'],
    createdAt: new Date(),
    updatedAt: new Date(),
    slackGroupDump: {},
    installation: mockInstallation,
    organization: mockOrganization,
  } as unknown as SlackSubgroups);

  beforeEach(() => {
    mockTypeOrmRepository = {
      find: vi.fn(),
      findOneBy: vi.fn(),
      findOne: vi.fn(),
      save: vi.fn(),
      create: vi.fn(),
      remove: vi.fn(),
      update: vi.fn(),
      findAndCount: vi.fn(),
    } as unknown as Repository<SlackSubgroups>;

    slackSubgroupsRepository = new SlackSubgroupsRepository(mockTypeOrmRepository);
  });

  describe('inheritance', () => {
    it('should extend BaseAbstractRepository', () => {
      expect(slackSubgroupsRepository.findAll).toBeDefined();
      expect(slackSubgroupsRepository.findOneById).toBeDefined();
      expect(slackSubgroupsRepository.findByCondition).toBeDefined();
      expect(slackSubgroupsRepository.create).toBeDefined();
      expect(slackSubgroupsRepository.createMany).toBeDefined();
      expect(slackSubgroupsRepository.update).toBeDefined();
      expect(slackSubgroupsRepository.remove).toBeDefined();
    });
  });

  describe('findAll', () => {
    it('should call the repository find method with the provided options', async () => {
      const options = { where: { id: '123' } };
      const expectedResult = [
        createMockSubgroup('123', 'test-subgroup'),
        createMockSubgroup('456', 'another-subgroup'),
      ];
      (mockTypeOrmRepository.find as Mock).mockResolvedValue(expectedResult);

      const result = await slackSubgroupsRepository.findAll(options);

      expect(mockTypeOrmRepository.find).toHaveBeenCalledWith(options);
      expect(result).toEqual(expectedResult);
    });
  });

  describe('findOneById', () => {
    it('should call the repository findOneBy method with the provided id', async () => {
      const id = '123';
      const expectedResult = createMockSubgroup('123', 'test-subgroup');
      (mockTypeOrmRepository.findOneBy as Mock).mockResolvedValue(expectedResult);

      const result = await slackSubgroupsRepository.findOneById(id);

      expect(mockTypeOrmRepository.findOneBy).toHaveBeenCalledWith({ id });
      expect(result).toEqual(expectedResult);
    });
  });

  describe('findByCondition', () => {
    it('should call the repository findOne method with the provided options', async () => {
      const options = { where: { id: '123' } };
      const expectedResult = createMockSubgroup('123', 'test-subgroup');
      (mockTypeOrmRepository.findOne as Mock).mockResolvedValue(expectedResult);

      const result = await slackSubgroupsRepository.findByCondition(options);

      expect(mockTypeOrmRepository.findOne).toHaveBeenCalledWith(options);
      expect(result).toEqual(expectedResult);
    });
  });

  describe('create', () => {
    it('should call the repository create method with the provided data', () => {
      const createData = {
        slackGroupId: 'G123',
        slackHandle: 'test-subgroup',
        description: 'Test subgroup description',
        installation: mockInstallation,
        organization: mockOrganization,
      };
      const createdEntity = createMockSubgroup('123', 'test-subgroup');
      (mockTypeOrmRepository.create as Mock).mockReturnValue(createdEntity);

      const result = slackSubgroupsRepository.create(createData);

      expect(mockTypeOrmRepository.create).toHaveBeenCalledWith(createData);
      expect(result).toEqual(createdEntity);
    });
  });

  describe('save', () => {
    it('should call the repository save method with the provided data', async () => {
      const saveData = createMockSubgroup('123', 'test-subgroup');
      (mockTypeOrmRepository.save as Mock).mockResolvedValue(saveData);

      const result = await slackSubgroupsRepository.save(saveData);

      expect(mockTypeOrmRepository.save).toHaveBeenCalledWith(saveData);
      expect(result).toEqual(saveData);
    });
  });

  describe('update', () => {
    it('should call the repository update method with the provided id and data', async () => {
      const id = '123';
      const updateData = { 
        slackHandle: 'updated-subgroup',
        description: 'Updated description'
      };
      const updateResult = { affected: 1 };
      (mockTypeOrmRepository.update as Mock).mockResolvedValue(updateResult);

      const result = await slackSubgroupsRepository.update(id, updateData);

      expect(mockTypeOrmRepository.update).toHaveBeenCalledWith(id, updateData);
      expect(result).toEqual(updateResult);
    });
  });

  describe('remove', () => {
    it('should call the repository remove method with the provided entity', async () => {
      const entity = createMockSubgroup('123', 'test-subgroup');
      (mockTypeOrmRepository.remove as Mock).mockResolvedValue(entity);

      const result = await slackSubgroupsRepository.remove(entity);

      expect(mockTypeOrmRepository.remove).toHaveBeenCalledWith(entity);
      expect(result).toEqual(entity);
    });
  });
});
