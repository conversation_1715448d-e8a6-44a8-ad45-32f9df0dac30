import { describe, it, expect, beforeEach, vi, Mock } from 'vitest';
import { PromptsRepository } from '../../../../../../src/database/entities/prompts/repositories/prompts.repository';
import { Repository } from 'typeorm';
import { Prompts } from '../../../../../../src/database/entities/prompts/prompts.entity';

describe('PromptsRepository', () => {
  let promptsRepository: PromptsRepository;
  let mockTypeOrmRepository: Repository<Prompts>;

  beforeEach(() => {
    mockTypeOrmRepository = {
      find: vi.fn(),
      findOneBy: vi.fn(),
      findOne: vi.fn(),
      save: vi.fn(),
      create: vi.fn(),
      remove: vi.fn(),
      update: vi.fn(),
      findAndCount: vi.fn(),
    } as unknown as Repository<Prompts>;

    promptsRepository = new PromptsRepository(mockTypeOrmRepository);
  });

  describe('inheritance', () => {
    it('should extend BaseAbstractRepository', () => {
      expect(promptsRepository.findAll).toBeDefined();
      expect(promptsRepository.findOneById).toBeDefined();
      expect(promptsRepository.findByCondition).toBeDefined();
      expect(promptsRepository.create).toBeDefined();
      expect(promptsRepository.createMany).toBeDefined();
      expect(promptsRepository.update).toBeDefined();
      expect(promptsRepository.remove).toBeDefined();
    });
  });

  describe('findAll', () => {
    it('should call the repository find method with the provided options', async () => {
      const options = { where: { id: '123' } };
      const expectedResult = [{ id: '123', name: 'Test Prompt' }] as Prompts[];
      (mockTypeOrmRepository.find as Mock).mockResolvedValue(expectedResult);

      const result = await promptsRepository.findAll(options);

      expect(mockTypeOrmRepository.find).toHaveBeenCalledWith(options);
      expect(result).toEqual(expectedResult);
    });
  });

  describe('findOneById', () => {
    it('should call the repository findOneBy method with the provided id', async () => {
      const id = '123';
      const expectedResult = { id: '123', name: 'Test Prompt' } as Prompts;
      (mockTypeOrmRepository.findOneBy as Mock).mockResolvedValue(expectedResult);

      const result = await promptsRepository.findOneById(id);

      expect(mockTypeOrmRepository.findOneBy).toHaveBeenCalledWith({ id });
      expect(result).toEqual(expectedResult);
    });
  });

  describe('findByCondition', () => {
    it('should call the repository findOne method with the provided options', async () => {
      const options = { where: { id: '123' } };
      const expectedResult = { id: '123', name: 'Test Prompt' } as Prompts;
      (mockTypeOrmRepository.findOne as Mock).mockResolvedValue(expectedResult);

      const result = await promptsRepository.findByCondition(options);

      expect(mockTypeOrmRepository.findOne).toHaveBeenCalledWith(options);
      expect(result).toEqual(expectedResult);
    });
  });

  describe('create', () => {
    it('should call the repository create method with the provided data', () => {
      const createData = { name: 'Test Prompt' };
      const createdEntity = { id: '123', ...createData } as Prompts;
      (mockTypeOrmRepository.create as Mock).mockReturnValue(createdEntity);

      const result = promptsRepository.create(createData);

      expect(mockTypeOrmRepository.create).toHaveBeenCalledWith(createData);
      expect(result).toEqual(createdEntity);
    });
  });

  describe('save', () => {
    it('should call the repository save method with the provided data', async () => {
      const saveData = { id: '123', name: 'Test Prompt' } as Prompts;
      (mockTypeOrmRepository.save as Mock).mockResolvedValue(saveData);

      const result = await promptsRepository.save(saveData);

      expect(mockTypeOrmRepository.save).toHaveBeenCalledWith(saveData);
      expect(result).toEqual(saveData);
    });
  });

  describe('update', () => {
    it('should call the repository update method with the provided id and data', async () => {
      const id = '123';
      const updateData = { name: 'Updated Prompt' };
      const updateResult = { affected: 1 };
      (mockTypeOrmRepository.update as Mock).mockResolvedValue(updateResult);

      const result = await promptsRepository.update(id, updateData);

      expect(mockTypeOrmRepository.update).toHaveBeenCalledWith(id, updateData);
      expect(result).toEqual(updateResult);
    });
  });

  describe('remove', () => {
    it('should call the repository remove method with the provided entity', async () => {
      const entity = { id: '123', name: 'Test Prompt' } as Prompts;
      (mockTypeOrmRepository.remove as Mock).mockResolvedValue(entity);

      const result = await promptsRepository.remove(entity);

      expect(mockTypeOrmRepository.remove).toHaveBeenCalledWith(entity);
      expect(result).toEqual(entity);
    });
  });
});
