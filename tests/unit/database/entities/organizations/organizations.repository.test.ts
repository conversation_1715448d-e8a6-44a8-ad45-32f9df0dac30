import { Test, TestingModule } from '@nestjs/testing';
import { getRepositoryToken } from '@nestjs/typeorm';
import { DataSource, Repository } from 'typeorm';
import { Organizations } from '../../../../../src/database/entities/organizations/organizations.entity';
import { Installations } from '../../../../../src/database/entities/installations/installations.entity';
import { Channels } from '../../../../../src/database/entities/channels/channels.entity';
import { Users } from '../../../../../src/database/entities/users/users.entity';
import { PlatformTeams } from '../../../../../src/database/entities/teams/teams.entity';
import { SlackSubgroups } from '../../../../../src/database/entities/subgroups/subgroups.entity';
import { beforeEach, describe, expect, it, vi } from 'vitest';

describe('Organization Repository Cascading Operations', () => {
  let dataSource: DataSource;
  let organizationRepo: Repository<Organizations>;
  let installationRepo: Repository<Installations>;
  let channelRepo: Repository<Channels>;
  let userRepo: Repository<Users>;
  let teamRepo: Repository<PlatformTeams>;
  let subgroupRepo: Repository<SlackSubgroups>;

  beforeEach(async () => {
    // Create mock implementations for TypeORM repositories
    const mockOrganizationRepo = {
      save: vi.fn(),
      delete: vi.fn(),
      softDelete: vi.fn(),
      findOne: vi.fn(),
      findOneBy: vi.fn(),
      remove: vi.fn(),
      find: vi.fn(),
      createQueryBuilder: vi.fn().mockReturnValue({
        where: vi.fn().mockReturnThis(),
        leftJoinAndSelect: vi.fn().mockReturnThis(),
        getOne: vi.fn(),
        getMany: vi.fn(),
      }),
    };

    const mockInstallationRepo = {
      find: vi.fn(),
      delete: vi.fn(),
      softDelete: vi.fn(),
      update: vi.fn(),
    };

    const mockChannelRepo = {
      find: vi.fn(),
      delete: vi.fn(),
      softDelete: vi.fn(),
      update: vi.fn(),
    };

    const mockUserRepo = {
      find: vi.fn(),
      delete: vi.fn(),
      softDelete: vi.fn(),
      update: vi.fn(),
    };

    const mockTeamRepo = {
      find: vi.fn(),
      delete: vi.fn(),
      softDelete: vi.fn(),
      update: vi.fn(),
    };

    const mockSubgroupRepo = {
      find: vi.fn(),
      delete: vi.fn(),
      softDelete: vi.fn(),
      update: vi.fn(),
    };

    // Mock DataSource with transaction capabilities
    const mockDataSource = {
      createQueryRunner: vi.fn().mockReturnValue({
        connect: vi.fn(),
        startTransaction: vi.fn(),
        commitTransaction: vi.fn(),
        rollbackTransaction: vi.fn(),
        release: vi.fn(),
        manager: {
          getRepository: (entity: any) => {
            if (entity === Organizations) return mockOrganizationRepo;
            if (entity === Installations) return mockInstallationRepo;
            if (entity === Channels) return mockChannelRepo;
            if (entity === Users) return mockUserRepo;
            if (entity === PlatformTeams) return mockTeamRepo;
            if (entity === SlackSubgroups) return mockSubgroupRepo;
            return {};
          },
          save: vi.fn(),
          delete: vi.fn(),
          softDelete: vi.fn(),
          update: vi.fn(),
          find: vi.fn().mockReturnValue([]),
          findOne: vi.fn(),
          remove: vi.fn(),
        },
      }),
      transaction: vi.fn().mockImplementation(async (cb) => {
        // Create a mock transaction manager for the callback function
        const mockTransactionManager = {
          getRepository: (entity: any) => {
            if (entity === Organizations) return mockOrganizationRepo;
            if (entity === Installations) return mockInstallationRepo;
            if (entity === Channels) return mockChannelRepo;
            if (entity === Users) return mockUserRepo;
            if (entity === PlatformTeams) return mockTeamRepo;
            if (entity === SlackSubgroups) return mockSubgroupRepo;
            return {};
          },
          save: vi.fn(),
          delete: vi.fn(),
          softDelete: vi.fn(),
          update: vi.fn(),
          find: vi.fn().mockReturnValue([
            { id: '1', teamId: 'T1', organization: { id: '1' } },
            { id: '2', teamId: 'T2', organization: { id: '1' } },
          ]),
          findOne: vi.fn(),
          remove: vi.fn(),
        };
        
        return cb(mockTransactionManager);
      }),
    };

    // Create a NestJS testing module
    const module: TestingModule = await Test.createTestingModule({
      providers: [
        {
          provide: DataSource,
          useValue: mockDataSource,
        },
        {
          provide: getRepositoryToken(Organizations),
          useValue: mockOrganizationRepo,
        },
        {
          provide: getRepositoryToken(Installations),
          useValue: mockInstallationRepo,
        },
        {
          provide: getRepositoryToken(Channels),
          useValue: mockChannelRepo,
        },
        {
          provide: getRepositoryToken(Users),
          useValue: mockUserRepo,
        },
        {
          provide: getRepositoryToken(PlatformTeams),
          useValue: mockTeamRepo,
        },
        {
          provide: getRepositoryToken(SlackSubgroups),
          useValue: mockSubgroupRepo,
        },
      ],
    }).compile();

    dataSource = module.get<DataSource>(DataSource);
    organizationRepo = module.get<Repository<Organizations>>(getRepositoryToken(Organizations));
    installationRepo = module.get<Repository<Installations>>(getRepositoryToken(Installations));
    channelRepo = module.get<Repository<Channels>>(getRepositoryToken(Channels));
    userRepo = module.get<Repository<Users>>(getRepositoryToken(Users));
    teamRepo = module.get<Repository<PlatformTeams>>(getRepositoryToken(PlatformTeams));
    subgroupRepo = module.get<Repository<SlackSubgroups>>(getRepositoryToken(SlackSubgroups));
  });

  describe('Cascade DELETE operations', () => {
    it('should delete all related entities when an organization is deleted', async () => {
      // Arrange
      const orgId = '1';
      const mockOrg = { id: orgId, name: 'Test Org', uid: 'test-org' } as Organizations;

      // Mock organization retrieval
      (organizationRepo.findOneBy as any).mockResolvedValue(mockOrg);

      // Mock finding related entities
      (installationRepo.find as any).mockResolvedValue([
        { id: '1', teamId: 'T1', organization: mockOrg },
        { id: '2', teamId: 'T2', organization: mockOrg },
      ]);
      (channelRepo.find as any).mockResolvedValue([
        { id: '1', channelId: 'C1', organization: mockOrg },
        { id: '2', channelId: 'C2', organization: mockOrg },
      ]);
      (userRepo.find as any).mockResolvedValue([
        { id: '1', slackId: 'U1', organization: mockOrg },
        { id: '2', slackId: 'U2', organization: mockOrg },
      ]);
      (teamRepo.find as any).mockResolvedValue([
        { id: '1', teamId: 'TEAM1', organization: mockOrg },
      ]);
      (subgroupRepo.find as any).mockResolvedValue([
        { id: '1', slackGroupId: 'G1', organization: mockOrg },
      ]);

      // Act - simulate transaction-based deletion
      await dataSource.transaction(async (transactionManager) => {
        // First remove the organization using the repository's delete method
        await transactionManager.delete(Organizations, { id: orgId });
        
        // In a real database, this would automatically cascade delete to related entities
        // Here we need to simulate that by explicitly calling delete on related entities
        await transactionManager.delete(Installations, { organization: { id: orgId } });
        await transactionManager.delete(Channels, { organization: { id: orgId } });
        await transactionManager.delete(Users, { organization: { id: orgId } });
        await transactionManager.delete(PlatformTeams, { organization: { id: orgId } });
        await transactionManager.delete(SlackSubgroups, { organization: { id: orgId } });
      });

      // Assert
      expect(dataSource.transaction).toHaveBeenCalled();
    });

    it('should perform soft delete of organizations and related entities', async () => {
      // Arrange
      const orgId = '1';
      
      // Mock softDelete operations to return affected counts
      (organizationRepo.softDelete as any).mockResolvedValue({ affected: 1 });
      (installationRepo.softDelete as any).mockResolvedValue({ affected: 2 });
      (channelRepo.softDelete as any).mockResolvedValue({ affected: 3 });
      (userRepo.softDelete as any).mockResolvedValue({ affected: 5 });
      (teamRepo.softDelete as any).mockResolvedValue({ affected: 1 });
      (subgroupRepo.softDelete as any).mockResolvedValue({ affected: 2 });

      // Act - simulate soft delete across multiple entities
      await dataSource.transaction(async (transactionManager) => {
        // Soft delete the organization
        await transactionManager.softDelete(Organizations, { id: orgId });
        
        // Simulate cascade soft delete - which may be implemented differently in the app
        await transactionManager.softDelete(Installations, { organization: { id: orgId } });
        await transactionManager.softDelete(Channels, { organization: { id: orgId } });
        await transactionManager.softDelete(Users, { organization: { id: orgId } });
        await transactionManager.softDelete(PlatformTeams, { organization: { id: orgId } });
        await transactionManager.softDelete(SlackSubgroups, { organization: { id: orgId } });
      });

      // Assert
      expect(dataSource.transaction).toHaveBeenCalled();
    });
  });

  describe('Cascade UPDATE operations', () => {
    it('should update related entity references when organization details change', async () => {
      // Arrange
      const orgId = '1';
      const mockOrg = { 
        id: orgId, 
        name: 'Test Org', 
        uid: 'test-org',
        apiKey: 'old-api-key'
      } as Organizations;
      
      const updatedOrg = {
        ...mockOrg,
        name: 'Updated Org Name',
        apiKey: 'new-api-key'
      };

      // Mock organization save
      (organizationRepo.save as any).mockResolvedValue(updatedOrg);

      // Mock finding related entities
      (installationRepo.find as any).mockResolvedValue([
        { id: '1', teamId: 'T1', organization: mockOrg },
        { id: '2', teamId: 'T2', organization: mockOrg },
      ]);

      // Act - simulate updating organization and checking refs in related entities
      await dataSource.transaction(async (transactionManager) => {
        // Update the organization
        await transactionManager.save(Organizations, updatedOrg);
        
        // Since our mock transactionManager.find already returns a predefined array,
        // we don't need to do anything else here
      });

      // Assert
      expect(dataSource.transaction).toHaveBeenCalled();
    });

    it('should maintain relationships when organization metadata is modified', async () => {
      // Arrange
      const orgId = '1';
      const mockOrg = { 
        id: orgId, 
        name: 'Test Org', 
        uid: 'test-org',
        metadata: {
          applicationId: 'app1',
          installationId: 'inst1',
          createdBy: 'user1'
        }
      } as Organizations;
      
      const updatedMetadata = {
        applicationId: 'app1',
        installationId: 'inst1',
        createdBy: 'user1',
        additionalInfo: 'new data'
      };

      // Mock finding the organization with relations
      (organizationRepo.findOne as any).mockResolvedValue({
        ...mockOrg,
        installations: [{ id: '1', teamId: 'T1' }],
        channels: [{ id: '1', channelId: 'C1' }],
        users: [{ id: '1', slackId: 'U1' }],
      });

      // Mock update operation
      (organizationRepo.save as any).mockImplementation(async (org) => {
        return {
          ...org,
          metadata: updatedMetadata
        };
      });

      // Act
      const result = await organizationRepo.save({
        ...mockOrg,
        metadata: updatedMetadata
      });

      // Assert
      expect(result.metadata).toEqual(updatedMetadata);
      expect(organizationRepo.save).toHaveBeenCalled();
    });
  });

  describe('Relationship query operations', () => {
    it('should query organization with all related entities', async () => {
      // Arrange
      const orgId = '1';
      const mockOrg = { 
        id: orgId, 
        name: 'Test Org', 
        uid: 'test-org' 
      } as Organizations;

      const mockQueryBuilder = organizationRepo.createQueryBuilder();
      (mockQueryBuilder.getOne as any).mockResolvedValue({
        ...mockOrg,
        installations: [{ id: '1', teamId: 'T1' }],
        channels: [{ id: '1', channelId: 'C1' }],
        users: [{ id: '1', slackId: 'U1' }],
        teams: [{ id: '1', teamId: 'TEAM1' }],
        subgroups: [{ id: '1', slackGroupId: 'G1' }],
      });

      // Act
      const result = await mockQueryBuilder
        .leftJoinAndSelect('organization.installations', 'installations')
        .leftJoinAndSelect('organization.channels', 'channels')
        .leftJoinAndSelect('organization.users', 'users')
        .leftJoinAndSelect('organization.teams', 'teams')
        .leftJoinAndSelect('organization.subgroups', 'subgroups')
        .where('organization.id = :id', { id: orgId })
        .getOne();

      // Assert
      expect(result).toBeDefined();
      expect(result?.installations).toHaveLength(1);
      expect(result?.channels).toHaveLength(1);
      expect(result?.users).toHaveLength(1);
      expect(result?.teams).toHaveLength(1);
      expect(result?.subgroups).toHaveLength(1);
      expect(mockQueryBuilder.leftJoinAndSelect).toHaveBeenCalledTimes(5);
    });

    it('should query all organizations with installation counts', async () => {
      // Arrange
      const mockQueryBuilder = organizationRepo.createQueryBuilder();
      (mockQueryBuilder.getMany as any).mockResolvedValue([
        {
          id: '1',
          name: 'Org 1',
          installations: [{ id: '1' }, { id: '2' }],
        },
        {
          id: '2',
          name: 'Org 2',
          installations: [{ id: '3' }],
        },
        {
          id: '3',
          name: 'Org 3',
          installations: [],
        },
      ]);

      // Act
      const result = await mockQueryBuilder
        .leftJoinAndSelect('organization.installations', 'installations')
        .getMany();

      // Assert
      expect(result).toHaveLength(3);
      expect(result[0].installations).toHaveLength(2);
      expect(result[1].installations).toHaveLength(1);
      expect(result[2].installations).toHaveLength(0);
      expect(mockQueryBuilder.leftJoinAndSelect).toHaveBeenCalledWith(
        'organization.installations',
        'installations'
      );
    });
  });
});
