import { Test, TestingModule } from '@nestjs/testing';
import { getRepositoryToken } from '@nestjs/typeorm';
import { Repository } from 'typeorm';
import { beforeEach, describe, expect, it, vi } from 'vitest';
import {
  ChannelType,
  Channels,
} from '../../../../../../src/database/entities/channels/channels.entity';
import { ChannelsRepository } from '../../../../../../src/database/entities/channels/repositories/channels.repository';
import { Installations } from '../../../../../../src/database/entities/installations/installations.entity';
import { Organizations } from '../../../../../../src/database/entities/organizations/organizations.entity';

describe('ChannelsRepository', () => {
  let repository: ChannelsRepository;
  let mockTypeOrmRepository: Repository<Channels>;
  let mockQueryBuilder: any;

  // Mock channel data for testing
  const mockInstallation: Partial<Installations> = {
    id: '1',
    teamId: 'T12345',
    teamName: 'Test Team',
  };

  const mockOrganization: Partial<Organizations> = {
    id: '1',
    name: 'Test Organization',
  };

  const mockChannel: Partial<Channels> = {
    id: '1',
    name: 'general',
    channelId: 'C12345',
    channelType: ChannelType.INTERNAL_HELPDESK,
    isBotActive: true,
    isBotJoined: true,
    isArchived: false,
    isPrivate: false,
    isShared: false,
    installation: mockInstallation as Installations,
    organization: mockOrganization as Organizations,
    channelDump: {
      id: 'C12345',
      name: 'general',
      created: '1234567890',
      is_channel: true,
      is_archived: false,
      is_private: false,
    },
  };

  const mockChannels: Partial<Channels>[] = [
    mockChannel,
    {
      ...mockChannel,
      id: '2',
      name: 'support',
      channelId: 'C67890',
      channelType: ChannelType.CUSTOMER_CHANNEL,
    },
    {
      ...mockChannel,
      id: '3',
      name: 'triage',
      channelId: 'C11111',
      channelType: ChannelType.TRIAGE_CHANNEL,
      isArchived: true,
    },
  ];

  beforeEach(async () => {
    // Create mock query builder
    mockQueryBuilder = {
      where: vi.fn().mockReturnThis(),
      andWhere: vi.fn().mockReturnThis(),
      orWhere: vi.fn().mockReturnThis(),
      innerJoinAndSelect: vi.fn().mockReturnThis(),
      leftJoinAndSelect: vi.fn().mockReturnThis(),
      select: vi.fn().mockReturnThis(),
      orderBy: vi.fn().mockReturnThis(),
      getOne: vi.fn(),
      getMany: vi.fn(),
      getManyAndCount: vi.fn(),
      getCount: vi.fn(),
      limit: vi.fn().mockReturnThis(),
      offset: vi.fn().mockReturnThis(),
    };

    // Create mock implementation for TypeORM repository methods
    mockTypeOrmRepository = {
      save: vi.fn(),
      upsert: vi.fn(),
      insert: vi.fn(),
      update: vi.fn(),
      count: vi.fn(),
      create: vi.fn(),
      createQueryBuilder: vi.fn().mockReturnValue(mockQueryBuilder),
      findOneBy: vi.fn(),
      findOne: vi.fn(),
      find: vi.fn(),
      remove: vi.fn(),
      softDelete: vi.fn(),
    } as unknown as Repository<Channels>;

    // Create a NestJS testing module
    const module: TestingModule = await Test.createTestingModule({
      providers: [
        ChannelsRepository,
        {
          provide: getRepositoryToken(Channels),
          useValue: mockTypeOrmRepository,
        },
      ],
    }).compile();

    repository = module.get<ChannelsRepository>(ChannelsRepository);
  });

  describe('Basic CRUD operations', () => {
    it('should create a new channel entity', () => {
      // Arrange
      (mockTypeOrmRepository.create as any).mockReturnValue(mockChannel);

      // Act
      const result = repository.create(mockChannel);

      // Assert
      expect(result).toEqual(mockChannel);
      expect(mockTypeOrmRepository.create).toHaveBeenCalledWith(mockChannel);
    });

    it('should save a new channel to the database', async () => {
      // Arrange
      (mockTypeOrmRepository.save as any).mockResolvedValue(mockChannel);

      // Act
      const result = await repository.save(mockChannel);

      // Assert
      expect(result).toEqual(mockChannel);
      expect(mockTypeOrmRepository.save).toHaveBeenCalledWith(mockChannel);
    });
  });

  describe('Query methods', () => {
    it('should find channel by ID', async () => {
      // Arrange
      (mockTypeOrmRepository.findOneBy as any).mockResolvedValue(mockChannel);

      // Act
      const result = await repository.findOneById('1');

      // Assert
      expect(result).toEqual(mockChannel);
      expect(mockTypeOrmRepository.findOneBy).toHaveBeenCalledWith({ id: '1' });
    });

    it('should find channel by Slack channel ID and installation ID', async () => {
      // Arrange
      (mockTypeOrmRepository.findOne as any).mockResolvedValue(mockChannel);
      const condition = {
        where: {
          channelId: 'C12345',
          installation: { id: '1' },
        },
        relations: ['installation'],
      };

      // Act
      const result = await repository.findByCondition(condition);

      // Assert
      expect(result).toEqual(mockChannel);
      expect(mockTypeOrmRepository.findOne).toHaveBeenCalledWith(condition);
    });

    it('should find all active bot channels for an installation', async () => {
      // Arrange
      (mockTypeOrmRepository.find as any).mockResolvedValue([mockChannel]);
      const options = {
        where: {
          installation: { id: '1' },
          isBotActive: true,
          isBotJoined: true,
        },
        relations: ['installation'],
      };

      // Act
      const result = await repository.findAll(options);

      // Assert
      expect(result).toEqual([mockChannel]);
      expect(mockTypeOrmRepository.find).toHaveBeenCalledWith(options);
    });

    it('should find channels by channel type', async () => {
      // Arrange
      const triageChannels = mockChannels.filter(
        (c) => c.channelType === ChannelType.TRIAGE_CHANNEL,
      );
      (mockTypeOrmRepository.find as any).mockResolvedValue(triageChannels);
      const options = {
        where: { channelType: ChannelType.TRIAGE_CHANNEL },
      };

      // Act
      const result = await repository.findAll(options);

      // Assert
      expect(result).toEqual(triageChannels);
      expect(mockTypeOrmRepository.find).toHaveBeenCalledWith(options);
    });

    it('should find channels with relations', async () => {
      // Arrange
      (mockTypeOrmRepository.find as any).mockResolvedValue([mockChannel]);
      const options = {
        relations: ['installation', 'organization', 'customerContacts'],
        where: { id: '1' },
      };

      // Act
      const result = await repository.findWithRelations(options);

      // Assert
      expect(result).toEqual([mockChannel]);
      expect(mockTypeOrmRepository.find).toHaveBeenCalledWith(options);
    });

    it('should count channels by installation ID', async () => {
      // Arrange
      (mockTypeOrmRepository.count as any).mockResolvedValue(3);
      const options = {
        where: { installation: { id: '1' } },
      };

      // Act
      const result = await repository.count(options);

      // Assert
      expect(result).toBe(3);
      expect(mockTypeOrmRepository.count).toHaveBeenCalledWith(options);
    });
  });

  describe('Query builder operations', () => {
    it('should create a query builder and return channels with complex filtering', async () => {
      // Arrange
      (mockQueryBuilder.getMany as any).mockResolvedValue([mockChannel]);

      // Act
      const queryBuilder = repository.createQueryBuilder('channel');
      queryBuilder
        .innerJoinAndSelect('channel.installation', 'installation')
        .where('channel.channelType = :channelType', {
          channelType: ChannelType.CUSTOMER_CHANNEL,
        })
        .andWhere('channel.isArchived = :isArchived', { isArchived: false })
        .andWhere('installation.teamId = :teamId', { teamId: 'T12345' });

      const result = await queryBuilder.getMany();

      // Assert
      expect(result).toEqual([mockChannel]);
      expect(mockTypeOrmRepository.createQueryBuilder).toHaveBeenCalledWith(
        'channel',
      );
      expect(mockQueryBuilder.innerJoinAndSelect).toHaveBeenCalledWith(
        'channel.installation',
        'installation',
      );
      expect(mockQueryBuilder.where).toHaveBeenCalledWith(
        'channel.channelType = :channelType',
        { channelType: ChannelType.CUSTOMER_CHANNEL },
      );
      expect(mockQueryBuilder.andWhere).toHaveBeenCalledWith(
        'channel.isArchived = :isArchived',
        { isArchived: false },
      );
      expect(mockQueryBuilder.andWhere).toHaveBeenCalledWith(
        'installation.teamId = :teamId',
        { teamId: 'T12345' },
      );
    });

    it('should get paginated channels with relations', async () => {
      // Arrange
      (mockQueryBuilder.getManyAndCount as any).mockResolvedValue([
        [mockChannel],
        1,
      ]);

      // Act
      const queryBuilder = repository.createQueryBuilder('channel');
      queryBuilder
        .leftJoinAndSelect('channel.installation', 'installation')
        .leftJoinAndSelect('channel.organization', 'organization')
        .orderBy('channel.createdAt', 'DESC')
        .limit(10)
        .offset(0);

      const [results, count] = await queryBuilder.getManyAndCount();

      // Assert
      expect(results).toEqual([mockChannel]);
      expect(count).toBe(1);
      expect(mockTypeOrmRepository.createQueryBuilder).toHaveBeenCalledWith(
        'channel',
      );
      expect(mockQueryBuilder.leftJoinAndSelect).toHaveBeenCalledWith(
        'channel.installation',
        'installation',
      );
      expect(mockQueryBuilder.leftJoinAndSelect).toHaveBeenCalledWith(
        'channel.organization',
        'organization',
      );
      expect(mockQueryBuilder.orderBy).toHaveBeenCalledWith(
        'channel.createdAt',
        'DESC',
      );
      expect(mockQueryBuilder.limit).toHaveBeenCalledWith(10);
      expect(mockQueryBuilder.offset).toHaveBeenCalledWith(0);
    });

    it('should return count of active channels by channel type', async () => {
      // Arrange
      (mockQueryBuilder.getCount as any).mockResolvedValue(2);

      // Act
      const queryBuilder = repository.createQueryBuilder('channel');
      queryBuilder
        .where('channel.channelType = :channelType', {
          channelType: ChannelType.CUSTOMER_CHANNEL,
        })
        .andWhere('channel.isArchived = :isArchived', { isArchived: false })
        .andWhere('channel.isBotActive = :isBotActive', { isBotActive: true });

      const count = await queryBuilder.getCount();

      // Assert
      expect(count).toBe(2);
      expect(mockTypeOrmRepository.createQueryBuilder).toHaveBeenCalledWith(
        'channel',
      );
      expect(mockQueryBuilder.where).toHaveBeenCalledWith(
        'channel.channelType = :channelType',
        { channelType: ChannelType.CUSTOMER_CHANNEL },
      );
      expect(mockQueryBuilder.andWhere).toHaveBeenCalledWith(
        'channel.isArchived = :isArchived',
        { isArchived: false },
      );
      expect(mockQueryBuilder.andWhere).toHaveBeenCalledWith(
        'channel.isBotActive = :isBotActive',
        { isBotActive: true },
      );
    });
  });

  describe('Update operations', () => {
    it('should update a channel status by ID', async () => {
      // Arrange
      const updateResult = { affected: 1, raw: {} };
      (mockTypeOrmRepository.update as any).mockResolvedValue(updateResult);

      const updates = {
        isBotActive: false,
        channelType: ChannelType.NOT_CONFIGURED,
      };

      // Act
      const result = await repository.update({ id: '1' }, updates);

      // Assert
      expect(result).toEqual(updateResult);
      expect(mockTypeOrmRepository.update).toHaveBeenCalledWith(
        { id: '1' },
        updates,
      );
    });

    it('should update multiple channels by installation ID', async () => {
      // Arrange
      const updateResult = { affected: 3, raw: {} };
      (mockTypeOrmRepository.update as any).mockResolvedValue(updateResult);

      const updates = { isBotJoined: false };

      // Act
      const result = await repository.update(
        { installation: { id: '1' } },
        updates,
      );

      // Assert
      expect(result).toEqual(updateResult);
      expect(mockTypeOrmRepository.update).toHaveBeenCalledWith(
        { installation: { id: '1' } },
        updates,
      );
    });
  });
});
