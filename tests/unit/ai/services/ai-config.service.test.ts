import { ConfigService } from '@nestjs/config';
import { Test, TestingModule } from '@nestjs/testing';
import { beforeEach, describe, expect, it, vi } from 'vitest';
import { AiConfigService } from '../../../../src/ai/services/ai-config.service';
import { MODEL_CONFIGS } from '../../../../src/ai/constants/ai-providers.constants';
import { ConfigKeys } from '../../../../src/config/config.service';

describe('AiConfigService', () => {
  let service: AiConfigService;
  let mockConfigService: any;

  beforeEach(async () => {
    mockConfigService = {
      get: vi.fn(),
    };

    const module: TestingModule = await Test.createTestingModule({
      providers: [
        AiConfigService,
        {
          provide: ConfigService,
          useValue: mockConfigService,
        },
      ],
    }).compile();

    service = module.get<AiConfigService>(AiConfigService);
    (service as any).configService = mockConfigService;
  });

  describe('createAiModuleOptions', () => {
    it('should return AI module options with providers', async () => {
      vi.spyOn(service as any, 'getOpenAIConfig').mockReturnValue({
        apiKey: 'openai-api-key',
        baseUrl: 'https://api.openai.com',
        defaultModel: 'gpt-4',
      });

      vi.spyOn(service as any, 'getClaudeConfig').mockReturnValue({
        apiKey: 'claude-api-key',
        baseUrl: 'https://api.anthropic.com',
        defaultModel: 'claude-3-7-sonnet-20250219',
      });

      const options = await service.createAiModuleOptions();

      expect(options).toEqual({
        providers: [
          {
            name: 'openai',
            provider: 'openai',
            config: {
              apiKey: 'openai-api-key',
              baseUrl: 'https://api.openai.com',
              defaultModel: 'gpt-4',
            },
          },
          {
            name: 'claude',
            provider: 'claude',
            config: {
              apiKey: 'claude-api-key',
              baseUrl: 'https://api.anthropic.com',
              defaultModel: 'claude-3-7-sonnet-20250219',
            },
          },
        ],
        defaultProvider: 'openai',
      });
    });
  });

  describe('getClaudeConfig', () => {
    it('should return Claude config with API key and default model from env', () => {
      vi.mock('../../../../src/ai/constants/ai-providers.constants', () => ({
        MODEL_CONFIGS: {
          'claude-3-7-sonnet-20250219': { isDefault: true },
          'claude-3-5-sonnet': {},
        },
      }));

      mockConfigService.get.mockImplementation((key) => {
        if (key === ConfigKeys.CLAUDE_API_KEY) return 'claude-api-key';
        if (key === 'CLAUDE_API_URL') return 'https://api.anthropic.com';
        if (key === 'CLAUDE_DEFAULT_MODEL') return 'claude-3-5-sonnet';
        return undefined;
      });

      const config = (service as any).getClaudeConfig();

      expect(config).toEqual({
        apiKey: 'claude-api-key',
        baseUrl: 'https://api.anthropic.com',
        defaultModel: 'claude-3-5-sonnet',
      });

      expect(mockConfigService.get).toHaveBeenCalledWith(ConfigKeys.CLAUDE_API_KEY);
      expect(mockConfigService.get).toHaveBeenCalledWith('CLAUDE_API_URL');
      expect(mockConfigService.get).toHaveBeenCalledWith('CLAUDE_DEFAULT_MODEL');
    });

    it('should use default Claude model from MODEL_CONFIGS if env variable not set', () => {
      const originalModelConfigs = { ...MODEL_CONFIGS };
      const mockModelConfigs = {
        'claude-3-7-sonnet-20250219': { isDefault: true },
        'claude-3-5-sonnet': {},
      };

      Object.keys(mockModelConfigs).forEach(key => {
        (MODEL_CONFIGS as any)[key] = mockModelConfigs[key];
      });

      mockConfigService.get.mockImplementation((key) => {
        if (key === ConfigKeys.CLAUDE_API_KEY) return 'claude-api-key';
        if (key === 'CLAUDE_API_URL') return 'https://api.anthropic.com';
        return undefined; // No default model in env
      });

      const config = (service as any).getClaudeConfig();

      expect(config).toEqual({
        apiKey: 'claude-api-key',
        baseUrl: 'https://api.anthropic.com',
        defaultModel: 'claude-3-7-sonnet-20250219', // Should use the model marked as default
      });

      Object.keys(MODEL_CONFIGS).forEach(key => {
        if (!originalModelConfigs[key]) {
          delete (MODEL_CONFIGS as any)[key];
        }
      });
    });

    it('should use fallback Claude model if no models in MODEL_CONFIGS', () => {
      const originalModelConfigs = { ...MODEL_CONFIGS };
      
      Object.keys(MODEL_CONFIGS).forEach(key => {
        if (key.startsWith('claude-')) {
          delete (MODEL_CONFIGS as any)[key];
        }
      });

      mockConfigService.get.mockImplementation((key) => {
        if (key === ConfigKeys.CLAUDE_API_KEY) return 'claude-api-key';
        if (key === 'CLAUDE_API_URL') return 'https://api.anthropic.com';
        return undefined; // No default model in env
      });

      const config = (service as any).getClaudeConfig();

      expect(config).toEqual({
        apiKey: 'claude-api-key',
        baseUrl: 'https://api.anthropic.com',
        defaultModel: 'claude-3-7-sonnet-20250219', // Should use the fallback model
      });

      Object.keys(originalModelConfigs).forEach(key => {
        (MODEL_CONFIGS as any)[key] = originalModelConfigs[key];
      });
    });
  });

  describe('getOpenAIConfig', () => {
    it('should return OpenAI config with API key and default model from env', () => {
      vi.mock('../../../../src/ai/constants/ai-providers.constants', () => ({
        MODEL_CONFIGS: {
          'gpt-4': { isDefault: true },
          'gpt-3.5-turbo': {},
        },
      }));

      mockConfigService.get.mockImplementation((key) => {
        if (key === ConfigKeys.OPENAI_API_KEY) return 'openai-api-key';
        if (key === 'OPENAI_API_URL') return 'https://api.openai.com';
        if (key === 'OPENAI_DEFAULT_MODEL') return 'gpt-3.5-turbo';
        return undefined;
      });

      const config = (service as any).getOpenAIConfig();

      expect(config).toEqual({
        apiKey: 'openai-api-key',
        baseUrl: 'https://api.openai.com',
        defaultModel: 'gpt-3.5-turbo',
      });

      expect(mockConfigService.get).toHaveBeenCalledWith(ConfigKeys.OPENAI_API_KEY);
      expect(mockConfigService.get).toHaveBeenCalledWith('OPENAI_API_URL');
      expect(mockConfigService.get).toHaveBeenCalledWith('OPENAI_DEFAULT_MODEL');
    });

    it('should use default OpenAI model from MODEL_CONFIGS if env variable not set', () => {
      const originalModelConfigs = { ...MODEL_CONFIGS };
      const mockModelConfigs = {
        'gpt-4': { isDefault: true },
        'gpt-3.5-turbo': {},
        'o3-mini-2025-01-31': {},
      };

      Object.keys(mockModelConfigs).forEach(key => {
        (MODEL_CONFIGS as any)[key] = mockModelConfigs[key];
      });

      mockConfigService.get.mockImplementation((key) => {
        if (key === ConfigKeys.OPENAI_API_KEY) return 'openai-api-key';
        if (key === 'OPENAI_API_URL') return 'https://api.openai.com';
        return undefined; // No default model in env
      });

      const config = (service as any).getOpenAIConfig();

      expect(config).toEqual({
        apiKey: 'openai-api-key',
        baseUrl: 'https://api.openai.com',
        defaultModel: 'gpt-4', // Should use the model marked as default
      });

      Object.keys(MODEL_CONFIGS).forEach(key => {
        if (!originalModelConfigs[key]) {
          delete (MODEL_CONFIGS as any)[key];
        }
      });
    });

    it('should use first OpenAI model if no default model is marked', () => {
      const originalModelConfigs = { ...MODEL_CONFIGS };
      const mockModelConfigs = {
        'gpt-4': {}, // Not marked as default
        'gpt-3.5-turbo': {},
        'o3-mini-2025-01-31': {},
      };

      Object.keys(mockModelConfigs).forEach(key => {
        (MODEL_CONFIGS as any)[key] = mockModelConfigs[key];
      });

      mockConfigService.get.mockImplementation((key) => {
        if (key === ConfigKeys.OPENAI_API_KEY) return 'openai-api-key';
        if (key === 'OPENAI_API_URL') return 'https://api.openai.com';
        return undefined; // No default model in env
      });

      const config = (service as any).getOpenAIConfig();

      expect(config.defaultModel).toBeTruthy();
      expect(['gpt-4', 'gpt-3.5-turbo', 'o3-mini-2025-01-31']).toContain(config.defaultModel);

      Object.keys(originalModelConfigs).forEach(key => {
        (MODEL_CONFIGS as any)[key] = originalModelConfigs[key];
      });
    });

    it('should use fallback OpenAI model if no models in MODEL_CONFIGS', () => {
      const originalModelConfigs = { ...MODEL_CONFIGS };
      
      Object.keys(MODEL_CONFIGS).forEach(key => {
        if (key.startsWith('gpt-') || key.startsWith('o3-')) {
          delete (MODEL_CONFIGS as any)[key];
        }
      });

      mockConfigService.get.mockImplementation((key) => {
        if (key === ConfigKeys.OPENAI_API_KEY) return 'openai-api-key';
        if (key === 'OPENAI_API_URL') return 'https://api.openai.com';
        return undefined; // No default model in env
      });

      const config = (service as any).getOpenAIConfig();

      expect(config).toEqual({
        apiKey: 'openai-api-key',
        baseUrl: 'https://api.openai.com',
        defaultModel: 'o3-mini-2025-01-31', // Should use the fallback model
      });

      Object.keys(originalModelConfigs).forEach(key => {
        (MODEL_CONFIGS as any)[key] = originalModelConfigs[key];
      });
    });
  });
});
