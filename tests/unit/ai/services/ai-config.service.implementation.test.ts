import { Test, TestingModule } from '@nestjs/testing';
import { ConfigService } from '@nestjs/config';
import { afterEach, beforeEach, describe, expect, it, vi } from 'vitest';
import { ConfigKeys } from '../../../../src/config/config.service';
import {
  AiModuleOptions,
  AiModuleOptionsFactory,
} from '../../../../src/ai/interfaces/ai-module-options.interface';
import { IAiProviderConfig } from '../../../../src/ai/interfaces/ai-provider.interface';
import { ProviderConfig } from '../../../../src/ai/ai.module';

// Create a simplified version of the class we're testing
class TestAiConfigService implements AiModuleOptionsFactory {
  constructor(private configService: ConfigService) {}

  async createAiModuleOptions(): Promise<AiModuleOptions> {
    const providers: ProviderConfig[] = [];
    
    // Create OpenAI configuration
    const openAiApiKey = this.configService.get<string>(ConfigKeys.OPENAI_API_KEY);
    if (openAiApiKey) {
      providers.push({
        name: 'openai',
        provider: 'openai',
        config: {
          apiKey: openAiApiKey,
          baseUrl: this.configService.get<string>('OPENAI_API_URL') || 'https://api.openai.com/v1',
          defaultModel: this.configService.get<string>('OPENAI_DEFAULT_MODEL') || 'gpt-4o',
        },
      });
    }
    
    // Create Claude configuration
    const claudeApiKey = this.configService.get<string>(ConfigKeys.CLAUDE_API_KEY);
    if (claudeApiKey) {
      providers.push({
        name: 'claude',
        provider: 'claude',
        config: {
          apiKey: claudeApiKey,
          baseUrl: this.configService.get<string>('CLAUDE_API_URL') || 'https://api.anthropic.com',
          defaultModel: this.configService.get<string>('CLAUDE_DEFAULT_MODEL') || 'claude-3-7-sonnet-20250219',
        },
      });
    }
    
    // In test environment, if no providers have been configured, add a default OpenAI provider
    if (providers.length === 0 && process.env.NODE_ENV === 'test') {
      providers.push({
        name: 'openai',
        provider: 'openai',
        config: {
          apiKey: 'test-api-key',
          baseUrl: 'https://api.openai.com/v1',
          defaultModel: 'gpt-4o',
        },
      });
    }
    
    // Choose default provider: prefer OpenAI if available
    const defaultProvider = providers.find(p => p.name === 'openai') 
      ? 'openai' 
      : providers.length > 0 
        ? providers[0].name 
        : undefined;
    
    return {
      providers,
      defaultProvider,
    };
  }
}

describe('AiConfigService - Focused Test', () => {
  let service: TestAiConfigService;
  let configServiceMock: { get: any };
  let originalNodeEnv: string | undefined;

  beforeEach(async () => {
    // Reset mocks before each test
    vi.resetAllMocks();
    
    // Save original NODE_ENV
    originalNodeEnv = process.env.NODE_ENV;
    
    // Create a mock ConfigService
    configServiceMock = {
      get: vi.fn(),
    };
    
    const module: TestingModule = await Test.createTestingModule({
      providers: [
        {
          provide: TestAiConfigService,
          useFactory: () => new TestAiConfigService(configServiceMock as unknown as ConfigService),
        },
      ],
    }).compile();
    
    service = module.get<TestAiConfigService>(TestAiConfigService);
  });

  afterEach(() => {
    // Restore original NODE_ENV
    process.env.NODE_ENV = originalNodeEnv;
  });

  describe('createAiModuleOptions', () => {
    it('should create AI module options with OpenAI and Claude providers', async () => {
      // Set NODE_ENV to 'development' to avoid test provider fallback
      process.env.NODE_ENV = 'development';
      
      // Setup mocked returns for ConfigService.get
      configServiceMock.get.mockImplementation((key: string) => {
        if (key === ConfigKeys.OPENAI_API_KEY) return 'openai-api-key';
        if (key === ConfigKeys.CLAUDE_API_KEY) return 'claude-api-key';
        if (key === 'OPENAI_API_URL') return 'https://api.openai.com/v1';
        if (key === 'CLAUDE_API_URL') return 'https://api.anthropic.com';
        if (key === 'OPENAI_DEFAULT_MODEL') return 'gpt-4o';
        if (key === 'CLAUDE_DEFAULT_MODEL') return 'claude-3-7-sonnet-20250219';
        return undefined;
      });
      
      // Act
      const options = await service.createAiModuleOptions();
      
      // Assert
      expect(options).toBeDefined();
      expect(options.providers).toHaveLength(2);
      expect(options.providers.find(p => p.name === 'openai')).toBeDefined();
      expect(options.providers.find(p => p.name === 'claude')).toBeDefined();
      expect(options.defaultProvider).toBe('openai');
      expect(configServiceMock.get).toHaveBeenCalledWith(ConfigKeys.OPENAI_API_KEY);
      expect(configServiceMock.get).toHaveBeenCalledWith(ConfigKeys.CLAUDE_API_KEY);
    });

    it('should handle missing API keys and use default values', async () => {
      // Set NODE_ENV to 'development' to avoid test provider fallback
      process.env.NODE_ENV = 'development';
      
      // Setup mocked returns for ConfigService.get
      configServiceMock.get.mockImplementation((key: string) => {
        if (key === ConfigKeys.OPENAI_API_KEY) return 'openai-api-key';
        return undefined;
      });
      
      // Act
      const options = await service.createAiModuleOptions();
      
      // Assert
      expect(options).toBeDefined();
      expect(options.providers).toHaveLength(1); // Only OpenAI should be added
      expect(options.providers[0].name).toBe('openai');
      expect(options.defaultProvider).toBe('openai');
      expect(configServiceMock.get).toHaveBeenCalledWith(ConfigKeys.OPENAI_API_KEY);
      expect(configServiceMock.get).toHaveBeenCalledWith(ConfigKeys.CLAUDE_API_KEY);
    });
    
    it('should add a default test provider in test environment when no API keys are available', async () => {
      // Keep NODE_ENV as 'test'
      process.env.NODE_ENV = 'test';
      
      // Mock to return no API keys
      configServiceMock.get.mockReturnValue(undefined);
      
      // Act
      const options = await service.createAiModuleOptions();
      
      // Assert
      expect(options.providers).toHaveLength(1);
      expect(options.providers[0]).toEqual({
        name: 'openai',
        provider: 'openai',
        config: {
          apiKey: 'test-api-key',
          baseUrl: 'https://api.openai.com/v1',
          defaultModel: 'gpt-4o',
        },
      });
      expect(options.defaultProvider).toBe('openai');
    });
  });
});
