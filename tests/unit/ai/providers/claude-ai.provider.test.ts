import { beforeEach, describe, expect, it, vi } from 'vitest';
import { ILogger } from '../../../../src/utils';
import { ClaudeAiProvider } from '../../../../src/ai/providers/claude-ai.provider';
import { IAiPromptRequest, IAiProviderConfig } from '../../../../src/ai/interfaces/ai-provider.interface';
import { PromptType } from '../../../../src/ai/constants/prompt-types.enum';

// Mock Anthropic client
vi.mock('@anthropic-ai/sdk', () => {
  return {
    default: vi.fn().mockImplementation(() => ({
      messages: {
        create: vi.fn().mockResolvedValue({
          content: [
            {
              type: 'text',
              text: '```json\n{"result": "Test response"}\n```',
            },
          ],
        }),
      },
    })),
  };
});

describe('ClaudeAiProvider', () => {
  let provider: Claude<PERSON>iP<PERSON>ider;
  let mockLogger: ILogger;

  beforeEach(() => {
    // Reset mocks before each test
    vi.resetAllMocks();

    // Create mock logger
    mockLogger = {
      log: vi.fn(),
      error: vi.fn(),
      warn: vi.fn(),
      debug: vi.fn(),
      verbose: vi.fn(),
    } as unknown as ILogger;

    // Create provider instance
    provider = new ClaudeAiProvider(mockLogger);
  });

  describe('initialize', () => {
    it('should initialize the provider with the given configuration', async () => {
      // Arrange
      const config: IAiProviderConfig = {
        apiKey: 'test-api-key',
        baseUrl: 'https://api.anthropic.com',
        defaultModel: 'claude-3-7-sonnet-********',
      };

      // Act
      await provider.initialize(config);

      // Assert
      expect(provider['initialized']).toBe(true);
      expect(provider['activeModel']).toBe('claude-3-7-sonnet-********');
      expect(mockLogger.debug).toHaveBeenCalledWith('Claude AI provider initialized');
    });

    it('should use default model if not specified in config', async () => {
      // Arrange
      const config: IAiProviderConfig = {
        apiKey: 'test-api-key',
        baseUrl: 'https://api.anthropic.com',
      };

      // Act
      await provider.initialize(config);

      // Assert
      expect(provider['initialized']).toBe(true);
      expect(provider['activeModel']).toBeDefined();
      expect(mockLogger.debug).toHaveBeenCalledWith('Claude AI provider initialized');
    });

    it('should add a fallback model if no Claude models are found', async () => {
      // Arrange
      const config: IAiProviderConfig = {
        apiKey: 'test-api-key',
        baseUrl: 'https://api.anthropic.com',
      };

      // Mock MODEL_CONFIGS to be empty
      vi.mock('../../../../src/ai/constants/ai-providers.constants', () => ({
        MODEL_CONFIGS: {},
      }));

      // Act
      await provider.initialize(config);

      // Assert
      expect(provider['initialized']).toBe(true);
      expect(provider['models']).toHaveProperty('claude-3-7-sonnet-********');
      expect(mockLogger.warn).toHaveBeenCalledWith(
        'No Claude models found in MODEL_CONFIGS, using fallback model'
      );
    });
  });

  describe('executePrompt', () => {
    beforeEach(async () => {
      // Initialize the provider before each test
      await provider.initialize({
        apiKey: 'test-api-key',
        baseUrl: 'https://api.anthropic.com',
        defaultModel: 'claude-3-7-sonnet-********',
      });
      
      // Create a mock client with the messages.create method
      provider['client'] = {
        messages: {
          create: vi.fn()
        }
      } as any;
    });

    it('should execute a prompt and return the response', async () => {
      // Arrange
      const request: IAiPromptRequest = {
        prompt: 'Test prompt',
        conversation: 'Test conversation',
        temperature: 0.7,
        maxTokens: 100,
      };

      // Mock the client.messages.create method
      provider['client'].messages.create = vi.fn().mockResolvedValue({
        content: [
          {
            type: 'text',
            text: '```json\n{"result": "Test response"}\n```',
          },
        ],
      });

      // Act
      const result = await provider.executePrompt(request);

      // Assert
      expect(result.success).toBe(true);
      expect(result.data).toEqual({ result: 'Test response' });
      expect(provider['client'].messages.create).toHaveBeenCalled();
    });

    it('should handle JSON without code fences', async () => {
      // Arrange
      const request: IAiPromptRequest = {
        prompt: 'Test prompt',
        conversation: 'Test conversation',
      };

      // Mock the client.messages.create method
      provider['client'].messages.create = vi.fn().mockResolvedValue({
        content: [
          {
            type: 'text',
            text: '{"result": "Test response without code fences"}',
          },
        ],
      });

      // Act
      const result = await provider.executePrompt(request);

      // Assert
      expect(result.success).toBe(true);
      expect(result.data).toEqual({ result: 'Test response without code fences' });
    });

    it('should handle errors during prompt execution', async () => {
      // Arrange
      const request: IAiPromptRequest = {
        prompt: 'Test prompt',
        conversation: 'Test conversation',
      };

      // Mock the client.messages.create method to throw an error
      provider['client'].messages.create = vi.fn().mockRejectedValue(
        new Error('API error')
      );

      // Act
      const result = await provider.executePrompt(request);

      // Assert
      expect(result.success).toBe(false);
      expect(result.error).toBe('API error');
    });

    it('should handle JSON parsing errors', async () => {
      // Arrange
      const request: IAiPromptRequest = {
        prompt: 'Test prompt',
        conversation: 'Test conversation',
      };

      // Mock the client.messages.create method to return invalid JSON
      provider['client'].messages.create = vi.fn().mockResolvedValue({
        content: [
          {
            type: 'text',
            text: 'This is not valid JSON',
          },
        ],
      });

      // Act
      const result = await provider.executePrompt(request);

      // Assert
      expect(result.success).toBe(false);
      expect(result.error).toContain('Failed to parse Claude AI response');
    });
  });

  describe('detectTicket', () => {
    beforeEach(async () => {
      // Initialize the provider before each test
      await provider.initialize({
        apiKey: 'test-api-key',
        baseUrl: 'https://api.anthropic.com',
        defaultModel: 'claude-3-7-sonnet-********',
      });

      // Set up the prompt for ticket detection
      provider.setPrompt(PromptType.TICKET_DETECTION, 'Test ticket detection prompt');
    });

    it('should detect a valid ticket', async () => {
      // Arrange
      const conversation = 'I need help with my account';
      
      // Mock the executePrompt method
      vi.spyOn(provider, 'executePrompt').mockResolvedValue({
        success: true,
        data: { requiresSupportTicket: true },
      });

      // Act
      const result = await provider.detectTicket(conversation);

      // Assert
      expect(result.success).toBe(true);
      expect(result.data.requiresSupportTicket).toBe(true);
      expect(provider.executePrompt).toHaveBeenCalledWith(
        expect.objectContaining({
          prompt: expect.any(String),
          conversation,
        }),
        PromptType.TICKET_DETECTION
      );
    });

    it('should detect an invalid ticket', async () => {
      // Arrange
      const conversation = 'Just saying hello';
      
      // Mock the executePrompt method
      vi.spyOn(provider, 'executePrompt').mockResolvedValue({
        success: true,
        data: { requiresSupportTicket: false },
      });

      // Act
      const result = await provider.detectTicket(conversation);

      // Assert
      expect(result.success).toBe(true);
      expect(result.data.requiresSupportTicket).toBe(false);
    });
  });

  describe('generateTitle', () => {
    beforeEach(async () => {
      // Initialize the provider before each test
      await provider.initialize({
        apiKey: 'test-api-key',
        baseUrl: 'https://api.anthropic.com',
        defaultModel: 'claude-3-7-sonnet-********',
      });

      // Set up the prompt for title generation
      provider.setPrompt(PromptType.TITLE_GENERATION, 'Test title generation prompt');
    });

    it('should generate a title for a conversation', async () => {
      // Arrange
      const conversation = 'I need help with my account. I cannot log in.';
      
      // Mock the executePrompt method
      vi.spyOn(provider, 'executePrompt').mockResolvedValue({
        success: true,
        data: { title: 'Account Login Issue' },
      });

      // Act
      const result = await provider.generateTitle(conversation);

      // Assert
      expect(result.success).toBe(true);
      expect(result.data.title).toBe('Account Login Issue');
      expect(provider.executePrompt).toHaveBeenCalledWith(
        expect.objectContaining({
          prompt: expect.any(String),
          conversation,
        }),
        PromptType.TITLE_GENERATION
      );
    });

    it('should handle errors during title generation', async () => {
      // Arrange
      const conversation = 'Test conversation';
      
      // Mock the executePrompt method to return an error
      vi.spyOn(provider, 'executePrompt').mockResolvedValue({
        success: false,
        error: 'API error',
      });

      // Act
      const result = await provider.generateTitle(conversation);

      // Assert
      expect(result.success).toBe(false);
      expect(result.error).toContain('API error');
    });
  });

  describe('model management', () => {
    beforeEach(async () => {
      // Initialize the provider before each test
      await provider.initialize({
        apiKey: 'test-api-key',
        baseUrl: 'https://api.anthropic.com',
        defaultModel: 'claude-3-7-sonnet-********',
      });
    });

    it('should get available models', () => {
      // Act
      const models = provider.getAvailableModels();

      // Assert
      expect(models).toBeInstanceOf(Array);
      expect(models.length).toBeGreaterThan(0);
      expect(models).toContain('claude-3-7-sonnet-********');
    });

    it('should get the active model', () => {
      // Act
      const activeModel = provider.getActiveModel();

      // Assert
      expect(activeModel).toBe('claude-3-7-sonnet-********');
    });

    it('should set the active model', () => {
      // Arrange
      const newModel = 'claude-3-opus-20240229';
      
      // Add the model if it doesn't exist
      if (!provider['models'][newModel]) {
        provider['models'][newModel] = {
          modelId: newModel,
          temperature: 0.5,
          maxTokens: 4000,
        };
      }

      // Act
      provider.setActiveModel(newModel);

      // Assert
      expect(provider.getActiveModel()).toBe(newModel);
    });
  });
});
