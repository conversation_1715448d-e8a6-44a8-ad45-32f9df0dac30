import { describe, expect, it, vi, beforeEach } from 'vitest';
import { GrokAiProvider } from '../../../../src/ai/providers/grok-ai.provider';
import OpenAI from 'openai';
import { Test } from '@nestjs/testing';
import { CUSTOM_LOGGER_TOKEN } from '../../../../src/utils';

vi.mock('openai', () => {
  return {
    default: vi.fn().mockImplementation(() => ({
      chat: {
        completions: {
          create: vi.fn(),
        },
      },
    })),
  };
});

describe('GrokAiProvider', () => {
  let provider: GrokAiProvider;
  let mockOpenAI: any;
  let mockLogger: any;

  beforeEach(async () => {
    mockLogger = {
      log: vi.fn(),
      debug: vi.fn(),
      error: vi.fn(),
      warn: vi.fn(),
    };

    const moduleRef = await Test.createTestingModule({
      providers: [
        {
          provide: GrokAiProvider,
          useClass: GrokAiProvider,
        },
        {
          provide: CUSTOM_LOGGER_TOKEN,
          useValue: mockLogger,
        },
      ],
    }).compile();

    provider = moduleRef.get<GrokAiProvider>(GrokAiProvider);
    mockOpenAI = OpenAI as any;
    vi.clearAllMocks();
  });

  describe('initialize', () => {
    it('should initialize with provided config', async () => {
      const config = {
        apiKey: 'test-api-key',
        baseUrl: 'https://custom-grok-api.com',
        defaultModel: 'grok-2',
        models: {
          'grok-2': {
            modelId: 'grok-2',
            temperature: 0.7,
            maxTokens: 2000,
          },
        },
      };

      await provider.initialize(config);

      expect(mockOpenAI).toHaveBeenCalledWith({
        apiKey: 'test-api-key',
        baseURL: 'https://custom-grok-api.com',
        timeout: 30000,
        maxRetries: 2,
      });

      expect((provider as any).models).toEqual(config.models);
      expect((provider as any).activeModel).toBe('grok-2');
      expect((provider as any).initialized).toBe(true);
    });

    it('should initialize with default values when not provided', async () => {
      const config = {
        apiKey: 'test-api-key',
      };

      await provider.initialize(config);

      expect(mockOpenAI).toHaveBeenCalledWith({
        apiKey: 'test-api-key',
        baseURL: 'https://api.x.ai/v1',
        timeout: 30000,
        maxRetries: 2,
      });

      expect((provider as any).models).toHaveProperty('grok-2');
      expect((provider as any).activeModel).toBe('grok-2');
      expect((provider as any).initialized).toBe(true);
    });
  });

  describe('executePrompt', () => {
    it('should return error if not initialized', async () => {
      const result = await provider.executePrompt({
        prompt: 'Test prompt',
      });

      expect(result).toEqual({
        success: false,
        error: 'Grok AI provider not initialized',
      });
    });

    it('should execute prompt successfully', async () => {
      await provider.initialize({
        apiKey: 'test-api-key',
      });

      const mockResponse = {
        choices: [
          {
            message: {
              content: '{"result": "success"}',
            },
          },
        ],
      };

      const mockCreateMethod = vi.fn().mockResolvedValue(mockResponse);
      (provider as any).client = {
        chat: {
          completions: {
            create: mockCreateMethod,
          },
        },
      };

      vi.spyOn(provider as any, 'buildPrompt').mockReturnValue('Built prompt');

      const result = await provider.executePrompt({
        prompt: 'Test prompt',
        temperature: 0.8,
        maxTokens: 3000,
      });

      expect(mockCreateMethod).toHaveBeenCalledWith({
        model: 'grok-2',
        messages: [
          {
            role: 'system',
            content: expect.stringContaining('You are a helpful assistant'),
          },
          { role: 'user', content: 'Built prompt' },
        ],
        temperature: 0.8,
        max_tokens: 3000,
        response_format: { type: 'json_object' },
      });

      expect(result).toEqual({
        success: true,
        data: { result: 'success' },
        rawResponse: mockResponse,
      });
    });

    it('should handle JSON parsing errors', async () => {
      await provider.initialize({
        apiKey: 'test-api-key',
      });

      const mockResponse = {
        choices: [
          {
            message: {
              content: 'Invalid JSON',
            },
          },
        ],
      };

      const mockCreateMethod = vi.fn().mockResolvedValue(mockResponse);
      (provider as any).client = {
        chat: {
          completions: {
            create: mockCreateMethod,
          },
        },
      };

      const consoleErrorSpy = vi.spyOn(console, 'error').mockImplementation(() => {});

      const result = await provider.executePrompt({
        prompt: 'Test prompt',
      });

      expect(result).toEqual({
        success: false,
        error: expect.stringContaining('Failed to parse AI response'),
      });

      expect(consoleErrorSpy).toHaveBeenCalled();
    });

    it('should handle API errors', async () => {
      await provider.initialize({
        apiKey: 'test-api-key',
      });

      const mockCreateMethod = vi.fn().mockRejectedValue(new Error('API error'));
      (provider as any).client = {
        chat: {
          completions: {
            create: mockCreateMethod,
          },
        },
      };

      const consoleErrorSpy = vi.spyOn(console, 'error').mockImplementation(() => {});

      const result = await provider.executePrompt({
        prompt: 'Test prompt',
      });

      expect(result).toEqual({
        success: false,
        error: 'API error',
      });

      expect(consoleErrorSpy).toHaveBeenCalled();
    });

    it('should handle timeout errors', async () => {
      await provider.initialize({
        apiKey: 'test-api-key',
      });

      const mockCreateMethod = vi.fn().mockRejectedValue(new Error('ECONNABORTED'));
      (provider as any).client = {
        chat: {
          completions: {
            create: mockCreateMethod,
          },
        },
      };

      const consoleErrorSpy = vi.spyOn(console, 'error').mockImplementation(() => {});

      const result = await provider.executePrompt({
        prompt: 'Test prompt',
      });

      expect(result).toEqual({
        success: false,
        error: 'Request timed out',
      });

      expect(consoleErrorSpy).toHaveBeenCalled();
    });

    it('should handle rate limit errors', async () => {
      await provider.initialize({
        apiKey: 'test-api-key',
      });

      const mockCreateMethod = vi.fn().mockRejectedValue(new Error('429'));
      (provider as any).client = {
        chat: {
          completions: {
            create: mockCreateMethod,
          },
        },
      };

      const consoleErrorSpy = vi.spyOn(console, 'error').mockImplementation(() => {});

      const result = await provider.executePrompt({
        prompt: 'Test prompt',
      });

      expect(result).toEqual({
        success: false,
        error: 'Rate limit exceeded. Please try again later.',
      });

      expect(consoleErrorSpy).toHaveBeenCalled();
    });
  });
});
