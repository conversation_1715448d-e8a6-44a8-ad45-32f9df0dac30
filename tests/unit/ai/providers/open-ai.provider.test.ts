import { beforeEach, describe, expect, it, vi } from 'vitest';
import { <PERSON><PERSON> } from 'vitest';
import { ILogger } from '../../../../src/utils';
import { OpenAiProvider } from '../../../../src/ai/providers/open-ai.provider';
import { IAiPromptRequest, IAiProviderConfig } from '../../../../src/ai/interfaces/ai-provider.interface';
import { PromptType } from '../../../../src/ai/constants/prompt-types.enum';

// Mock OpenAI client
vi.mock('openai', () => {
  return {
    default: vi.fn().mockImplementation(() => ({
      chat: {
        completions: {
          create: vi.fn().mockResolvedValue({
            choices: [
              {
                message: {
                  content: '{"success": true, "data": {"test": "value"}}',
                },
              },
            ],
          }),
        },
      },
    })),
  };
});

describe('OpenAiProvider', () => {
  let provider: OpenAiProvider;
  let mockLogger: ILogger;

  beforeEach(() => {
    // Reset mocks before each test
    vi.resetAllMocks();

    // Create mock logger
    mockLogger = {
      log: vi.fn(),
      error: vi.fn(),
      warn: vi.fn(),
      debug: vi.fn(),
      verbose: vi.fn(),
    } as unknown as ILogger;

    // Create provider instance
    provider = new OpenAiProvider(mockLogger);
  });

  describe('initialize', () => {
    it('should initialize the provider with the given configuration', async () => {
      // Arrange
      const config: IAiProviderConfig = {
        apiKey: 'test-api-key',
        baseUrl: 'https://api.openai.com/v1',
        defaultModel: 'gpt-4o',
      };

      // Act
      await provider.initialize(config);

      // Assert
      expect(provider['initialized']).toBe(true);
      expect(provider['activeModel']).toBe('gpt-4o');
      expect(mockLogger.debug).toHaveBeenCalledWith('OpenAI provider initialized with model: gpt-4o');
    });

    it('should use default model if not specified in config', async () => {
      // Arrange
      const config: IAiProviderConfig = {
        apiKey: 'test-api-key',
        baseUrl: 'https://api.openai.com/v1',
      };

      // Act
      await provider.initialize(config);

      // Assert
      expect(provider['initialized']).toBe(true);
      expect(provider['activeModel']).toBeDefined();
      expect(mockLogger.debug).toHaveBeenCalledWith('OpenAI provider initialized with model: ' + provider['activeModel']);
    });
  });

  describe('executePrompt', () => {
    beforeEach(async () => {
      // Initialize the provider before each test
      await provider.initialize({
        apiKey: 'test-api-key',
        baseUrl: 'https://api.openai.com/v1',
        defaultModel: 'gpt-4o',
      });
      
      // Create a mock client with chat.completions structure
      provider['client'] = {
        chat: {
          completions: {
            create: vi.fn()
          }
        }
      } as any;
    });

    it('should execute a prompt and return the response', async () => {
      // Arrange
      const request: IAiPromptRequest = {
        prompt: 'Test prompt',
        conversation: 'Test conversation',
        temperature: 0.7,
        maxTokens: 100,
      };

      // Mock the client.chat.completions.create method
      provider['client'].chat.completions.create = vi.fn().mockResolvedValue({
        choices: [
          {
            message: {
              content: '{"result": "Test response"}',
            },
          },
        ],
      });

      // Act
      const result = await provider.executePrompt(request);

      // Assert
      expect(result.success).toBe(true);
      expect(result.data).toEqual({ result: 'Test response' });
      expect(provider['client'].chat.completions.create).toHaveBeenCalled();
    });

    it('should handle errors during prompt execution', async () => {
      // Arrange
      const request: IAiPromptRequest = {
        prompt: 'Test prompt',
        conversation: 'Test conversation',
      };

      // Mock the client.chat.completions.create method to throw an error
      provider['client'].chat.completions.create = vi.fn().mockRejectedValue(
        new Error('API error')
      );

      // Act
      const result = await provider.executePrompt(request);

      // Assert
      expect(result.success).toBe(false);
      expect(result.error).toBe('API error');
      expect(mockLogger.error).toHaveBeenCalledWith(
        expect.stringContaining('OpenAI API error: API error')
      );
    });

    it('should handle rate limiting errors', async () => {
      // Arrange
      const request: IAiPromptRequest = {
        prompt: 'Test prompt',
        conversation: 'Test conversation',
      };

      // Mock the client.chat.completions.create method to throw a rate limit error
      const rateLimitError = new Error('Rate limit exceeded');
      (rateLimitError as any).status = 429;
      provider['client'].chat.completions.create = vi.fn().mockRejectedValue(
        rateLimitError
      );

      // Act
      const result = await provider.executePrompt(request);

      // Assert
      expect(result.success).toBe(false);
      expect(result.error).toBe('Rate limit exceeded. Please try again later.');
      expect(mockLogger.error).toHaveBeenCalledWith('OpenAI API rate limit exceeded');
    });

    it('should handle timeout errors', async () => {
      // Arrange
      const request: IAiPromptRequest = {
        prompt: 'Test prompt',
        conversation: 'Test conversation',
      };

      // Mock the client.chat.completions.create method to throw a timeout error
      const timeoutError = new Error('Request timed out');
      (timeoutError as any).code = 'ETIMEDOUT';
      provider['client'].chat.completions.create = vi.fn().mockRejectedValue(
        timeoutError
      );

      // Act
      const result = await provider.executePrompt(request);

      // Assert
      expect(result.success).toBe(false);
      expect(result.error).toBe('Request timed out');
      expect(mockLogger.error).toHaveBeenCalledWith('OpenAI API request timed out');
    });
  });

  describe('detectTicket', () => {
    beforeEach(async () => {
      // Initialize the provider before each test
      await provider.initialize({
        apiKey: 'test-api-key',
        baseUrl: 'https://api.openai.com/v1',
        defaultModel: 'gpt-4o',
      });

      // Set up the prompt for ticket detection
      provider.setPrompt(PromptType.TICKET_DETECTION, 'Test ticket detection prompt');
    });

    it('should detect a valid ticket', async () => {
      // Arrange
      const conversation = 'I need help with my account';
      
      // Mock the executePrompt method
      vi.spyOn(provider, 'executePrompt').mockResolvedValue({
        success: true,
        data: { requiresSupportTicket: true },
      });

      // Act
      const result = await provider.detectTicket(conversation);

      // Assert
      expect(result.success).toBe(true);
      expect(result.data.requiresSupportTicket).toBe(true);
      expect(provider.executePrompt).toHaveBeenCalledWith(
        expect.objectContaining({
          prompt: expect.any(String),
          conversation,
        }),
        PromptType.TICKET_DETECTION
      );
    });

    it('should detect an invalid ticket', async () => {
      // Arrange
      const conversation = 'Just saying hello';
      
      // Mock the executePrompt method
      vi.spyOn(provider, 'executePrompt').mockResolvedValue({
        success: true,
        data: { requiresSupportTicket: false },
      });

      // Act
      const result = await provider.detectTicket(conversation);

      // Assert
      expect(result.success).toBe(true);
      expect(result.data.requiresSupportTicket).toBe(false);
    });

    it('should handle errors during ticket detection', async () => {
      // Arrange
      const conversation = 'Test conversation';
      
      // Mock the executePrompt method to return an error
      vi.spyOn(provider, 'executePrompt').mockResolvedValue({
        success: false,
        error: 'API error',
      });

      // Act
      const result = await provider.detectTicket(conversation);

      // Assert
      expect(result.success).toBe(false);
      expect(result.error).toBe('API error');
    });
  });

  describe('analyzeSentiment', () => {
    beforeEach(async () => {
      // Initialize the provider before each test
      await provider.initialize({
        apiKey: 'test-api-key',
        baseUrl: 'https://api.openai.com/v1',
        defaultModel: 'gpt-4o',
      });

      // Set up the prompt for sentiment analysis
      provider.setPrompt(PromptType.SENTIMENT_ANALYSIS, 'Test sentiment analysis prompt');
    });

    it('should analyze sentiment correctly', async () => {
      // Arrange
      const conversation = 'I am very happy with your service';
      
      // Mock the executePrompt method
      vi.spyOn(provider, 'executePrompt').mockResolvedValue({
        success: true,
        data: { 
          sentiment: 'positive',
          confidence: 0.9,
          explanation: 'The customer expresses happiness'
        },
      });

      // Act
      const result = await provider.analyzeSentiment(conversation);

      // Assert
      expect(result.success).toBe(true);
      expect(result.data.sentiment).toBe('positive');
      expect(result.data.confidence).toBe(0.9);
      expect(provider.executePrompt).toHaveBeenCalledWith(
        expect.objectContaining({
          prompt: expect.any(String),
          conversation,
        }),
        PromptType.SENTIMENT_ANALYSIS
      );
    });
  });

  describe('model management', () => {
    beforeEach(async () => {
      // Initialize the provider before each test
      await provider.initialize({
        apiKey: 'test-api-key',
        baseUrl: 'https://api.openai.com/v1',
        defaultModel: 'gpt-4o',
      });
    });

    it('should get available models', () => {
      // Act
      const models = provider.getAvailableModels();

      // Assert
      expect(models).toBeInstanceOf(Array);
      expect(models.length).toBeGreaterThan(0);
      expect(models).toContain('gpt-4o');
    });

    it('should get the active model', () => {
      // Act
      const activeModel = provider.getActiveModel();

      // Assert
      expect(activeModel).toBe('gpt-4o');
    });

    it('should set the active model', () => {
      // Arrange
      const newModel = 'o3-mini-2025-01-31';
      
      // Add the model if it doesn't exist
      if (!provider['models'][newModel]) {
        provider['models'][newModel] = {
          modelId: newModel,
          temperature: 0.5,
          maxTokens: 4000,
        };
      }

      // Act
      provider.setActiveModel(newModel);

      // Assert
      expect(provider.getActiveModel()).toBe(newModel);
    });
  });
});
