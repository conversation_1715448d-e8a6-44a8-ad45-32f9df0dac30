import { CACHE_MANAGER } from '@nestjs/cache-manager';
import { Test, TestingModule } from '@nestjs/testing';
import { getRepositoryToken } from '@nestjs/typeorm';
import { beforeEach, describe, expect, it, vi } from 'vitest';
import { AiService } from '../../../src/ai/ai.service';
import { PromptType } from '../../../src/ai/constants/prompt-types.enum';
import { AI_PROVIDER } from '../../../src/ai/constants/provider.token';
import { AiProviderRegistry } from '../../../src/ai/providers/ai-provider-registry';
import { Prompts } from '../../../src/database/entities/prompts/prompts.entity';
import { CUSTOM_LOGGER_TOKEN } from '../../../src/utils';

describe('AiService', () => {
  let service: AiService;
  let mockLogger: any;
  let mockAiProvider: any;
  let mockCacheManager: any;
  let mockPromptsRepository: any;

  beforeEach(async () => {
    mockLogger = {
      log: vi.fn(),
      debug: vi.fn(),
      warn: vi.fn(),
      error: vi.fn(),
    };

    mockAiProvider = {
      detectTicket: vi.fn(),
      analyzeSentiment: vi.fn(),
      routeToTeam: vi.fn(),
      extractCustomFields: vi.fn(),
      executePrompt: vi.fn(),
      setPrompt: vi.fn(),
      setTeamPrompts: vi.fn(),
      getRegisteredProviders: vi.fn(),
      setActiveProvider: vi.fn(),
      getActiveProvider: vi.fn(),
      getAvailableModels: vi.fn(),
      setActiveModel: vi.fn(),
      getActiveModel: vi.fn(),
      addModel: vi.fn(),
      complete: vi.fn(),
      generateTitle: vi.fn(),
      generateDescription: vi.fn(),
    };

    mockCacheManager = {
      get: vi.fn(),
      set: vi.fn(),
    };

    mockPromptsRepository = {
      findOne: vi.fn(),
    };

    const module: TestingModule = await Test.createTestingModule({
      providers: [
        AiService,
        {
          provide: CUSTOM_LOGGER_TOKEN,
          useValue: mockLogger,
        },
        {
          provide: AI_PROVIDER,
          useValue: mockAiProvider,
        },
        {
          provide: CACHE_MANAGER,
          useValue: mockCacheManager,
        },
        {
          provide: getRepositoryToken(Prompts),
          useValue: mockPromptsRepository,
        },
      ],
    }).compile();

    service = module.get<AiService>(AiService);
  });

  describe('getErrorMessage', () => {
    it('should return error message for Error instance', () => {
      const error = new Error('Test error');
      const result = (service as any).getErrorMessage(error);
      expect(result).toBe('Test error');
    });

    it('should convert non-Error to string', () => {
      const result = (service as any).getErrorMessage('string error');
      expect(result).toBe('string error');
    });
  });

  describe('safeAiOperation', () => {
    it('should return operation result on success', async () => {
      const operation = vi.fn().mockResolvedValue('success');
      const result = await (service as any).safeAiOperation(
        operation,
        'fallback',
        'test operation'
      );
      expect(result).toBe('success');
      expect(operation).toHaveBeenCalled();
    });

    it('should return fallback on error', async () => {
      const operation = vi.fn().mockRejectedValue(new Error('Test error'));
      const result = await (service as any).safeAiOperation(
        operation,
        'fallback',
        'test operation'
      );
      expect(result).toBe('fallback');
      expect(mockLogger.error).toHaveBeenCalledWith(
        'test operation failed: Test error',
        expect.any(String)
      );
    });

    it('should handle timeout', async () => {
      const operation = vi.fn().mockRejectedValue(
        new Error('test operation timed out after 45000ms')
      );
      
      const result = await (service as any).safeAiOperation(
        operation,
        'fallback',
        'test operation'
      );
      
      expect(result).toBe('fallback');
      expect(mockLogger.error).toHaveBeenCalledWith(
        'test operation failed: test operation timed out after 45000ms',
        expect.stringContaining('test operation timed out after 45000ms')
      );
    });
  });

  describe('withTeamPrompts', () => {
    it('should load team prompts if team ID is provided', async () => {
      const loadTeamPromptsSpy = vi.spyOn(service as any, 'loadTeamPrompts').mockResolvedValue(true);
      const operation = vi.fn().mockResolvedValue('result');

      const result = await (service as any).withTeamPrompts(
        operation,
        'team-id',
        'installation-id',
        'org-id'
      );

      expect(loadTeamPromptsSpy).toHaveBeenCalledWith('team-id', 'installation-id', 'org-id');
      expect(operation).toHaveBeenCalled();
      expect(result).toBe('result');
    });

    it('should not load team prompts if team ID is not provided', async () => {
      const loadTeamPromptsSpy = vi.spyOn(service as any, 'loadTeamPrompts');
      const operation = vi.fn().mockResolvedValue('result');

      const result = await (service as any).withTeamPrompts(operation);

      expect(loadTeamPromptsSpy).not.toHaveBeenCalled();
      expect(operation).toHaveBeenCalled();
      expect(result).toBe('result');
    });
  });

  describe('loadTeamPrompts', () => {
    it('should use cached prompts if available', async () => {
      const cachedPrompts = { ticket_detection: 'cached prompt' };
      mockCacheManager.get.mockResolvedValue(cachedPrompts);

      const result = await service.loadTeamPrompts('team-id', 'installation-id', 'org-id');

      expect(mockCacheManager.get).toHaveBeenCalledWith('team-prompts-team-id');
      expect(mockLogger.debug).toHaveBeenCalledWith('Using cached prompts for team team-id');
      expect(mockAiProvider.setTeamPrompts).toHaveBeenCalledWith('team-id', cachedPrompts);
      expect(result).toBe(true);
    });

    it('should load prompts from repository if not cached', async () => {
      mockCacheManager.get.mockResolvedValue(null);
      
      const mockPrompt = {
        prompts: {
          ticket_detection: 'test prompt',
          sentiment_analysis: 'sentiment prompt',
          urgency_detection: 'urgency prompt',
          custom_fields: 'custom fields prompt',
          title_generation: 'title prompt',
          description_generation: 'description prompt',
        },
      };
      
      mockPromptsRepository.findOne.mockResolvedValue(mockPrompt);

      const result = await service.loadTeamPrompts('team-id', 'installation-id', 'org-id');

      expect(mockCacheManager.get).toHaveBeenCalledWith('team-prompts-team-id');
      expect(mockPromptsRepository.findOne).toHaveBeenCalledWith({
        where: {
          platformTeam: { id: 'team-id' },
          installation: { id: 'installation-id' },
          organization: { id: 'org-id' },
          isDefault: true,
          isEnabled: true,
        },
      });
      
      expect(mockCacheManager.set).toHaveBeenCalled();
      expect(mockAiProvider.setTeamPrompts).toHaveBeenCalled();
      expect(result).toBe(true);
    });

    it('should return false if no prompt is found', async () => {
      mockCacheManager.get.mockResolvedValue(null);
      mockPromptsRepository.findOne.mockResolvedValue(null);

      const result = await service.loadTeamPrompts('team-id', 'installation-id', 'org-id');

      expect(mockLogger.warn).toHaveBeenCalledWith('No default prompt found for team team-id');
      expect(result).toBe(false);
    });

    it('should handle errors', async () => {
      mockPromptsRepository.findOne.mockRejectedValueOnce(new Error('Repository error'));
      mockCacheManager.get.mockResolvedValueOnce(null);
      
      const result = await service.loadTeamPrompts('team-id', 'installation-id', 'org-id');
      
      expect(mockLogger.error).toHaveBeenCalledWith(
        expect.stringContaining('Error loading team prompts for team team-id')
      );
      expect(result).toBe(false);
    });
  });

  describe('isValidTicket', () => {
    it('should return true if ticket detection succeeds and requires ticket', async () => {
      mockAiProvider.detectTicket.mockResolvedValue({
        success: true,
        data: { requiresSupportTicket: true },
      });

      const result = await service.isValidTicket('test conversation', 'team-id', 'installation-id', 'org-id');

      expect(mockAiProvider.detectTicket).toHaveBeenCalledWith('test conversation', 'team-id');
      expect(result).toBe(true);
    });

    it('should return false if ticket detection succeeds and does not require ticket', async () => {
      mockAiProvider.detectTicket.mockResolvedValue({
        success: true,
        data: { requiresSupportTicket: false },
      });

      const result = await service.isValidTicket('test conversation', 'team-id', 'installation-id', 'org-id');

      expect(mockAiProvider.detectTicket).toHaveBeenCalledWith('test conversation', 'team-id');
      expect(result).toBe(false);
    });

    it('should return true (default) if ticket detection fails', async () => {
      mockAiProvider.detectTicket.mockResolvedValue({
        success: false,
        error: 'Detection failed',
      });

      const result = await service.isValidTicket('test conversation', 'team-id', 'installation-id', 'org-id');

      expect(mockAiProvider.detectTicket).toHaveBeenCalledWith('test conversation', 'team-id');
      expect(result).toBe(true);
    });
  });

  describe('analyzeSentiment', () => {
    it('should return sentiment analysis result on success', async () => {
      const mockResponse = {
        success: true,
        data: { sentiment: 'positive', score: 0.8 },
      };
      mockAiProvider.analyzeSentiment.mockResolvedValue(mockResponse);

      const result = await service.analyzeSentiment('test conversation', 'team-id', 'installation-id', 'org-id');

      expect(mockAiProvider.analyzeSentiment).toHaveBeenCalledWith('test conversation', 'team-id');
      expect(result).toEqual(mockResponse);
    });

    it('should return error response if sentiment analysis fails', async () => {
      mockAiProvider.analyzeSentiment.mockRejectedValue(new Error('Analysis failed'));

      const result = await service.analyzeSentiment('test conversation', 'team-id', 'installation-id', 'org-id');

      expect(mockAiProvider.analyzeSentiment).toHaveBeenCalledWith('test conversation', 'team-id');
      expect(result).toEqual({
        success: false,
        error: 'Failed to analyze sentiment',
      });
    });
  });

  describe('detectUrgency', () => {
    it('should return detected urgency when valid', async () => {
      const urgencies = [
        { name: 'High', description: 'Urgent issues' },
        { name: 'Medium', description: 'Normal issues' },
        { name: 'Low', description: 'Minor issues' },
      ];

      mockAiProvider.complete.mockResolvedValue('{"urgency_level": "High"}');

      const result = await service.detectUrgency('urgent issue', urgencies, {
        teamId: 'team-id',
        installationId: 'installation-id',
        organizationId: 'org-id',
      });

      expect(mockAiProvider.complete).toHaveBeenCalled();
      expect(result).toBe('High');
    });

    it('should return default urgency when detection fails', async () => {
      const urgencies = [
        { name: 'High', description: 'Urgent issues' },
        { name: 'Medium', description: 'Normal issues' },
        { name: 'Low', description: 'Minor issues' },
      ];

      mockAiProvider.complete.mockRejectedValue(new Error('Detection failed'));

      const result = await service.detectUrgency('urgent issue', urgencies, {
        defaultUrgency: 'Medium',
      });

      expect(result).toBe('Medium');
    });

    it('should return default urgency when content is empty', async () => {
      const urgencies = [
        { name: 'High', description: 'Urgent issues' },
        { name: 'Medium', description: 'Normal issues' },
      ];

      const result = await service.detectUrgency('', urgencies, {
        defaultUrgency: 'Medium',
      });

      expect(mockAiProvider.complete).not.toHaveBeenCalled();
      expect(result).toBe('Medium');
    });

    it('should return default urgency when urgencies array is empty', async () => {
      const result = await service.detectUrgency('urgent issue', [], {
        defaultUrgency: 'Medium',
      });

      expect(mockAiProvider.complete).not.toHaveBeenCalled();
      expect(result).toBe('Medium');
    });

    it('should return default urgency when response is not valid JSON', async () => {
      const urgencies = [
        { name: 'High', description: 'Urgent issues' },
        { name: 'Medium', description: 'Normal issues' },
      ];

      mockAiProvider.complete.mockResolvedValue('invalid json');

      const result = await service.detectUrgency('urgent issue', urgencies, {
        defaultUrgency: 'Medium',
      });

      expect(mockAiProvider.complete).toHaveBeenCalled();
      expect(result).toBe('Medium');
    });

    it('should return default urgency when detected urgency is not in the list', async () => {
      const urgencies = [
        { name: 'High', description: 'Urgent issues' },
        { name: 'Medium', description: 'Normal issues' },
      ];

      mockAiProvider.complete.mockResolvedValue('{"urgency_level": "Critical"}');

      const result = await service.detectUrgency('urgent issue', urgencies, {
        defaultUrgency: 'Medium',
      });

      expect(mockAiProvider.complete).toHaveBeenCalled();
      expect(result).toBe('Medium');
    });
  });

  describe('routeToTeam', () => {
    it('should return team routing result on success', async () => {
      const mockResponse = {
        success: true,
        data: { teamId: 'team-1', confidence: 0.9 },
      };
      mockAiProvider.routeToTeam.mockResolvedValue(mockResponse);

      const result = await service.routeToTeam('test conversation', 'team-id', 'installation-id', 'org-id');

      expect(mockAiProvider.routeToTeam).toHaveBeenCalledWith('test conversation', 'team-id');
      expect(result).toEqual(mockResponse);
    });

    it('should return error response if team routing fails', async () => {
      mockAiProvider.routeToTeam.mockRejectedValue(new Error('Routing failed'));

      const result = await service.routeToTeam('test conversation', 'team-id', 'installation-id', 'org-id');

      expect(mockAiProvider.routeToTeam).toHaveBeenCalledWith('test conversation', 'team-id');
      expect(result).toEqual({
        success: false,
        error: 'Failed to route to team',
      });
    });
  });

  describe('extractCustomFields', () => {
    it('should return custom fields result on success', async () => {
      const mockResponse = {
        success: true,
        data: { fields: { category: 'billing', priority: 'high' } },
      };
      mockAiProvider.extractCustomFields.mockResolvedValue(mockResponse);

      const result = await service.extractCustomFields(
        'test conversation',
        ['category', 'priority'],
        'team-id',
        'installation-id',
        'org-id'
      );

      expect(mockAiProvider.extractCustomFields).toHaveBeenCalledWith(
        'test conversation',
        ['category', 'priority'],
        'team-id'
      );
      expect(result).toEqual(mockResponse);
    });

    it('should return error response if custom fields extraction fails', async () => {
      mockAiProvider.extractCustomFields.mockRejectedValue(new Error('Extraction failed'));

      const result = await service.extractCustomFields(
        'test conversation',
        ['category', 'priority'],
        'team-id',
        'installation-id',
        'org-id'
      );

      expect(mockAiProvider.extractCustomFields).toHaveBeenCalledWith(
        'test conversation',
        ['category', 'priority'],
        'team-id'
      );
      expect(result).toEqual({
        success: false,
        error: 'Failed to extract custom fields',
      });
    });
  });

  describe('executePrompt', () => {
    it('should return prompt execution result on success', async () => {
      const mockRequest = {
        prompt: 'test prompt',
        teamId: 'team-id',
        installationId: 'installation-id',
        organizationId: 'org-id',
      };
      
      const mockResponse = {
        success: true,
        data: { result: 'test result' },
      };
      
      mockAiProvider.executePrompt.mockResolvedValue(mockResponse);

      const result = await service.executePrompt(mockRequest);

      expect(mockAiProvider.executePrompt).toHaveBeenCalledWith(mockRequest);
      expect(result).toEqual(mockResponse);
    });

    it('should return error response if prompt execution fails', async () => {
      const mockRequest = {
        prompt: 'test prompt',
        teamId: 'team-id',
        installationId: 'installation-id',
        organizationId: 'org-id',
      };
      
      mockAiProvider.executePrompt.mockRejectedValue(new Error('Execution failed'));

      const result = await service.executePrompt(mockRequest);

      expect(mockAiProvider.executePrompt).toHaveBeenCalledWith(mockRequest);
      expect(result).toEqual({
        success: false,
        error: 'Failed to execute prompt',
      });
    });
  });

  describe('setPrompt', () => {
    it('should set prompt successfully', () => {
      const promptType = 'TICKET_DETECTION' as any;
      const promptText = 'test prompt';

      service.setPrompt(promptType, promptText);

      expect(mockAiProvider.setPrompt).toHaveBeenCalledWith(promptType, promptText);
      expect(mockLogger.debug).toHaveBeenCalledWith(`Successfully updated ${promptType} prompt template`);
    });

    it('should throw error if setting prompt fails', () => {
      const promptType = 'TICKET_DETECTION' as any;
      const promptText = 'test prompt';
      
      mockAiProvider.setPrompt.mockImplementation(() => {
        throw new Error('Failed to set prompt');
      });

      expect(() => service.setPrompt(promptType, promptText)).toThrow('Failed to set prompt');
      expect(mockLogger.error).toHaveBeenCalledWith(
        `Failed to set ${promptType} prompt: Failed to set prompt`
      );
    });
  });

  describe('setTeamPrompts', () => {
    it('should set team prompts successfully', async () => {
      const teamId = 'team-id';
      const prompts = { TICKET_DETECTION: 'test prompt' } as any;

      await service.setTeamPrompts(teamId, prompts);

      expect(mockAiProvider.setTeamPrompts).toHaveBeenCalledWith(teamId, prompts);
      expect(mockLogger.log).toHaveBeenCalledWith(`Set prompts for team ${teamId}`);
      expect(mockCacheManager.set).toHaveBeenCalledWith(
        `team-prompts-${teamId}`,
        prompts,
        expect.any(Number)
      );
    });

    it('should throw error if setting team prompts fails', async () => {
      const teamId = 'team-id';
      const prompts = { TICKET_DETECTION: 'test prompt' } as any;
      
      mockAiProvider.setTeamPrompts.mockImplementation(() => {
        throw new Error('Failed to set team prompts');
      });

      await expect(service.setTeamPrompts(teamId, prompts)).rejects.toThrow('Failed to set team prompts');
      expect(mockLogger.error).toHaveBeenCalledWith(
        `Failed to set team prompts for ${teamId}: Failed to set team prompts`
      );
    });
  });

  describe('Provider management methods', () => {
    describe('getRegisteredProviders', () => {
      it('should return registered providers from registry', () => {
        const originalAiProvider = (service as any).aiProvider;
        
        const mockRegistry = new AiProviderRegistry();
        mockRegistry.getRegisteredProviders = vi.fn().mockReturnValue(['provider1', 'provider2']);
        
        (service as any).aiProvider = mockRegistry;
        
        const result = service.getRegisteredProviders();
        
        expect(mockRegistry.getRegisteredProviders).toHaveBeenCalled();
        expect(result).toEqual(['provider1', 'provider2']);
        
        (service as any).aiProvider = originalAiProvider;
      });

      it('should return default provider if not using registry', () => {
        const originalAiProvider = (service as any).aiProvider;
        
        (service as any).aiProvider = { 
          constructor: { name: 'SingleProvider' } 
        };
        
        const result = service.getRegisteredProviders();
        expect(result).toEqual(['default']);
        
        (service as any).aiProvider = originalAiProvider;
      });
    });

    describe('setActiveProvider', () => {
      it('should set active provider in registry', () => {
        const originalAiProvider = (service as any).aiProvider;
        
        const mockRegistry = new AiProviderRegistry();
        mockRegistry.setActiveProvider = vi.fn();
        
        (service as any).aiProvider = mockRegistry;
        
        service.setActiveProvider('provider1');
        
        expect(mockRegistry.setActiveProvider).toHaveBeenCalledWith('provider1');
        expect(mockLogger.log).toHaveBeenCalledWith('Set active provider to provider1');
        
        (service as any).aiProvider = originalAiProvider;
      });

      it('should throw error if not using registry', () => {
        const originalAiProvider = (service as any).aiProvider;
        
        (service as any).aiProvider = { 
          constructor: { name: 'SingleProvider' } 
        };
        
        expect(() => service.setActiveProvider('provider1')).toThrow(
          'Provider switching not available with single provider'
        );
        
        (service as any).aiProvider = originalAiProvider;
      });
    });

    describe('getActiveProvider', () => {
      it('should get active provider from registry', () => {
        const originalAiProvider = (service as any).aiProvider;
        
        const mockRegistry = new AiProviderRegistry();
        mockRegistry.getActiveProvider = vi.fn().mockReturnValue('provider1');
        
        (service as any).aiProvider = mockRegistry;
        
        const result = service.getActiveProvider();
        
        expect(mockRegistry.getActiveProvider).toHaveBeenCalled();
        expect(result).toBe('provider1');
        
        (service as any).aiProvider = originalAiProvider;
      });

      it('should return default if not using registry', () => {
        const originalAiProvider = (service as any).aiProvider;
        
        (service as any).aiProvider = { 
          constructor: { name: 'SingleProvider' } 
        };
        
        const result = service.getActiveProvider();
        expect(result).toBe('default');
        
        (service as any).aiProvider = originalAiProvider;
      });
    });
  });

  describe('Model management methods', () => {
    describe('getAvailableModels', () => {
      it('should return available models from provider', () => {
        mockAiProvider.getAvailableModels.mockReturnValue(['model1', 'model2']);
        
        const result = service.getAvailableModels();
        
        expect(mockAiProvider.getAvailableModels).toHaveBeenCalled();
        expect(result).toEqual(['model1', 'model2']);
      });
    });

    describe('setActiveModel', () => {
      it('should set active model in provider', () => {
        service.setActiveModel('model1');
        
        expect(mockAiProvider.setActiveModel).toHaveBeenCalledWith('model1');
        expect(mockLogger.log).toHaveBeenCalledWith('Set active model to model1');
      });

      it('should throw error if setting active model fails', () => {
        mockAiProvider.setActiveModel.mockImplementation(() => {
          throw new Error('Failed to set active model');
        });

        expect(() => service.setActiveModel('model1')).toThrow('Failed to set active model');
        expect(mockLogger.error).toHaveBeenCalledWith(
          'Failed to set active model to model1: Failed to set active model'
        );
      });
    });

    describe('getActiveModel', () => {
      it('should get active model from provider', () => {
        mockAiProvider.getActiveModel.mockReturnValue('model1');
        
        const result = service.getActiveModel();
        
        expect(mockAiProvider.getActiveModel).toHaveBeenCalled();
        expect(result).toBe('model1');
      });
    });

    describe('addModel', () => {
      it('should add model to provider', () => {
        const modelConfig = { name: 'Model 1', capabilities: ['text'] };
        
        service.addModel('model1', modelConfig as any);
        
        expect(mockAiProvider.addModel).toHaveBeenCalledWith('model1', modelConfig);
        expect(mockLogger.log).toHaveBeenCalledWith('Added model model1');
      });

      it('should throw error if adding model fails', () => {
        const modelConfig = { name: 'Model 1', capabilities: ['text'] };
        
        mockAiProvider.addModel.mockImplementation(() => {
          throw new Error('Failed to add model');
        });

        expect(() => service.addModel('model1', modelConfig as any)).toThrow('Failed to add model');
        expect(mockLogger.error).toHaveBeenCalledWith(
          'Failed to add model model1: Failed to add model'
        );
      });
    });
  });

  describe('getProviders', () => {
    it('should return provider information', async () => {
      vi.spyOn(service, 'getRegisteredProviders').mockReturnValue(['openai', 'anthropic']);
      vi.spyOn(service, 'getActiveProvider').mockReturnValue('openai');
      
      const result = await service.getProviders();
      
      expect(result).toEqual([
        expect.objectContaining({
          id: 'openai',
          isActive: true,
        }),
        expect.objectContaining({
          id: 'anthropic',
          isActive: false,
        }),
      ]);
    });

    it('should handle errors', async () => {
      vi.spyOn(service, 'getRegisteredProviders').mockImplementation(() => {
        throw new Error('Failed to get providers');
      });
      
      const result = await service.getProviders();
      
      expect(mockLogger.error).toHaveBeenCalledWith(
        'Error fetching AI providers: Failed to get providers'
      );
      expect(result).toEqual([]);
    });
  });

  describe('getModels', () => {
    it('should return model information for a provider', async () => {
      const originalAiProvider = (service as any).aiProvider;
      
      const mockRegistry = new AiProviderRegistry();
      mockRegistry.getActiveProvider = vi.fn().mockReturnValue('original-provider');
      mockRegistry.setActiveProvider = vi.fn();
      mockRegistry.getAvailableModels = vi.fn().mockReturnValue(['gpt-4', 'gpt-3.5-turbo']);
      mockRegistry.getActiveModel = vi.fn().mockReturnValue('gpt-4');
      
      (service as any).aiProvider = mockRegistry;
      
      
      const result = await service.getModels('openai');
      
      expect(mockRegistry.setActiveProvider).toHaveBeenCalledWith('openai');
      expect(mockRegistry.getAvailableModels).toHaveBeenCalled();
      expect(mockRegistry.getActiveModel).toHaveBeenCalled();
      
      expect(result.length).toBe(2);
      expect(result[0].id).toBe('gpt-4');
      expect(result[0].isActive).toBe(true);
      
      expect(mockRegistry.setActiveProvider).toHaveBeenCalledWith('original-provider');
      
      (service as any).aiProvider = originalAiProvider;
    });

    it('should handle errors', async () => {
      const originalAiProvider = (service as any).aiProvider;
      
      const mockRegistry = new AiProviderRegistry();
      mockRegistry.getAvailableModels = vi.fn().mockImplementation(() => {
        throw new Error('Failed to get models');
      });
      
      (service as any).aiProvider = mockRegistry;
      
      const result = await service.getModels('openai');
      
      expect(mockLogger.error).toHaveBeenCalledWith(
        'Error fetching models for provider openai: No active AI provider'
      );
      expect(result).toEqual([]);
      
      (service as any).aiProvider = originalAiProvider;
    });
  });

  describe('generateTicketTitle', () => {
    it('should return generated title on success', async () => {
      mockAiProvider.generateTitle.mockResolvedValue({
        success: true,
        data: { title: 'Generated Title' },
      });

      const result = await service.generateTicketTitle('test conversation', 'team-id', 'installation-id', 'org-id');

      expect(mockAiProvider.generateTitle).toHaveBeenCalledWith('test conversation', 'team-id');
      expect(result).toBe('Generated Title');
    });

    it('should clean up the title', async () => {
      mockAiProvider.generateTitle.mockResolvedValue({
        success: true,
        data: { title: '"Title with quotes"\n and newlines' },
      });

      const result = await service.generateTicketTitle('test conversation');

      expect(result).toBe('Title with quotes"  and newlines');
    });

    it('should return default title if content is empty', async () => {
      const result = await service.generateTicketTitle('');

      expect(mockAiProvider.generateTitle).not.toHaveBeenCalled();
      expect(result).toBe('New Support Ticket');
    });

    it('should return default title if generation fails', async () => {
      mockAiProvider.generateTitle.mockResolvedValue({
        success: false,
        error: 'Generation failed',
      });

      const result = await service.generateTicketTitle('test conversation');

      expect(mockAiProvider.generateTitle).toHaveBeenCalled();
      expect(result).toBe('New Support Ticket');
    });
  });

  describe('generateTicketDescription', () => {
    it('should return generated description on success', async () => {
      mockAiProvider.generateDescription.mockResolvedValue({
        success: true,
        data: { description: 'Generated Description' },
      });

      const result = await service.generateTicketDescription('test conversation', 'team-id', 'installation-id', 'org-id');

      expect(mockAiProvider.generateDescription).toHaveBeenCalledWith('test conversation', 'team-id');
      expect(result).toBe('Generated Description');
    });

    it('should trim the description', async () => {
      mockAiProvider.generateDescription.mockResolvedValue({
        success: true,
        data: { description: '  Description with whitespace  ' },
      });

      const result = await service.generateTicketDescription('test conversation');

      expect(result).toBe('Description with whitespace');
    });

    it('should return default description if content is empty', async () => {
      const result = await service.generateTicketDescription('');

      expect(mockAiProvider.generateDescription).not.toHaveBeenCalled();
      expect(result).toBe('No description provided.');
    });

    it('should return default description if generation fails', async () => {
      mockAiProvider.generateDescription.mockResolvedValue({
        success: false,
        error: 'Generation failed',
      });

      const result = await service.generateTicketDescription('test conversation');

      expect(mockAiProvider.generateDescription).toHaveBeenCalled();
      expect(result).toBe('No description could be generated.');
    });
  });
});
