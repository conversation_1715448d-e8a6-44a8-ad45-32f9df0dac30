import Redis from 'ioredis';
import { afterEach, beforeEach, describe, expect, it, vi } from 'vitest';
import { MockRedisClient as ModuleMockRedisClient } from '../../../../src/common/redis/redis.module';
import { RedisService } from '../../../../src/common/redis/redis.service';

// Create a mock Redis client class
class TestMockRedisClient {
  get = vi.fn();
  set = vi.fn();
  del = vi.fn();
  exists = vi.fn();
  expire = vi.fn();
  ttl = vi.fn();
  publish = vi.fn();
  subscribe = vi.fn();
  unsubscribe = vi.fn();
  on = vi.fn();
  incr = vi.fn();
  incrby = vi.fn();
  decr = vi.fn();
  decrby = vi.fn();
  getset = vi.fn();
  mset = vi.fn();
  mget = vi.fn();
  sadd = vi.fn();
  srem = vi.fn();
  smembers = vi.fn();
  sismember = vi.fn();
  scard = vi.fn();
  zadd = vi.fn();
  zrange = vi.fn();
}

// Mock Redis
vi.mock('ioredis', () => {
  return {
    default: vi.fn().mockImplementation(() => new TestMockRedisClient()),
  };
});

describe('RedisService', () => {
  let service: RedisService;
  let redisClient: Redis;
  let subscriberClient: Redis;
  let publisherClient: Redis;

  beforeEach(async () => {
    // Create mock Redis clients with explicit methods
    redisClient = {
      get: vi.fn(),
      set: vi.fn(),
      del: vi.fn(),
      exists: vi.fn(),
      expire: vi.fn(),
      ttl: vi.fn(),
      incr: vi.fn(),
      incrby: vi.fn(),
      decr: vi.fn(),
      decrby: vi.fn(),
      getset: vi.fn(),
      mset: vi.fn(),
      mget: vi.fn(),
      sadd: vi.fn(),
      srem: vi.fn(),
      smembers: vi.fn(),
      sismember: vi.fn(),
      scard: vi.fn(),
      zadd: vi.fn(),
      zrange: vi.fn(),
      on: vi.fn(),
    } as unknown as Redis;

    subscriberClient = {
      subscribe: vi.fn(),
      unsubscribe: vi.fn(),
      on: vi.fn(),
    } as unknown as Redis;

    publisherClient = {
      publish: vi.fn(),
    } as unknown as Redis;

    // Create the service manually instead of using NestJS DI
    service = new RedisService(redisClient, subscriberClient, publisherClient);
  });

  afterEach(() => {
    vi.clearAllMocks();
  });

  it('should be defined', () => {
    expect(service).toBeDefined();
  });

  it('should get the Redis client', () => {
    expect(service.getClient()).toBe(redisClient);
  });

  it('should get the subscriber client', () => {
    expect(service.getSubscriberClient()).toBe(subscriberClient);
  });

  it('should get the publisher client', () => {
    expect(service.getPublisherClient()).toBe(publisherClient);
  });

  describe('get', () => {
    it('should call Redis get with the correct key', async () => {
      const key = 'test-key';
      const value = 'test-value';
      vi.spyOn(redisClient, 'get').mockResolvedValue(value);

      const result = await service.get(key);

      expect(redisClient.get).toHaveBeenCalledWith(key);
      expect(result).toBe(value);
    });

    it('should handle errors and return null', async () => {
      const key = 'test-key';
      vi.spyOn(redisClient, 'get').mockRejectedValue(new Error('Redis error'));

      const result = await service.get(key);

      expect(redisClient.get).toHaveBeenCalledWith(key);
      expect(result).toBeNull();
    });
  });

  describe('set', () => {
    it('should call Redis set with the correct key and value', async () => {
      const key = 'test-key';
      const value = 'test-value';
      vi.spyOn(redisClient, 'set').mockResolvedValue('OK');

      const result = await service.set(key, value);

      expect(redisClient.set).toHaveBeenCalledWith(key, value);
      expect(result).toBe('OK');
    });

    it('should call Redis set with TTL when provided', async () => {
      const key = 'test-key';
      const value = 'test-value';
      const ttl = 60;
      vi.spyOn(redisClient, 'set').mockResolvedValue('OK');

      const result = await service.set(key, value, 'EX', ttl);

      expect(redisClient.set).toHaveBeenCalledWith(key, value, 'EX', ttl);
      expect(result).toBe('OK');
    });

    it('should handle errors and return null', async () => {
      const key = 'test-key';
      const value = 'test-value';
      vi.spyOn(redisClient, 'set').mockRejectedValue(new Error('Redis error'));

      const result = await service.set(key, value);

      expect(redisClient.set).toHaveBeenCalledWith(key, value);
      expect(result).toBeNull();
    });
  });

  describe('del', () => {
    it('should call Redis del with the correct key', async () => {
      const key = 'test-key';
      vi.spyOn(redisClient, 'del').mockResolvedValue(1);

      const result = await service.del(key);

      expect(redisClient.del).toHaveBeenCalledWith(key);
      expect(result).toBe(1);
    });

    it('should handle errors and return 0', async () => {
      const key = 'test-key';
      vi.spyOn(redisClient, 'del').mockRejectedValue(new Error('Redis error'));

      const result = await service.del(key);

      expect(redisClient.del).toHaveBeenCalledWith(key);
      expect(result).toBe(0);
    });
  });
});

describe('MockRedisClient', () => {
  let mockRedis: ModuleMockRedisClient;

  beforeEach(() => {
    mockRedis = new ModuleMockRedisClient();
  });

  it('should be defined', () => {
    expect(mockRedis).toBeDefined();
  });

  it('should set and get values', async () => {
    await mockRedis.set('test-key', 'test-value');
    const value = await mockRedis.get('test-key');
    expect(value).toBe('test-value');
  });

  it('should handle TTL with EX parameter', async () => {
    await mockRedis.set('test-key', 'test-value', 'EX', '1');
    let value = await mockRedis.get('test-key');
    expect(value).toBe('test-value');

    // Mock time passing
    const originalDateNow = Date.now;
    Date.now = vi.fn(() => originalDateNow() + 2000); // 2 seconds later

    value = await mockRedis.get('test-key');
    expect(value).toBeNull(); // Key should have expired

    // Restore Date.now
    Date.now = originalDateNow;
  });

  it('should handle NX parameter', async () => {
    const result1 = await mockRedis.set('test-key', 'test-value', 'NX');
    expect(result1).toBe('OK');

    const result2 = await mockRedis.set('test-key', 'new-value', 'NX');
    expect(result2).toBeNull(); // Should not set if key exists

    const value = await mockRedis.get('test-key');
    expect(value).toBe('test-value'); // Original value should remain
  });

  it('should delete keys', async () => {
    await mockRedis.set('test-key', 'test-value');
    const result = await mockRedis.del('test-key');
    expect(result).toBe(1);

    const value = await mockRedis.get('test-key');
    expect(value).toBeNull();
  });

  it('should delete keys', async () => {
    await mockRedis.set('test-key', 'test-value');
    const result = await mockRedis.del('test-key');
    expect(result).toBe(1);

    const value = await mockRedis.get('test-key');
    expect(value).toBeNull();
  });
});
