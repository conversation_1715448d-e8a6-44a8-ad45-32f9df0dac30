import { Test, TestingModule } from '@nestjs/testing';
import { afterEach, beforeEach, describe, expect, it, vi } from 'vitest';
import { EventDeduplicationService } from '../../../../src/common/redis/event-deduplication.service';
import { RedisService } from '../../../../src/common/redis/redis.service';
import { ConfigService } from '../../../../src/config/config.service';

describe('EventDeduplicationService', () => {
  let service: EventDeduplicationService;
  let redisService: RedisService;

  beforeEach(async () => {
    // Create mock RedisService
    const mockRedisService = {
      get: vi.fn(),
      set: vi.fn(),
      del: vi.fn(),
    };

    const module: TestingModule = await Test.createTestingModule({
      providers: [
        EventDeduplicationService,
        {
          provide: RedisService,
          useValue: mockRedisService,
        },
        {
          provide: ConfigService,
          useValue: {
            get: vi.fn(),
          },
        },
      ],
    }).compile();

    service = module.get<EventDeduplicationService>(EventDeduplicationService);
    redisService = module.get<RedisService>(RedisService);

    // Manually set the redisService property on the EventDeduplicationService instance
    (service as any).redisService = mockRedisService;
  });

  afterEach(() => {
    vi.clearAllMocks();
  });

  it('should be defined', () => {
    expect(service).toBeDefined();
  });

  describe('processIdempotently', () => {
    it('should process event if not already processed', async () => {
      vi.spyOn(redisService, 'get').mockResolvedValue(null);
      vi.spyOn(redisService, 'set').mockResolvedValue('OK');
      const processor = vi.fn().mockResolvedValue('result');

      const result = await service.processIdempotently(
        'event-id',
        'event-type',
        processor,
      );

      expect(redisService.get).toHaveBeenCalledWith(
        'slack:event:status:event-type:event-id',
      );
      expect(redisService.set).toHaveBeenCalledWith(
        'slack:event:status:event-type:event-id',
        'in_progress',
        'EX',
        86400,
        'NX',
      );
      expect(processor).toHaveBeenCalled();
      expect(redisService.set).toHaveBeenCalledWith(
        'slack:event:status:event-type:event-id',
        'completed',
        'EX',
        86400,
      );
      expect(result).toBe('result');
    });

    it('should not process event if already completed', async () => {
      vi.spyOn(redisService, 'get').mockResolvedValue('completed');
      const processor = vi.fn().mockResolvedValue('result');

      const result = await service.processIdempotently(
        'event-id',
        'event-type',
        processor,
      );

      expect(redisService.get).toHaveBeenCalledWith(
        'slack:event:status:event-type:event-id',
      );
      expect(processor).not.toHaveBeenCalled();
      expect(result).toBeNull();
    });

    it('should not process event if already in progress', async () => {
      vi.spyOn(redisService, 'get').mockResolvedValue('in_progress');
      const processor = vi.fn().mockResolvedValue('result');

      const result = await service.processIdempotently(
        'event-id',
        'event-type',
        processor,
      );

      expect(redisService.get).toHaveBeenCalledWith(
        'slack:event:status:event-type:event-id',
      );
      expect(processor).not.toHaveBeenCalled();
      expect(result).toBeNull();
    });

    it('should delete key if processor throws', async () => {
      vi.spyOn(redisService, 'get').mockResolvedValue(null);
      vi.spyOn(redisService, 'set').mockResolvedValue('OK');
      vi.spyOn(redisService, 'del').mockResolvedValue(1);
      const error = new Error('Processing error');
      const processor = vi.fn().mockRejectedValue(error);

      await expect(
        service.processIdempotently('event-id', 'event-type', processor),
      ).rejects.toThrow(error);

      expect(redisService.del).toHaveBeenCalledWith(
        'slack:event:status:event-type:event-id',
      );
    });

    it('should use custom prefix when provided', async () => {
      vi.spyOn(redisService, 'get').mockResolvedValue(null);
      vi.spyOn(redisService, 'set').mockResolvedValue('OK');
      const processor = vi.fn().mockResolvedValue('result');
      const customPrefix = 'custom:prefix:';

      await service.processIdempotently(
        'event-id',
        'event-type',
        processor,
        'namespace',
        customPrefix,
      );

      expect(redisService.get).toHaveBeenCalledWith(
        'custom:prefix:namespace:event-type:event-id',
      );
    });
  });
});
