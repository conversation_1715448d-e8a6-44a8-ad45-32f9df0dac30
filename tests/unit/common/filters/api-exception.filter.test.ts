import { describe, it, expect, vi, beforeEach } from 'vitest';
import { ApiExceptionFilter } from '../../../../src/common/filters/api-exception.filter';
import { HttpException, HttpStatus } from '@nestjs/common';

describe('ApiExceptionFilter', () => {
  let filter: ApiExceptionFilter;
  let mockResponse: any;
  let mockHost: any;

  beforeEach(() => {
    mockResponse = {
      status: vi.fn().mockReturnThis(),
      json: vi.fn(),
    };

    mockHost = {
      switchToHttp: vi.fn().mockReturnValue({
        getResponse: vi.fn().mockReturnValue(mockResponse),
      }),
    };

    filter = new ApiExceptionFilter();
  });

  it('should handle HttpException correctly', () => {
    const status = HttpStatus.BAD_REQUEST;
    const message = 'Bad request error';
    const responseData = { field: 'username', message: 'Username is required' };
    
    const exception = new HttpException(
      {
        message,
        response: responseData,
      },
      status,
    );
    
    exception.getResponse = vi.fn().mockReturnValue(responseData);
    
    filter.catch(exception, mockHost);
    
    expect(mockHost.switchToHttp).toHaveBeenCalled();
    expect(mockHost.switchToHttp().getResponse).toHaveBeenCalled();
    expect(mockResponse.status).toHaveBeenCalledWith(status);
    expect(mockResponse.json).toHaveBeenCalledWith({
      ok: false,
      data: null,
      error: message,
      response: responseData,
    });
  });

  it('should handle generic Error with 500 status', () => {
    const error = new Error('Some unexpected error');
    
    filter.catch(error, mockHost);
    
    expect(mockResponse.status).toHaveBeenCalledWith(HttpStatus.INTERNAL_SERVER_ERROR);
    expect(mockResponse.json).toHaveBeenCalledWith({
      ok: false,
      data: null,
      error: 'Internal server error',
      response: null,
    });
  });

  it('should format response according to ApiResponse interface', () => {
    const status = HttpStatus.NOT_FOUND;
    const message = 'Resource not found';
    const exception = new HttpException(message, status);
    
    filter.catch(exception, mockHost);
    
    expect(mockResponse.json).toHaveBeenCalledWith(expect.objectContaining({
      ok: false,
      data: null,
      error: expect.any(String),
      response: expect.anything(),
    }));
  });
});
