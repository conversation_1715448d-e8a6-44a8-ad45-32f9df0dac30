ackChannelService > getAllSlackChannelsByTeamId > should return channels for a team with correct pagination 2ms
     → queryBuilder.orWhere is not a function
   × SlackChannelService > getAllSlackChannelsByTeamId > should throw BadRequestException when limit exceeds 100 2ms
     → expected error to be instance of BadRequestException
   × SlackChannelService > getAllSlackChannelsByTeamId > should throw BadRequestException when page is less than 1 2ms
     → expected error to be instance of BadRequestException
   × SlackChannelService > getAllSlackChannels > should return channels with correct pagination 5ms
     → queryBuilder.orWhere is not a function
   ✓ SlackChannelService > getAllSlackChannels > should throw BadRequestException when limit exceeds 100
   ✓ SlackChannelService > getAllSlackChannels > should throw BadRequestException when page is less than 1
   ✓ SlackChannelService > getAllCustomersFromChannel > should return all customers from a channel
   ✓ SlackChannelService > getAllCustomersFromChannel > should return empty array when no customers are found
 ✓ tests/unit/utils/parsers/slack/html-to-slack-blocks.parser.test.ts (14 tests | 11 skipped) 12ms
 ✓ tests/unit/slack/event-handlers/handlers/message/subtypes/message-deleted.handler.test.ts (15 tests | 3 skipped) 11ms
 ✓ tests/unit/platform/event-handlers/comments/comment-deleted.handler.test.ts (13 tests) 31ms
 ✓ tests/unit/database/entities/bots/repositories/bots.repository.test.ts (8 tests) 6ms
[Nest] 6850  - 04/30/2025, 1:06:11 AM   ERROR [BaseSlackBlocksToHtml] Error converting Slack blocks to HTML: Test error, stack: Error: Test error
    at /Users/<USER>/Development/thena-curated-apps-slack/tests/unit/utils/parsers/slack/base-slack-to-html.parser.test.ts:133:83
    at file:///Users/<USER>/Development/thena-curated-apps-slack/node_modules/.pnpm/@vitest+runner@3.0.5/node_modules/@vitest/runner/dist/index.js:174:14
    at file:///Users/<USER>/Development/thena-curated-apps-slack/node_modules/.pnpm/@vitest+runner@3.0.5/node_modules/@vitest/runner/dist/index.js:561:28
    at file:///Users/<USER>/Development/thena-curated-apps-slack/node_modules/.pnpm/@vitest+runner@3.0.5/node_modules/@vitest/runner/dist/index.js:61:24
    at new Promise (<anonymous>)
    at runWithTimeout (file:///Users/<USER>/Development/thena-curated-apps-slack/node_modules/.pnpm/@vitest+runner@3.0.5/node_modules/@vitest/runner/dist/index.js:41:12)
    at runTest (file:///Users/<USER>/Development/thena-curated-apps-slack/node_modules/.pnpm/@vitest+runner@3.0.5/node_modules/@vitest/runner/dist/index.js:1140:17)
    at processTicksAndRejections (node:internal/process/task_queues:105:5)
    at runSuite (file:///Users/<USER>/Development/thena-curated-apps-slack/node_modules/.pnpm/@vitest+runner@3.0.5/node_modules/@vitest/runner/dist/index.js:1294:15)
    at runSuite (file:///Users/<USER>/Development/thena-curated-apps-slack/node_modules/.pnpm/@vitest+runner@3.0.5/node_modules/@vitest/runner/dist/index.js:1294:15)


