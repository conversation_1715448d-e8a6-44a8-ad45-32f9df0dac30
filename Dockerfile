# Build stage
FROM node:20-alpine AS builder

# Install pnpm
RUN npm install -g pnpm

ARG SENTRY_AUTH_TOKEN
ENV SENTRY_AUTH_TOKEN=$SENTRY_AUTH_TOKEN

ARG TIPTAP_PRO_TOKEN
ENV TIPTAP_PRO_TOKEN=$TIPTAP_PRO_TOKEN

WORKDIR /app

# Copy package files
COPY package*.json pnpm-lock.yaml ./

# Install dependencies
RUN pnpm config set "//registry.tiptap.dev/:_authToken" $TIPTAP_PRO_TOKEN
RUN pnpm install

# Copy source code
COPY . .

# Build application
RUN pnpm run build

# Expose port
EXPOSE 8000

# Start the application
CMD ["node", "dist/main"]