changelog_type: pull_request
header_prefix: "Version:"
commit_changelog: true
comment_changelog: true
pull_request_title_regex: '^(?i:release)'
version_regex: '^v?\d+\.\d+\.\d+$'
group_config:
  - title: Features
    regex: '^(?i:feat|feature|enhancement)'
  - title: Bug Fixes
    regex: '^(?i:fix|bug)'
  - title: Documentation
    regex: '^(?i:doc|docs)'
  - title: Other Changes
    regex: '^(?i:chore|refactor|test|style)'