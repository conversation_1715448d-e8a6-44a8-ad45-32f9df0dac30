{
  "typescript.preferences.importModuleSpecifier": "relative",
  "editor.formatOnSave": true,
  "editor.detectIndentation": false,
  "editor.defaultFormatter": "biomejs.biome",
  "editor.indentSize": 2,
  "prettier.enable": false,
  "eslint.enable": false,
  "editor.insertSpaces": true,
  "biome.enabled": true,
  "editor.codeActionsOnSave": {
    "source.organizeImports.biome": "explicit"
  },
  "eslint.workingDirectories": [
    {
      "mode": "auto"
    }
  ],
  // "editor.lineNumbers": "interval",
  "editor.smoothScrolling": true,
  "prettier.printWidth": 80,
  "cSpell.words": [
    "APEC",
    "archivable",
    "camelcase",
    "checkmark",
    "CONV",
    "copyfiles",
    "dind",
    "dtos",
    "EMEA",
    "enterlocal",
    "enterlocalpg",
    "entertest",
    "entertestpg",
    "eventbridge",
    "fastify",
    "flushall",
    "gotrue",
    "hubspot",
    "kanban",
    "linebreak",
    "luxon",
    "maneskin",
    "millis",
    "mpim",
    "mrkdwn",
    "nestjs",
    "oneofs",
    "pino",
    "platformup",
    "plpgsql",
    "proto",
    "protobuf",
    "protoc",
    "resetlocal",
    "resettest",
    "rtracer",
    "seeddb",
    "setex",
    "SLASQS",
    "subtickets",
    "supabase",
    "temporalio",
    "THENA",
    "thenaappsplatform",
    "thenaplatform",
    "timestamptz",
    "tldts",
    "toplevel",
    "typeorm",
    "typesense",
    "uids",
    "usergroups",
    "uuidv",
    "wfclientup",
    "wfworkerup",
    "toplevel",
    "seeddb",
    "mpim",
    "usergroups",
    "upserts",
    "upserted",
    "metatype",
    "syncpack",
    "upserting",
    "helpdesk",
    "platformteams",
    "ptcm",
    "bullmq",
    "DDMM",
    "oneofs",
    "pg_trgm",
    "tiptap",
    "presigner",
    "fuzzystrmatch",
    "venv",
    "uvicorn",
    "pyproject",
    "svix",
    "pytest",
    "vitest",
    "dbconstants",
    "thenaslackapp",
    "ENOSTID",
    "subteam",
    "subteams",
    "deepseek",
    "noopener",
    "noreferrer",
    "usergroup",
    "fanout",
    "dbname",
    "ingestor",
    "bigserial",
    "unarchived",
    "fallover"
  ],
  "[typescript]": {
    "editor.defaultFormatter": "biomejs.biome"
  }
}
