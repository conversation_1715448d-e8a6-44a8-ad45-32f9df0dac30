{"$schema": "https://biomejs.dev/schemas/1.9.4/schema.json", "vcs": {"enabled": false, "clientKind": "git", "useIgnoreFile": false}, "files": {"ignoreUnknown": false, "ignore": ["dist/**/*", "node_modules/**/*", "./src/**/*.spec.ts", ".eslintrc.js"]}, "formatter": {"enabled": true, "useEditorconfig": true, "formatWithErrors": false, "indentStyle": "space", "indentWidth": 2, "lineEnding": "lf", "lineWidth": 80, "bracketSpacing": true}, "organizeImports": {"enabled": true}, "linter": {"enabled": true, "rules": {"recommended": true, "suspicious": {"noExplicitAny": "off"}, "correctness": {"noUnusedVariables": {"level": "error"}, "noUnusedImports": {"level": "error"}}, "style": {"useConst": "error", "noVar": "error", "useBlockStatements": "error", "useImportType": "off"}, "complexity": {"useOptionalChain": "error", "noUselessConstructor": "error"}}}, "javascript": {"formatter": {"jsxQuoteStyle": "double", "quoteProperties": "asNeeded", "trailingCommas": "all", "semicolons": "always", "arrowParentheses": "always", "bracketSameLine": false, "quoteStyle": "single", "bracketSpacing": true}, "parser": {"unsafeParameterDecoratorsEnabled": true}}}