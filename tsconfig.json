{"compilerOptions": {"outDir": "./dist", "emitDecoratorMetadata": true, "experimentalDecorators": true, "allowJs": true, "strict": true, "module": "node16", "moduleResolution": "node16", "declaration": true, "resolveJsonModule": true, "removeComments": true, "allowSyntheticDefaultImports": true, "target": "esnext", "sourceMap": true, "incremental": true, "skipLibCheck": true, "strictNullChecks": false, "noImplicitAny": false, "strictBindCallApply": false, "forceConsistentCasingInFileNames": false, "noFallthroughCasesInSwitch": false, "strictPropertyInitialization": false, "esModuleInterop": true, "inlineSources": true}, "include": ["src/**/*", "./custom.d.ts"], "exclude": ["./global-setup.ts", "./global-teardown.ts", "dist"]}