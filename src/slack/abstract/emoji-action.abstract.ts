export enum EmojiActionFlow {
  /**
   * The action is performed when the emoji is added to a message.
   */
  ADD = 'add',

  /**
   * The action is performed when the emoji is removed from a message.
   */
  REMOVE = 'remove',

  /**
   * The action is performed when the emoji is added or removed from a message.
   */
  BOTH = 'both',
}

export interface EmojiActionCtx {
  flow: EmojiActionFlow;
}

export interface EmojiAction {
  /**
   * The name of the action.
   */
  name: string;

  /**
   * The action to perform when the emoji is used.
   */
  action: string;

  /**
   * The context to pass to the action.
   */
  context: EmojiActionCtx;

  /**
   * Action executor
   */
  executeAction(data: any): Promise<void>;

  /**
   * Action executor for the reverse action.
   */
  executeActionReverse(data: any): Promise<void>;
}
