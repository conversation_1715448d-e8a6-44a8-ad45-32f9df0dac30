import { Inject, Injectable } from '@nestjs/common';
import { SlackViewMiddlewareArgs } from '@slack/bolt';
import { TransactionService } from '../../../database/common/transactions.service';
import {
  CustomerContacts,
  Installations,
  PlatformTeams,
  PlatformTeamsToChannelMappings,
} from '../../../database/entities';
import { CommentThreadMapsRepository } from '../../../database/entities/mappings/repositories/comment-thread-maps.repository';
import { TeamChannelMapsRepository } from '../../../database/entities/mappings/repositories/team-channel-maps.repository';
import { SlackMessagesRepository } from '../../../database/entities/slack-messages/repositories/slack-messages.repository';
import { TeamsRepository } from '../../../database/entities/teams';
import { CreateNewComment } from '../../../external/provider/interfaces';
import { ThenaPlatformApiProvider } from '../../../external/provider/thena-platform-api.provider';
import { Ticket } from '../../../platform/interfaces';
import { CUSTOM_LOGGER_TOKEN, ILogger } from '../../../utils';
import { CreateTicketsBlocksComposite } from '../../blocks/components';
import { CreateTicketCommand } from '../../commands/create-ticket.command';
import { SlackAppManagementService } from '../../core';
import { SlackView } from '../../decorators';
import { DecoratedSlackViewMiddlewareArgs } from '../../event-handlers';
import { SlackActionHandler } from '../../interfaces';
import { SlackWebAPIService } from '../../providers/slack-apis/slack-apis.service';
import { EnrichedSlackArgsContext } from '../../services/slack-action-discovery.service';

const LOG_SPAN = 'CreateTicketViewHandler';

@Injectable()
@SlackView(CreateTicketCommand.CREATE_TICKET_VIEW_CALLBACK_ID)
export class CreateTicketViewHandler implements SlackActionHandler {
  constructor(
    @Inject(CUSTOM_LOGGER_TOKEN) private readonly logger: ILogger,

    // Database Repositories
    private readonly slackMessagesRepository: SlackMessagesRepository,
    private readonly teamChannelMapsRepo: TeamChannelMapsRepository,
    private readonly platformTeamsRepo: TeamsRepository,
    private readonly commentThreadMapsRepository: CommentThreadMapsRepository,

    // Core utilities
    private readonly transactionService: TransactionService,
    private readonly slackAppManagementService: SlackAppManagementService,

    // External API Providers
    private readonly thenaPlatformApiProvider: ThenaPlatformApiProvider,
    private readonly slackApiProvider: SlackWebAPIService,
  ) {}

  async handle(args: DecoratedSlackViewMiddlewareArgs) {
    let context: EnrichedSlackArgsContext;
    if ('context' in args) {
      context = args.context as EnrichedSlackArgsContext;
    }

    this.logger.log(`${LOG_SPAN} Handling create ticket view submission`);

    const { body } = args;
    const { installation, organization, client } = context;

    try {
      if ('view' in args) {
        const view = args.view;

        const privateMetadata: {
          channelId: string;
          responseUrl: string;
          threadTs?: string;
          shouldLinkSlackMessage?: boolean;
        } = this.getPrivateMetadata(view);

        // Get the channel from the database
        const teamChannelMap = await this.teamChannelMapsRepo.findByCondition({
          where: {
            channel: { channelId: privateMetadata.channelId },
            installation: { id: installation.id },
            organization: { id: organization.id },
          },
          relations: { channel: true, platformTeam: true },
        });

        let platformTeam: PlatformTeams | null = null;

        // If the team channel map is not found, throw an error
        if (!teamChannelMap) {
          this.logger.debug(
            `${LOG_SPAN} Team channel map not found for channel ${privateMetadata.channelId}`,
          );

          const selectedTeamId =
            view.state.values?.team_select_block?.team_select?.selected_option
              ?.value;
          if (selectedTeamId) {
            platformTeam = await this.platformTeamsRepo.findByCondition({
              where: {
                uid: selectedTeamId,
                installation: { id: installation.id },
              },
            });

            if (!platformTeam) {
              throw new Error(
                'This channel was not found mapped to a team on platform.',
              );
            }
          } else {
            throw new Error(
              'This channel was not found mapped to a team on platform.',
            );
          }
        } else {
          platformTeam = teamChannelMap.platformTeam;
        }

        const ticketData = this.getFormValues(view);

        // Create a ticket
        const ticket = await this.thenaPlatformApiProvider.createNewTicket(
          installation,
          {
            title: ticketData.title,
            requestorEmail: ticketData.requestorEmail,
            teamId: platformTeam.uid,
            text: ticketData.description,
            metadata: {
              slack: {
                channel: privateMetadata.channelId,
                ts: 'SLASH_TICKET',
                user: body.user.id,
              },
            },
          },
        );

        // If the response URL is present, send a message to the user
        if (privateMetadata.responseUrl) {
          await fetch(privateMetadata.responseUrl, {
            method: 'POST' as const,
            headers: { 'Content-Type': 'application/json' },
            body: JSON.stringify({
              response_type: 'ephemeral',
              delete_original: true,
            }),
          });
        }

        // If the should link slack message flag is present, link the slack message
        if (privateMetadata.shouldLinkSlackMessage) {
          await this.linkSlackMessage(
            installation,
            ticket,
            privateMetadata.threadTs,
            teamChannelMap,
          );
        } else {
          // Send a message to the user to configure the channel
          await client.chat.postMessage({
            token: installation.botToken,
            channel: privateMetadata.channelId,
            text: `Ticket created successfully. Ticket ID: ${ticket.ticketId}`,
          });
        }
      }
    } catch (error) {
      if (error instanceof Error) {
        this.logger.error(
          `${LOG_SPAN} Error handling create ticket view submission`,
          error.stack,
        );
      } else {
        console.error(
          `${LOG_SPAN} Error handling create ticket view submission`,
          error,
        );
      }

      // Send a message to the user to configure the channel
      await client.chat.postMessage({
        token: installation.botToken,
        channel: body.user.id,
        text: 'An error occurred while creating a ticket. Please try again later or if the issue persists, contact support.',
      });
    }
  }

  private async linkSlackMessage(
    installation: Installations,
    ticket: Ticket,
    threadTs: string,
    teamChannelMap: PlatformTeamsToChannelMappings,
  ) {
    const { channel } = teamChannelMap;

    // Get the message history
    const slackMessageHistory =
      await this.slackApiProvider.getConversationHistory(
        installation.botToken,
        {
          oldest: threadTs,
          inclusive: true,
          channel: channel.channelId,
          limit: 1,
        },
      );

    // If the slack message history is not ok, log an error and return
    if (!slackMessageHistory.ok) {
      this.logger.error(
        `${LOG_SPAN} Error getting slack message history`,
        slackMessageHistory.error,
      );

      return;
    }

    // Get the slack message
    const slackMessage = slackMessageHistory.messages[0];

    // Get the user
    const user =
      await this.slackAppManagementService.upsertPersonWithIdentification(
        slackMessage.user,
        installation,
        channel,
      );

    // Get the permalink
    const permalinkResponse = await this.slackApiProvider.getPermalink(
      installation.botToken,
      {
        channel: channel.channelId,
        message_ts: threadTs,
      },
    );

    // If the permalink response is not ok, log an error and return
    if (!permalinkResponse.ok) {
      this.logger.error(
        `${LOG_SPAN} Error getting slack message permalink`,
        permalinkResponse.error,
      );
    }

    // Get the permalink
    const permalink = permalinkResponse?.permalink;

    // Run the transaction
    const comment = await this.transactionService.runInTransaction(
      async (txnContext) => {
        // Save the slack message
        const slackMessageRecord =
          await this.slackMessagesRepository.saveWithTxn(txnContext, {
            channel: { id: channel.id },
            platformTicketId: ticket.id,
            slackPermalink: permalink,
            slackMessageTs: slackMessage.ts,
            slackMessageThreadTs: slackMessage.thread_ts,
            slackUserId: slackMessage.user,
            installation: { id: installation.id },
            organization: { id: installation.organization.id },
            metadata: {
              ticket_details: {
                status: ticket.status,
                statusId: ticket.statusId,
                priority: ticket.priority,
                priorityId: ticket.priorityId,
              },
            },
          });

        // Create a comment payload
        const commentPayload: CreateNewComment = {
          channelId: channel.channelId,
          content: slackMessage.text,
          files: [],
          impersonatedUserAvatar: user.getUserAvatar(),
          impersonatedUserEmail: user.slackProfileEmail,
          impersonatedUserName: user.displayName || user.realName,
          ticketId: ticket.id,
          commentVisibility: 'public',
          metadata: {
            ignoreSelf: true,
            ts: slackMessage.ts,
            threadTs: threadTs,
          },
        };

        // If the user is a customer contact, add the customer email to the comment payload
        if (user instanceof CustomerContacts) {
          commentPayload.customerEmail = user.slackProfileEmail;
        }

        // Create a comment on the ticket
        const comment = await this.thenaPlatformApiProvider.createNewComment(
          installation,
          commentPayload,
        );

        // Update the slack message with the comment id
        await this.slackMessagesRepository.updateWithTxn(
          txnContext,
          slackMessageRecord.id,
          { platformCommentId: comment.data.id },
        );

        return comment;
      },
    );

    try {
      // Create comment thread mapping for the parent message
      // This mapping allows the system to track the relationship between Slack threads and platform comments
      await this.transactionService.runInTransaction(async (txnContext) => {
        await this.commentThreadMapsRepository.saveWithTxn(txnContext, {
          organization: installation.organization,
          platformCommentThreadId: comment.data.id,
          platformCommentTicketId: ticket.id,
          slackThreadId: threadTs,
          slackChannelId: channel.channelId,
        });
      });

      // Get all replies in the thread (similar to createTicketDirectly)
      const slackThreadRepliesResponse =
        await this.slackApiProvider.getConversationReplies(
          installation.botToken,
          {
            channel: channel.channelId,
            ts: threadTs,
          },
        );

      if (slackThreadRepliesResponse.ok && slackThreadRepliesResponse.messages) {
        // Skip the first message (parent) as it's already processed
        const replyMessages = slackThreadRepliesResponse.messages.slice(1);

        // Process each reply message
        for (const replyMessage of replyMessages) {
          try {
            // Get the user who sent the reply
            const replyUser = await this.slackAppManagementService.upsertPersonWithIdentification(
              replyMessage.user,
              installation,
              channel,
            );

            if (!replyUser) {
              this.logger.warn(
                `${LOG_SPAN} User not found for reply message: ${replyMessage.user}`,
              );
              continue;
            }

            // Get permalink for the reply message
            const replyPermalink = await this.slackApiProvider.getPermalink(
              installation.botToken,
              {
                channel: channel.channelId,
                message_ts: replyMessage.ts,
              },
            );

            if (!replyPermalink.ok) {
              this.logger.error(
                `${LOG_SPAN} Error getting permalink for reply message`,
                replyPermalink.error,
              );
              continue;
            }

            // Process the reply message in a transaction
            await this.transactionService.runInTransaction(async (txnContext) => {
              // Save the reply slack message
              const replySlackMessageRecord =
                await this.slackMessagesRepository.saveWithTxn(txnContext, {
                  channel: { id: channel.id },
                  platformTicketId: ticket.id,
                  slackPermalink: replyPermalink.permalink,
                  slackMessageTs: replyMessage.ts,
                  slackMessageThreadTs: replyMessage.thread_ts,
                  slackUserId: replyMessage.user,
                  installation: { id: installation.id },
                  organization: { id: installation.organization.id },
                  metadata: {
                    ticket_details: {
                      status: ticket.status,
                      statusId: ticket.statusId,
                      priority: ticket.priority,
                      priorityId: ticket.priorityId,
                    },
                  },
                });

              // Create comment payload for the reply
              const replyCommentPayload: CreateNewComment = {
                channelId: channel.channelId,
                content: replyMessage.text || '',
                files: [],
                impersonatedUserAvatar: replyUser.getUserAvatar(),
                impersonatedUserEmail: replyUser.slackProfileEmail,
                impersonatedUserName: replyUser.displayName || replyUser.realName,
                ticketId: ticket.id,
                commentVisibility: 'public',
                parentCommentId: comment.data.id, // Link to parent comment
                metadata: {
                  ignoreSelf: true,
                  ts: replyMessage.ts,
                  threadTs: threadTs,
                },
              };

              // If the reply user is a customer contact, add the customer email
              if (replyUser instanceof CustomerContacts) {
                replyCommentPayload.customerEmail = replyUser.slackProfileEmail;
              }

              // Create a comment for the reply
              const replyComment = await this.thenaPlatformApiProvider.createNewComment(
                installation,
                replyCommentPayload,
              );

              // Update the reply slack message with the comment id
              await this.slackMessagesRepository.updateWithTxn(
                txnContext,
                replySlackMessageRecord.id,
                { platformCommentId: replyComment.data.id },
              );
            });
          } catch (replyError) {
            this.logger.error(
              `${LOG_SPAN} Error processing reply message ${replyMessage.ts}`,
              replyError instanceof Error ? replyError.stack : String(replyError),
            );
            // Continue processing other replies even if one fails
          }
        }
      }

      // Send a message in the thread informing about the ticket creation
      await this.slackApiProvider.sendMessage(installation.botToken, {
        channel: channel.channelId,
        text: `Ticket created successfully. Ticket ID: ${ticket.ticketId}`,
        thread_ts: threadTs,
        unfurl_links: true,
        unfurl_media: true,
      });

      const confirmationCommentPayload: CreateNewComment = {
        channelId: channel.channelId,
        files: [],
        content: `Ticket created successfully. Ticket ID: ${ticket.ticketId}`,
        ticketId: ticket.id,
        commentVisibility: 'public',
        parentCommentId: comment.data.id,
        impersonatedUserAvatar: user.getUserAvatar(),
        impersonatedUserEmail: user.slackProfileEmail,
        impersonatedUserName: user.displayName || user.realName,
        metadata: {
          ignoreSelf: true,
          ts: slackMessage.ts,
          threadTs: threadTs,
        },
      };

      // If the user is a customer contact, add the customer email to the comment payload
      if (user instanceof CustomerContacts) {
        confirmationCommentPayload.customerEmail = user.slackProfileEmail;
      }

      // Create a comment on the ticket
      await this.thenaPlatformApiProvider.createNewComment(
        installation,
        confirmationCommentPayload,
      );
    } catch (error) {
      if (error instanceof Error) {
        this.logger.error(
          `${LOG_SPAN} Error sending message in the thread informing about the ticket creation`,
          error.stack,
        );
      } else {
        console.error(
          `${LOG_SPAN} Error sending message in the thread informing about the ticket creation`,
          error,
        );
      }
    }
  }

  /**
   * Get the form values from the view
   * @param view The view
   * @returns The form values
   */
  private getFormValues(view: SlackViewMiddlewareArgs['view']) {
    const values = view.state.values;

    // Get the form values
    const ticketData = {
      title: values.title_block.title_input.value,
      priority:
        values?.priority_block?.[
          CreateTicketsBlocksComposite.ACTION_IDS.PRIORITY
        ]?.selected_option?.value,
      requestorEmail: values.requestor_email_block.requestor_email_input.value,
      description: values.description_block.description_input.value,
    };

    return ticketData;
  }

  /**
   * Get the private metadata from the view
   * @param view The view
   * @returns The private metadata
   */
  private getPrivateMetadata(view: SlackViewMiddlewareArgs['view']) {
    try {
      return JSON.parse(view.private_metadata);
    } catch (parsingError) {
      if (parsingError instanceof Error) {
        this.logger.error(
          `${LOG_SPAN} Error parsing private metadata`,
          parsingError.stack,
        );
      } else {
        console.error(
          `${LOG_SPAN} Error parsing private metadata`,
          parsingError,
        );
      }

      throw parsingError;
    }
  }
}
