import { Inject, Injectable } from '@nestjs/common';
import { SlackViewMiddlewareArgs } from '@slack/bolt';
import { ThenaPlatformApiProvider } from '../../../external/provider/thena-platform-api.provider';
import { CUSTOM_LOGGER_TOKEN, ILogger } from '../../../utils';
import { SelectAssigneeComposite } from '../../blocks/components';
import { TicketDetailsMetadata } from '../../constants/interfaces';
import { SlackView } from '../../decorators';
import { SlackActionHandler } from '../../interfaces';
import { EnrichedSlackArgsContext } from '../../services/slack-action-discovery.service';

const LOG_SPAN = 'AssignTicketHandler';

interface TicketAssigneeSlackView {
  channelId: string;
  metadata: TicketDetailsMetadata;
}

@Injectable()
@SlackView('triage_assign_modal_submit')
export class AssignTicketHandler implements SlackActionHandler {
  constructor(
    @Inject(CUSTOM_LOGGER_TOKEN) private readonly logger: ILogger,

    // External API Providers
    private readonly thenaPlatformApiProvider: ThenaPlatformApiProvider,
  ) {}

  async handle(args: SlackViewMiddlewareArgs) {
    let context: EnrichedSlackArgsContext;
    if ('context' in args) {
      context = args.context as EnrichedSlackArgsContext;
    }

    const { installation } = context;

    let ticketId: string;
    let selectedUser: string;
    if ('view' in args) {
      const view = args.view;

      const privateMetadata = JSON.parse(view.private_metadata);
      const { metadata }: TicketAssigneeSlackView = privateMetadata;
      ticketId = metadata?.event_payload?.ticket_id;
      selectedUser = this.getSelectedUser(view);
    }

    // Check if the ticket id and selected user are present
    if (!ticketId || !selectedUser) {
      this.logger.error(`${LOG_SPAN} Failed to get ticket id or selected user`);
      throw new Error('Failed to get ticket id or selected user');
    }

    // Update the ticket
    try {
      await this.thenaPlatformApiProvider.updateTicket(installation, ticketId, {
        assignedAgentId: selectedUser,
      });
    } catch (platformError) {
      if (platformError instanceof Error) {
        this.logger.error(
          `${LOG_SPAN} Failed to update ticket, ${platformError.message}`,
        );
      } else {
        console.error(platformError);
      }

      throw platformError;
    }
  }

  /**
   * Get the selected user from the view
   * @param view The view
   * @returns The selected user
   */
  private getSelectedUser(view: SlackViewMiddlewareArgs['view']) {
    try {
      const selectedUser =
        view.state.values[SelectAssigneeComposite.BLOCK_ID][
          SelectAssigneeComposite.ACTION_IDS.ASSIGNEE_SELECT
        ].selected_option.value;

      return selectedUser;
    } catch (error) {
      if (error instanceof TypeError) {
        this.logger.error(
          `${LOG_SPAN} Failed to parse private metadata, ${error.message}`,
        );
      }
    }
  }
}
