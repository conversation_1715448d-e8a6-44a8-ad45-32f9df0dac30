import { Inject, Injectable } from '@nestjs/common';
import { SlackViewMiddlewareArgs } from '@slack/bolt';
import { In } from 'typeorm';
import { BotCtx } from '../../../auth/interfaces';
import { TransactionService } from '../../../database/common';
import { Channels } from '../../../database/entities/channels/channels.entity';
import { ChannelType } from '../../../database/entities/channels/channels.entity';
import { ChannelsRepository } from '../../../database/entities/channels/repositories/channels.repository';
import { Installations } from '../../../database/entities/installations/installations.entity';
import { TriageMapsRepository } from '../../../database/entities/mappings/repositories/triage-maps.repository';
import { Organizations } from '../../../database/entities/organizations/organizations.entity';
import { CUSTOM_LOGGER_TOKEN, ILogger } from '../../../utils';
import { ConfigureTriageChannelComposite } from '../../blocks/components/composite/channels';
import { SlackView } from '../../decorators';
import { ConfigureTriageChannelDTO } from '../../dtos';
import { SlackActionHandler } from '../../interfaces';
import { SlackWebAPIService } from '../../providers/slack-apis/slack-apis.service';
import { EnrichedSlackArgsContext } from '../../services/slack-action-discovery.service';

interface ExecuteSetupTriageChannelData {
  installation: Installations;
  organization: Organizations;
  channelId: string;
  selectedChannelId: string;
}

@Injectable()
@SlackView('configure_triage_channel_submit')
export class TriageChannelConfigHandler implements SlackActionHandler {
  constructor(
    @Inject(CUSTOM_LOGGER_TOKEN) private readonly logger: ILogger,

    // Database Repositories
    private readonly transactionService: TransactionService,
    private readonly channelRepository: ChannelsRepository,
    private readonly triageMapsRepository: TriageMapsRepository,

    // External API Providers
    private readonly slackWebAPIService: SlackWebAPIService,
  ) {}

  async handle(args: SlackViewMiddlewareArgs) {
    let context: EnrichedSlackArgsContext;
    if ('context' in args) {
      context = args.context as EnrichedSlackArgsContext;
    }

    const { installation, organization } = context;

    // This was a view submission
    let selectedChannelId: string;
    let channelId: string;
    if ('view' in args) {
      // Get view from args
      const view = args.view;

      const privateMetadata = JSON.parse(view.private_metadata);
      channelId = privateMetadata.channelId;

      selectedChannelId =
        view.state.values[ConfigureTriageChannelComposite.BLOCK_ID][
          ConfigureTriageChannelComposite.ACTION_IDS.CHANNEL_SELECT
        ].selected_option.value;
    }

    await this.execute({
      installation,
      organization,
      channelId,
      selectedChannelId,
    });
  }

  async handleViaApi(data: ConfigureTriageChannelDTO, botCtx: BotCtx) {
    const { installation, organization } = botCtx;
    const { channelToTriage, triageChannel } = data;

    await this.execute({
      installation,
      organization,
      channelId: channelToTriage,
      selectedChannelId: triageChannel,
    });
  }

  private async execute(data: ExecuteSetupTriageChannelData) {
    const { installation, organization, channelId, selectedChannelId } = data;

    // Get the channel to configure
    const channels = await this.channelRepository.findAll({
      where: {
        channelId: In([channelId, selectedChannelId]),
        installation: { id: installation.id },
        organization: { id: organization.id },
      },
    });

    // Check if the channels were found
    if (channels.length !== 2) {
      throw new Error('Some of the provided channels were not found!');
    }

    // Get the selected triage channel
    const selectedTriageChannel = channels.find(
      (channel) => channel.channelId === selectedChannelId,
    );

    const isValidTriageChannel = await this.isValidTriageChannel(
      selectedTriageChannel,
    );

    // If the triage channel is not valid, throw an error
    if (!isValidTriageChannel.isValid) {
      throw new Error(isValidTriageChannel.error);
    }

    const customerChannel = channels.find(
      (channel) => channel.channelId === channelId,
    );

    const isValidCustomerChannel =
      await this.isValidCustomerChannel(customerChannel);

    // If the customer channel is not valid, throw an error
    if (!isValidCustomerChannel.isValid) {
      throw new Error(isValidCustomerChannel.error);
    }

    try {
      await this.joinTriageChannel(
        installation.botToken,
        selectedTriageChannel,
      );
    } catch (joinConversationError) {
      if (joinConversationError instanceof Error) {
        this.logger.error(
          `Failed to join the triage channel: ${joinConversationError.message}`,
        );
      } else {
        console.error(joinConversationError);
      }
    }

    // Create a common lookup object
    const commonLookup = {
      installation: { id: installation.id },
      organization: { id: organization.id },
    };

    // Update channels in a transaction
    await this.transactionService.runInTransaction(async (txnContext) => {
      // Update the triage channel type
      await this.channelRepository.updateWithTxn(
        txnContext,
        { id: selectedTriageChannel.id },
        { channelType: ChannelType.TRIAGE_CHANNEL },
      );

      // Update the customer channel type
      await this.triageMapsRepository.saveWithTxn(txnContext, {
        activeChannel: { id: customerChannel.id },
        triageChannel: { id: selectedTriageChannel.id },
        ...commonLookup,
      });
    });
  }

  /**
   * Check if the channel is a valid triage channel
   * @param channel The channel to check
   * @returns Whether the channel is valid and an error message if it is not
   */
  private async isValidTriageChannel(channel: Channels) {
    const invalidChannelTypes = [
      ChannelType.CUSTOMER_CHANNEL,
      ChannelType.INTERNAL_HELPDESK,
    ];

    // Check if the channel is a valid triage channel
    if (invalidChannelTypes.includes(channel.channelType)) {
      return {
        isValid: false,
        error: 'The selected channel is not a valid triage channel!',
      };
    }

    // Check if the channel is archived
    if (channel.isArchived) {
      return {
        isValid: false,
        error: 'The selected channel is archived!',
      };
    }

    // Check if the channel is shared
    if (channel.isShared) {
      return {
        isValid: false,
        error: 'The selected channel is shared!',
      };
    }

    return {
      isValid: true,
      error: null,
    };
  }

  /**
   * Check if the channel is a valid customer channel
   * @param channel The channel to check
   * @returns Whether the channel is valid and an error message if it is not
   */
  private async isValidCustomerChannel(channel: Channels) {
    // Check if the channel is a customer channel
    if (channel.channelType !== ChannelType.CUSTOMER_CHANNEL) {
      return {
        isValid: false,
        error: 'The selected channel is not a valid customer channel!',
      };
    }

    // Check if the channel is archived
    if (channel.isArchived) {
      return {
        isValid: false,
        error: 'The selected channel is archived!',
      };
    }

    // Check if the channel is shared
    if (!channel.isShared) {
      return {
        isValid: false,
        error: 'The selected channel is not shared!',
      };
    }

    return {
      isValid: true,
      error: null,
    };
  }

  /**
   * Join the triage channel
   * @param token The Slack token
   * @param channel The channel to join
   */
  private async joinTriageChannel(token: string, channel: Channels) {
    // Join the triage channel
    await this.slackWebAPIService.joinConversation(token, {
      channel: channel.channelId,
    });
  }
}
