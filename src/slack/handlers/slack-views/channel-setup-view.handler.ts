import { Inject, Injectable } from '@nestjs/common';
import { InjectRepository } from '@nestjs/typeorm';
import { WebClient } from '@slack/web-api';
import { Repository } from 'typeorm';
import { ChannelType } from '../../../database/entities/channels/channels.entity';
import { ChannelsRepository } from '../../../database/entities/channels/repositories/channels.repository';
import {
  PlatformTeamsToChannelMappings,
  TeamRelationshipType,
} from '../../../database/entities/mappings/platform-teams-to-channel-mappings.entity';
import { TeamsRepository } from '../../../database/entities/teams';
import {
  CUSTOM_LOGGER_TOKEN,
  ILogger,
  safeJsonStringify,
} from '../../../utils';
import { ChannelSetupBlocks } from '../../blocks/components';
import { BotChannelJoinedHandler } from '../../core/slack-channel/bot-channel-joined.handler';
import { SlackView } from '../../decorators';
import { DecoratedSlackViewMiddlewareArgs } from '../../event-handlers';
import { SlackActionHandler } from '../../interfaces';

interface ChannelUpdateData {
  isBotActive: boolean;
  isBotJoined: boolean;
  channelType: ChannelType;
  sharedTeamIds?: string[];
}

@Injectable()
@SlackView(BotChannelJoinedHandler.CHANNEL_SETUP_MODAL_CALLBACK_ID)
export class ChannelSetupViewHandler implements SlackActionHandler {
  constructor(
    @Inject(CUSTOM_LOGGER_TOKEN) private readonly logger: ILogger,
    private readonly channelsRepository: ChannelsRepository,
    private readonly teamsRepository: TeamsRepository,
    @InjectRepository(PlatformTeamsToChannelMappings)
    private readonly platformTeamsToChannelMappingsRepository: Repository<PlatformTeamsToChannelMappings>,
  ) {}

  async handle(args: DecoratedSlackViewMiddlewareArgs) {
    const { context, view } = args;
    const { client, installation } = context;

    try {
      // Parse private metadata to get channelId
      let channelId: string | undefined;
      let responseUrl: string | undefined;
      try {
        if (view.private_metadata) {
          const privateMetadata = JSON.parse(view.private_metadata);
          responseUrl = privateMetadata.responseUrl;
          if (
            typeof privateMetadata === 'object' &&
            privateMetadata !== null &&
            typeof privateMetadata.channelId === 'string'
          ) {
            channelId = privateMetadata.channelId;
          }
        }
      } catch (error) {
        this.logger.error(
          `Error parsing private metadata: ${safeJsonStringify(error, { fallback: 'Unknown error parsing private metadata' })}`,
        );
        return;
      }

      if (!channelId) {
        this.logger.error('No channel ID found in private metadata');
        return;
      }

      // Get values from the input fields
      const teamBlockId = `${ChannelSetupBlocks.BLOCK_ID}_team`;
      const typeBlockId = `${ChannelSetupBlocks.BLOCK_ID}_type`;

      const teamBlockValues = view.state.values[teamBlockId];
      const typeBlockValues = view.state.values[typeBlockId];

      // Extract the selected team ID
      let teamId = null;
      if (teamBlockValues?.[ChannelSetupBlocks.ACTION_IDS.TEAM_SELECT]) {
        const teamSelection =
          teamBlockValues[ChannelSetupBlocks.ACTION_IDS.TEAM_SELECT];
        if (
          'selected_option' in teamSelection &&
          teamSelection.selected_option
        ) {
          teamId = teamSelection.selected_option.value;
        }
      }

      // Extract the selected channel type
      let channelType = ChannelType.NOT_CONFIGURED;
      if (
        typeBlockValues?.[ChannelSetupBlocks.ACTION_IDS.CHANNEL_TYPE_SELECT]
      ) {
        const typeSelection =
          typeBlockValues[ChannelSetupBlocks.ACTION_IDS.CHANNEL_TYPE_SELECT];
        if (
          'selected_option' in typeSelection &&
          typeSelection.selected_option
        ) {
          channelType = typeSelection.selected_option.value as ChannelType;
        }
      }

      // Get the channel from the database
      const channel = await this.channelsRepository.findByCondition({
        where: { channelId, installation: { id: installation.id } },
        relations: { installation: true, organization: true },
      });

      if (!channel) {
        this.logger.error(`Channel ${channelId} not found`);
        return;
      }

      // Update channel configuration
      const updateData: ChannelUpdateData = {
        isBotActive: true,
        isBotJoined: true,
        channelType,
      };

      // Handle shared team IDs
      let sharedTeamIds: string[] | undefined;
      if (channel.channelDump) {
        if (Array.isArray(channel.channelDump.shared_team_ids)) {
          sharedTeamIds = channel.channelDump.shared_team_ids;
        } else if (channel.isShared && Array.isArray(channel.sharedTeamIds)) {
          sharedTeamIds = channel.sharedTeamIds;
        }
      }

      if (sharedTeamIds && sharedTeamIds.length > 0) {
        updateData.sharedTeamIds = sharedTeamIds;
      }

      await this.channelsRepository.update(channel.id, updateData);

      // If a team was selected, set up the relationship
      if (teamId) {
        const platformTeam = await this.teamsRepository.findByCondition({
          where: {
            uid: teamId,
            installation: { id: installation.id },
          },
        });

        if (!platformTeam) {
          this.logger.error(`Platform team ${teamId} not found`);
        } else {
          await this.platformTeamsToChannelMappingsRepository.upsert(
            {
              relationshipType: TeamRelationshipType.PRIMARY,
              platformTeam: { id: platformTeam.id },
              channel: { id: channel.id },
              installation: channel.installation,
              organization: channel.organization,
            },
            {
              conflictPaths: ['platformTeam', 'channel', 'relationshipType'],
              indexPredicate: "relationship_type = 'primary'",
            },
          );
        }
      }

      // Send confirmation messages
      await this.sendConfirmationMessages(
        client,
        channelId,
        channel.name,
        channelType,
        teamId,
        args.body.user.id,
        responseUrl,
      );
    } catch (error) {
      this.logger.error(
        `Error handling channel setup view submission: ${safeJsonStringify(error, { fallback: 'Unknown error handling channel setup view submission' })}`,
      );
    }
  }

  private async sendConfirmationMessages(
    client: WebClient,
    channelId: string,
    channelName: string,
    channelType: ChannelType,
    teamId: string | null,
    userId: string,
    responseUrl: string | undefined,
  ) {
    // Send ephemeral message to channel
    await client.chat.postEphemeral({
      channel: channelId,
      user: userId,
      text: `✅ This channel has been configured as a *${this.getChannelTypeName(channelType)}* channel${teamId ? ' and is linked to the team' : ''}.`,
      blocks: [
        {
          type: 'section',
          text: {
            type: 'mrkdwn',
            text: `✅ This channel has been configured as a *${this.getChannelTypeName(channelType)}* channel${teamId ? ' and is linked to the team' : ''}.`,
          },
        },
      ],
    });

    const deletedEphemeralMessage = async url => {
     await fetch(url, {
      method: 'POST',
      headers: {
        'Content-Type': 'application/json',
      },
    body: JSON.stringify({
      "response_type": "ephemeral",
      "delete_original":  true
    })
    })
    }

    if(responseUrl){
      await deletedEphemeralMessage(responseUrl);
    }

    // Send direct message to user
    try {
      await client.chat.postMessage({
        channel: userId,
        text: `✅ *Configuration Complete!*\nThe channel <#${channelId}|${channelName}> has been successfully configured as a *${this.getChannelTypeName(channelType)}* channel${teamId ? ' and is linked to the team' : ''}.`,
        blocks: [
          {
            type: 'section',
            text: {
              type: 'mrkdwn',
              text: `✅ *Configuration Complete!*\nThe channel <#${channelId}|${channelName}> has been successfully configured as a *${this.getChannelTypeName(channelType)}* channel${teamId ? ' and is linked to the team' : ''}.`,
            },
          },
        ],
      });
    } catch (dmError) {
      this.logger.error(
        `Failed to send confirmation DM: ${safeJsonStringify(dmError, { fallback: 'Unknown error sending confirmation DM' })}`,
      );
    }
  }

  private getChannelTypeName(channelType: ChannelType): string {
    switch (channelType) {
      case ChannelType.CUSTOMER_CHANNEL:
        return 'Customer Support';
      case ChannelType.INTERNAL_HELPDESK:
        return 'Internal Helpdesk';
      case ChannelType.TRIAGE_CHANNEL:
        return 'Triage';
      default:
        return 'Unconfigured';
    }
  }
}
