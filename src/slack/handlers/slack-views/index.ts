import { FormSubmissionHandler } from '../slack-actions/form-handlers/form-submission.handler';
import { AssignTicketHandler } from './assign-ticket.handler';
import { ChannelSetupViewHandler } from './channel-setup-view.handler';
import { TeamConfigHandler } from './channel-team-config.handler';
import { CreateTicketViewHandler } from './create-ticket-view.handler';
import { TriageChannelConfigHandler } from './triage-channel-config.handler';

export const slackViewHandlers = [
  CreateTicket<PERSON>iewHandler,
  AssignTicket<PERSON>andler,
  TeamConfigHandler,
  TriageChannelConfigHandler,
  ChannelSetupViewHandler,
  FormSubmissionHandler,
];

export {
  CreateTicket<PERSON>iewHandler,
  AssignTicketHandler,
  TeamConfigHandler,
  TriageChannelConfigHandler,
  ChannelSetupViewHandler,
  FormSubmissionHandler,
};
