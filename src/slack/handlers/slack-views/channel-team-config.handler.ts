import { Inject, Injectable } from '@nestjs/common';
import { InjectRepository } from '@nestjs/typeorm';
import { StringIndexed } from '@slack/bolt';
import { Repository } from 'typeorm';
import { Installations } from '../../../database/entities';
import { Channels } from '../../../database/entities/channels/channels.entity';
import { ChannelType } from '../../../database/entities/channels/channels.entity';
import { ChannelsRepository } from '../../../database/entities/channels/repositories/channels.repository';
import {
  PlatformTeamsToChannelMappings,
  TeamRelationshipType,
} from '../../../database/entities/mappings/platform-teams-to-channel-mappings.entity';
import { TeamsRepository } from '../../../database/entities/teams';
import { CUSTOM_LOGGER_TOKEN, safeJsonStringify } from '../../../utils';
import { ILogger } from '../../../utils/logger';
import { SelectChannelTeamComposite } from '../../blocks/components/composite/channels';
import { SlackView } from '../../decorators';
import { DecoratedSlackViewMiddlewareArgs } from '../../event-handlers';
import { SlackActionHandler } from '../../interfaces';

@Injectable()
@SlackView('team_config_modal_submit')
export class TeamConfigHandler implements SlackActionHandler {
  constructor(
    @Inject(CUSTOM_LOGGER_TOKEN) private readonly logger: ILogger,

    private readonly channelsRepository: ChannelsRepository,

    @InjectRepository(PlatformTeamsToChannelMappings)
    private readonly platformTeamsToChannelMappingsRepository: Repository<PlatformTeamsToChannelMappings>,
    private readonly platformTeamsRepository: TeamsRepository,
  ) {}

  async handle(args: DecoratedSlackViewMiddlewareArgs) {
    const { context } = args;
    const { installation } = context;

    try {
      // This was a view submission
      let selectedTeam: string;
      let channelId: string;
      let responseUrl: string;
      if ('view' in args) {
        // Get view from args
        const view = args.view;

        const privateMetadata = JSON.parse(view.private_metadata);
        channelId = privateMetadata.channelId;
        responseUrl = privateMetadata.ephemeralResponseUrl;

        selectedTeam =
          view.state.values[SelectChannelTeamComposite.BLOCK_ID][
            SelectChannelTeamComposite.ACTION_IDS.TEAM_SELECT
          ].selected_option.value;
      }

      this.logger.debug(
        `Selected team: ${selectedTeam} for channel: ${channelId}`,
      );

      // Remove the ephemeral response
      try {
        await this.removeEphemeralResponse({ response_url: responseUrl });
      } catch (_error) {
        // TODO: Capture this
      }

      const channel = await this.channelsRepository.findByCondition({
        where: { channelId },
        relations: { installation: true, organization: true },
      });

      this.logger.debug(
        `Found channel: ${channel.channelId}/${channel.name} for team: ${selectedTeam}`,
      );

      await this.updateChannelConfig(installation, channel, selectedTeam);
    } catch (error) {
      if (error instanceof Error) {
        this.logger.error(
          `Failed to update channel config: ${error.message}`,
          error.stack,
        );
      } else {
        console.error(error);
      }

      // Send an ephemeral response to the user
      await context.client.chat.postEphemeral({
        channel: context.channelId,
        user: context.user.id,
        text: 'Failed to update channel config',
      });
    }
  }

  private async updateChannelConfig(
    installation: Installations,
    channel: Channels,
    selectedTeam: string,
  ) {
    const platformTeam = await this.platformTeamsRepository.findByCondition({
      where: {
        uid: selectedTeam,
        installation: { id: installation.id },
      },
    });

    // If the platform team is not found, throw an error
    if (!platformTeam) {
      throw new Error('Platform team not found');
    }

    const results = await Promise.allSettled([
      // Update the channel to set bot active
      this.channelsRepository.update(channel.id, {
        isBotActive: true,
        channelType: ChannelType.CUSTOMER_CHANNEL,
      }),

      // Create a new Platform to Channel Mapping
      this.platformTeamsToChannelMappingsRepository.upsert(
        {
          relationshipType: TeamRelationshipType.PRIMARY,
          platformTeam: { id: platformTeam.id },
          channel,
          installation: channel.installation,
          organization: channel.organization,
        },
        {
          conflictPaths: ['platformTeam', 'channel', 'relationshipType'],
          indexPredicate: "relationship_type = 'primary'",
        },
      ),
    ]);

    // Log the results
    for (const result of results) {
      if (result.status === 'fulfilled') {
        this.logger.debug('Successfully updated channel config');
      } else {
        this.logger.error('Failed to update channel config', result.reason);
      }
    }
  }

  /**
   * Remove the ephemeral response if it exists
   * @param body The body of the request
   */
  private async removeEphemeralResponse(body: StringIndexed) {
    if ('response_url' in body) {
      const responseUrl = body.response_url;
      if (responseUrl) {
        try {
          await fetch(responseUrl, {
            method: 'POST' as const,
            headers: { 'Content-Type': 'application/json' },
            body: JSON.stringify({
              response_type: 'ephemeral',
              delete_original: true,
            }),
          });
        } catch (error) {
          this.logger.error(
            `Error removing ephemeral response: ${safeJsonStringify(error, { fallback: 'Unknown error removing ephemeral response' })}`,
          );
        }
      }
    }
  }
}
