import { Inject } from '@nestjs/common';
import { Injectable } from '@nestjs/common';
import { StringIndexed } from '@slack/bolt';
import { Channels, Installations } from '../../../../database/entities';
import { ChannelsRepository } from '../../../../database/entities/channels/repositories';
import { TeamChannelMapsRepository } from '../../../../database/entities/mappings/repositories/team-channel-maps.repository';
import { CUSTOM_LOGGER_TOKEN, ILogger } from '../../../../utils';
import {
  CreateNewTicketBlocks,
  CreateTicketsBlocksComposite,
} from '../../../blocks/components';
import { CreateTicketCommand } from '../../../commands/create-ticket.command';
import { SlackAction } from '../../../decorators';
import { DecoratedSlackActionMiddlewareArgs } from '../../../event-handlers';
import { SlackActionHandler } from '../../../interfaces';
import { EnrichedSlackArgsContext } from '../../../services/slack-action-discovery.service';
import { SlackResponse } from '../../../types';

@Injectable()
@SlackAction([CreateNewTicketBlocks.ACTION_IDS.CREATE_TICKET_FROM_REACTION])
export class MitigateNewTicketToFormActionHandler
  implements SlackActionHandler
{
  constructor(
    @Inject(CUSTOM_LOGGER_TOKEN) private readonly logger: ILogger,

    // Database Repositories
    private readonly channelsRepository: ChannelsRepository,
    private readonly teamChannelMapsRepo: TeamChannelMapsRepository,

    // Blocks
    private readonly createNewTicketBlock: CreateTicketsBlocksComposite,
  ) {}

  async handle(
    args: DecoratedSlackActionMiddlewareArgs,
  ): Promise<SlackResponse> {
    const { body } = args;

    if (!('context' in args)) {
      this.logger.error('No context found in arguments');
      return;
    }

    const context = args.context;

    // Get the channel
    const channelId = body.channel?.id;
    const channel = await this.getChannel(context.installation, channelId);

    // Check if the channel is present
    if (!channel) {
      throw new Error('Channel not found');
    }

    // Build the assign modal
    await this.buildCreateNewTicketModal(context, channel, body);
  }

  private async buildCreateNewTicketModal(
    context: EnrichedSlackArgsContext,
    channel: Channels,
    body: StringIndexed,
  ) {
    const { client, installation, organization } = context;

    let responseUrl: string;
    let threadTs: string;
    if ('container' in body && 'response_url' in body) {
      if (body.container?.is_ephemeral) {
        responseUrl = body.response_url;
      }

      if ('thread_ts' in body.container && body.container.thread_ts) {
        threadTs = body.container?.thread_ts;
      }
    }

    // Get the channel from the database
    const teamChannelMap = await this.teamChannelMapsRepo.findByCondition({
      where: {
        channel: { channelId: channel.channelId },
        installation: { id: installation.id },
        organization: { id: organization.id },
      },
      relations: { channel: true, platformTeam: true },
    });

    // If the team channel map is not found, throw an error
    if (!teamChannelMap) {
      this.logger.debug(
        `Team channel map not found for channel ${channel.channelId}`,
      );

      throw new Error(
        'This channel was not found mapped to a team on platform.',
      );
    }

    await client.views.open({
      token: installation.botToken,
      trigger_id: body.trigger_id,
      view: {
        type: 'modal',
        private_metadata: JSON.stringify({
          channelId: channel.channelId,
          platformTeamId: teamChannelMap.platformTeam.uid,
          responseUrl,
          threadTs,
          shouldLinkSlackMessage: true,
        }),
        callback_id: CreateTicketCommand.CREATE_TICKET_VIEW_CALLBACK_ID,
        title: {
          type: 'plain_text',
          text: 'Create ticket',
          emoji: true,
        },
        blocks: this.createNewTicketBlock.build(teamChannelMap.platformTeam.uid)
          .blocks,
        submit: {
          type: 'plain_text',
          text: 'Save',
        },
        close: {
          type: 'plain_text',
          text: 'Cancel',
        },
      },
    });
  }

  /**
   * Get the channel
   * @param installation Installation
   * @param channelId Channel ID
   * @returns Channel
   */
  private async getChannel(installation: Installations, channelId: string) {
    // Check if the channel id is present
    if (!channelId) {
      throw new Error('No channel id found in body');
    }

    const channel = await this.channelsRepository.findByCondition({
      where: { channelId, installation: { id: installation.id } },
    });

    return channel;
  }
}
