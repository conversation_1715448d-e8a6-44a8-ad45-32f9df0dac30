import { ConfigureChannelActionHandler } from './channel-setup/configure-channel.action-handler';
import { FormBuilderHandler } from './form-handlers/form-builder.handler';
import { MitigateNewTicketToFormActionHandler } from './mitigators';
import { TriageAssignActionHandler } from './ticket-triage';
import { UpdateTicketDetailsActionHandler } from './ticket-triage';
import { AckNoOpActionHandler } from './utility';

export const slackActions = [
  UpdateTicketDetailsActionHandler,
  TriageAssignActionHandler,
  AckNoOpActionHandler,
  MitigateNewTicketToFormActionHandler,
  ConfigureChannelActionHandler,
  FormBuilderHandler,
];
