import { Inject } from '@nestjs/common';
import { Injectable } from '@nestjs/common';
import { SlackActionMiddlewareArgs, StringIndexed } from '@slack/bolt';
import { Channels, Installations } from '../../../../database/entities';
import { ChannelsRepository } from '../../../../database/entities/channels/repositories';
import { CUSTOM_LOGGER_TOKEN, ILogger } from '../../../../utils';
import { TriageMessageBlock } from '../../../blocks/components';
import { SelectAssigneeComposite } from '../../../blocks/components/composite/triage';
import { SlackAction } from '../../../decorators';
import { SlackActionHandler } from '../../../interfaces';
import { EnrichedSlackArgsContext } from '../../../services/slack-action-discovery.service';
import { SlackResponse } from '../../../types';

@Injectable()
@SlackAction([TriageMessageBlock.ACTION_IDS.ASSIGN])
export class TriageAssignActionHandler implements SlackActionHandler {
  constructor(
    @Inject(CUSTOM_LOGGER_TOKEN) private readonly logger: ILogger,

    // Database Repositories
    private readonly channelsRepository: ChannelsRepository,

    // Blocks
    private readonly assignTicketBlock: SelectAssigneeComposite,
  ) {}

  async handle(args: SlackActionMiddlewareArgs): Promise<SlackResponse> {
    const { body } = args;

    if (!('context' in args)) {
      this.logger.error('No context found in arguments');
      return;
    }

    const context = args.context as EnrichedSlackArgsContext;

    // Get the channel
    const channelId = body.channel?.id;
    const channel = await this.getChannel(context.installation, channelId);

    // Check if the channel is present
    if (!channel) {
      throw new Error('Channel not found');
    }

    // Build the assign modal
    await this.buildAssignModal(context, channel, body);
  }

  private async buildAssignModal(
    context: EnrichedSlackArgsContext,
    channel: Channels,
    body: StringIndexed,
  ) {
    const { client, installation } = context;

    const metadata = body?.message?.metadata || {};

    await client.views.open({
      token: installation.botToken,
      trigger_id: body.trigger_id,
      view: {
        private_metadata: JSON.stringify({
          channelId: channel.channelId,
          metadata,
        }),
        type: 'modal',
        callback_id: 'triage_assign_modal_submit',
        title: {
          type: 'plain_text',
          text: 'Assign ticket',
          emoji: true,
        },
        blocks: this.assignTicketBlock.build().blocks,
        submit: {
          type: 'plain_text',
          text: 'Assign',
        },
        close: {
          type: 'plain_text',
          text: 'Cancel',
        },
      },
    });
  }

  /**
   * Get the channel
   * @param installation Installation
   * @param channelId Channel ID
   * @returns Channel
   */
  private async getChannel(installation: Installations, channelId: string) {
    // Check if the channel id is present
    if (!channelId) {
      throw new Error('No channel id found in body');
    }

    const channel = await this.channelsRepository.findByCondition({
      where: { channelId, installation: { id: installation.id } },
    });

    return channel;
  }
}
