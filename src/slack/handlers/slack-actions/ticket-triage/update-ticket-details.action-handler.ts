import { Inject, Injectable } from '@nestjs/common';
import { InjectRepository } from '@nestjs/typeorm';
import { SlackActionMiddlewareArgs } from '@slack/bolt';
import { Repository } from 'typeorm';
import { Installations, SlackMessages } from '../../../../database/entities';
import { ThenaPlatformApiProvider } from '../../../../external/provider/thena-platform-api.provider';
import { CUSTOM_LOGGER_TOKEN, ILogger } from '../../../../utils/logger';
import { TriageMessageBlock } from '../../../blocks/components';
import { TicketDetailsMetadata } from '../../../constants/interfaces';
import { CoreTriageService } from '../../../core/messages';
import { SlackAction } from '../../../decorators';
import { <PERSON>lack<PERSON><PERSON><PERSON>andler } from '../../../interfaces';
import { EnrichedSlackArgsContext } from '../../../services/slack-action-discovery.service';
import { SlackResponse } from '../../../types';

interface SlackMessage {
  [key: string]: unknown;
  metadata?: TicketDetailsMetadata;
}

@Injectable()
@SlackAction([
  TriageMessageBlock.ACTION_IDS.STATUS,
  TriageMessageBlock.ACTION_IDS.PRIORITY,
  TriageMessageBlock.ACTION_IDS.LOOKING,
  TriageMessageBlock.ACTION_IDS.ARCHIVE,
  TriageMessageBlock.ACTION_IDS.CLOSE,
])
export class UpdateTicketDetailsActionHandler implements SlackActionHandler {
  constructor(
    @Inject(CUSTOM_LOGGER_TOKEN) private readonly logger: ILogger,

    // External API Providers
    private readonly thenaPlatformApiProvider: ThenaPlatformApiProvider,

    // Database Repositories
    @InjectRepository(SlackMessages)
    private readonly slackMessagesRepository: Repository<SlackMessages>,

    // Utility Services
    private readonly coreTriageService: CoreTriageService,
  ) {}

  async handle(args: SlackActionMiddlewareArgs): Promise<SlackResponse> {
    const { payload, body } = args;

    // Check if the context is present
    let context: EnrichedSlackArgsContext;
    if (!('context' in args)) {
      this.logger.error('No context found in arguments');
      return;
    }

    // Set the context
    context = args.context as EnrichedSlackArgsContext;

    // Message contains the metadata for the ticket and its details
    let message: SlackMessage;
    if ('message' in body && 'metadata' in body.message) {
      message = body.message as SlackMessage;
    }

    // Get the action id
    let actionId = '';
    if ('action_id' in payload) {
      actionId = payload.action_id;
    }

    // Get the selected option value
    let selectedOptionValue = '';
    if ('selected_option' in payload) {
      selectedOptionValue = payload.selected_option.value;
    }

    // Get the ticket id
    let ticketId = '';
    if (message.metadata) {
      ticketId = message?.metadata?.event_payload?.ticket_id;
    }

    // Get the ticket team id
    let ticketTeamId = '';
    if (message.metadata) {
      ticketTeamId = message?.metadata?.event_payload?.ticket_team_id;
    }

    // If the ticket id is not present, throw an error
    if (!ticketId) {
      this.logger.error('No ticket id found in message');
      throw new Error('No ticket id found in message');
    }

    // TODO: Know that if we update anything we need to update all the triage channels
    // Switch on action performed and update the ticket details
    switch (actionId) {
      // Update the ticket status
      case TriageMessageBlock.ACTION_IDS.STATUS: {
        await this.updateTicketStatus(
          context.installation,
          ticketId,
          selectedOptionValue,
        );

        break;
      }

      // Update the ticket priority
      case TriageMessageBlock.ACTION_IDS.PRIORITY: {
        await this.updateTicketPriority(
          context.installation,
          ticketId,
          selectedOptionValue,
        );

        break;
      }

      // Archive the ticket
      case TriageMessageBlock.ACTION_IDS.ARCHIVE: {
        await this.archiveTicket(context.installation, ticketId);
        break;
      }

      // Close the ticket
      case TriageMessageBlock.ACTION_IDS.CLOSE: {
        await this.closeTicket(context.installation, ticketId, ticketTeamId);
        break;
      }

      // Either slack didn't send something or the action itself was invalid
      default: {
        this.logger.error(`Unsupported action id: ${actionId}`);
        throw new Error(`Unsupported action id: ${actionId}`);
      }
    }

    // Fetch the slack message
    const slackMessage = await this.slackMessagesRepository.findOne({
      where: {
        platformTicketId: ticketId,
        installation: { id: context.installation.id },
      },
    });

    // Post this we'll update the triage blocks
    await this.coreTriageService.updateTriageMessagesForSlackMessage(
      context.installation,
      slackMessage,
    );
  }

  /**
   * Update the ticket status
   * @param installation Installation
   * @param ticketId Ticket ID
   * @param statusId Status ID
   */
  private async updateTicketStatus(
    installation: Installations,
    ticketId: string,
    statusId: string,
  ) {
    // Update the ticket status
    await this.thenaPlatformApiProvider.updateTicket(installation, ticketId, {
      statusId,
    });
  }

  /**
   * Update the ticket priority
   * @param installation Installation
   * @param ticketId Ticket ID
   * @param priorityId Priority ID
   */
  private async updateTicketPriority(
    installation: Installations,
    ticketId: string,
    priorityId: string,
  ) {
    // Update the ticket priority
    await this.thenaPlatformApiProvider.updateTicket(installation, ticketId, {
      priorityId,
    });
  }

  /**
   * Assign the ticket to the user
   * @param installation Installation
   * @param ticketId Ticket ID
   * @param assigneeId Assignee ID
   */
  private async assignTicket(
    installation: Installations,
    ticketId: string,
    assigneeId: string,
  ) {
    // Update the ticket assignee
    await this.thenaPlatformApiProvider.updateTicket(installation, ticketId, {
      assignedAgentId: assigneeId,
    });
  }

  /**
   * Close the ticket
   * @param installation Installation
   * @param ticketId Ticket ID
   * @param ticketTeamId Ticket Team ID
   */
  private async closeTicket(
    installation: Installations,
    ticketId: string,
    ticketTeamId: string,
  ) {
    // Close the ticket
    await this.thenaPlatformApiProvider.closeTicket(
      installation,
      ticketId,
      ticketTeamId,
    );
  }

  /**
   * Archive the ticket
   * @param installation Installation
   * @param ticketId Ticket ID
   */
  private async archiveTicket(installation: Installations, ticketId: string) {
    // Archive the ticket
    await this.thenaPlatformApiProvider.archiveTicket(installation, ticketId);
  }
}
