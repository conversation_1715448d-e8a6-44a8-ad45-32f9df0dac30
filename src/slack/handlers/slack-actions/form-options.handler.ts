import { Inject, Injectable } from '@nestjs/common';
import { CUSTOM_LOGGER_TOKEN, ILogger } from '../../../utils';
import { SlackOptions } from '../../decorators/slack-options.decorator';
import { DecoratedSlackOptionsMiddlewareArgs } from '../../event-handlers';
import { <PERSON>lack<PERSON><PERSON><PERSON>andler } from '../../interfaces/action-handler.interface';
import { FormBuilderService } from '../../services/form-builder.service';

const LOG_SPAN = 'FormOptionsHandler';
const FORM_FIELD_OPTIONS_PREFIX = 'field_options_';

@Injectable()
@SlackOptions(FORM_FIELD_OPTIONS_PREFIX)
export class FormOptionsHandler implements SlackActionHandler {
  constructor(
    @Inject(CUSTOM_LOGGER_TOKEN) private readonly logger: ILogger,
    private readonly formBuilderService: FormBuilderService,
  ) {}

  async handle(args: DecoratedSlackOptionsMiddlewareArgs) {
    try {
      this.logger.debug(
        `${LOG_SPAN} Handling options load:`,
        JSON.stringify(args.body, null, 2),
      );

      const { value = '', view } = args.body;

      // Get metadata to know which form and field we're dealing with
      const metadata = view?.private_metadata
        ? JSON.parse(view.private_metadata)
        : {};

      const { fields = [], teamId } = metadata;

      // Get the field ID from the action ID
      const fieldId = args.body.action_id.replace(
        FORM_FIELD_OPTIONS_PREFIX,
        '',
      );

      // Find the field definition
      const field = fields.find((f: any) => f.id === fieldId);
      if (!field || !field.apiForOptions) {
        this.logger.error(
          `${LOG_SPAN} No API endpoint found for field ${fieldId}`,
        );
        return { options: [] };
      }

      // Call the API to get options
      const options = await this.formBuilderService.getFieldOptions(
        args.context.installation,
        field.apiForOptions,
        teamId,
        value,
      );

      // Transform options to Slack format
      const slackOptions = options.map((option: any) => ({
        text: {
          type: 'plain_text',
          text: option.label,
        },
        value: option.value,
      }));

      this.logger.debug(
        `${LOG_SPAN} Returning options:`,
        JSON.stringify(slackOptions, null, 2),
      );

      return {
        options: slackOptions,
      };
    } catch (error) {
      this.logger.error(
        `${LOG_SPAN} Error handling options load:`,
        error instanceof Error ? error.message : 'Unknown error',
      );
      return { options: [] };
    }
  }
}
