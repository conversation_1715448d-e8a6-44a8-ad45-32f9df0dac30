import { Inject, Injectable } from '@nestjs/common';
import { BlockAction } from '@slack/bolt';
import { ChannelsRepository } from '../../../../database/entities/channels/repositories';
import { TeamRelationshipType } from '../../../../database/entities/mappings';
import { CUSTOM_LOGGER_TOKEN, ILogger } from '../../../../utils';
import { ConditionalFormBuilderComposite } from '../../../blocks/components/composite/form-builder/conditional-form-builder.composite';
import { SettingsCore } from '../../../core/management/settings.management';
import { SlackAction } from '../../../decorators';
import { DecoratedSlackActionMiddlewareArgs } from '../../../event-handlers';
import { SlackActionHandler } from '../../../interfaces/action-handler.interface';
import { FormBuilderService } from '../../../services/form-builder.service';
import { EnrichedSlackArgsContext } from '../../../services/slack-action-discovery.service';

// Constants for form handling
export const FORM_CONTINUE_ACTION_ID = 'form_continue';
export const FORM_SUBMISSION_MODAL_CALLBACK_ID = 'form_submission_modal';
export const LOG_SPAN = 'FormBuilderHandler';

@Injectable()
@SlackAction(FORM_CONTINUE_ACTION_ID)
export class FormBuilderHandler implements SlackActionHandler {
  constructor(
    @Inject(CUSTOM_LOGGER_TOKEN) private readonly logger: ILogger,
    private readonly formBuilderService: FormBuilderService,
    private readonly formBuilder: ConditionalFormBuilderComposite,
    private readonly channelsRepository: ChannelsRepository,
    private readonly settingsCore: SettingsCore,
  ) {}

  /**
   * Handles form continuation action
   * Validates form selection and opens a submission modal
   */
  async handle(args: DecoratedSlackActionMiddlewareArgs): Promise<void> {
    this.logger.log(`${LOG_SPAN} Handling form_continue action`);

    try {
      const context = this.extractContext(args);
      if (!context) {
        return;
      }

      const { installation, client } = context;
      if (!client) {
        this.logger.error(`${LOG_SPAN} No client in context`);
        return;
      }

      const blockAction = args.body as BlockAction;

      // Extract form ID from action payload
      const formId = this.extractFormId(blockAction);
      if (!formId) {
        await this.notifyUserToSelectForm(blockAction, client);
        return;
      }

      // Get channel ID and validate trigger
      const channelId = blockAction.container?.channel_id;
      const triggerId = blockAction.trigger_id;

      if (!channelId || !triggerId) {
        this.logger.error(
          `${LOG_SPAN} Missing channel ID or trigger ID in payload`,
        );
        return;
      }

      // Get channel and team information
      const channelInfo = await this.getChannelInfo(channelId, installation);
      if (!channelInfo) {
        return;
      }

      const { teamId, selectedForms } = channelInfo;

      // Fetch form data
      const formData = await this.formBuilderService.getFormById(
        installation,
        formId,
        teamId,
        selectedForms,
      );

      if (!formData) {
        this.logger.error(`${LOG_SPAN} Selected form not found: ${formId}`);
        return;
      }

      // Open modal with form
      await this.openFormModal(
        client,
        blockAction,
        formData,
        formId,
        teamId,
        channelId,
      );
    } catch (error) {
      this.logError(error);
    }
  }

  /**
   * Extracts the context from the middleware args
   */
  private extractContext(
    args: DecoratedSlackActionMiddlewareArgs,
  ): EnrichedSlackArgsContext | null {
    if ('context' in args) {
      return args.context as EnrichedSlackArgsContext;
    }

    this.logger.error(`${LOG_SPAN} No context in args`);
    return null;
  }

  /**
   * Extracts form ID from block action
   */
  private extractFormId(blockAction: BlockAction): string | undefined {
    // First try from action value
    if (blockAction.actions && blockAction.actions.length > 0) {
      const action = blockAction.actions[0] as any;
      if (action.value) {
        return action.value;
      }
    }

    // Then try from state values
    if (blockAction.state?.values) {
      for (const [_, block] of Object.entries(blockAction.state.values)) {
        for (const [_, action] of Object.entries(
          block as Record<string, any>,
        )) {
          if (action.selected_option?.value) {
            return action.selected_option.value;
          }
        }
      }
    }

    return undefined;
  }

  /**
   * Notifies user to select a form before continuing
   */
  private async notifyUserToSelectForm(
    blockAction: BlockAction,
    client: any,
  ): Promise<void> {
    this.logger.error(`${LOG_SPAN} Could not find form ID in payload`);

    if (blockAction.container?.channel_id && blockAction.user?.id) {
      await client.chat.postEphemeral({
        channel: blockAction.container.channel_id,
        user: blockAction.user.id,
        text: 'Please select a form before clicking Continue',
      });
    }
  }

  /**
   * Gets channel information including team ID and selected forms
   */
  private async getChannelInfo(
    channelId: string,
    installation: any,
  ): Promise<{
    teamId: string;
    selectedForms: string[];
  } | null> {
    const channel = await this.channelsRepository.findByCondition({
      where: {
        channelId: channelId,
        installation: { id: installation.id },
      },
      relations: [
        'platformTeamsToChannelMappings',
        'platformTeamsToChannelMappings.platformTeam',
      ],
    });

    if (!channel) {
      this.logger.error(`${LOG_SPAN} Channel not found for ID: ${channelId}`);
      return null;
    }

    // Get the primary team mapping
    const primaryTeamMapping = channel.platformTeamsToChannelMappings.find(
      (mapping) => mapping.relationshipType === TeamRelationshipType.PRIMARY,
    );

    if (!primaryTeamMapping || !primaryTeamMapping.platformTeam) {
      this.logger.error(`${LOG_SPAN} Primary team not found for this channel`);
      return null;
    }

    const teamId = primaryTeamMapping.platformTeam.uid;

    // Get selected forms from settings
    const selectedForms =
      ((await this.settingsCore.getValue('selected_forms', {
        workspace: installation,
        platformTeam: primaryTeamMapping.platformTeam,
      })) as string[]) ?? [];

    return { teamId, selectedForms };
  }

  /**
   * Opens a modal with the form for submission
   */
  private async openFormModal(
    client: any,
    blockAction: BlockAction,
    formData: any,
    formId: string,
    teamId: string,
    channelId: string,
  ): Promise<void> {
    const { fields, conditions, conditionOrder, name } = formData;

    const formBlocks = this.formBuilder.build({
      fields,
      conditions,
      conditionOrder,
      values: {}, // Initial values
      name,
    });

    try {
      await client.views.open({
        trigger_id: blockAction.trigger_id,
        view: {
          type: 'modal',
          callback_id: FORM_SUBMISSION_MODAL_CALLBACK_ID,
          private_metadata: JSON.stringify({
            formId,
            teamId,
            channelId,
            threadTs: blockAction.container?.thread_ts,
          }),
          title: {
            type: 'plain_text' as const,
            text: name || 'Create Ticket',
            emoji: true,
          },
          submit: {
            type: 'plain_text' as const,
            text: 'Submit',
            emoji: true,
          },
          close: {
            type: 'plain_text' as const,
            text: 'Cancel',
            emoji: true,
          },
          blocks: formBlocks.blocks,
        },
      });
    } catch (error) {
      this.logger.error(
        `${LOG_SPAN} Error opening modal: ${error instanceof Error ? error.message : String(error)}`,
      );

      this.sendErrorNotification(blockAction, client);
    }
  }

  /**
   * Sends an error notification to the user
   */
  private async sendErrorNotification(
    blockAction: BlockAction,
    client: any,
  ): Promise<void> {
    try {
      if (blockAction.container?.channel_id && blockAction.user?.id) {
        await client.chat.postEphemeral({
          channel: blockAction.container.channel_id,
          user: blockAction.user.id,
          text: "Sorry, we couldn't open the form. Please try again.",
        });
      }
    } catch (notifyError) {
      this.logger.error(
        `${LOG_SPAN} Failed to notify user of modal error: ${notifyError}`,
      );
    }
  }

  /**
   * Logs errors in a consistent format
   */
  private logError(error: unknown): void {
    if (error instanceof Error) {
      this.logger.error(
        `${LOG_SPAN} Error handling form selection: ${error.message}`,
        error.stack,
      );
    } else {
      this.logger.error(
        `${LOG_SPAN} Error handling form selection`,
        String(error),
      );
    }
  }
}
