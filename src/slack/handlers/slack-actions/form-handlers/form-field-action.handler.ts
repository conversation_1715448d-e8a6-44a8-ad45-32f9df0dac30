import { Inject, Injectable } from '@nestjs/common';
import { CUSTOM_LOGGER_TOKEN, ILogger } from '../../../../utils';
import {
  ConditionalFormBuilderComposite,
  FormValues,
} from '../../../blocks/components/composite/form-builder/conditional-form-builder.composite';
import { DecoratedSlackActionMiddlewareArgs } from '../../../event-handlers';
import { SlackActionHandler } from '../../../interfaces/action-handler.interface';
import { FormBuilderService } from '../../../services/form-builder.service';
import { validateFieldValue } from '../../../utils/form-field-validation.utils';

// Constants
export const FORM_FIELD_BLOCK_ID_PREFIX = 'block_';
const LOG_SPAN = 'FormFieldActionHandler';

// Define an interface for payload with view property for type safety
interface BlockActionWithView {
  view: {
    id: string;
    hash: string;
    private_metadata: string;
    callback_id: string;
    state: {
      values: Record<string, any>;
    };
    title: any;
    submit?: any;
    close?: any;
  };
}

/**
 * Handles interactive field changes in form modals
 * Validates input and updates the modal view in real-time
 */
@Injectable()
export class FormFieldActionHandler implements SlackActionHandler {
  constructor(
    @Inject(CUSTOM_LOGGER_TOKEN) private readonly logger: ILogger,
    private readonly formBuilder: ConditionalFormBuilderComposite,
    private readonly formBuilderService: FormBuilderService,
  ) {}

  async handle(args: DecoratedSlackActionMiddlewareArgs): Promise<void> {
    try {
      // Only handle block_actions for form fields
      if (!this.isFormFieldAction(args)) {
        return;
      }

      // Check for view in payload by casting to the right type
      const bodyWithView = args.body as unknown as BlockActionWithView;
      if (!bodyWithView.view) {
        this.logger.error(`${LOG_SPAN} No view found in payload`);
        return;
      }
      const view = bodyWithView.view;

      // Extract current form values from view state
      const currentValues = this.extractFormValues(view.state?.values || {});

      // Process any date/time fields that need to be combined
      this.processDateTimeFields(view.state?.values || {}, currentValues);

      // Get form data from metadata
      const metadata = JSON.parse(view.private_metadata);
      const { formId, teamId } = metadata;

      const formData = await this.formBuilderService.getFormById(
        args.context.installation,
        formId,
        teamId,
      );

      if (!formData) {
        this.logger.error(
          `${LOG_SPAN} Failed to fetch form data for ID: ${formId}`,
        );
        return;
      }

      // Validate form fields
      const errors = this.validateFields(formData.fields || [], currentValues);
      const hasValidationErrors = Object.keys(errors).length > 0;

      // Build updated form blocks
      const formBlocks = this.formBuilder.build({
        fields: formData.fields || [],
        conditions: formData.conditions || [],
        conditionOrder: formData.conditionOrder || [],
        values: currentValues,
        name: formData.name,
      });

      // Update the view with validation results
      await this.updateView(
        args.context.client,
        view,
        formBlocks,
        errors,
        hasValidationErrors,
      );
    } catch (error) {
      this.logger.error(
        `${LOG_SPAN} Error handling field change:`,
        error instanceof Error ? error.message : 'Unknown error',
      );
    }
  }

  /**
   * Checks if the action is a form field action
   */
  private isFormFieldAction(args: DecoratedSlackActionMiddlewareArgs): boolean {
    return (
      args.body.type === 'block_actions' &&
      args.body.actions?.[0]?.block_id?.startsWith(FORM_FIELD_BLOCK_ID_PREFIX)
    );
  }

  /**
   * Extracts form values from view state
   */
  private extractFormValues(stateValues: Record<string, any>): FormValues {
    const currentValues: FormValues = {};

    for (const [blockId, blockData] of Object.entries(stateValues)) {
      // Skip labels and date/time parts (handled separately)
      if (
        blockId.endsWith('_label') ||
        blockId.endsWith('_date') ||
        blockId.endsWith('_time')
      ) {
        continue;
      }

      const fieldId = blockId.replace(FORM_FIELD_BLOCK_ID_PREFIX, '');
      const element = Object.values(blockData)[0] as any;

      if (!element) {
        continue;
      }

      currentValues[fieldId] = this.extractElementValue(element);
    }

    return currentValues;
  }

  /**
   * Extracts a value from an element based on its type
   */
  private extractElementValue(element: any): any {
    switch (element.type) {
      case 'plain_text_input':
        return element.value || '';
      case 'number_input':
        return element.value ? Number(element.value) : null;
      case 'static_select':
      case 'radio_buttons':
        return element.selected_option?.value || null;
      case 'multi_static_select':
        return element.selected_options?.map((opt: any) => opt.value) || [];
      case 'datepicker':
        return element.selected_date || null;
      case 'timepicker':
        return element.selected_time || null;
      case 'checkboxes':
        return element.selected_options?.length > 0;
      default:
        return null;
    }
  }

  /**
   * Processes and combines date and time fields
   */
  private processDateTimeFields(
    stateValues: Record<string, any>,
    currentValues: FormValues,
  ): void {
    const dateTimeBlocks = new Map<string, { date?: string; time?: string }>();

    // First, collect all date/time parts
    for (const [blockId, blockData] of Object.entries(stateValues)) {
      if (blockId.includes('_date') || blockId.includes('_time')) {
        const baseFieldIdWithPrefix = blockId.replace(/_date$|_time$/, '');
        const baseFieldId = baseFieldIdWithPrefix.replace(
          FORM_FIELD_BLOCK_ID_PREFIX,
          '',
        );

        if (!dateTimeBlocks.has(baseFieldId)) {
          dateTimeBlocks.set(baseFieldId, {});
        }

        const dateTimeValue = dateTimeBlocks.get(baseFieldId);
        // Safe to use non-null assertion as we just checked for existence
        if (dateTimeValue) {
          const element = Object.values(blockData)[0] as any;

          if (blockId.endsWith('_date') && element.selected_date) {
            dateTimeValue.date = element.selected_date;
          } else if (blockId.endsWith('_time') && element.selected_time) {
            dateTimeValue.time = element.selected_time;
          }
        }
      }
    }

    // Then process and combine them
    for (const [baseFieldId, values] of dateTimeBlocks.entries()) {
      this.logger.debug(
        `${LOG_SPAN} Processing datetime field ${baseFieldId} with values:`,
        JSON.stringify(values),
      );

      if (!values.date && !values.time) {
        this.logger.debug(
          `${LOG_SPAN} No date or time values for field ${baseFieldId}`,
        );
        continue;
      }

      try {
        const combinedValue = this.combineDateTime(values.date, values.time);
        if (combinedValue) {
          currentValues[baseFieldId] = combinedValue;
          this.logger.debug(
            `${LOG_SPAN} Final UTC datetime: ${combinedValue} for field ${baseFieldId}`,
          );
        } else {
          currentValues[baseFieldId] = null;
        }
      } catch (error) {
        this.logger.error(
          `${LOG_SPAN} Error processing date/time for field ${baseFieldId}:`,
          error instanceof Error ? error.message : String(error),
        );
        currentValues[baseFieldId] = null;
      }
    }
  }

  /**
   * Combines date and time values into ISO string
   */
  private combineDateTime(date?: string, time?: string): string | null {
    if (!date && !time) {
      return null;
    }

    try {
      if (date && time) {
        // Full date and time in UTC
        const dateTimeString = `${date}T${time}:00Z`;
        const dateObj = new Date(dateTimeString);
        return dateObj.toISOString().split('.')[0];
      }

      if (date) {
        // Date only, use midnight UTC
        const dateTimeString = `${date}T00:00:00Z`;
        const dateObj = new Date(dateTimeString);
        return dateObj.toISOString().split('.')[0];
      }

      if (time) {
        // Time only, use today's date
        const today = new Date().toISOString().split('T')[0];
        const dateTimeString = `${today}T${time}:00Z`;
        const dateObj = new Date(dateTimeString);
        return dateObj.toISOString().split('.')[0];
      }

      return null;
    } catch (error) {
      this.logger.error(
        `${LOG_SPAN} Error combining date/time:`,
        error instanceof Error ? error.message : String(error),
      );
      return null;
    }
  }

  /**
   * Validates form fields and returns errors
   */
  private validateFields(
    fields: any[],
    currentValues: FormValues,
  ): Record<string, string> {
    const errors: Record<string, string> = {};

    for (const field of fields) {
      const fieldValue = currentValues[field.id];
      const validationResult = validateFieldValue(field, fieldValue);

      if (!validationResult.isValid && validationResult.errorMessage) {
        errors[`${FORM_FIELD_BLOCK_ID_PREFIX}${field.id}`] =
          validationResult.errorMessage;
        this.logger.debug(
          `${LOG_SPAN} Validation error for field ${field.id} (${field.name}): ${validationResult.errorMessage}`,
        );
      }
    }

    return errors;
  }

  /**
   * Updates the view with validation results
   */
  private async updateView(
    client: any,
    view: any,
    formBlocks: any,
    errors: Record<string, string>,
    hasValidationErrors: boolean,
  ): Promise<void> {
    // For validation errors, add error messages as context blocks
    if (hasValidationErrors) {
      const blocksWithErrors = this.addErrorsToBlocks(
        formBlocks.blocks,
        errors,
      );

      await client.views.update({
        view_id: view.id,
        hash: view.hash,
        view: {
          type: 'modal',
          callback_id: view.callback_id,
          private_metadata: view.private_metadata,
          title: view.title,
          submit: view.submit || {
            type: 'plain_text',
            text: 'Submit',
            emoji: true,
          },
          close: view.close || {
            type: 'plain_text',
            text: 'Cancel',
            emoji: true,
          },
          blocks: blocksWithErrors,
        },
      });

      this.logger.debug(
        `${LOG_SPAN} Updated view with error messages:`,
        JSON.stringify(errors),
      );
      return;
    }

    // No validation errors, just update the view
    await client.views.update({
      view_id: view.id,
      hash: view.hash,
      view: {
        type: 'modal',
        callback_id: view.callback_id,
        private_metadata: view.private_metadata,
        title: view.title,
        submit: view.submit || {
          type: 'plain_text',
          text: 'Submit',
          emoji: true,
        },
        close: view.close || {
          type: 'plain_text',
          text: 'Cancel',
          emoji: true,
        },
        blocks: formBlocks.blocks,
      },
    });
  }

  /**
   * Adds error messages to form blocks
   */
  private addErrorsToBlocks(
    blocks: any[],
    errors: Record<string, string>,
  ): any[] {
    const blocksWithErrors: any[] = [];

    for (const block of blocks) {
      // Add the original block
      blocksWithErrors.push(block);

      // If this block has an error, add an error context block after it
      if (block.block_id && errors[block.block_id]) {
        blocksWithErrors.push({
          type: 'context',
          elements: [
            {
              type: 'mrkdwn',
              text: `:warning: *${errors[block.block_id]}*`,
            },
          ],
        });
      }
    }

    return blocksWithErrors;
  }
}
