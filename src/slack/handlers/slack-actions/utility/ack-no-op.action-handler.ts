import { Inject } from '@nestjs/common';
import { Injectable } from '@nestjs/common';
import { SlackActionMiddlewareArgs } from '@slack/bolt';
import { SLACK_ACTION_NOOP } from '../../../../utils/constants';
import { CUSTOM_LOGGER_TOKEN, ILogger } from '../../../../utils/logger';
import { FormSelectorComposite } from '../../../blocks/components/composite/form-builder';
import { SlackAction } from '../../../decorators';
import { SlackActionHandler } from '../../../interfaces';

@Injectable()
@SlackAction([SLACK_ACTION_NOOP, FormSelectorComposite.ACTION_ID])
export class AckNoOpActionHandler implements SlackActionHandler {
  constructor(@Inject(CUSTOM_LOGGER_TOKEN) private readonly logger: ILogger) {}

  async handle(_args: SlackActionMiddlewareArgs) {}
}
