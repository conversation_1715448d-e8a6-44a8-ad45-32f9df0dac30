import { Inject, Injectable } from '@nestjs/common';
import { SlackActionMiddlewareArgs, StringIndexed } from '@slack/bolt';
import { Channels } from '../../../database/entities/channels/channels.entity';
import { ChannelType } from '../../../database/entities/channels/channels.entity';
import { ChannelsRepository } from '../../../database/entities/channels/repositories/channels.repository';
import { safeJsonStringify } from '../../../utils/external/safe-json-stringify.utils';
import { CUSTOM_LOGGER_TOKEN, ILogger } from '../../../utils/logger';
import {
  ChannelSetupBlocks,
  SelectChannelTeamComposite,
} from '../../blocks/components/composite';
import { SlackAppManagementService } from '../../core';
import { SlackAction } from '../../decorators';
import { SlackActionHandler } from '../../interfaces';
import { EnrichedSlackArgsContext } from '../../services/slack-action-discovery.service';
import { SlackResponse } from '../../types';

@Injectable()
@SlackAction([
  ChannelSetupBlocks.ACTION_IDS.TEAM_SELECT,
  ChannelSetupBlocks.ACTION_IDS.CHANNEL_TYPE_SELECT,
])
export class ChannelSetupHandler implements SlackActionHandler {
  constructor(
    private readonly slackAppManagementService: SlackAppManagementService,
    private readonly teamConfigBlock: SelectChannelTeamComposite,

    @Inject(CUSTOM_LOGGER_TOKEN)
    private readonly logger: ILogger,
    private readonly channelsRepository: ChannelsRepository,
  ) {}

  async handle(args: SlackActionMiddlewareArgs): Promise<SlackResponse> {
    // Check if the context is present
    if (!('context' in args)) {
      this.logger.error('No context found in arguments');
      return;
    }

    const { action, body, context } = args;

    const channelMetadata = body.channel;
    const slackContext = context as EnrichedSlackArgsContext;

    // Get the channel from the database, upsert and get if it does not exist
    const channel = await this.slackAppManagementService.getAndUpsertChannel({
      channelId: channelMetadata.id,
      token: slackContext.botToken,
      installationId: slackContext.installation.id,
      organizationId: slackContext.organization.id,
    });

    try {
      // If the action id is present then handle the action, and setup the channel
      if ('action_id' in action) {
        switch (action.action_id) {
          case ChannelSetupBlocks.ACTION_IDS.CHANNEL_TYPE_SELECT: {
            if ('selected_option' in action && action.selected_option?.value) {
              const selectedChannelType = action.selected_option
                .value as ChannelType;

              // Handle the channel type based on selection
              if (selectedChannelType === ChannelType.CUSTOMER_CHANNEL) {
                await this.handleCustomerChannel(channel, slackContext, body);
              } else if (
                selectedChannelType === ChannelType.INTERNAL_HELPDESK
              ) {
                await this.handleInternalHelpdesk(channel, slackContext);
              } else if (selectedChannelType === ChannelType.TRIAGE_CHANNEL) {
                await this.handleTriageChannel(channel, slackContext, body);
              }
            }
            break;
          }

          case ChannelSetupBlocks.ACTION_IDS.TEAM_SELECT: {
            // Team selection will be handled in the view submission
            break;
          }

          default: {
            this.logger.error(`Unknown action id: ${action.action_id}`);
            break;
          }
        }
      }
    } catch (error) {
      this.logger.error(`Error handling channel setup: ${error}`);

      // Respond to the user with the error message
      if (error instanceof Error) {
        args.respond({ text: error.message });
      } else {
        args.respond({
          text: 'An unknown error occurred while handling the channel setup, please try again.',
        });
      }
    }

    // Remove the ephemeral response if it exists
    try {
      await this.removeEphemeralResponse(body);
    } catch (error) {
      const errorMessage =
        error instanceof Error
          ? error.message
          : safeJsonStringify(error, {
              fallback: 'Unknown error',
              handleCircular: true,
            });
      this.logger.error(`Error removing ephemeral response: ${errorMessage}`);
    }

    return {
      response_type: 'ephemeral',
      text: 'Channel setup complete',
      replace_original: true,
      delete_original: true,
    };
  }

  /**
   * Handle the internal helpdesk channel setup
   * @param channel The channel
   * @param _slackContext The Slack context
   */
  private async handleInternalHelpdesk(
    channel: Channels,
    _slackContext: EnrichedSlackArgsContext,
  ) {
    this.logger.log(
      `Internal helpdesk channel selected for ${channel.channelId}`,
    );

    // [SANITY] Check if the bot is joined to the channel
    if (channel.isBotJoined === false) {
      throw new Error(
        'Bot is not present in this channel, please invite the bot first and try again.',
      );
    }

    // Update the channel type and mark the bot active
    await this.channelsRepository.update(channel.id, {
      isBotActive: true,
      channelType: ChannelType.INTERNAL_HELPDESK,
    });
  }

  /**
   * Handle the customer channel setup
   * @param channel The channel
   * @param _slackContext The Slack context
   */
  private async handleCustomerChannel(
    channel: Channels,
    slackContext: EnrichedSlackArgsContext,
    body: StringIndexed,
  ) {
    this.logger.log(`Customer channel selected for ${channel.channelId}`);

    // [SANITY] Check if the bot is joined to the channel
    if (channel.isBotJoined === false) {
      throw new Error(
        'Bot is not present in this channel, please invite the bot first and try again.',
      );
    }

    await slackContext.client.views.open({
      token: slackContext.installation.botToken,
      trigger_id: body.trigger_id,
      view: {
        private_metadata: JSON.stringify({
          channelId: channel.channelId,
          ephemeralResponseUrl: body.response_url,
        }),
        type: 'modal',
        callback_id: 'team_config_modal_submit',
        title: {
          type: 'plain_text',
          text: 'Configure Team',
          emoji: true,
        },
        blocks: this.teamConfigBlock.build().blocks,
        submit: {
          type: 'plain_text',
          text: 'Save',
        },
        close: {
          type: 'plain_text',
          text: 'Cancel',
        },
      },
    });
  }

  /**
   * Handle the triage channel setup
   *
   * @param channel The channel
   * @param _slackContext The Slack context
   * @param _body The body of the request
   */
  private async handleTriageChannel(
    channel: Channels,
    _slackContext: EnrichedSlackArgsContext,
    _body: StringIndexed,
  ) {
    this.logger.log(`Triage channel selected for ${channel.channelId}`);

    // [SANITY] Check if the bot is joined to the channel
    if (channel.isBotJoined === false) {
      throw new Error(
        'Bot is not present in this channel, please invite the bot first and try again.',
      );
    }

    // Update the channel type to triage
    await this.channelsRepository.update(channel.id, {
      isBotActive: true,
      channelType: ChannelType.TRIAGE_CHANNEL,
    });
  }

  /**
   * Remove the ephemeral response if it exists
   * @param body The body of the request
   */
  private async removeEphemeralResponse(body: StringIndexed) {
    if ('response_url' in body) {
      const responseUrl = body.response_url;
      if (responseUrl) {
        try {
          await fetch(responseUrl, {
            method: 'POST' as const,
            headers: { 'Content-Type': 'application/json' },
            body: JSON.stringify({
              response_type: 'ephemeral',
              delete_original: true,
            }),
          });
        } catch (error) {
          const errorMessage =
            error instanceof Error
              ? error.message
              : safeJsonStringify(error, {
                  fallback: 'Unknown error',
                  handleCircular: true,
                });
          this.logger.error(
            `Error removing ephemeral response: ${errorMessage}`,
          );
        }
      }
    }
  }
}
