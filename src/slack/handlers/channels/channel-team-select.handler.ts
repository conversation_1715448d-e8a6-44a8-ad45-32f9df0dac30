import { Injectable } from '@nestjs/common';
import { ThenaPlatformApiProvider } from '../../../external/provider/thena-platform-api.provider';
import { SelectChannelTeamComposite } from '../../blocks/components/composite/channels';
import { SlackOptions } from '../../decorators';
import { SlackActionHandler } from '../../interfaces';
import { EnrichedSlackArgsContext } from '../../services/slack-action-discovery.service';

@Injectable()
@SlackOptions(SelectChannelTeamComposite.ACTION_IDS.TEAM_SELECT)
export class TeamOptionsLoadHandler implements SlackActionHandler {
  constructor(
    private readonly thenaPlatformApiProvider: ThenaPlatformApiProvider,
  ) {}

  async handle(args: EnrichedSlackArgsContext) {
    const { context } = args;

    // Fetch teams based on search query
    const teams: any = await this.thenaPlatformApiProvider.getTeams(
      context.installation,
    );

    // Format teams for Slack's option list
    const options = teams.map((team) => ({
      text: {
        type: 'plain_text',
        text: team.name,
      },
      value: team.id,
    }));

    return { options };
  }
}
