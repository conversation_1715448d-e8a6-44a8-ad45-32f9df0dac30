import { ChannelSetup<PERSON>and<PERSON>, TeamOptionsLoadHandler } from './channels';
import {
  TicketAssigneeOptionsHandler,
  TicketPriorityOptionsHandler,
  TicketStatusOptionsHandler,
  TriageChannelOptionsHandler,
} from './slack-options';
import {
  Assign<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>,
  ChannelSetupViewHandler,
  CreateTicketViewHandler,
  TeamConfigHandler,
  TriageChannelConfigHandler,
} from './slack-views';

export const slackActionHandlers = [
  ChannelSetupHandler,
  TeamOptionsLoadHandler,
];

export const slackViewHandlers = [
  <PERSON><PERSON>onfig<PERSON>and<PERSON>,
  TriageChannelConfigHandler,
  AssignTicketHandler,
  CreateTicketViewHandler,
  ChannelSetupViewHandler,
];

export const slackOptionsHandlers = [
  TriageChannelOptionsHandler,
  TicketStatusOptionsHandler,
  TicketAssigneeOptionsHandler,
  TicketPriorityOptionsHandler,
];
