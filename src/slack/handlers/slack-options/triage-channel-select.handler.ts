import { Injectable } from '@nestjs/common';
import { SlackOptionsMiddlewareArgs } from '@slack/bolt';
import { FindOptionsWhere, In, Like, Not } from 'typeorm';
import {
  ChannelType,
  Channels,
} from '../../../database/entities/channels/channels.entity';
import { ChannelsRepository } from '../../../database/entities/channels/repositories/channels.repository';
import { ConfigureTriageChannelComposite } from '../../blocks/components/composite/channels';
import { SlackOptions } from '../../decorators';
import { SlackActionHandler } from '../../interfaces';
import { EnrichedSlackArgsContext } from '../../services/slack-action-discovery.service';

@Injectable()
@SlackOptions(ConfigureTriageChannelComposite.ACTION_IDS.CHANNEL_SELECT)
export class TriageChannelOptionsHandler implements SlackActionHandler {
  constructor(private readonly channelsRepository: ChannelsRepository) {}

  async handle(args: SlackOptionsMiddlewareArgs) {
    // Get the context
    let context: EnrichedSlackArgsContext;
    if ('context' in args) {
      context = args.context as EnrichedSlackArgsContext;
    }

    // Get the payload
    let payload: SlackOptionsMiddlewareArgs['payload'];
    if ('payload' in args) {
      payload = args.payload;
    }

    // Build the where clause
    const whereClause: FindOptionsWhere<Channels> = {
      isShared: false,
      isArchived: false,
      channelType: Not(In([ChannelType.CUSTOMER_CHANNEL])),
      installation: { id: context.installation.id },
      organization: { id: context.organization.id },
    };

    // Get the query and construct the where clause
    let query: string;
    if ('value' in payload) {
      query = payload.value;
      whereClause.name = Like(`%${query}%`);
    }

    // Fetch all channels that are not customer channels
    const channels = await this.channelsRepository.findAll({
      where: whereClause,
      take: 100,
    });

    // Format channels for Slack's option list
    const options = channels.map((channel) => ({
      text: {
        type: 'plain_text',
        text: channel.name,
      },
      value: channel.channelId,
    }));

    return { options };
  }
}
