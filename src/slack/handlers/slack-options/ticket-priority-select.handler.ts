import { Inject, Injectable } from '@nestjs/common';
import { SlackOptionsMiddlewareArgs } from '@slack/bolt';
import { ThenaPlatformApiProvider } from '../../../external/provider/thena-platform-api.provider';
import { TriageMessageBlock } from '../../../slack/blocks/components';
import { ILogger } from '../../../utils/logger';
import { CUSTOM_LOGGER_TOKEN } from '../../../utils/logger/logger.interface';
import { TicketDetailsMetadata } from '../../constants/interfaces';
import { SlackOptions } from '../../decorators';
import { SlackActionHandler } from '../../interfaces';
import { EnrichedSlackArgsContext } from '../../services/slack-action-discovery.service';

interface SlackMessage {
  metadata: TicketDetailsMetadata;
}

const LOG_SPAN = 'TicketPriorityOptionsHandler';

@Injectable()
@SlackOptions(TriageMessageBlock.ACTION_IDS.PRIORITY)
export class TicketPriorityOptionsHandler implements SlackActionHandler {
  constructor(
    @Inject(CUSTOM_LOGGER_TOKEN) private readonly logger: ILogger,
    private readonly thenaPlatformApiProvider: ThenaPlatformApiProvider,
  ) {}

  async handle(args: SlackOptionsMiddlewareArgs) {
    try {
      // Get the context
      let context: EnrichedSlackArgsContext;
      if ('context' in args) {
        context = args.context as EnrichedSlackArgsContext;
      }

      // Get the payload
      let payload: SlackOptionsMiddlewareArgs['payload'];
      if ('payload' in args) {
        payload = args.payload;
      }

      // Get the message
      let message: SlackMessage;
      let channelId: string;
      if ('message' in payload) {
        message = payload?.message as SlackMessage;
      } else {
        const parsedPrivateMetadata: {
          channelId: string;
          platformTeamId: string;
        } = JSON.parse(payload.view?.private_metadata);
        channelId = parsedPrivateMetadata.channelId;
      }

      let ticketTeamId: string;
      if (!channelId) {
        // Get the metadata
        const metadata = message.metadata;

        // If the message is not a ticket details message, return an empty array
        if (metadata?.event_type !== 'ticket_details') {
          return { options: [] };
        }

        const { installation_id: installationId, ticket_team_id } =
          metadata.event_payload;

        // [SANITY CHECK] If the installation ID does not match, throw an error
        if (context.installation.id !== installationId) {
          throw new Error("You're not allowed to interact with this entity!");
        }

        ticketTeamId = ticket_team_id;
      } else {
        const parsedPrivateMetadata: { platformTeamId: string } = JSON.parse(
          payload.view?.private_metadata,
        );

        ticketTeamId = parsedPrivateMetadata.platformTeamId;
      }

      // Get the statuses for the team
      const priorities =
        await this.thenaPlatformApiProvider.getPrioritiesForTeam(
          context.installation,
          ticketTeamId,
        );

      // Build the options
      const options = priorities.map((priority) => ({
        text: {
          type: 'plain_text',
          text: priority.name,
        },
        value: priority.id,
      }));

      return { options };
    } catch (error) {
      if (error instanceof Error) {
        this.logger.error(
          `${LOG_SPAN} Error getting ticket priorities. ${error.message}`,
        );
      } else {
        console.error(`${LOG_SPAN} Error getting ticket priorities.`, error);
      }

      return { options: [] };
    }
  }
}
