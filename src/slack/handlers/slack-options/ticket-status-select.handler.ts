import { Injectable } from '@nestjs/common';
import { SlackOptionsMiddlewareArgs } from '@slack/bolt';
import { ThenaPlatformApiProvider } from '../../../external/provider/thena-platform-api.provider';
import { TriageMessageBlock } from '../../../slack/blocks/components';
import { TicketDetailsMetadata } from '../../constants/interfaces';
import { SlackOptions } from '../../decorators';
import { SlackActionHandler } from '../../interfaces';
import { EnrichedSlackArgsContext } from '../../services/slack-action-discovery.service';

interface SlackMessage {
  metadata: TicketDetailsMetadata;
}

interface TicketStatus {
  id: string;
  name: string;
  displayName?: string;
  description?: string;
  isDefault?: boolean;
  teamId?: string;
  organizationId?: string;
  parentStatusId?: string;
  createdAt?: string;
  updatedAt?: string;
}

@Injectable()
@SlackOptions(TriageMessageBlock.ACTION_IDS.STATUS)
export class TicketStatusOptionsHandler implements SlackActionHandler {
  constructor(
    private readonly thenaPlatformApiProvider: ThenaPlatformApiProvider,
  ) {}

  async handle(args: SlackOptionsMiddlewareArgs) {
    // Get the context
    let context: EnrichedSlackArgsContext;
    if ('context' in args) {
      context = args.context as EnrichedSlackArgsContext;
    }

    // Get the payload
    let payload: SlackOptionsMiddlewareArgs['payload'];
    if ('payload' in args) {
      payload = args.payload;
    }

    // Get the message
    let message: SlackMessage;
    if ('message' in payload) {
      message = payload?.message as SlackMessage;
    }

    // Get the metadata
    const metadata = message.metadata;

    // If the message is not a ticket details message, return an empty array
    if (metadata?.event_type !== 'ticket_details') {
      return { options: [] };
    }

    const { installation_id: installationId, ticket_team_id: ticketTeamId } =
      metadata.event_payload;

    // [SANITY CHECK] If the installation ID does not match, throw an error
    if (context.installation.id !== installationId) {
      throw new Error("You're not allowed to interact with this entity!");
    }

    // Get the statuses for the team
    const statuses = (await this.thenaPlatformApiProvider.getStatusesForTeam(
      context.installation,
      ticketTeamId,
    )) as TicketStatus[];

    // Filter statuses: if children exist for a parent status, only show the children, otherwise show the parent
    const parentIds = new Set(
      statuses
        .filter((status: TicketStatus) => status.parentStatusId)
        .map((status: TicketStatus) => status.parentStatusId),
    );

    // Filter statuses to only include:
    // 1. Child statuses (they always show)
    // 2. Parent statuses that don't have any children
    const filteredStatuses = statuses.filter(
      (status: TicketStatus) =>
        status.parentStatusId || !parentIds.has(status.id),
    );

    // Build the options
    const options = filteredStatuses.map((status: TicketStatus) => ({
      text: {
        type: 'plain_text',
        text: status.displayName || status.name,
      },
      value: status.id,
    }));

    return { options };
  }
}
