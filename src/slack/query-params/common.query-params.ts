import { Type } from 'class-transformer';
import {
  IsBoolean,
  IsEnum,
  IsInt,
  IsOptional,
  IsString,
  Max,
  Min,
} from 'class-validator';

export class SearchQueryParams {
  @IsOptional()
  @IsString()
  searchQuery?: string;
}

export class CommonQueryParamsToFetchEntityData extends SearchQueryParams {
  @IsOptional()
  @IsInt()
  @Min(1)
  @Type(() => Number)
  page?: number = 1;

  @IsOptional()
  @IsInt()
  @Min(1)
  @Max(5000)
  @Type(() => Number)
  limit?: number = 10;

  @IsOptional()
  @IsBoolean()
  @Type(() => Boolean)
  forOrganization?: boolean = false;
}

export enum SlackChannelAvailabilityTypes {
  configured = 'configured',
  available = 'available',
}

export class GetAllSlackChannelsQueryParams extends CommonQueryParamsToFetchEntityData {
  @IsOptional()
  @IsString()
  @IsEnum(SlackChannelAvailabilityTypes)
  availabilityType?: SlackChannelAvailabilityTypes;

  @IsOptional()
  @IsBoolean()
  @Type(() => Boolean)
  showBotAddedChannels?: boolean = false;

  @IsOptional()
  @IsBoolean()
  @Type(() => Boolean)
  showExternalChannels?: boolean = false;

  @IsOptional()
  @IsBoolean()
  @Type(() => Boolean)
  showPrivateChannels?: boolean = false;

  @IsOptional()
  @IsBoolean()
  @Type(() => Boolean)
  isBotActive?: boolean = false;

  @IsOptional()
  @IsString()
  forPlatformTeamId?: string;
}
