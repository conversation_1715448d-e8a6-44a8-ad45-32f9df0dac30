import { Processor, WorkerHost } from '@nestjs/bullmq';
import { Inject, Injectable } from '@nestjs/common';
import { Job } from 'bullmq';
import { InstallationStatus } from '../../database/entities/installations';
import { InstallationRepository } from '../../database/entities/installations/repositories';
import { CUSTOM_LOGGER_TOKEN, ILogger } from '../../utils/logger';
import { SLACK_WORKSPACE_SYNC_QUEUE } from '../constants/slack-queue.constants';
import {
  SlackAccountsSyncJob,
  SlackChannelsSyncJob,
  SlackEmojiSyncJob,
  SlackExternalUsersSyncJob,
  SlackUsersSyncJob,
} from './jobs';
import { SlackSubgroupsSyncJob } from './jobs/slack-subgroups-sync.job';

@Injectable()
@Processor(SLACK_WORKSPACE_SYNC_QUEUE)
export class WorkspaceSyncProcessor extends WorkerHost {
  constructor(
    @Inject(CUSTOM_LOGGER_TOKEN) private readonly logger: ILogger,

    // Database Repositories
    private readonly installationsRepository: InstallationRepository,

    // Jobs
    private readonly slackChannelsSyncJob: SlackChannelsSyncJob,
    private readonly slackUsersSyncJob: SlackUsersSyncJob,
    private readonly slackExternalUsersSyncJob: SlackExternalUsersSyncJob,
    private readonly slackSubgroupsSyncJob: SlackSubgroupsSyncJob,
    private readonly slackAccountsSyncJob: SlackAccountsSyncJob,
    private readonly slackEmojisSyncJob: SlackEmojiSyncJob,
  ) {
    super();
  }

  async process(
    job: Job<{ installationId: string }, any, string>,
  ): Promise<any> {
    // Get the installation from the database with the job data
    const installation = await this.installationsRepository.findByCondition({
      where: { id: job.data.installationId },
      relations: ['organization'],
    });

    // If the installation is not found, we will skip the job
    if (!installation) {
      this.logger.error(
        `Installation not found for id ${job.data.installationId}`,
      );
      return;
    }

    this.logger.log(
      `Slack workspace sync started for installation ${installation.id}`,
    );

    // Sync the channels
    await this.slackChannelsSyncJob.execute(installation);

    // Sync the workspace users
    await this.slackUsersSyncJob.execute(installation);

    // Sync the subgroups
    await this.slackSubgroupsSyncJob.execute(installation);

    // Sync the emojis
    await this.slackEmojisSyncJob.execute(installation);

    console.time('SLACK_ACCOUNTS_SYNC_QUEUE');
    // Sync the accounts
    const domainToAccountIdMap =
      await this.slackAccountsSyncJob.execute(installation);
    console.timeEnd('SLACK_ACCOUNTS_SYNC_QUEUE');

    console.time('SLACK_EXTERNAL_USERS_SYNC_QUEUE');
    // Sync external users from shared channels
    await this.slackExternalUsersSyncJob.execute(
      installation,
      null,
      domainToAccountIdMap,
    );
    console.timeEnd('SLACK_EXTERNAL_USERS_SYNC_QUEUE');

    await this.installationsRepository.update(installation.id, {
      status: InstallationStatus.SYNCED,
    });
  }
}
