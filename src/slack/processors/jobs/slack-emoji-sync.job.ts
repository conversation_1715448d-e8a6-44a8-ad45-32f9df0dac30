import { Inject, Injectable } from '@nestjs/common';
import { EmojiListResponse } from '@slack/web-api/dist/types/response';
import { DeepPartial } from 'typeorm';
import { TransactionService } from '../../../database/common';
import { Installations, SlackEmojis } from '../../../database/entities';
import { SlackEmojisRepository } from '../../../database/entities/emojis/repository/slack-emojis.repository';
import { CUSTOM_LOGGER_TOKEN } from '../../../utils/logger';
import { ILogger } from '../../../utils/logger';
import { SlackWebAPIService } from '../../providers/slack-apis/slack-apis.service';

@Injectable()
export class SlackEmojiSyncJob {
  constructor(
    @Inject(CUSTOM_LOGGER_TOKEN) private readonly logger: ILogger,

    // External Services
    private readonly slackWebAPIService: SlackWebAPIService,

    // Utility Services
    private readonly transactionService: TransactionService,

    // Database Repositories
    private readonly slackEmojisRepository: SlackEmojisRepository,
  ) {}

  /**
   * Process the subgroups sync job
   */
  async execute(installation: Installations) {
    try {
      // Get the subgroups from the Slack's WebAPI, note this API is not paginated
      const listEmojisResponse =
        await this.slackWebAPIService.listWorkspaceEmojis(
          installation.botToken,
        );

      // If the response is not ok, we will throw an error
      if (!listEmojisResponse.ok) {
        this.logger.error(
          `Slack emojis sync failed, error: ${listEmojisResponse.error}`,
        );

        return;
      }

      const { emoji } = listEmojisResponse;

      // Write the emojis to the database
      await this.writeEmojis(emoji, installation);
    } catch (error) {
      if (error instanceof Error) {
        this.logger.error(
          `Slack emojis sync failed, error: ${error.message}`,
          error.stack,
        );
      }
    }
  }

  /**
   * @description
   * Writes the emojis to the database
   * @param emojis The emojis to write
   * @param installation The installation to write the emojis for
   */
  private async writeEmojis(
    emojis: EmojiListResponse['emoji'],
    installation: Installations,
  ) {
    const emojisToArray = Object.entries(emojis);

    // Construct the emojis to be written to the database
    const emojisToWrite: Array<DeepPartial<SlackEmojis>> = emojisToArray.map(
      ([name, url]) => ({
        name,
        url,
        installation: { id: installation.id },
        organization: { id: installation.organization.id },
      }),
    );

    // Run the transaction
    await this.transactionService.runInTransaction(async (txnContext) => {
      // Upsert the emojis into the database
      await this.slackEmojisRepository.upsertWithTxn(
        txnContext,
        emojisToWrite,
        { conflictPaths: ['name', 'installation'] },
      );
    });
  }
}
