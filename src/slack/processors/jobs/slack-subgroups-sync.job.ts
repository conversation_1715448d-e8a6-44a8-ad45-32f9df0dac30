import { Inject, Injectable } from '@nestjs/common';
import { UsergroupsListResponse } from '@slack/web-api/dist/types/response';
import { DeepPartial } from 'typeorm';
import { TransactionService } from '../../../database/common';
import { Installations, SlackSubgroups } from '../../../database/entities';
import { SlackSubgroupsRepository } from '../../../database/entities/subgroups/repositories/subgroups.repository';
import { CUSTOM_LOGGER_TOKEN } from '../../../utils/logger';
import { ILogger } from '../../../utils/logger';
import { SlackWebAPIService } from '../../providers/slack-apis/slack-apis.service';

@Injectable()
export class SlackSubgroupsSyncJob {
  constructor(
    @Inject(CUSTOM_LOGGER_TOKEN) private readonly logger: ILogger,

    // External Services
    private readonly slackWebAPIService: SlackWebAPIService,

    // Utility Services
    private readonly transactionService: TransactionService,

    // Database Repositories
    private readonly slackSubgroupsRepository: SlackSubgroupsRepository,
  ) {}

  /**
   * Process the subgroups sync job
   */
  async execute(installation: Installations) {
    try {
      // Get the subgroups from the Slack's WebAPI, note this API is not paginated
      const listSubGroupsResponse =
        await this.slackWebAPIService.listUserGroups(installation.botToken, {
          include_count: true,
          include_users: true,
        });

      // If the response is not ok, we will throw an error
      if (!listSubGroupsResponse.ok) {
        this.logger.error(
          `Slack subgroups sync failed, error: ${listSubGroupsResponse.error}`,
        );

        return;
      }

      const { usergroups } = listSubGroupsResponse;

      // Write the subgroups to the database
      await this.writeSubgroups(usergroups, installation);
    } catch (error) {
      if (error instanceof Error) {
        this.logger.error(
          `Slack channels sync failed, error: ${error.message}`,
          error.stack,
        );
      }
    }
  }

  /**
   * @description
   * Writes the subgroups to the database
   * @param subgroups The subgroups to write
   * @param installation The installation to write the subgroups for
   */
  private async writeSubgroups(
    subgroups: UsergroupsListResponse['usergroups'],
    installation: Installations,
  ) {
    // Construct the subgroups to be written to the database
    const userGroups: Array<DeepPartial<SlackSubgroups>> =
      this.constructSubGroups(subgroups, installation);

    // Run the transaction
    await this.transactionService.runInTransaction(async (txnContext) => {
      // Upsert the subgroups into the database
      await this.slackSubgroupsRepository.upsertWithTxn(
        txnContext,
        userGroups,
        { conflictPaths: ['slackGroupId', 'installation'] },
      );
    });
  }

  /**
   * @description
   * Constructs the subgroups to be written to the database
   * @param subgroups The subgroups to construct
   * @param installation The installation to construct the subgroups for
   * @returns The constructed subgroups
   */
  private constructSubGroups(
    subgroups: UsergroupsListResponse['usergroups'],
    installation: Installations,
  ) {
    const subGroups: Array<DeepPartial<SlackSubgroups>> = subgroups.map(
      (subgroup) => ({
        slackGroupId: subgroup.id,
        slackGroupDump: subgroup,
        slackHandle: subgroup.handle,
        description: subgroup.description,
        isExternal: subgroup.is_external,
        usersCount: subgroup.user_count,
        createdBy: { slackId: subgroup.created_by },
        updatedBy: { slackId: subgroup.updated_by },
        installation: { id: installation.id },
        organization: { id: installation.organization.id },
        users: subgroup.users,
      }),
    );

    return subGroups;
  }
}
