import { Inject, Injectable } from '@nestjs/common';
import { cloneDeep, get, merge } from 'lodash';
import { Channels, Installations } from '../../../database/entities';
import { ChannelsRepository } from '../../../database/entities/channels/repositories';
import { InstallationRepository } from '../../../database/entities/installations/repositories';
import {
  Account,
  CreateAccount,
  CustomFieldValue,
  CustomObjectRecord,
} from '../../../external/provider/interfaces';
import { ThenaPlatformApiProvider } from '../../../external/provider/thena-platform-api.provider';
import { ILogger } from '../../../utils/logger';
import { CUSTOM_LOGGER_TOKEN } from '../../../utils/logger';
import { SlackWebAPIService } from '../../providers/slack-apis/slack-apis.service';

interface SlackTeamAndChannelConfiguration {
  teamId: string;
  teamName: string;
  teamDomain: string;
  teamLogo: string;
  channels: {
    channelId: string;
    channelName: string;
  }[];
}

interface AccountsDumpForInstallation {
  customFields: {
    slackChannelId: string;
    slackTeamId: string;
    slackChannelName: string;
    slackTeamName: string;
    slackUserId: string;
    accountId: string;
    customerContactId: string;
  };
  customObjects: {
    accountCustomObjectId: string;
    contactCustomObjectId: string;
  };
}

@Injectable()
export class SlackAccountsSyncJob {
  private readonly LOG_SPAN = 'SlackAccountsSyncJob';

  constructor(
    @Inject(CUSTOM_LOGGER_TOKEN)
    private readonly logger: ILogger,

    private readonly channelsRepository: ChannelsRepository,
    private readonly installationsRepository: InstallationRepository,

    private readonly slackWebAPIService: SlackWebAPIService,
    private readonly platformApiProvider: ThenaPlatformApiProvider,
  ) {}

  async execute(installation: Installations) {
    try {
      this.logger.log(
        `${this.LOG_SPAN}: Starting Slack accounts sync for installation ${installation.name}`,
      );

      // Step 1: Setup custom fields and objects
      this.logger.log(
        `${this.LOG_SPAN}: Setting up custom fields and objects for installation ${installation.name}`,
      );
      const { customFields, customObjects } =
        await this.setupCustomFieldsAndObjects(installation);
      this.logger.log(
        `${this.LOG_SPAN}: Successfully set up custom fields and objects for installation ${installation.name}`,
      );

      // Step 2: Get team and channel mappings
      this.logger.log(
        `${this.LOG_SPAN}: Fetching team and channel mappings for installation ${installation.name}`,
      );
      const { channels, domainToTeamChannelsMap } =
        await this.getTeamAndChannelMappings(installation);
      this.logger.log(
        `${this.LOG_SPAN}: Found ${channels.length} channels across ${
          Object.keys(domainToTeamChannelsMap).length
        } teams for installation ${installation.name}`,
      );

      // Step 3: Process accounts
      this.logger.log(
        `${this.LOG_SPAN}: Processing accounts for installation ${installation.name}`,
      );
      const domainToAccountIdMap = await this.processAccounts(
        installation,
        channels,
        domainToTeamChannelsMap,
        customFields,
        customObjects,
      );

      this.logger.log(
        `${this.LOG_SPAN}: Completed Slack accounts sync for installation ${installation.name}`,
      );

      return domainToAccountIdMap;
    } catch (error) {
      this.logger.error(
        `${this.LOG_SPAN}: Failed to sync Slack accounts for installation ${installation.name} > Error: ${error}`,
      );
    }
  }

  /**
   * Sets up custom fields and objects for the installation
   */
  private async setupCustomFieldsAndObjects(
    installation: Installations,
  ): Promise<AccountsDumpForInstallation> {
    // Check if installation already has custom fields and objects
    if (
      installation.platformDump?.customFields?.accountId &&
      installation.platformDump?.customObjects?.accountCustomObjectId
    ) {
      return installation.platformDump as AccountsDumpForInstallation;
    }

    const customFields = await this.createCustomFields(installation);
    const customObjects = await this.createCustomObjects(installation);

    await this.updateInstallationWithCustomFieldsAndObjects(
      installation,
      customFields,
      customObjects,
    );

    return { customFields, customObjects };
  }

  /**
   * Gets team and channel mappings
   */
  private async getTeamAndChannelMappings(installation: Installations) {
    // Get shared channels
    const channels = await this.channelsRepository.findAll({
      where: { installation: { id: installation.id }, isShared: true },
    });

    // Get unique team IDs and their info
    const teamInfoMap = await this.getTeamInfo(installation, channels);

    // Build domain to team/channel mapping
    const domainToTeamChannelsMap = this.buildDomainToTeamChannelsMap(
      channels,
      teamInfoMap,
    );

    return { channels, domainToTeamChannelsMap };
  }

  /**
   * Gets team info for all unique teams
   */
  private async getTeamInfo(installation: Installations, channels: Channels[]) {
    // Get unique team IDs
    const uniqueTeamIds = new Set<string>();
    for (const channel of channels) {
      if (channel.sharedTeamIds) {
        for (const teamId of channel.sharedTeamIds) {
          uniqueTeamIds.add(teamId);
        }
      }
    }

    this.logger.log(
      `${this.LOG_SPAN}: Fetching team info for ${uniqueTeamIds.size} teams`,
    );

    // Get team info for each team ID
    const teamInfoMap = new Map<
      string,
      { name: string; domain: string; logo: string }
    >();
    const teamInfoPromises = Array.from(uniqueTeamIds).map(async (teamId) => {
      try {
        const teamInfoResponse = await this.slackWebAPIService.getTeamInfo(
          installation.botToken,
          { team: teamId },
        );

        if (!teamInfoResponse.ok) {
          this.logger.error(
            `${this.LOG_SPAN}: Failed to get team info for team ${teamId}, error: ${teamInfoResponse.error}`,
          );
          return;
        }

        teamInfoMap.set(teamId, {
          name: teamInfoResponse.team.name,
          domain: teamInfoResponse.team.email_domain,
          logo: teamInfoResponse.team.icon.image_102,
        });
      } catch (error) {
        this.logger.error(
          `${this.LOG_SPAN}: Error fetching team info for team ${teamId}: ${error}`,
        );
      }
    });

    await Promise.all(teamInfoPromises);
    this.logger.log(
      `${this.LOG_SPAN}: Successfully fetched info for ${teamInfoMap.size} teams`,
    );

    return teamInfoMap;
  }

  /**
   * Builds mapping of domain to team and channel configurations
   */
  private buildDomainToTeamChannelsMap(
    channels: Channels[],
    teamInfoMap: Map<string, { name: string; domain: string; logo: string }>,
  ) {
    const domainToTeamChannelsMap: Record<
      string,
      SlackTeamAndChannelConfiguration
    > = {};

    for (const channel of channels) {
      if (channel.sharedTeamIds) {
        for (const teamId of channel.sharedTeamIds) {
          const teamInfo = teamInfoMap.get(teamId);
          if (teamInfo?.domain) {
            if (!domainToTeamChannelsMap[teamInfo.domain]) {
              domainToTeamChannelsMap[teamInfo.domain] = {
                teamId,
                teamName: teamInfo.name,
                teamDomain: teamInfo.domain,
                teamLogo: teamInfo.logo,
                channels: [],
              };
            }
            domainToTeamChannelsMap[teamInfo.domain].channels.push({
              channelId: channel.channelId,
              channelName: channel.name,
            });
          }
        }
      }
    }

    return domainToTeamChannelsMap;
  }

  /**
   * Processes accounts for each domain
   */
  private async processAccounts(
    installation: Installations,
    channels: Channels[],
    domainToTeamChannelsMap: Record<string, SlackTeamAndChannelConfiguration>,
    customFields: AccountsDumpForInstallation['customFields'],
    customObjects: AccountsDumpForInstallation['customObjects'],
  ): Promise<Record<string, string>> {
    const teamDomains = Object.keys(domainToTeamChannelsMap);
    const domainToAccountIdMap: Record<string, string> = {};

    if (teamDomains.length === 0) {
      this.logger.log(
        `${this.LOG_SPAN}: No external teams found for installation ${installation.name}`,
      );
      return;
    }

    // Get all existing accounts
    const accounts = await this.platformApiProvider.getAccountsByDomains(
      installation,
      teamDomains,
    );

    // Create missing accounts
    const accountDomainsToCreate = teamDomains.filter(
      (domain) => !accounts.some((account) => account.primaryDomain === domain),
    );

    await this.createMissingAccounts(
      installation,
      accountDomainsToCreate,
      domainToTeamChannelsMap,
      domainToAccountIdMap,
    );

    // Process existing accounts
    const primaryDomainToAccountsMap =
      this.buildPrimaryDomainToAccountsMap(accounts);
    await this.processExistingAccounts(
      installation,
      channels,
      primaryDomainToAccountsMap,
      domainToTeamChannelsMap,
      { customFields, customObjects },
      domainToAccountIdMap,
    );

    return domainToAccountIdMap;
  }

  /**
   * Creates accounts for domains that don't have one and their custom object records
   */
  private async createMissingAccounts(
    installation: Installations,
    accountDomainsToCreate: string[],
    domainToTeamChannelsMap: Record<string, SlackTeamAndChannelConfiguration>,
    domainToAccountIdMap: Record<string, string>,
  ): Promise<void> {
    if (accountDomainsToCreate.length === 0) {
      return;
    }

    const customFieldsAndObjects =
      installation.platformDump as AccountsDumpForInstallation;

    // Prepare all account creation requests
    const accountCreationRequests = accountDomainsToCreate.map((domain) => ({
      domain,
      configuration: domainToTeamChannelsMap[domain],
    }));

    // Create all accounts in parallel and collect their custom object record creation tasks
    const createdAccounts = await Promise.all(
      accountCreationRequests.map(async ({ configuration }) => {
        const createdAccount = await this.platformApiProvider.createAccount(
          installation,
          this.constructAccount(installation, configuration),
        );

        domainToAccountIdMap[configuration.teamDomain] = createdAccount.id;

        return {
          account: createdAccount,
          configuration,
        };
      }),
    );

    // Create custom object records for all accounts in parallel
    const customObjectRecordCreationTasks = createdAccounts.map(
      ({ account, configuration }) =>
        this.createCustomObjectRecordsForChannels(
          installation,
          account,
          configuration,
          customFieldsAndObjects,
        ),
    );

    // Wait for all custom object records to be created
    const createdRecords = await Promise.all(customObjectRecordCreationTasks);

    this.logger.log(
      `${this.LOG_SPAN}: Created ${accountDomainsToCreate.length} new accounts with ${createdRecords.flat().length} custom object records`,
    );
  }

  /**
   * Creates custom object records for each channel of an account
   */
  private async createCustomObjectRecordsForChannels(
    installation: Installations,
    account: Account,
    configuration: SlackTeamAndChannelConfiguration,
    customFieldsAndObjects: AccountsDumpForInstallation,
  ): Promise<CustomObjectRecord[]> {
    this.logger.log(
      `${this.LOG_SPAN}: Creating custom object records for ${configuration.channels.length} channels in account ${account.id}`,
    );

    const records = await Promise.all(
      configuration.channels.map(async (channel) => {
        try {
          const customFieldValues: CustomFieldValue[] = [
            {
              customFieldId: customFieldsAndObjects.customFields.accountId,
              data: [{ value: account.id }],
            },
            {
              customFieldId: customFieldsAndObjects.customFields.slackTeamId,
              data: [{ value: configuration.teamId }],
            },
            {
              customFieldId: customFieldsAndObjects.customFields.slackTeamName,
              data: [{ value: configuration.teamName }],
            },
            {
              customFieldId: customFieldsAndObjects.customFields.slackChannelId,
              data: [{ value: channel.channelId }],
            },
            {
              customFieldId:
                customFieldsAndObjects.customFields.slackChannelName,
              data: [{ value: channel.channelName }],
            },
          ];

          return this.platformApiProvider.createCustomObjectRecord(
            installation,
            {
              customObjectId:
                customFieldsAndObjects.customObjects.accountCustomObjectId,
              customFieldValues,
            },
          );
        } catch (error) {
          this.logger.error(
            `${this.LOG_SPAN}: Failed to create custom object record for channel ${channel.channelId} in account ${account.id}: ${error}`,
          );
          return null;
        }
      }),
    );

    const validRecords = records.filter(
      (record): record is CustomObjectRecord =>
        record !== null &&
        'id' in record &&
        'version' in record &&
        'customFieldValues' in record,
    );

    this.logger.log(
      `${this.LOG_SPAN}: Successfully created ${validRecords.length} custom object records for account ${account.id}`,
    );

    return validRecords;
  }

  /**
   * Processes existing accounts in batches for better performance
   */
  private async processExistingAccounts(
    installation: Installations,
    channels: Channels[],
    primaryDomainToAccountsMap: Map<string, Account[]>,
    domainToTeamChannelsMap: Record<string, SlackTeamAndChannelConfiguration>,
    customFieldsAndObjects: AccountsDumpForInstallation,
    domainToAccountIdMap: Record<string, string>,
  ): Promise<void> {
    this.logger.log(
      `${this.LOG_SPAN}: Processing ${primaryDomainToAccountsMap.size} existing accounts`,
    );

    // Collect all updates to be made
    const accountUpdates: Array<{ account: Account; metadata: any }> = [];
    const channelUpdates: Array<{
      channel: Channels;
      account: Account;
      customObjectRecords: CustomObjectRecord[];
    }> = [];
    const customObjectRecordsToCreate: Array<{
      account: Account;
      configuration: SlackTeamAndChannelConfiguration;
    }> = [];

    // Collect all updates needed
    for (const [
      domain,
      domainAccounts,
    ] of primaryDomainToAccountsMap.entries()) {
      const configuration = domainToTeamChannelsMap[domain];

      if (domainAccounts.length === 1) {
        const account = domainAccounts[0];

        domainToAccountIdMap[domain] = account.id;

        // Prepare account metadata update
        const channelData = configuration.channels.map((channel) => ({
          channelId: channel.channelId,
          channelName: channel.channelName,
          teamId: installation.teamId,
          teamName: installation.teamName,
        }));

        const accountMetadata = cloneDeep(account.metadata) || {};
        if (get(accountMetadata, 'sinks.slack.syncStatus') === 'success') {
          accountMetadata.sinks.slack.channels = channelData;
          accountMetadata.sinks.slack.lastSyncedAt = new Date().toISOString();
        } else {
          accountMetadata.sinks = merge(accountMetadata.sinks || {}, {
            slack: {
              syncStatus: 'success',
              lastSyncedAt: new Date().toISOString(),
              channels: channelData,
            },
          });
        }

        accountUpdates.push({ account, metadata: accountMetadata });
        customObjectRecordsToCreate.push({ account, configuration });
      } else {
        // Handle multiple accounts case
        for (const account of domainAccounts) {
          const failureMetadata = cloneDeep(account.metadata) || {};
          failureMetadata.sinks = merge(failureMetadata.sinks || {}, {
            slack: {
              syncStatus: 'failed',
              failureReason:
                'Failed to sync as there are multiple accounts with same primary domain',
            },
          });
          accountUpdates.push({ account, metadata: failureMetadata });
        }
      }
    }

    // Execute all updates in parallel
    this.logger.log(
      `${this.LOG_SPAN}: Executing ${accountUpdates.length} account updates and ${customObjectRecordsToCreate.length} custom object record creations`,
    );

    await Promise.all([
      // Update all account metadata
      ...accountUpdates.map(async ({ account, metadata }) => {
        try {
          await this.platformApiProvider.updateAccount(
            installation,
            account.id,
            {
              metadata,
            },
          );
        } catch (error) {
          this.logger.error(
            `${this.LOG_SPAN}: Failed to update account ${account.id}: ${error}`,
          );
        }
      }),

      // Create all custom object records
      ...customObjectRecordsToCreate.map(async ({ account, configuration }) => {
        try {
          const records = await this.createCustomObjectRecordsForChannels(
            installation,
            account,
            configuration,
            customFieldsAndObjects,
          );

          // Collect channel updates for this account
          for (const channel of configuration.channels) {
            const dbChannel = channels.find(
              (c) => c.channelId === channel.channelId,
            );
            if (dbChannel) {
              channelUpdates.push({
                channel: dbChannel,
                account,
                customObjectRecords: records,
              });
            }
          }
        } catch (error) {
          this.logger.error(
            `${this.LOG_SPAN}: Failed to create custom object records for account ${account.id}: ${error}`,
          );
        }
      }),
    ]);

    // Batch update channels
    this.logger.log(
      `${this.LOG_SPAN}: Updating ${channelUpdates.length} channels with account information`,
    );

    const channelMap = new Map<string, Channels>();
    for (const { channel, account, customObjectRecords } of channelUpdates) {
      const existingChannel = channelMap.get(channel.channelId) || channel;
      const existingAccounts = existingChannel.platformDump?.accounts || [];
      const accountIndex = existingAccounts.findIndex(
        (a) => a.accountId === account.id,
      );
      const recordIndex = customObjectRecords.findIndex((r) =>
        r.customFieldValues.some(
          (v) =>
            v.customFieldId ===
              customFieldsAndObjects.customFields.slackChannelId &&
            v.data[0].value === channel.channelId,
        ),
      );

      if (accountIndex === -1 && recordIndex !== -1) {
        existingChannel.platformDump = {
          accounts: [
            ...existingAccounts,
            {
              accountId: account.id,
              customObjectRecordIds: [customObjectRecords[recordIndex].id],
            },
          ],
        };
      } else if (recordIndex !== -1) {
        existingAccounts[accountIndex].customObjectRecordIds = [
          customObjectRecords[recordIndex].id,
        ];
        existingChannel.platformDump = { accounts: existingAccounts };
      }

      channelMap.set(channel.channelId, existingChannel);
    }

    // Save all updated channels in one batch
    if (channelMap.size > 0) {
      const channelsToUpdate = Array.from(channelMap.values()).map(
        (channel) => ({
          id: channel.id,
          platformDump: channel.platformDump,
        }),
      );
      try {
        await this.channelsRepository.saveMany(channelsToUpdate);
        this.logger.log(
          `${this.LOG_SPAN}: Successfully updated ${channelsToUpdate.length} channels`,
        );
      } catch (error) {
        this.logger.error(
          `${this.LOG_SPAN}: Failed to update channels in batch: ${error}`,
        );
      }
    }
  }

  /**
   * Constructs an account
   * @param configuration The configuration to use for the account
   * @returns The constructed account {@link CreateAccount}
   */
  private constructAccount(
    installation: Installations,
    configuration: SlackTeamAndChannelConfiguration,
  ): CreateAccount {
    return {
      name: configuration.teamName,
      primaryDomain: configuration.teamDomain,
      source: 'slack',
      logo: configuration.teamLogo,
      metadata: {
        sinks: {
          slack: {
            syncStatus: 'success',
            channels: configuration.channels.map((channel) => ({
              channelId: channel.channelId,
              channelName: channel.channelName,
              teamId: installation.teamId,
              teamName: installation.teamName,
            })),
          },
        },
      },
    };
  }

  /**
   * Creates or gets custom fields required for Slack integration
   */
  private async createCustomFields(
    installation: Installations,
  ): Promise<AccountsDumpForInstallation['customFields']> {
    this.logger.log(
      `${this.LOG_SPAN}: Creating custom fields for installation ${installation.name}`,
    );

    try {
      const [
        slackChannelId,
        slackTeamId,
        slackChannelName,
        slackTeamName,
        slackUserId,
        accountId,
        customerContactId,
      ] = await Promise.all([
        this.platformApiProvider.createCustomField(installation, {
          name: 'Slack Channel ID',
          source: 'custom_object',
          fieldType: 'single_line',
          description:
            'The ID of the slack channel associated with the account/contact',
        }),
        this.platformApiProvider.createCustomField(installation, {
          name: 'Slack Team ID',
          source: 'custom_object',
          fieldType: 'single_line',
          description:
            'The ID of the slack team associated with the account/contact',
        }),
        this.platformApiProvider.createCustomField(installation, {
          name: 'Slack Channel Name',
          source: 'custom_object',
          fieldType: 'single_line',
          description:
            'The name of the slack channel associated with the account/contact',
        }),
        this.platformApiProvider.createCustomField(installation, {
          name: 'Slack Team Name',
          source: 'custom_object',
          fieldType: 'single_line',
          description:
            'The name of the slack team associated with the account/contact',
        }),
        this.platformApiProvider.createCustomField(installation, {
          name: 'Slack User ID',
          source: 'custom_object',
          fieldType: 'single_line',
          description: 'The ID of the slack user associated with the contact',
        }),
        this.platformApiProvider.createCustomField(installation, {
          name: 'Account ID',
          source: 'custom_object',
          fieldType: 'single_line',
          description:
            'Lookup field to link accounts to slack_accounts custom object',
        }),
        this.platformApiProvider.createCustomField(installation, {
          name: 'Customer Contact ID',
          source: 'custom_object',
          fieldType: 'single_line',
          description:
            'Lookup field to link customer contacts to slack_customer_contacts custom object',
        }),
      ]);

      this.logger.log(
        `${this.LOG_SPAN}: Successfully created all custom fields for installation ${installation.name}`,
      );

      return {
        slackChannelId: slackChannelId.id,
        slackTeamId: slackTeamId.id,
        slackChannelName: slackChannelName.id,
        slackTeamName: slackTeamName.id,
        slackUserId: slackUserId.id,
        accountId: accountId.id,
        customerContactId: customerContactId.id,
      };
    } catch (error) {
      this.logger.error(
        `${this.LOG_SPAN}: Failed to create custom fields for installation ${installation.name}: ${error}`,
      );
      throw error;
    }
  }

  /**
   * Creates or gets custom objects required for Slack integration
   */
  private async createCustomObjects(
    installation: Installations,
  ): Promise<AccountsDumpForInstallation['customObjects']> {
    this.logger.log(
      `${this.LOG_SPAN}: Creating custom objects for installation ${installation.name}`,
    );

    try {
      const [accountsCustomObject, contactsCustomObject] = await Promise.all([
        this.platformApiProvider.createCustomObject(installation, {
          name: 'Slack Accounts',
          description: 'Slack Accounts Relationship',
        }),
        this.platformApiProvider.createCustomObject(installation, {
          name: 'Slack Customer Contacts',
          description: 'Slack Customer Contacts Relationship',
        }),
      ]);

      this.logger.log(
        `${this.LOG_SPAN}: Successfully created custom objects for installation ${installation.name}`,
      );

      return {
        accountCustomObjectId: accountsCustomObject.id,
        contactCustomObjectId: contactsCustomObject.id,
      };
    } catch (error) {
      this.logger.error(
        `${this.LOG_SPAN}: Failed to create custom objects for installation ${installation.name}: ${error}`,
      );
      throw error;
    }
  }

  /**
   * Updates installation with custom fields and objects and saves to database
   */
  private async updateInstallationWithCustomFieldsAndObjects(
    installation: Installations,
    customFields: AccountsDumpForInstallation['customFields'],
    customObjects: AccountsDumpForInstallation['customObjects'],
  ): Promise<void> {
    this.logger.log(
      `${this.LOG_SPAN}: Updating installation ${installation.name} with custom fields and objects`,
    );

    try {
      // Update installation's platformDump
      installation.platformDump = {
        ...installation.platformDump,
        customFields: {
          ...installation.platformDump?.customFields,
          ...customFields,
        },
        customObjects: {
          ...installation.platformDump?.customObjects,
          ...customObjects,
        },
      };

      // Save installation to database
      await this.installationsRepository.save(installation);

      const {
        slackUserId: slackUserIdCF,
        customerContactId: customerContactIdCF,
        accountId: accountIdCF,
        ...commonCustomFields
      } = customFields;

      // Add custom fields to custom objects
      await Promise.all([
        // Add fields to Slack Accounts custom object
        ...Object.values(commonCustomFields).map((fieldId) =>
          this.platformApiProvider.addCustomObjectField(installation, {
            customObjectId: customObjects.accountCustomObjectId,
            customFieldId: fieldId,
          }),
        ),

        this.platformApiProvider.addCustomObjectField(installation, {
          customObjectId: customObjects.accountCustomObjectId,
          customFieldId: accountIdCF,
        }),

        // Add fields to Slack Customer Contacts custom object
        ...Object.values(commonCustomFields).map((fieldId) =>
          this.platformApiProvider.addCustomObjectField(installation, {
            customObjectId: customObjects.contactCustomObjectId,
            customFieldId: fieldId,
          }),
        ),

        this.platformApiProvider.addCustomObjectField(installation, {
          customObjectId: customObjects.contactCustomObjectId,
          customFieldId: customerContactIdCF,
        }),

        // Add slackUserId to Slack Customer Contacts custom object
        this.platformApiProvider.addCustomObjectField(installation, {
          customObjectId: customObjects.contactCustomObjectId,
          customFieldId: slackUserIdCF,
        }),
      ]);
    } catch (error) {
      this.logger.error(
        `${this.LOG_SPAN}: Failed to update installation ${installation.name} with custom fields and objects: ${error}`,
      );
      throw error;
    }
  }

  /**
   * Builds map of primary domain to accounts
   */
  private buildPrimaryDomainToAccountsMap(
    accounts: Account[],
  ): Map<string, Account[]> {
    const primaryDomainToAccountsMap = new Map<string, Account[]>();
    for (const account of accounts) {
      if (!primaryDomainToAccountsMap.has(account.primaryDomain)) {
        primaryDomainToAccountsMap.set(account.primaryDomain, []);
      }
      primaryDomainToAccountsMap.get(account.primaryDomain)?.push(account);
    }
    return primaryDomainToAccountsMap;
  }
}
