import { Inject, Injectable } from '@nestjs/common';
import { InjectRepository } from '@nestjs/typeorm';
import { Member } from '@slack/web-api/dist/types/response/UsersListResponse';
import { get } from 'lodash';
import { DeepPartial, In, Repository } from 'typeorm';
import {
  Channels,
  CustomerContacts,
  Installations,
} from '../../../database/entities';
import { ChannelsRepository } from '../../../database/entities/channels/repositories/channels.repository';
import { Account, CreateAccount, CreateCustomObjectRecord } from '../../../external/provider/interfaces';
import {
  CreateCustomerContactDTO,
  IngestCustomerContactDTO,
} from '../../../external/provider/interfaces/customer-contacts.interface';
import { ThenaPlatformApiProvider } from '../../../external/provider/thena-platform-api.provider';
import { CUSTOM_LOGGER_TOKEN, ILogger } from '../../../utils/logger';
import { FALLBACK_EMAIL_DOMAIN } from '../../constants/users.constants';
import { SlackWebAPIService } from '../../providers/slack-apis/slack-apis.service';

const MEMBERS_PER_PAGE = 100;

interface UserChannelConfig {
  channelId: string;
  channelName: string;
  teamId: string;
  teamName: string;
}

@Injectable()
export class SlackExternalUsersSyncJob {
  constructor(
    @Inject(CUSTOM_LOGGER_TOKEN)
    private readonly logger: ILogger,

    @InjectRepository(CustomerContacts)
    private readonly customerContactsRepository: Repository<CustomerContacts>,

    private readonly channelsRepository: ChannelsRepository,

    private readonly slackWebAPIService: SlackWebAPIService,
    private readonly externalService: ThenaPlatformApiProvider,
  ) {}

  async execute(
    installation: Installations,
    providedChannels?: Channels[],
    domainToAccountIdMap?: Record<string, string>,
  ) {
    try {
      let channels: Channels[] = providedChannels;
      if (!channels) {
        // Get all channels for this installation
        channels = await this.channelsRepository.findAll({
          where: {
            installation: { id: installation.id },
            isShared: true, // Only get shared channels
          },
        });
      } else {
        // Check if all the channels belong to this installation
        // Validate that provided channels belong to the installation
        const invalidChannels = channels.filter(
          (channel) => channel.installation.id !== installation.id,
        );

        if (invalidChannels.length > 0) {
          throw new Error(
            `Some channels do not belong to the installation: ${invalidChannels
              .map((c) => c.channelId)
              .join(', ')}`,
          );
        }
      }

      // Get our team ID
      const teamId = installation.teamId;

      // If no shared channels are found, return
      if (!channels.length) {
        this.logger.log('No shared channels found for external user sync');
        return;
      }

      const channelsMap: Record<string, Channels> = {};

      // Collect all unique member IDs across channels
      const uniqueMemberIds = new Set<string>();
      const userChannelsMap = new Map<string, Set<UserChannelConfig>>();

      // Get all members from shared channels
      for (const channel of channels) {
        channelsMap[channel.channelId] = channel;

        let cursor: string | undefined;

        // Get all members from shared channels
        do {
          let nextCursor: string | undefined;
          let members: string[];
          try {
            const membersResponse =
              await this.slackWebAPIService.getConversationMembers(
                installation.botToken,
                {
                  channel: channel.channelId,
                  limit: MEMBERS_PER_PAGE,
                  cursor,
                },
              );

            if (!membersResponse.ok) {
              this.logger.error(
                `Failed to get members for channel ${channel.channelId}: ${membersResponse.error}`,
              );
              continue;
            }

            members = membersResponse.members;
            nextCursor = membersResponse.response_metadata?.next_cursor;
          } catch (error) {
            // If failed to get members, log an error and continue
            this.logger.error(
              `Failed to get members for channel ${channel.channelId}: ${error}`,
            );
            continue;
          }

          // If no members are found, log a warning and continue
          if (!members) {
            this.logger.warn(
              `No members found for channel ${channel.channelId}`,
            );

            continue;
          }

          for (const memberId of members) {
            // Add member IDs to set for deduplication
            uniqueMemberIds.add(memberId);

            // Add the channel to the user's channel config
            const userChannelConfig: Set<UserChannelConfig> =
              userChannelsMap.get(memberId) || new Set<UserChannelConfig>();

            userChannelConfig.add({
              channelId: channel.channelId,
              channelName: channel.name,
              teamId: installation.teamId,
              teamName: installation.teamName,
            });

            userChannelsMap.set(memberId, userChannelConfig);
          }

          cursor = nextCursor;
        } while (cursor);
      }

      // Process users sequentially
      const memberIds = Array.from(uniqueMemberIds);

      this.logger.log(`Found ${memberIds.length} unique member IDs`);

      const batchSize = 10;
      let processedCount = 0;

      const teamIdToUsersMap: Record<string, Member[]> = {};
      const validUsers: Member[] = [];

      // Process users in batches only for our db writes
      for (let i = 0; i < memberIds.length; i += batchSize) {
        const batch = memberIds.slice(i, i + batchSize);
        

        this.logger.log(`Processing batch ${i / batchSize + 1}`);

        // Process users one at a time within the batch
        for (const userId of batch) {
          try {
            // TODO: CHANGE THIS TO GET ALL SLACK USERS OF A INSTALLATION FROM DB.
            // Get the user info
            const userResponse = await this.slackWebAPIService.getUserInfo(
              installation.botToken,
              { user: userId },
            );

            // If the user response is ok and the user is not from our team, add them to the valid users
            if (userResponse.ok && userResponse.user.team_id !== teamId) {
              validUsers.push(userResponse.user as Member);
              
              if (!teamIdToUsersMap[userResponse.user.team_id]) {
                teamIdToUsersMap[userResponse.user.team_id] = [];
              }
              teamIdToUsersMap[userResponse.user.team_id].push(userResponse.user as Member);
            }
          } catch (error) {
            if (error instanceof Error) {
              this.logger.error(
                `Failed to get user info for ${userId}: ${error.message}`,
              );
            }
          }
        }

        this.logger.log(
          `Found ${validUsers.length} valid users in current batch`,
        );
      }

      // If there are valid users, write them to the database and push them to the platform
      if (validUsers.length) {
        // Create accounts first 

        const teamInfoMap = await this.getTeamInfo(
          installation,
          Object.keys(teamIdToUsersMap),
        );

        // Get the most frequent email domain for each team
        const teamDomainMap: Record<string, string> = {};
        
        Object.keys(teamIdToUsersMap).forEach((teamId) => {
          const users = teamIdToUsersMap[teamId];
          const domainFrequency: Record<string, number> = {};
          let maxDomain = '';
          let maxCount = 0;
          
          for (const user of users) {
            if (!user) continue;
            
            const domain = this.getDomainFromEmail(user);
            if (!domain) {
              continue;
            }

            domainFrequency[domain] = (domainFrequency[domain] || 0) + 1;
            
            if (domainFrequency[domain] > maxCount) {
              maxCount = domainFrequency[domain];
              maxDomain = domain;
            }
          }

          teamDomainMap[teamId] = maxDomain;
        });

        const createdAccounts = await this.createPlatformAccounts(installation, teamDomainMap, teamInfoMap);

        this.logger.log(`Created ${Object.keys(createdAccounts).length} accounts`);

        // Create customer contacts in batches
        const batchSize = 10;
        let processedCount = 0;

        for (let i = 0; i < validUsers.length; i += batchSize) {
          this.logger.log(`Creating customer contacts for batch ${i / batchSize + 1}`);

          const batch = validUsers.slice(i, i + batchSize);

          let ingestedCustomerContactIds: Record<string, string>;
          let createdCustomObjectRecords: Record<string, string[]>;

          // Get all existing customer contacts
          const existingCustomerContacts =
            await this.customerContactsRepository.find({
              where: {
                installation: { id: installation.id },
                slackId: In(batch.map((user) => user.id)),
              },
            });

          // Push the customer contacts to the platform
          try {
            const {
              ingestedCustomerContactIdMap,
              createdCustomObjectRecordMap,
            } = await this.pushToPlatform(
              batch,
              userChannelsMap,
              installation,
              domainToAccountIdMap,
              existingCustomerContacts,
              createdAccounts,
            );

            ingestedCustomerContactIds = ingestedCustomerContactIdMap;
            createdCustomObjectRecords = createdCustomObjectRecordMap;
          } catch (error) {
            this.logger.error(
              `Failed to push customer contacts to the platform: ${error}`,
            );
          }

          await this.writeCustomerContacts(
            batch,
            installation,
            userChannelsMap,
            channelsMap,
            ingestedCustomerContactIds,
            createdCustomObjectRecords,
          );

          processedCount += batch.length;
        }

        this.logger.log(
          `Created ${processedCount} customer contacts for installation ${installation.id}`,
        );
      }

      this.logger.log(
        `External users sync completed. Processed ${processedCount} users from ${channels.length} channels for installation ${installation.id}`,
      );
    } catch (error) {
      if (error instanceof Error) {
        this.logger.error(
          `External users sync failed: ${error.message}`,
          error.stack,
        );
      }

      throw error;
    }
  }

  /**
   * Gets team info for all unique teams
   */
  private async getTeamInfo(installation: Installations, teamIds: string[]) {
    const teamInfoMap = new Map<
      string,
      { name: string; domain: string; logo: string }
    >();

    this.logger.log(`Fetching team info for ${teamIds.length} teams`);

    for (const teamId of teamIds) {
      if (teamInfoMap.has(teamId)) {
        continue;
      }

      try {
        const teamInfoResponse = await this.slackWebAPIService.getTeamInfo(
          installation.botToken,
          { team: teamId },
        );

        if (!teamInfoResponse.ok) {
          this.logger.error(
            `Failed to get team info for team ${teamId}, error: ${teamInfoResponse.error}`,
          );
          continue;
        }

        teamInfoMap.set(teamId, {
          name: teamInfoResponse.team.name,
          domain: teamInfoResponse.team.email_domain,
          logo: teamInfoResponse.team.icon.image_102,
        });

        await new Promise((resolve) => setTimeout(resolve, 1200)); // Wait for 1sec to avoid rate limiting
      } catch (error) {
        this.logger.error(
          `Error fetching team info for team ${teamId}: ${error}`,
        );
      }
    }

    this.logger.log(
      `Successfully fetched info for ${teamInfoMap.size} teams`,
    );

    return teamInfoMap;
  }

  private async createPlatformAccounts(installation: Installations, teamDomainMap: Record<string, string>, teamInfoMap: Map<string, { name: string; domain: string; logo: string }>) {
    const accountsToCreate: CreateAccount[] = [];

    for (const teamId of Object.keys(teamDomainMap)) {
      const domain = teamDomainMap[teamId];
      const teamInfo = teamInfoMap.get(teamId);

      if (!teamInfo) {
        this.logger.error(`Team info not found for team ${teamId}`);
        continue;
      }

      accountsToCreate.push({
        name: teamInfo.name,
        primaryDomain: domain,
        logo: teamInfo.logo,
        source: 'SLACK',
      });
    }

    const createdAccounts = await this.externalService.createAccounts(installation, accountsToCreate);

    return createdAccounts.reduce((acc, account) => {
      acc[account.primaryDomain] = account;
      return acc;
    }, {} as Record<string, Account>);
  }

  /**
   * Pushes the customer contacts to the platform.
   * @param users The Slack users to push to the platform.
   * @param organization The organization to push the customer contacts to.
   */
  private async pushToPlatform(
    users: Member[],
    userChannelsMap: Map<string, Set<UserChannelConfig>>,
    installation: Installations,
    domainToAccountIdMap: Record<string, string>,
    existingCustomerContacts: CustomerContacts[],
    createdAccounts: Record<string, Account>,
  ) {
    // Construct the customer contacts
    const customerContacts = users.map((user) =>
      this.constructPlatformCustomerContact(
        user,
        userChannelsMap,
        domainToAccountIdMap,
        createdAccounts,
      ),
    );

    // Construct the ingest customer contact DTO
    const ingestCustomerContactDTO: IngestCustomerContactDTO = {
      users: customerContacts,
      sinkSource: 'SLACK',
    };

    // Construct the custom object records
    const customObjectRecordsToCreate =
      this.constructPlatformCustomObjectRecords(
        installation,
        users,
        userChannelsMap,
      );

    // Push the customer contacts to the platform
    const ingestedCustomerContacts =
      await this.externalService.ingestCustomerContacts(
        installation,
        ingestCustomerContactDTO,
      );

    const contactCustomObjectId = get(
      installation.platformDump,
      'customObjects.contactCustomObjectId',
      null,
    );

    const customObjectRecordsToDelete = existingCustomerContacts.flatMap(
      (contact) => contact.platformDump?.customObjectRecordIds || [],
    );

    // Delete existing custom object records for the existing customer contacts
    await Promise.all(
      customObjectRecordsToDelete.map((recordId) =>
        this.externalService.deleteCustomObjectRecord(
          installation,
          contactCustomObjectId,
          recordId,
        ),
      ),
    );

    const ingestedCustomerContactIdMap: Record<string, string> =
      ingestedCustomerContacts.reduce(
        (acc, contact) => {
          acc[contact.email as string] = contact.id;
          return acc;
        },
        {} as Record<string, string>,
      );

    // Create the custom object records
    const createdCustomObjectRecordMap: Record<string, string[]> = {};
    await Promise.all(
      Object.entries(customObjectRecordsToCreate).map(
        async ([email, records]) => {
          const createdRecords = await Promise.all(
            records.map((record) =>
              this.externalService.createCustomObjectRecord(
                installation,
                record,
              ),
            ),
          );

          createdCustomObjectRecordMap[email] = createdRecords.map(
            (record) => record.id,
          );
        },
      ),
    );

    return {
      ingestedCustomerContactIdMap,
      createdCustomObjectRecordMap,
    };
  }

  /**
   * Writes customer contacts to the database.
   * @param users The Slack users to write to the database.
   * @param installation The installation to associate the customer contacts with.
   * @param ingestedCustomerContactIds The map of email to ingested customer contact ID created on platform
   * @param createdCustomObjectRecords The map of email to created custom object records created on platform
   */
  private async writeCustomerContacts(
    users: Member[],
    installation: Installations,
    userChannelsMap: Map<string, Set<UserChannelConfig>>,
    channelsMap: Record<string, Channels>,
    ingestedCustomerContactIds: Record<string, string>,
    createdCustomObjectRecords: Record<string, string[]>,
  ) {
    // First upsert the customer contacts without channels
    const contactsToWrite: Array<DeepPartial<CustomerContacts>> = users.map(
      (user) => {
        return this.constructCustomerContact(
          user,
          installation,
          ingestedCustomerContactIds,
          createdCustomObjectRecords,
        );
      },
    );

    await this.customerContactsRepository.upsert(contactsToWrite, {
      conflictPaths: ['slackId', 'installation'],
      skipUpdateIfNoValuesChanged: true,
    });

    // Create a map of slackId to channels for bulk processing
    const slackIdToChannelsMap = new Map<string, Channels[]>();
    for (const user of users) {
      const channels = Array.from(userChannelsMap.get(user.id) || []).map(
        (channel) => channelsMap[channel.channelId],
      );

      if (channels.length > 0) {
        slackIdToChannelsMap.set(user.id, channels);
      }
    }

    if (slackIdToChannelsMap.size > 0) {
      // Fetch all contacts with their current channels in one query
      const contactsWithChannels = await this.customerContactsRepository.find({
        where: {
          slackId: In(Array.from(slackIdToChannelsMap.keys())),
          installation: { id: installation.id },
        },
        relations: ['channels'],
      });

      // Update channels for all contacts in one go, only using valid channels
      const updatedContacts = contactsWithChannels.map((contact) => {
        const channelsForContact =
          slackIdToChannelsMap.get(contact.slackId) || [];
        contact.channels = channelsForContact;
        return contact;
      });

      // Save all contacts with their updated channels in one transaction
      await this.customerContactsRepository.save(updatedContacts);
    }
  }

  /**
   * Constructs a customer contact from a Slack user.
   * @param user The Slack user to construct a customer contact from.
   * @param installation The installation to associate the customer contact with.
   * @param ingestedCustomerContactIds The map of email to ingested customer contact ID created on platform
   * @param createdCustomObjectRecords The map of email to created custom object records created on platform
   * @returns The constructed customer contact.
   */
  private constructCustomerContact(
    user: Member,
    installation: Installations,
    ingestedCustomerContactIds: Record<string, string>,
    createdCustomObjectRecords: Record<string, string[]>,
  ): DeepPartial<CustomerContacts> {
    const images = {
      image_original: user.profile?.image_original,
      image_512: user.profile?.image_512,
      image_192: user.profile?.image_192,
      image_72: user.profile?.image_72,
      image_48: user.profile?.image_48,
      image_32: user.profile?.image_32,
      image_24: user.profile?.image_24,
    };

    const email = this.getEmail(user);
    const ingestedCustomerContactId = ingestedCustomerContactIds[email];
    const customObjectRecords = createdCustomObjectRecords[email];

    return {
      userDump: user as Record<string, any>,
      slackId: user.id,
      slackDeleted: user.deleted,
      name: user.real_name || user.name,
      realName: user.real_name || user.profile?.real_name,
      displayName: user.profile?.display_name,
      installation: { id: installation.id },
      organization: { id: installation.organization.id },
      tz: user.tz,
      tzLabel: user.tz_label,
      isAdmin: user.is_admin,
      isOwner: user.is_owner,
      isRestricted: user.is_restricted,
      isUltraRestricted: user.is_ultra_restricted,
      isBot: user.is_bot,
      userTitle: user.profile?.title,
      slackProfileRealName: user.profile?.real_name,
      slackProfileDisplayName: user.profile?.display_name,
      slackProfilePhone: user.profile?.phone,
      slackStatusText: user.profile?.status_text,
      slackStatusEmoji: user.profile?.status_emoji,
      slackProfileEmail: email,
      images,
      platformDump: {
        customerContactId: ingestedCustomerContactId,
        customObjectRecordIds: customObjectRecords,
      },
    };
  }

  /**
   * Constructs a customer contact for the platform.
   * @param user The Slack user to construct a customer contact for the platform from.
   * @returns The constructed customer contact for the platform.
   */
  private constructPlatformCustomerContact(
    user: Member,
    userChannelsMap: Map<string, Set<UserChannelConfig>>,
    domainToAccountIdMap: Record<string, string>,
    createdAccounts: Record<string, Account>,
  ): CreateCustomerContactDTO {
    const email = this.getEmail(user);
    const domain = this.getDomainFromEmail(user);
    const accountId = get(domainToAccountIdMap, domain, null);
    const createdAccount = get(createdAccounts, domain, null);

    const accounts = accountId ? [accountId] : createdAccount ? [createdAccount.id] : [];

    return {
      firstName: user.real_name || user.name,
      lastName: '',
      email,
      phoneNumber: user.profile?.phone,
      avatarUrl: user.profile?.image_original,
      accountIds: accounts,
      metadata: {
        sinks: {
          slack: {
            syncStatus: 'success',
            lastSyncedAt: new Date(),
            userId: user.id,
            channels: Array.from(userChannelsMap.get(user.id) || []),
          },
        },
      },
    };
  }

  /**
   * Constructs the custom object records for the platform.
   * @param installation The installation to construct the custom object records for.
   * @param user The Slack user to construct the custom object records for.
   * @param userChannelsMap The user channels map to construct the custom object records for.
   * @returns The constructed email to custom object record map.
   */
  private constructPlatformCustomObjectRecords(
    installation: Installations,
    users: Member[],
    userChannelsMap: Map<string, Set<UserChannelConfig>>,
  ): Record<string, CreateCustomObjectRecord[]> {
    // Extract the custom object ID and custom fields from the installation platform dump

    const contactCustomObjectId = get(
      installation.platformDump,
      'customObjects.contactCustomObjectId',
      null,
    );

    const {
      slackChannelId,
      slackTeamId,
      slackChannelName,
      slackTeamName,
      slackUserId,
    } = get(installation.platformDump, 'customFields', {
      slackChannelId: null,
      slackTeamId: null,
      slackChannelName: null,
      slackTeamName: null,
      slackUserId: null,
    });

    if (
      !contactCustomObjectId ||
      !slackChannelId ||
      !slackTeamId ||
      !slackChannelName ||
      !slackTeamName ||
      !slackUserId
    ) {
      return {};
    }

    const recordsMap: Record<string, CreateCustomObjectRecord[]> = {};

    // Iterate over the users and construct the custom object records
    for (const user of users) {
      const recordsToCreate: CreateCustomObjectRecord[] = [];
      for (const channel of userChannelsMap.get(user.id) || []) {
        recordsToCreate.push({
          customObjectId: contactCustomObjectId,
          customFieldValues: [
            {
              customFieldId: slackChannelId,
              data: [{ value: channel.channelId }],
            },
            { customFieldId: slackTeamId, data: [{ value: channel.teamId }] },
            {
              customFieldId: slackChannelName,
              data: [{ value: channel.channelName }],
            },
            {
              customFieldId: slackTeamName,
              data: [{ value: channel.teamName }],
            },
            {
              customFieldId: slackUserId,
              data: [{ value: user.id }],
            },
          ],
        });
      }

      recordsMap[this.getEmail(user)] = recordsToCreate;
    }

    return recordsMap;
  }
  /**
   * Gets the email for a Slack user
   * @param user The Slack user to get the email for
   * @returns The email for the Slack user
   */
  private getEmail(user: Member) {
    let email = user.profile?.email;

    // If the user has no email, create an email using the user's team ID and user ID
    if (!email) {
      const userEnterpriseId = user?.enterprise_user?.enterprise_id;
      const userTeamId = user.team_id;
      const userId = user.id;

      // If the user is not an enterprise user, use the team ID and user ID to create an email
      email = `${userTeamId}.${userId}@${FALLBACK_EMAIL_DOMAIN}`;

      // If the user is an enterprise user, add the enterprise ID to the email
      if (userEnterpriseId) {
        email = `${userEnterpriseId}.${userTeamId}.${userId}@${FALLBACK_EMAIL_DOMAIN}`;
      }
    }

    return email;
  }

  /**
   * Gets the domain from a Slack user's email
   * @param user The Slack user to get the domain from
   * @returns The domain from the Slack user's email
   */
  private getDomainFromEmail(user: Member) {
    return user.profile?.email ? user.profile?.email.split('@')[1] : null;
  }
}
