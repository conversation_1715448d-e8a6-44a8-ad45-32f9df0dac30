import { Inject, Injectable } from '@nestjs/common';
import { InjectRepository } from '@nestjs/typeorm';
import { KnownEventFromType } from '@slack/bolt';
import { FileShareMessageEvent } from '@slack/types';
import { Between, IsNull, Not, Repository } from 'typeorm';
import {
  Channels,
  CustomerContacts,
  Installations,
  SlackMessages,
} from '../../database/entities';
import { TeamRelationshipType } from '../../database/entities/mappings';
import { Person } from '../../database/interfaces/person.interface';
import { CreateNewComment } from '../../external/provider/interfaces';
import { ThenaPlatformApiProvider } from '../../external/provider/thena-platform-api.provider';
import { CUSTOM_LOGGER_TOKEN, ILogger, SLACK_SENTRY_TAG } from '../../utils';
import { SentryService } from '../../utils/filters/sentry-alerts.filter';
import { BaseSlackBlocksToHtml } from '../../utils/parsers/slack/slack-blocks-to-html/base-slack-to-html.parser';
import { SettingsCore } from './management';

@Injectable()
export class ConversationGroupingService {
  constructor(
    @Inject(CUSTOM_LOGGER_TOKEN) private readonly logger: ILogger,
    @Inject('Sentry') private readonly sentryService: SentryService,

    // Database Repositories
    @InjectRepository(SlackMessages)
    private readonly slackMessagesRepository: Repository<SlackMessages>,

    // External API Providers
    private readonly thenaPlatformApiProvider: ThenaPlatformApiProvider,

    // Management
    private readonly settingsCore: SettingsCore,

    // Parsers
    private readonly baseSlackBlocksToHtml: BaseSlackBlocksToHtml,
  ) {}

  /**
   * Determines if a new message should be grouped with an existing conversation
   * and create a comment instead of a new ticket
   */
  async shouldGroupWithExistingConversation(
    installation: Installations,
    channel: Channels,
    userInQuestion: Person,
  ): Promise<{
    shouldGroup: boolean;
    existingTicketId?: string;
    slackMessage?: SlackMessages;
  }> {
    try {
      // Get the platform team from the channel
      let platformTeam = null;

      // Get the primary team
      const primaryTeam = channel.platformTeamsToChannelMappings.find(
        (mapping) => mapping.relationshipType === TeamRelationshipType.PRIMARY,
      );

      // If there is a primary team, use it
      if (primaryTeam) {
        platformTeam = primaryTeam.platformTeam;
      } else if (channel.platformTeamsToChannelMappings.length === 1) {
        platformTeam = channel.platformTeamsToChannelMappings[0].platformTeam;
      }

      // If no platform team is found, don't group
      if (!platformTeam) {
        this.logger.log(
          'No platform team found for channel, cannot group conversations',
        );
        return { shouldGroup: false };
      }

      // Get conversation window setting (in minutes)
      const conversationWindow = await this.settingsCore.getValue(
        'conversation_window',
        {
          recursivelyLookup: false,
          workspace: installation,
          platformTeam,
        },
      );

      this.logger.log(`Conversation window: ${conversationWindow} minutes`);

      // If conversation window is 0 or not set, don't group
      if (!conversationWindow) {
        this.logger.log(
          'Conversation window is 0 or not set, skipping grouping',
        );
        return { shouldGroup: false };
      }

      // Calculate the timestamp for the conversation window
      const now = new Date();
      const windowStart = new Date(
        now.getTime() - conversationWindow * 60 * 1000,
      ); // Convert minutes to milliseconds

      this.logger.log(
        `Window start: ${windowStart}, Now: ${now}, Conversation window: ${conversationWindow} minutes`,
      );

      // Find the most recent message from this user in this channel that created a ticket
      const recentMessages = await this.slackMessagesRepository.find({
        where: {
          channel: { id: channel.id },
          installation: { id: installation.id },
          slackUserId: userInQuestion.slackId,
          platformTicketId: Not(IsNull()),
          createdAt: Between(windowStart, now),
        },
        order: { createdAt: 'DESC' },
        take: 1,
      });

      if (recentMessages.length === 0) {
        this.logger.debug(
          'No recent messages found for user in channel, creating new ticket',
        );
        return { shouldGroup: false };
      }

      const recentMessage = recentMessages[0];

      this.logger.log(
        `Recent message: ${JSON.stringify({
          ts: recentMessage.slackMessageTs,
          threadTs: recentMessage.slackMessageThreadTs,
          platformTicketId: recentMessage.platformTicketId,
          createdAt: recentMessage.createdAt,
        })}`,
      );

      // Check if the ticket is still open
      if (recentMessage.platformTicketId) {
        try {
          const ticket = await this.thenaPlatformApiProvider.getTicket(
            installation,
            recentMessage.platformTicketId,
          );

          this.logger.log(`Found ticket ${ticket.id} status: ${ticket.status}`);

          if (ticket) {
            this.logger.log(
              `Grouping message with existing ticket ${recentMessage.platformTicketId}`,
            );
            return {
              shouldGroup: true,
              existingTicketId: recentMessage.platformTicketId,
              slackMessage: recentMessage,
            };
          }
        } catch (error) {
          this.logger.error(
            `Error fetching ticket ${recentMessage.platformTicketId}: ${error instanceof Error ? error.message : 'Unknown error'}`,
            error instanceof Error ? error.stack : undefined,
          );

          // Report to Sentry
          this.sentryService.captureException(error, {
            tag: SLACK_SENTRY_TAG,
            name: '🚨 Error fetching ticket for conversation grouping',
            ticketId: recentMessage.platformTicketId,
            channelId: channel.channelId,
            installationId: installation.id,
          });
        }
      }

      return { shouldGroup: false };
    } catch (error) {
      this.logger.error(
        `Error determining if message should be grouped: ${error instanceof Error ? error.message : 'Unknown error'}`,
        error instanceof Error ? error.stack : undefined,
      );

      // Report to Sentry
      this.sentryService.captureException(error, {
        tag: SLACK_SENTRY_TAG,
        name: '🚨 Error determining if message should be grouped',
        channelId: channel.channelId,
        userId: userInQuestion.slackId,
        installationId: installation.id,
      });

      return { shouldGroup: false };
    }
  }

  /**
   * Creates a comment on an existing ticket instead of creating a new ticket
   */
  async createCommentOnExistingTicket(
    installation: Installations,
    ticketId: string,
    userInQuestion: Person,
    event: KnownEventFromType<'message'>,
  ) {
    try {
      if (!('text' in event)) {
        throw new Error('Message event does not contain text');
      }

      if (!('blocks' in event)) {
        throw new Error('Blocks are required for comments');
      }

      let files: FileShareMessageEvent['files'] = [];
      if ('files' in event) {
        files = event.files;
      }

      // Initialize the converter with the blocks and installation
      this.baseSlackBlocksToHtml.initialize(event.blocks, installation);

      // Convert blocks to HTML content
      const htmlContent = await this.baseSlackBlocksToHtml.convert();

      const commentPayload: CreateNewComment = {
        ticketId,
        content: htmlContent,
        htmlContent,
        files,
        commentVisibility: 'public',
        impersonatedUserAvatar: userInQuestion.getUserAvatar(),
        impersonatedUserEmail: userInQuestion.slackProfileEmail,
        impersonatedUserName:
          userInQuestion.displayName || userInQuestion.realName,
        channelId: event.channel,
        metadata: {
          ignoreSelf: true,
          ts: event.ts,
          threadTs: event.thread_ts ?? event.ts,
        },
      };

      if (userInQuestion instanceof CustomerContacts) {
        commentPayload.customerEmail = userInQuestion.slackProfileEmail;
      }

      // Create comment on the existing ticket
      const comment = await this.thenaPlatformApiProvider.createNewComment(
        installation,
        commentPayload,
      );

      // Create a new slack message record for this comment
      const slackMessage = await this.slackMessagesRepository.save({
        installation: { id: installation.id },
        organization: { id: installation.organization.id },
        channel: { channelId: event.channel },
        slackTs: event.ts,
        slackUserId: event.user,
        platformTicketId: ticketId,
        platformCommentId: comment.data.id,
        messageText: event.text,
      });

      return { comment, slackMessage };
    } catch (error) {
      this.logger.error(
        `Error creating comment on existing ticket: ${error instanceof Error ? error.message : 'Unknown error'}`,
        error instanceof Error ? error.stack : undefined,
      );

      // Report to Sentry
      this.sentryService.captureException(error, {
        tag: SLACK_SENTRY_TAG,
        name: '🚨 Error creating comment on existing ticket',
        ticketId: ticketId,
        channelId: event.channel,
        userId: userInQuestion.slackId,
        installationId: installation.id,
      });

      throw error;
    }
  }
}
