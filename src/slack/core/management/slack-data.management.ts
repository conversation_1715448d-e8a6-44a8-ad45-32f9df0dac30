import { Inject, Injectable } from '@nestjs/common';
import { InjectRepository } from '@nestjs/typeorm';
import { Repository } from 'typeorm';
import {
  CustomerContacts,
  Installations,
  Users,
} from '../../../database/entities';
import { ChannelType } from '../../../database/entities/channels/channels.entity';
import { Channels } from '../../../database/entities/channels/channels.entity';
import { ChannelsRepository } from '../../../database/entities/channels/repositories/channels.repository';
import { Person } from '../../../database/interfaces/person.interface';
import { CUSTOM_LOGGER_TOKEN, ILogger } from '../../../utils';
import { WebApiClient } from '../../../utils/web-api.client';
import { SlackWebAPIService } from '../../providers/slack-apis/slack-apis.service';

/**
 * @interface CommonUpsertOptions
 * @description
 * The common options for upserting a Slack user or channel
 */
interface CommonUpsertOptions {
  /**
   * The Slack token
   */
  token: string;

  /**
   * The Slack installation ID
   */
  installationId: string;

  /**
   * The Slack organization ID
   */
  organizationId: string;
}

/**
 * @interface UpsertSlackUserOptions
 * @description
 * The options for upserting a Slack user
 */
interface UpsertSlackUserOptions extends CommonUpsertOptions {
  /**
   * The Slack user ID
   */
  userId: string;

  /**
   * Whether the user is a customer
   */
  isCustomer: boolean;
}

/**
 * @interface GetUserOptions
 * @description
 * Fetches a Slack user from the database, given if this user does not exist, it will be upserted
 * the function can panic if the user's information cannot be retrieved from Slack
 *
 * @throws If the user's information cannot be retrieved from Slack
 */
interface GetUserOptions extends CommonUpsertOptions {
  /**
   * The Slack user ID
   */
  userId: string;

  /**
   * Whether to upsert the user if it does not exist
   * @default true
   */
  upsert?: boolean;
}

/**
 * @interface GetChannelOptions
 * @description
 * Fetches a Slack channel from the database, given if this channel does not exist, it will be upserted
 * the function can panic if the channel's information cannot be retrieved from Slack
 *
 * @throws If the channel's information cannot be retrieved from Slack
 */
interface GetChannelOptions extends CommonUpsertOptions {
  /**
   * The Slack channel ID
   */
  channelId: string;

  /**
   * Whether the bot is active in the channel, defaults to false
   */
  isBotActive?: boolean;

  /**
   * Whether the bot is joined in the channel, defaults to false
   */
  isBotJoined?: boolean;

  /**
   * The type of the channel
   */
  channelType?: ChannelType;
}

/**
 * @interface UpsertSlackChannelOptions
 * @description
 * The options for upserting a Slack channel
 */
interface UpsertSlackChannelOptions extends CommonUpsertOptions {
  /**
   * The Slack channel ID
   */
  channelId: string;

  /**
   * Whether the bot is active in the channel, defaults to false
   */
  isBotActive?: boolean;

  /**
   * Whether the bot is joined in the channel, defaults to false
   */
  isBotJoined?: boolean;

  /**
   * The type of the channel
   */
  channelType?: ChannelType;
}

/**
 * @class SlackAppManagementService
 * @description
 * This service is responsible for managing the Slack app's data. The learning comes from
 * Thena's first slack app's implementation. The following were the problems that kept coming up:
 * - A Slack user might NOT be available in the database
 * - A Slack channel might NOT be available in the database
 * - There was NO tuple that guaranteed uniqueness [WHICH FITS THIS DEFINITION PERFECTLY #REF1] for either a `User` or a `Channel`
 *
 * @references
 * #REF1
 * The definition of "uniqueness" for a 'User' or 'Channel' can be defined as:
 * - A unique way to identify a `Slack User` and his/her `Installation` WHICH MUST correspond to a `User` with unique `Organization` in Platform
 * - A unique way to identify a `Slack Channel` and its `Installation` WHICH MUST correspond to a `Channel` with unique `Organization` in Platform
 */
@Injectable()
export class SlackAppManagementService {
  constructor(
    @Inject(CUSTOM_LOGGER_TOKEN) private readonly logger: ILogger,

    @InjectRepository(Users)
    private readonly usersRepository: Repository<Users>,

    @InjectRepository(CustomerContacts)
    private readonly customerContactsRepository: Repository<CustomerContacts>,

    private readonly channelsRepository: ChannelsRepository,
    private readonly slackWebAPIService: SlackWebAPIService,
  ) {}

  /**
   * @description
   * Upserts a person into the database based on the identification, the identification
   * process checks if the Person bends towards the definition of a Customer or a User
   * and based on that it upserts the person into the database
   *
   * Prefer placing a TypeGuard on the CustomerContacts or Users class to assert the returned
   * entity type.
   *
   * @param userId The user's ID
   * @param installation The installation
   * @param channel The channel
   * @returns The upserted person's object
   */
  async upsertPersonWithIdentification(
    userId: string,
    installation: Installations,
    channel: Channels,
  ) {
    // We assume that this user DOES NOT exist in the database
    const userInfoResponse = await this.slackWebAPIService.getUserInfo(
      installation.botToken,
      { user: userId },
    );

    // If the user info response is not ok, log an error
    if (!userInfoResponse.ok) {
      this.logger.error(
        `Failed to get user info for user ${userId} in installation ${installation.id}, ${userInfoResponse.error}`,
      );

      throw new Error(userInfoResponse.error);
    }

    // Get the user's data
    const { user } = userInfoResponse;

    // Upsert the user into the database
    const isCustomer = this.isCustomer(
      installation,
      {
        slackId: user.id,
        slackProfileEmail: user.profile?.email,
        userDump: user,
        realName: user.profile?.real_name,
        displayName: user.profile?.display_name,
        isRestricted: user.is_restricted,
        isUltraRestricted: user.is_ultra_restricted,
        /**
         * We need this method here inside the {@link isCustomer} method
         * because we need to access the user's email check
         */
        userHasEmail: () => {
          return (
            typeof user.profile?.email === 'string' &&
            user.profile?.email.includes('@')
          );
        },

        getUserAvatar: () => {
          return (
            user.profile?.image_original ??
            user.profile?.image_512 ??
            user.profile?.image_192 ??
            user.profile?.image_72 ??
            user.profile?.image_48 ??
            user.profile?.image_32 ??
            user.profile?.image_24
          );
        },
      },
      channel,
    );

    // Upsert the user into the database
    const upsertedUser = await this.upsertSlackUser({
      installationId: installation.id,
      organizationId: installation.organization.id,
      token: installation.botToken,
      userId,
      isCustomer,
    });

    // Return the upserted user
    return upsertedUser;
  }

  /**
   * Performs check to detect if a user is a customer or not. The check is performed
   * in a Slack channel to match if this user is a customer or not
   *
   * @param installation The Slack Workspace for which to check against
   * @param user The user who is possibly a customer
   * @param channel The channel where we are performing this check
   * @returns Whether this person is a customer or not
   */
  isCustomer(installation: Installations, user: Person, channel: Channels) {
    let isFromExternalWorkspace = false;
    let isGuestUser = false;
    let isOurDomainUser = true;

    // If the user dump is not available, then we cannot proceed further
    if (!user.userDump || !user.userDump?.team_id) {
      this.logger.error(`User ${user.slackId} does not have a user dump`);

      return false;
    }

    // 1. Check the if the user's Slack Team ID doesn't match the installation's team ID
    if (user.userDump?.team_id !== installation.teamId) {
      isFromExternalWorkspace = true;
    }

    // 2. Should the installation/channel treat guest users as customers?
    isGuestUser = user.isRestricted || user.isUltraRestricted;

    // 3. Get organization's domains and figure out if the user has one of them
    const userHasMail = user.userHasEmail();
    if (userHasMail) {
      const userEmail = user.slackProfileEmail;
      const domains = installation.domains?.split(',');

      // If the domains are not empty, check if the user's email is in one of them
      if (Array.isArray(domains) && domains.length > 0) {
        const userEmailDomain = userEmail?.split('@')[1];
        isOurDomainUser = domains.includes(userEmailDomain);
      }
    }

    // If the user is guest and the channels marks guests as customers
    const isCustomerForChannel = isGuestUser && channel.guestAreCustomers;
    if (isCustomerForChannel) {
      this.logger.debug(
        `User ${user.slackId} is a guest user and the channel ${channel.channelId} marks guests as customers, therefore they are a customer`,
      );

      return isCustomerForChannel;
    }

    // If the user is from an external workspace, then they are a customer
    if (isFromExternalWorkspace) {
      this.logger.debug(
        `User ${user.slackId} is from an external workspace, therefore they are a customer`,
      );

      return true;
    }

    // If the user is not from our domain, then they are a customer
    this.logger.debug(
      `User ${user.slackId} is our domain user? ${isOurDomainUser}`,
    );

    return !isOurDomainUser;
  }

  /**
   * @description
   * Upserts a Slack user into the database
   * @param options The options for the upsert
   * @returns The Slack user's object
   *
   * @throws If the user's information cannot be retrieved
   */
  async upsertSlackUser(
    options: Omit<UpsertSlackUserOptions, 'isCustomer'> & { isCustomer: true },
  ): Promise<CustomerContacts>;
  async upsertSlackUser(
    options: Omit<UpsertSlackUserOptions, 'isCustomer'> & { isCustomer: false },
  ): Promise<Users>;
  async upsertSlackUser(
    options: Omit<UpsertSlackUserOptions, 'isCustomer'> & {
      isCustomer: boolean;
    },
  ): Promise<CustomerContacts | Users>;
  async upsertSlackUser(
    options: UpsertSlackUserOptions,
  ): Promise<CustomerContacts | Users> {
    const { userId, token, installationId, organizationId, isCustomer } =
      options;

    // Create a new Slack WebAPI Client
    const client = new WebApiClient(token);

    // Get the user's slack object
    const response = await client.users.info({ user: userId });
    if (!response.ok) {
      this.logger.error(
        `Failed to get user info for user ${userId} in installation ${installationId}, ${response.error}`,
      );

      throw new Error(response.error);
    }

    // Get the user's data
    const { user } = response;

    // If the user is not found, throw an error
    if (!user || !user.id) {
      throw new Error('User not found');
    }

    let upsertedUser: CustomerContacts | Users | null = null;
    if (isCustomer) {
      upsertedUser = await this.upsertCustomerEntity(
        user as Record<string, any> & { id: string },
        installationId,
        organizationId,
      );
    } else {
      upsertedUser = await this.upsertUserEntity(
        user as Record<string, any> & { id: string },
        installationId,
        organizationId,
      );
    }

    return upsertedUser;
  }

  /**
   * @description
   * Upserts a Slack channel into the database
   * @param options The options for the upsert
   * @returns The Slack channel's object
   *
   * @throws If the channel's information cannot be retrieved
   */
  async upsertSlackChannel(options: UpsertSlackChannelOptions) {
    const { channelId, token, installationId, organizationId } = options;

    // Create a new Slack WebAPI Client
    const client = new WebApiClient(token);

    // Get the channel's slack object
    const response = await client.conversations.info({ channel: channelId });
    if (!response.ok) {
      this.logger.error(
        `Failed to get channel info for channel ${channelId} in installation ${installationId}, ${response.error}`,
      );

      throw new Error(response.error);
    }

    // Get the channel's data
    const { channel } = response;

    // If the channel is not found, throw an error
    if (!channel) {
      throw new Error('Channel not found');
    }

    // Extract shared team IDs from the channel if it's externally shared
    const sharedTeamIds =
      channel.is_ext_shared && channel.shared_team_ids
        ? channel.shared_team_ids
        : [];

    if (channel.is_ext_shared) {
      this.logger.log(
        `Upserting externally shared channel ${channel.id} (${channel.name}) with shared team IDs: ${sharedTeamIds.length ? sharedTeamIds.join(', ') : 'none'}`,
      );
    }

    // Upsert the channel into the database
    await this.channelsRepository.upsert(
      {
        name: channel.name || channel.name_normalized,
        channelDump: channel as Record<string, any>,
        channelId: channel.id,
        slackCreatedAt: channel.created.toString(),
        isBotActive: options.isBotActive || false,
        isBotJoined: options.isBotJoined || false,
        channelType: options.channelType || ChannelType.NOT_SETUP,
        isArchived: channel.is_archived,
        isPrivate: channel.is_private,
        isShared: channel.is_ext_shared,
        sharedTeamIds: sharedTeamIds,
        installation: { id: installationId },
        organization: { id: organizationId },
      },
      { conflictPaths: ['channelId', 'installation'] },
    );

    // Return the upserted channel
    const upsertedChannel = await this.channelsRepository.findByCondition({
      where: { channelId: channelId, installation: { id: installationId } },
      relations: { installation: true },
    });

    return upsertedChannel;
  }

  /**
   * @description
   * Fetches a Slack user from the database, given if this user does not exist, it will be upserted.
   * This function is guaranteed to return a user otherwise panic
   * @param options The options for the fetch
   * @returns The Slack user's object
   *
   * @throws If the user's information cannot be retrieved from Slack
   */
  async getAndUpsertUser(options: GetUserOptions) {
    const {
      userId,
      token,
      installationId,
      organizationId,
      upsert = true,
    } = options;

    // Get the user from the database
    let user = await this.usersRepository.findOne({
      where: { slackId: userId, installation: { id: installationId } },
    });

    // If the user is not found, upsert the user
    if (!user && upsert) {
      user = await this.upsertSlackUser({
        userId,
        token,
        installationId,
        organizationId,
        isCustomer: false,
      });
    }

    return user;
  }

  /**
   * @description
   * Fetches a Slack channel from the database, given if this channel does not exist, it will be upserted.
   * This function is guaranteed to return a channel otherwise panic
   * @param options The options for the fetch
   * @returns The Slack channel's object
   *
   * @throws If the channel's information cannot be retrieved from Slack
   */
  async getAndUpsertChannel(options: GetChannelOptions) {
    const { channelId, token, installationId, organizationId } = options;

    // Get the channel from the database
    let channel = await this.channelsRepository.findByCondition({
      where: { channelId: channelId, installation: { id: installationId } },
    });

    // If the channel is not found, upsert the channel
    if (!channel) {
      channel = await this.upsertSlackChannel({
        channelId,
        token,
        installationId,
        organizationId,
      });
    }

    return channel;
  }

  /**
   * @description
   * Upserts a Slack user into the database
   * @param user The user's data
   * @param installationId The installation ID
   * @param organizationId The organization ID
   * @returns The upserted user's object
   */
  private async upsertUserEntity(
    user: Record<string, any> & { id: string },
    installationId: string,
    organizationId: string,
  ) {
    // Get the user's all possible images
    const images = {
      image_original: user.profile?.image_original,
      image_512: user.profile?.image_512,
      image_192: user.profile?.image_192,
      image_72: user.profile?.image_72,
      image_48: user.profile?.image_48,
      image_32: user.profile?.image_32,
      image_24: user.profile?.image_24,
    };

    // Upsert the user into the database
    await this.usersRepository.upsert(
      {
        userDump: user as Record<string, any>,
        slackId: user.id,
        slackDeleted: user.deleted,
        name: user.name,
        realName: user.real_name,
        displayName: user.profile?.display_name,
        installation: { id: installationId },
        organization: { id: organizationId },
        tz: user.tz,
        tzLabel: user.tz_label,
        isAdmin: user.is_admin,
        isOwner: user.is_owner,
        isRestricted: user.is_restricted,
        isUltraRestricted: user.is_ultra_restricted,
        isBot: user.is_bot,
        userTitle: user.profile?.title,
        slackProfileRealName: user.profile?.real_name,
        slackProfileDisplayName: user.profile?.display_name,
        slackProfilePhone: user.profile?.phone,
        slackStatusText: user.profile?.status_text,
        slackStatusEmoji: user.profile?.status_emoji,
        slackProfileEmail: user.profile?.email,
        images,
      },
      { conflictPaths: ['slackId', 'installation'] },
    );

    // Return the upserted user
    const upsertedUser = await this.usersRepository.findOne({
      where: { slackId: user.id, installation: { id: installationId } },
    });

    return upsertedUser;
  }

  /**
   * @description
   * Upserts a Slack customer into the database
   * @param user The user's data
   * @param installationId The installation ID
   * @param organizationId The organization ID
   * @returns The upserted customer's object
   */
  private async upsertCustomerEntity(
    user: Record<string, any> & { id: string },
    installationId: string,
    organizationId: string,
  ) {
    // Get the user's all possible images
    const images = {
      image_original: user.profile?.image_original,
      image_512: user.profile?.image_512,
      image_192: user.profile?.image_192,
      image_72: user.profile?.image_72,
      image_48: user.profile?.image_48,
      image_32: user.profile?.image_32,
      image_24: user.profile?.image_24,
    };

    // Upsert the customer contact into the database
    await this.customerContactsRepository.upsert(
      {
        userDump: user as Record<string, any>,
        slackId: user.id,
        slackDeleted: user.deleted,
        name: user.name,
        realName: user.real_name,
        displayName: user.profile?.display_name,
        installation: { id: installationId },
        organization: { id: organizationId },
        tz: user.tz,
        tzLabel: user.tz_label,
        isAdmin: user.is_admin,
        isOwner: user.is_owner,
        isRestricted: user.is_restricted,
        isUltraRestricted: user.is_ultra_restricted,
        isBot: user.is_bot,
        userTitle: user.profile?.title,
        slackProfileRealName: user.profile?.real_name,
        slackProfileDisplayName: user.profile?.display_name,
        slackProfilePhone: user.profile?.phone,
        slackStatusText: user.profile?.status_text,
        slackStatusEmoji: user.profile?.status_emoji,
        slackProfileEmail: user.profile?.email,
        images,
      },
      { conflictPaths: ['slackId', 'installation'] },
    );

    // Return the upserted user
    const upsertedCustomer = await this.customerContactsRepository.findOne({
      where: { slackId: user.id, installation: { id: installationId } },
    });

    return upsertedCustomer;
  }
}
