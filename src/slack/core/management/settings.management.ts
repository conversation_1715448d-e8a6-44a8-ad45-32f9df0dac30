import { Injectable } from '@nestjs/common';
import {
  Installations,
  Organizations,
  PlatformTeams,
} from '../../../database/entities';
import { SlackAppSettings } from '../../../database/entities/settings/interfaces/slack-app-settings.interface';
import { SettingsRepository } from '../../../database/entities/settings/repositories/settings.repository';

export type SettingsKey = keyof SlackAppSettings;

interface CommonSettingsOpts {
  /**
   * The installation to use for the settings.
   */
  workspace: Installations;

  /**
   * The platform team to use for the settings.
   */
  platformTeam: PlatformTeams;

  /**
   * The organization to use for the settings.
   */
  organization?: Organizations;

  /**
   * Recursively lookup the setting in the parent installations, if no value is found.
   * The recursive lookup will check from Team level to the Installation and then organization.
   * @default true
   */
  recursivelyLookup?: boolean;
}

@Injectable()
export class SettingsCore {
  constructor(private readonly settingsRepository: SettingsRepository) {}

  /**
   * Default values for settings
   */
  private readonly defaultSettings: Partial<Record<SettingsKey, any>> = {
    automatic_tickets: false,
    thena_bot_tagging_enabled: true,
    slash_commands: true,
    require_form: false,
    conversation_window: 24, // 24 hours
    enable_ticket_creation_via_reaction: false,
    enable_emoji_actions: false,
    enable_action_to_emoji: true,
    enable_emoji_to_action: true,
    // Default to OpenAI GPT-o3-mini
    ai_model: 'o3-mini-2025-01-31',
    ai_provider: 'openai',

    emoji_to_create_ticket: 'ticket',
    ai_enable_extended_thinking: false,
    ai_temperature: 0.5,
    ai_max_tokens: 1000,
    ticket_command: true,
    selected_forms: [],
  };

  /**
   * Coerce value to its correct type based on setting key
   */
  private coerceValue(
    key: string,
    value: any,
  ): boolean | string | number | string[] {
    // Define type mapping for settings
    const settingTypes: Record<
      string,
      'boolean' | 'string' | 'number' | 'array'
    > = {
      automatic_tickets: 'boolean',
      thena_bot_tagging_enabled: 'boolean',
      slash_commands: 'boolean',
      require_form: 'boolean',
      conversation_window: 'number',
      enable_ticket_creation_via_reaction: 'boolean',
      enable_emoji_actions: 'boolean',
      enable_action_to_emoji: 'boolean',
      enable_emoji_to_action: 'boolean',
      ai_provider: 'string',
      ai_model: 'string',
      ticket_command: 'boolean',
      selected_forms: 'array',
    };

    const type = settingTypes[key];
    if (!type) {
      return value;
    }

    // If the value is null, return null, this is to avoid type coercion issues
    if (value == null) {
      return null;
    }

    switch (type) {
      case 'boolean':
        // Handle various boolean string representations
        if (typeof value === 'string') {
          return value.toLowerCase() === 'true' || value === '1';
        }
        return Boolean(value);

      case 'number':
        return Number(value);

      case 'string':
        return String(value);

      case 'array':
        // Handle array values
        if (Array.isArray(value)) {
          return value;
        }
        // If it's a string, try to parse it as JSON
        if (typeof value === 'string') {
          try {
            const parsed = JSON.parse(value);
            return Array.isArray(parsed) ? parsed : [];
          } catch (_) {
            return [];
          }
        }
        return [];

      default:
        return value;
    }
  }

  /**
   * Get setting value with proper type coercion
   */
  private async getValueRaw<T extends SettingsKey>(
    k: T,
    opts?: CommonSettingsOpts,
  ): Promise<SlackAppSettings[T] | null> {
    const {
      recursivelyLookup = false,
      workspace,
      platformTeam,
      organization,
    } = opts || {};

    // Non recursive lookup
    if (!recursivelyLookup) {
      const result = await this.settingsRepository
        .createQueryBuilder('settings')
        .select(`settings.settings->>'${k}' as value`)
        .where(
          'settings.installation_id = :installationId AND settings.platform_team_id = :teamId',
          {
            installationId: workspace.id,
            teamId: platformTeam.id,
          },
        )
        .getRawOne<{ value: SlackAppSettings[T] }>();

      // Case when the setting is not found in the database
      if (result?.value == null) {
        // Check if we have a default value for this setting
        if (k in this.defaultSettings) {
          // Insert the default value
          await this.upsertSetting(
            k,
            this.defaultSettings[k] as SlackAppSettings[T],
            {
              workspace,
              platformTeam,
              organization,
            },
          );

          // Return the default value
          return this.defaultSettings[k] as SlackAppSettings[T];
        }
      }

      return result?.value;
    }

    // Recursive lookup, fallback to parents
    const result = await this.settingsRepository
      .createQueryBuilder('settings')
      .select(`
        COALESCE(
          (
            SELECT s1.settings->>'${k}'
            FROM settings s1
            WHERE s1.platform_team_id = :teamId
            AND s1.settings->>'${k}' IS NOT NULL
            LIMIT 1
          ),
          (
            SELECT s2.settings->>'${k}'
            FROM settings s2
            WHERE s2.installation_id = :workspaceId
            AND s2.platform_team_id IS NULL
            AND s2.settings->>'${k}' IS NOT NULL
            LIMIT 1
          ),
          (
            SELECT s3.settings->>'${k}'
            FROM settings s3
            WHERE s3.organization_id = :orgId
            AND s3.platform_team_id IS NULL
            AND s3.installation_id IS NULL
            AND s3.settings->>'${k}' IS NOT NULL
            LIMIT 1
          )
        ) as value
      `)
      .setParameters({
        orgId: organization?.id,
        teamId: platformTeam?.id,
        workspaceId: workspace.id,
      })
      .getRawOne<{ value: SlackAppSettings[T] }>();

    // If no value found in any level, use default
    if (result?.value == null && k in this.defaultSettings) {
      // Insert the default value at the team level
      await this.upsertSetting(
        k,
        this.defaultSettings[k] as SlackAppSettings[T],
        {
          workspace,
          platformTeam,
          organization,
        },
      );

      // Return the default value
      return this.defaultSettings[k] as SlackAppSettings[T];
    }

    return result?.value;
  }

  /**
   * Upsert a setting value
   */
  private async upsertSetting<T extends SettingsKey>(
    key: T,
    value: SlackAppSettings[T],
    opts: CommonSettingsOpts,
  ): Promise<void> {
    const { workspace, platformTeam, organization } = opts;

    // Find existing setting
    const existingSetting = await this.settingsRepository.findByCondition({
      where: {
        installation: { id: workspace.id },
        platformTeam: platformTeam ? { id: platformTeam.id } : null,
      },
    });

    if (existingSetting) {
      // Update existing setting
      await this.settingsRepository.update(
        { id: existingSetting.id },
        {
          settings: {
            ...existingSetting.settings,
            [key]: value,
          },
        },
      );
    } else {
      // Create new setting
      await this.settingsRepository.save({
        installation: { id: workspace.id },
        platformTeam: platformTeam ? { id: platformTeam.id } : null,
        organization: organization ? { id: organization.id } : null,
        settings: {
          [key]: value,
        },
      });
    }
  }

  /**
   * Get setting value with proper type coercion
   */
  async getValue<T extends SettingsKey>(
    key: T,
    options: CommonSettingsOpts,
  ): Promise<SlackAppSettings[T] | null> {
    const value = await this.getValueRaw(key, options);
    return this.coerceValue(key, value) as SlackAppSettings[T];
  }

  /**
   * Get multiple setting values with proper type coercion in a single database call
   * @param keys Array of setting keys to retrieve
   * @param options Common settings options
   * @returns Object with requested settings as key-value pairs
   */
  async getMultipleValues<T extends SettingsKey[]>(
    keys: T,
    options: CommonSettingsOpts,
  ): Promise<{ [K in T[number]]: SlackAppSettings[K] | null }> {
    const {
      recursivelyLookup = false,
      workspace,
      platformTeam,
      organization,
    } = options;

    const result: Record<string, any> = {};

    if (keys.length === 0) {
      return result as { [K in T[number]]: SlackAppSettings[K] | null };
    }

    // For non-recursive lookup, use a simpler query
    if (!recursivelyLookup) {
      const queryResult = await this.settingsRepository
        .createQueryBuilder('settings')
        .select(
          keys.map((k) => `settings.settings->>'${k}' as "${k}"`).join(', '),
        )
        .where(
          'settings.installation_id = :installationId AND settings.platform_team_id = :teamId',
          {
            installationId: workspace.id,
            teamId: platformTeam.id,
          },
        )
        .getRawOne();

      // Process and coerce all values
      for (const key of keys) {
        // If value is null and we have a default, use default
        if (queryResult?.[key] == null && key in this.defaultSettings) {
          // Insert the default value asynchronously (don't wait)
          this.upsertSetting(
            key,
            this.defaultSettings[key] as SlackAppSettings[typeof key],
            { workspace, platformTeam, organization },
          ).catch((err) =>
            console.error(`Error upserting default setting for ${key}:`, err),
          );

          result[key] = this.defaultSettings[key];
        } else {
          result[key] = this.coerceValue(key, queryResult?.[key]);
        }
      }

      return result as { [K in T[number]]: SlackAppSettings[K] | null };
    }

    // For recursive lookup, build a more complex query with COALESCE for each key
    const selectors = keys
      .map(
        (k) => `
      COALESCE(
        (
          SELECT s1.settings->>'${k}'
          FROM settings s1
          WHERE s1.platform_team_id = :teamId
          AND s1.settings->>'${k}' IS NOT NULL
          LIMIT 1
        ),
        (
          SELECT s2.settings->>'${k}'
          FROM settings s2
          WHERE s2.installation_id = :workspaceId
          AND s2.platform_team_id IS NULL
          AND s2.settings->>'${k}' IS NOT NULL
          LIMIT 1
        ),
        (
          SELECT s3.settings->>'${k}'
          FROM settings s3
          WHERE s3.organization_id = :orgId
          AND s3.platform_team_id IS NULL
          AND s3.installation_id IS NULL
          AND s3.settings->>'${k}' IS NOT NULL
          LIMIT 1
        )
      ) as "${k}"
    `,
      )
      .join(', ');

    const queryResult = await this.settingsRepository
      .createQueryBuilder('settings')
      .select(selectors)
      .setParameters({
        orgId: organization?.id,
        teamId: platformTeam?.id,
        workspaceId: workspace.id,
      })
      .getRawOne();

    // Process and coerce all values
    for (const key of keys) {
      // If value is null and we have a default, use default
      if (queryResult?.[key] == null && key in this.defaultSettings) {
        // Insert the default value asynchronously (don't wait)
        this.upsertSetting(
          key,
          this.defaultSettings[key] as SlackAppSettings[typeof key],
          { workspace, platformTeam, organization },
        ).catch((err) =>
          console.error(`Error upserting default setting for ${key}:`, err),
        );

        result[key] = this.defaultSettings[key];
      } else {
        result[key] = this.coerceValue(key, queryResult?.[key]);
      }
    }

    return result as { [K in T[number]]: SlackAppSettings[K] | null };
  }
}
