import { Injectable } from '@nestjs/common';
import { Channels, Installations } from '../../../database/entities';
import { Person } from '../../../database/interfaces/person.interface';
import { Ticket } from '../../../platform/interfaces';

interface ConstructSlackMessage {
  ticket: Ticket;
  channel: Channels;
  installation: Installations;
  customer: Person;
  slackDetails: {
    eventTs: string;
    ts: string;
    threadTs: string;
    messagePermalink: string;
    user: string;
  };
}

@Injectable()
export class ForMessageFactory {
  constructSlackMessage(data: ConstructSlackMessage) {
    const { ticket, channel, installation, customer, slackDetails } = data;
    const { eventTs, ts, threadTs, messagePermalink, user } = slackDetails;

    return {
      platformTicketId: ticket.id,
      slackEventCreatedAt: new Date(+eventTs).toISOString(),
      slackMessageTs: ts,
      slackMessageThreadTs: threadTs,
      slackPermalink: messagePermalink,
      slackUserId: user,
      metadata: {
        customer: {
          email: customer.slackProfileEmail,
          name: customer.displayName ?? customer.realName,
        },
        ticket_details: {
          status: ticket.status,
          statusId: ticket.statusId,
          priority: ticket.priority,
          priorityId: ticket.priorityId,
        },
      },

      // Link FKs
      channel: { id: channel.id },
      installation: { id: installation.id },
      organization: { id: installation.organization.id },
    };
  }
}
