import { Inject, Injectable } from '@nestjs/common';
import { InjectRepository } from '@nestjs/typeorm';
import { Repository } from 'typeorm';
import {
  Installations,
  Organizations,
  Users,
} from '../../../database/entities';
import { ChannelType } from '../../../database/entities/channels/channels.entity';
import { ChannelsRepository } from '../../../database/entities/channels/repositories/channels.repository';
import { InstallationRepository } from '../../../database/entities/installations/repositories';
import { CUSTOM_LOGGER_TOKEN, ILogger } from '../../../utils/logger';
import { ChannelSetupBlocks } from '../../blocks/components';
import { SlackEventMap } from '../../event-handlers';
import { SlackExternalUsersSyncJob } from '../../processors/jobs';
import { SlackWebAPIService } from '../../providers/slack-apis/slack-apis.service';
import { SlackAppManagementService } from '../management';

interface HandleThenaBotJoinedOptions {
  inviter: string | undefined;
  channel: string;
  slackUser: Users;
  installation: Installations;
  organization: Organizations;
}

// Interface for the channel update data
interface ChannelUpdateData {
  isBotActive: boolean;
  isBotJoined: boolean;
  channelType: ChannelType;
  sharedTeamIds?: string[];
}

@Injectable()
export class BotChannelJoinedHandler {
  constructor(
    @Inject(CUSTOM_LOGGER_TOKEN)
    private readonly logger: ILogger,

    @InjectRepository(Users)
    private readonly userRepository: Repository<Users>,

    // Database Repositories
    private readonly channelsRepository: ChannelsRepository,
    private readonly installationRepository: InstallationRepository,

    // Slack Blocks
    private readonly channelSetupBlocks: ChannelSetupBlocks,

    // External Services
    private readonly slackWebAPIService: SlackWebAPIService,
    private readonly slackAppManagementService: SlackAppManagementService,

    // Sync Jobs
    private readonly slackExternalUsersSyncJob: SlackExternalUsersSyncJob,
  ) {}

  // Define a constant for the modal callback ID
  static readonly CHANNEL_SETUP_MODAL_CALLBACK_ID = 'channel_setup_modal';

  // Define constants for Configure channel button and common blocks
  private getConfigureChannelButton(channelId: string, channelName: string) {
    return {
      type: 'button',
      text: {
        type: 'plain_text',
        text: 'Configure channel',
        emoji: true,
      },
      style: 'primary',
      action_id: 'configure_channel_action',
      value: JSON.stringify({
        channelId,
        channelName,
      }),
    };
  }

  private getConfigureChannelActionBlock(
    channelId: string,
    channelName: string,
  ) {
    return {
      type: 'actions',
      elements: [this.getConfigureChannelButton(channelId, channelName)],
    };
  }

  private getEphemeralMessageBlocks(channelId: string, channelName: string) {
    return [
      {
        type: 'section',
        text: {
          type: 'mrkdwn',
          text: 'Configure the channel to get started.',
        },
      },
      this.getConfigureChannelActionBlock(channelId, channelName),
    ];
  }

  private getDirectMessageBlocks(channelId: string, channelName: string) {
    return [
      {
        type: 'section',
        text: {
          type: 'mrkdwn',
          text: 'You can configure the channel using the button below:',
        },
      },
      this.getConfigureChannelActionBlock(channelId, channelName),
    ];
  }

  async handleEvent(e: SlackEventMap['member_joined_channel']) {
    // Well the event :)
    const { event } = e;

    // Select out the relevant fields, note all the fields relate Slack's construct
    // don't confuse teams and users with platform users and teams
    const { channel, user, team, event_ts } = event;

    this.logger.log(
      `Received 'member_joined_channel' event for slack team ${team} for user ${user} in channel ${channel} at ${event_ts}`,
    );
    try {
      // Per https://api.slack.com/events/message/channel_join the `inviter` field is only present if
      // the user was invited to the channel. Note that event above is classified as either a
      // `GenericMessageEvent` or a `ChannelJoinMessageEvent`. We'll pull out the `inviter` field
      // in-case it is present.
      let inviter: string | undefined;
      if ('inviter' in event) {
        inviter = event.inviter;

        this.logger.log(
          `The user ${user} was invited to the channel by ${inviter} at ${event_ts}`,
        );
      }

      // Get the user and installation from the database
      const { slackUser, installation, organization } =
        await this.getUserAndInstallation(user, team);

      this.logger.log(
        `Found user ${slackUser.id} and installation ${installation.id} for team ${team} attached to organization ${organization.id}`,
      );

      // If this flag is true, then the Bot just got added to the channel
      const isThenaBot = installation.botSlackUserId === user;
      if (isThenaBot) {
        // Handle the bot joining the channel
        await this.handleThenaBotJoined({
          inviter,
          channel,
          slackUser,
          installation,
          organization,
        });
      }

      return isThenaBot;
    } catch (error) {
      if (error instanceof Error) {
        this.logger.error(
          `Error handling 'member_joined_channel' event for slack team ${team} for user ${user} in channel ${channel} at ${event_ts}`,
          error.stack,
        );
      } else {
        console.error(error);
      }
    }

    return false;
  }

  /**
   * @private
   *
   * Handle the bot joining a channel
   * @param options The options for the bot joining a channel
   */
  private async handleThenaBotJoined(options: HandleThenaBotJoinedOptions) {
    const { channel, installation, organization, inviter } = options;

    this.logger.log(
      `Handling thena bot joined channel ${channel} for installation ${installation.id} attached to organization ${organization.id}`,
    );

    // Get the channel from the database
    let slackChannel = await this.channelsRepository.findByCondition({
      where: { channelId: channel, installation: { id: installation.id } },
    });

    // If the channel is not found, create it and sync it
    if (!slackChannel) {
      slackChannel = await this.slackAppManagementService.upsertSlackChannel({
        channelId: channel,
        token: installation.botToken,
        installationId: installation.id,
        organizationId: organization.id,
      });

      try {
        // Execute the external users sync job
        await this.slackExternalUsersSyncJob.execute(installation, [
          slackChannel,
        ]);
      } catch (syncError) {
        if (syncError instanceof Error) {
          this.logger.error(
            `Failed to sync external users for channel ${channel}, ${syncError.message}`,
          );
        } else {
          console.error('Failed to sync external users for channel', syncError);
        }
      }
    } else {
      // Channel exists but we should refresh the data from Slack to ensure shared team IDs are current
      try {
        const conversationInfo =
          await this.slackWebAPIService.getConversationInfo(
            installation.botToken,
            {
              channel: channel,
            },
          );

        if (conversationInfo.ok && conversationInfo.channel) {
          // Handle externally shared channels (channels shared between multiple Slack workspaces)
          // This is important for cross-workspace functionality and proper message routing
          if (conversationInfo.channel.is_ext_shared) {
            this.logger.log(
              `Channel ${channel} is externally shared, updating shared team IDs`,
            );

            // Store the IDs of all workspaces this channel is shared with
            // These IDs are essential for properly handling messages in multi-workspace environments
            if (Array.isArray(conversationInfo.channel.shared_team_ids)) {
              this.logger.log(
                `Setting shared team IDs for channel ${channel}: ${conversationInfo.channel.shared_team_ids.join(', ')}`,
              );

              // Persist the channel metadata, shared status, and workspace IDs to our database
              // This enables proper message handling across connected workspaces
              await this.channelsRepository.update(slackChannel.id, {
                channelDump: conversationInfo.channel as Record<string, any>,
                isShared: true,
                sharedTeamIds: conversationInfo.channel.shared_team_ids,
              });

              // Refresh our channel object with the updated information
              slackChannel = await this.channelsRepository.findByCondition({
                where: { id: slackChannel.id },
              });
            }
          }
        }
      } catch (refreshError) {
        this.logger.error(
          `Failed to refresh channel info for ${channel}: ${
            refreshError instanceof Error
              ? refreshError.message
              : String(refreshError)
          }`,
        );
      }
    }

    // If the channel is already active, then we don't need to do anything
    if (slackChannel.isBotActive && !slackChannel.isBotJoined) {
      // TODO: This is a bad situation, because the channel was marked active but the bot wasn't present
      this.logger.error(
        `[ABNORMAL] The channel ${channel} was marked active but the bot wasn't present`,
      );

      return;
    }

    // Extract shared team IDs from the channel dump if available
    const updateData: Partial<ChannelUpdateData> = {};

    // Check if the channel has shared team IDs in the dump
    const channelDump = slackChannel.channelDump;
    const isSharedChannel = channelDump?.is_shared;
    const hasSharedTeamIds =
      channelDump?.shared_team_ids &&
      Array.isArray(channelDump.shared_team_ids);

    if (isSharedChannel && hasSharedTeamIds) {
      const sharedTeamIds = channelDump.shared_team_ids;
      updateData.sharedTeamIds = sharedTeamIds;
      this.logger.log(
        `Setting shared team IDs for channel ${channel}: ${sharedTeamIds.join(', ')}`,
      );
    }

    await this.channelsRepository.update(slackChannel.id, updateData);

    // Send the configuration message
    await this.sendChannelConfigurationMessage(
      channel,
      installation.id,
      inviter,
    );
  }

  /**
   * @private
   *
   * Get the user and installation from the database
   * @param userId The user ID from Slack
   * @param teamId The team ID from Slack
   * @returns The user and installation
   */
  private async getUserAndInstallation(userId: string, teamId: string) {
    const installation = await this.installationRepository.findByCondition({
      where: { teamId },
      relations: { organization: true },
    });

    // If installation not found, throw an error
    if (!installation) {
      throw new Error('Installation not found!');
    }

    // Get the organization from the user
    const organization = installation?.organization;

    // If organization not found, throw an error
    if (!organization) {
      throw new Error('Organization not found!');
    }

    // Get the user from the database
    let slackUser = await this.userRepository.findOne({
      where: {
        slackId: userId,
        installation: { id: installation.id },
        organization: { id: organization.id },
      },
    });

    // If user not found, create it
    if (!slackUser) {
      slackUser = await this.slackAppManagementService.upsertSlackUser({
        userId,
        token: installation.botToken,
        installationId: installation.id,
        organizationId: organization.id,
        isCustomer: false,
      });
    }

    return { slackUser, installation, organization };
  }

  /**
   * Send a configuration message to a channel
   * This can be called from different places to make sure the channel is configured
   */
  public async sendChannelConfigurationMessage(
    channelId: string,
    installationId: string,
    inviterId?: string,
  ) {
    // Get the channel and installation
    const channel = await this.channelsRepository.findByCondition({
      where: { channelId, installation: { id: installationId } },
      relations: { installation: true },
    });

    if (!channel) {
      this.logger.error(`Channel ${channelId} not found`);
      return false;
    }

    // Don't send configuration message if channel is already active or configured
    if (channel.isBotActive) {
      this.logger.log(
        `Channel ${channelId} is already active, skipping configuration message`,
      );
      return true;
    }

    // If the channel is already configured, we don't need to send a message
    if (
      channel.channelType !== ChannelType.NOT_CONFIGURED &&
      channel.channelType !== ChannelType.NOT_SETUP
    ) {
      this.logger.log(
        `Channel ${channelId} is already configured as ${channel.channelType}, skipping configuration message`,
      );
      return true;
    }

    try {
      // Prefer the inviter (who added the bot to this channel) if available
      // Fall back to the installing user ID if inviter is not available
      const targetUserId = inviterId || channel.installation.installingUserId;

      if (!targetUserId) {
        this.logger.error('No user ID found for ephemeral message target');
        return false;
      }

      // Send an ephemeral message that only the target user can see
      const sentMessage = await this.slackWebAPIService.sendEphemeral(
        channel.installation.botToken,
        {
          channel: channelId,
          user: targetUserId,
          text: `Setup required to start using this channel. Click ‘Configure channel’ to set it up.`,
          blocks: this.getEphemeralMessageBlocks(channelId, channel.name),
        },
      );

      if (!sentMessage.ok) {
        throw new Error(
          `Failed to send ephemeral message to channel: ${sentMessage.error}`,
        );
      }

      this.logger.log(
        `Sent ephemeral configuration message to user ${targetUserId} in channel ${channelId}`,
      );

      // TODO: Send a message directly to the user who added the bot to the channel from the bot with the same message

      // Send a direct message to the user who added the bot to the channel
      try {
        const directMessage = await this.slackWebAPIService.sendMessage(
          channel.installation.botToken,
          {
            channel: targetUserId, // Using the user ID directly as the channel
            text: `Hello! I was just added to the #${channel.name} channel. The channel needs to be configured before I can be used.`,
            blocks: this.getDirectMessageBlocks(channelId, channel.name),
          },
        );

        if (directMessage.ok) {
          this.logger.log(
            `Sent direct message to user ${targetUserId} about channel configuration`,
          );
        } else {
          this.logger.error(
            `Failed to send direct message to user: ${directMessage.error}`,
          );
        }
      } catch (dmError) {
        // Just log the error but don't fail the entire operation if DM fails
        this.logger.error(
          `Failed to send direct message to user ${targetUserId}: ${dmError instanceof Error ? dmError.message : String(dmError)}`,
        );
      }

      return true;
    } catch (error) {
      // Log the error
      this.logger.error(
        `Failed to send ephemeral message: ${error instanceof Error ? error.message : String(error)}`,
      );

      // Simply log errors without falling back to regular messages
      if (
        error instanceof Error &&
        error.message.includes('user_not_in_channel')
      ) {
        this.logger.warn(
          `User ${inviterId || channel.installation.installingUserId} is not in channel ${channelId}, no configuration message sent`,
        );
      }

      return false;
    }
  }
}
