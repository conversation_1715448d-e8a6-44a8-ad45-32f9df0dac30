import { Inject, Injectable } from '@nestjs/common';
import { InjectRepository } from '@nestjs/typeorm';
import { KnownEventFromType, StringIndexed } from '@slack/bolt';
import { FileShareMessageEvent } from '@slack/types';
import { Repository } from 'typeorm';
import {
  Channels,
  CommentThreadMappings,
  CustomerContacts,
  Installations,
  SlackMessages,
} from '../../../database/entities';
import { ChannelType } from '../../../database/entities/channels/channels.entity';
import { TeamRelationshipType } from '../../../database/entities/mappings';
import { Person } from '../../../database/interfaces/person.interface';
import { CreateNewComment } from '../../../external/provider/interfaces';
import { ThenaPlatformApiProvider } from '../../../external/provider/thena-platform-api.provider';
import { Ticket } from '../../../platform/interfaces';
import { CUSTOM_LOGGER_TOKEN, type ILogger } from '../../../utils';
import { getSlackMentions } from '../../../utils/common';
import { BaseSlackBlocksToHtml } from '../../../utils/parsers/slack/slack-blocks-to-html/base-slack-to-html.parser';
import { SlackEventMap } from '../../event-handlers';
import { SettingsCore } from '../management';
import { CoreSlackMessage } from './core-slack-message';
import { OnThreadMessageHandler } from './on-thread-message';

@Injectable()
export class OnMessageHandler {
  constructor(
    @Inject(CUSTOM_LOGGER_TOKEN)
    private readonly logger: ILogger,

    // Database Repositories
    @InjectRepository(SlackMessages)
    private readonly slackMessagesRepository: Repository<SlackMessages>,

    @InjectRepository(CommentThreadMappings)
    private readonly commentThreadMappingsRepository: Repository<CommentThreadMappings>,

    // External API Providers
    private readonly platformApiProvider: ThenaPlatformApiProvider,

    // Injected Services
    private readonly coreSlackMessage: CoreSlackMessage,

    // Parsers
    private readonly baseSlackBlocksToHtml: BaseSlackBlocksToHtml,

    // Management
    private readonly onThreadMessageHandler: OnThreadMessageHandler,
    private readonly settingsCore: SettingsCore,
  ) {}

  async onMessage(e: SlackEventMap['message']) {
    // TypeGuard: Validate that the event has a context
    if (!('context' in e)) {
      throw new Error('Context not found');
    }

    // TypeGuard: Validate that the event has a context
    const { event, context } = e;
    const { installation } = context;

    // TypeGuard: Validate that the message has text to fetch the union type [[Possibly] A Top Level Message]
    const topLevelMessage = 'text' in event && !('thread_ts' in event);

    // TypeGuard: Validate that the message has thread_ts [Thread Message]
    const threadMessage = 'text' in event && 'thread_ts' in event;

    // TypeGuard: Validate that the message has bot_profile and metadata
    // If the message was sent by the bot and ignoreSelf is in metadata, we will drop this event
    // Note that mostly this comes because the message was sent by the Bot but using User Token
    if ('bot_profile' in event && 'metadata' in event) {
      const ignoreSelf = (event.metadata as StringIndexed)?.event_payload
        ?.ignoreSelf;
      if (event.bot_profile && ignoreSelf) {
        this.logger.debug(
          `[ts=${event?.ts}] Bot is mentioned in the message! Skipping ticket creation.`,
        );
        return;
      }
    }

    // Handle thread messages
    if (threadMessage) {
      await this.onThreadMessageHandler.handle(e);
    }

    // If the message is a top level message, we need to create a new ticket
    if (topLevelMessage && !threadMessage) {
      await this.handleNewMessage(installation, event);
    }
  }

  /**
   * Handle new message
   * @param installation Installation
   * @param event Message event
   */
  private async handleNewMessage(
    installation: Installations,
    event: KnownEventFromType<'message'>,
  ) {
    try {
      // TypeGuard: Validate that the message has text to fetch the union type [[Possibly] A Top Level Message]
      const topLevelMessage = 'text' in event;
      if (!topLevelMessage) {
        throw new Error('Message is not a top level message');
      }

      // TypeGuard: Validate that the message has a team
      if (!('team' in event)) {
        throw new Error('Slack workspace not attached to the event message!');
      }

      // If the message is in a direct message channel, we don't need to create a ticket
      if (event.channel.startsWith('D')) {
        this.logger.debug(
          `[ts=${event?.ts}] Message received in a direct message channel ${event.channel}! Skipping ticket creation.`,
        );

        return;
      }

      // Perform core detection
      const { channel, user, isGrouped, isCustomer } =
        await this.coreSlackMessage.checkAndGetSlackMessageDetails(
          installation,
          event,
        );

      if (!channel) {
        this.logger.debug(
          `[ts=${event?.ts}] Channel ${event.channel} not found in the database! Skipping ticket creation.`,
        );

        return;
      }

      this.logger.log(
        `[ts=${event?.ts}] Checking if the bot is mentioned in the message`,
      );

      // Check if the bot is mentioned
      // Get the platform team from the channel
      let platformTeam = null;

      // Get the primary team
      const primaryTeam = channel.platformTeamsToChannelMappings.find(
        (mapping) => mapping.relationshipType === TeamRelationshipType.PRIMARY,
      );

      // If there is a primary team, use it
      if (primaryTeam) {
        platformTeam = primaryTeam.platformTeam;
      } else if (channel.platformTeamsToChannelMappings.length === 1) {
        platformTeam = channel.platformTeamsToChannelMappings[0].platformTeam;
      }

      const { botMentioned } = getSlackMentions(installation, event.text);

      // Get the create ticket on bot mention setting
      const createTicketOnBotMention = await this.settingsCore.getValue(
        'thena_bot_tagging_enabled',
        {
          recursivelyLookup: false,
          workspace: installation,
          platformTeam,
        },
      );

      // If the bot is mentioned and the setting is enabled, we will create a ticket
      if (botMentioned && createTicketOnBotMention) {
        this.logger.debug(
          `[ts=${event?.ts}] Bot is mentioned in the message! Creating ticket.`,
        );

        // Create a new ticket if the bot is mentioned and the setting is enabled
        const { ticket, comment } = await this.ticketFromSourceSlack(
          installation,
          channel,
          user,
          event,
        );

        return { ticket, comment };
      }

      // If the user is not a customer and the channel is not an internal helpdesk channel, we don't need to create a ticket
      if (
        !isCustomer &&
        channel.channelType !== ChannelType.INTERNAL_HELPDESK
      ) {
        this.logger.debug(`[ts=${event?.ts}] Message is not from a customer!`);
        return;
      }

      // If the message is grouped with an existing conversation, we don't need to create a ticket
      if (isGrouped) {
        this.logger.log(
          `[ts=${event?.ts}] Message is grouped with an existing conversation! Skipping ticket creation.`,
        );
        return;
      }

      // Get the automatic ticket creation setting
      const automaticTicketCreation = await this.settingsCore.getValue(
        'automatic_tickets',
        {
          recursivelyLookup: false,
          workspace: installation,
          platformTeam,
        },
      );

      // If automatic ticket creation is not enabled, we will not create a ticket
      if (!automaticTicketCreation) {
        this.logger.debug(
          `[ts=${event?.ts}] Automatic ticket creation is not enabled! Skipping ticket creation.`,
        );

        return;
      }

      this.logger.log(
        `[ts=${event?.ts}] Checking if the message is a valid ticket`,
      );

      // Get the AI model
      const aiModel = await this.settingsCore.getValue('ai_model', {
        recursivelyLookup: false,
        workspace: installation,
        platformTeam,
      });

      // If AI Model is disabled and we have automatic ticket creation enabled, we'll create ticket
      // for every message received
      if (!aiModel && automaticTicketCreation) {
        this.logger.debug(
          `[ts=${event?.ts}] AI model is disabled! Creating ticket for every message.`,
        );

        // Create a new ticket
        const { ticket, comment } = await this.ticketFromSourceSlack(
          installation,
          channel,
          user,
          event,
        );

        return { ticket, comment };
      }

      // If the AI model is not set, we will not create a ticket
      if (!aiModel) {
        this.logger.debug(
          `[ts=${event?.ts}] AI model is not set! Skipping ticket creation.`,
        );

        return;
      }

      // Check with AI if the message is a valid ticket
      const isValidTicket = await this.coreSlackMessage.aiCheckIsValidTicket(
        event.text,
        platformTeam,
        installation,
      );

      if (!isValidTicket) {
        this.logger.debug(
          `[ts=${event?.ts}] Message is not a valid ticket! Skipping ticket creation.`,
        );

        return;
      }

      // Create a new ticket if the bot is mentioned and the setting is enabled
      const { ticket, comment } = await this.ticketFromSourceSlack(
        installation,
        channel,
        user,
        event,
      );

      return { ticket, comment };
    } catch (error) {
      if (error instanceof Error) {
        this.logger.error(
          `[ts=${event?.ts}] Error handling new message: ${error.message}`,
          error.stack,
        );
      } else {
        console.error('Error handling new message:', error);
      }
    }
  }

  /**
   * Create a new ticket and slack message
   * @param installation Installation
   * @param channel Channel
   * @param customer Customer
   * @param event Message event
   * @returns Platform ticket and Slack message
   */
  private async ticketFromSourceSlack(
    installation: Installations,
    channel: Channels,
    user: Person,
    event: KnownEventFromType<'message'>,
  ) {
    // TypeGuard: Validate that the message has text to fetch the union type [[Possibly] A Top Level Message]
    if (!('text' in event)) {
      throw new Error('Message is not a top level message');
    }

    this.logger.log(
      `[ts=${event?.ts}] Creating a new ticket and slack message`,
    );

    // Create a new ticket and slack message
    const { ticket, slackMessage } = await this.createTicketAndSlackMessage(
      installation,
      channel,
      user,
      event,
    );

    // Create comment and mapping
    const comment = await this.createCommentAndMapping(
      installation,
      ticket,
      user,
      event,
    );

    this.logger.log(
      `[ts=${event?.ts}] Created a new comment and mapping for the ticket ${ticket.id}`,
    );

    // Update the slack message with the comment id
    await this.slackMessagesRepository.update(slackMessage.id, {
      platformCommentId: comment.data.id,
    });

    return { ticket, comment };
  }

  /**
   * Create a new ticket and slack message
   *
   * @param installation Installation
   * @param channel Channel
   * @param customer Customer
   * @param event Message event
   * @returns Platform ticket and Slack message
   */
  private async createTicketAndSlackMessage(
    installation: Installations,
    channel: Channels,
    customer: Person,
    event: KnownEventFromType<'message'>,
  ) {
    // TypeGuard: Validate that the message has text to fetch the union type [[Possibly] A Top Level Message]
    const topLevelMessage = 'text' in event;
    if (!topLevelMessage) {
      throw new Error('Message is not a top level message');
    }

    // TypeGuard: Validate that the message has a team
    if (!('team' in event)) {
      throw new Error('Slack workspace not attached to the event message!');
    }

    // Create a new ticket
    const { ticket, event_ts, threadTs, messagePermalink, ts } =
      await this.coreSlackMessage.createTicketForTeam(
        installation,
        channel,
        customer,
        event,
      );

    this.logger.log(
      `[ts=${event?.ts}] Created a new ticket ${ticket.id} for the team ${installation.organization.name}`,
    );

    // Create a new slack message
    const slackMessage = await this.slackMessagesRepository.save({
      platformTicketId: ticket.id,
      slackEventCreatedAt: new Date(+event_ts).toISOString(),
      slackMessageTs: ts,
      slackMessageThreadTs: threadTs,
      slackPermalink: messagePermalink.permalink,
      slackUserId: event.user,
      metadata: {
        customer: {
          email: customer.slackProfileEmail,
          name: customer.displayName ?? customer.realName,
        },
        ticket_details: {
          status: ticket.status,
          statusId: ticket.statusId,
          priority: ticket.priority,
          priorityId: ticket.priorityId,
        },
      },

      // Link FKs
      channel: { id: channel.id },
      installation: { id: installation.id },
      organization: { id: installation.organization.id },
    });

    return { slackMessage, ticket };
  }

  private async createCommentAndMapping(
    installation: Installations,
    ticket: Ticket,
    userInQuestion: Person,
    event: KnownEventFromType<'message'>,
  ) {
    // TypeGuard: Validate that the message has text to fetch the union type [[Possibly] A Top Level Message]
    const topLevelMessage = 'text' in event;
    if (!topLevelMessage) {
      throw new Error('Message is not a top level message');
    }

    if (!('blocks' in event)) {
      throw new Error('Blocks are required for comments');
    }

    this.logger.log(
      `[ts=${event?.ts}] Creating a new comment and mapping for the ticket ${ticket.id}`,
    );

    // Get the files from the event
    let files: FileShareMessageEvent['files'] = [];
    if ('files' in event) {
      files = event.files;
    }

    // Initialize the converter with the blocks and installation
    this.baseSlackBlocksToHtml.initialize(event.blocks, installation);

    // Convert blocks to HTML content
    const htmlContent = await this.baseSlackBlocksToHtml.convert();

    // Create a new comment payload
    const commentPayload: CreateNewComment = {
      content: htmlContent,
      htmlContent,
      ticketId: ticket.id,
      metadata: {
        ignoreSelf: true,
        ts: event.ts,
        threadTs: event.ts,
      },
      files,
      channelId: event.channel,
      impersonatedUserAvatar: userInQuestion.getUserAvatar(),
      impersonatedUserEmail: userInQuestion.slackProfileEmail,
      impersonatedUserName:
        userInQuestion.displayName || userInQuestion.realName,
    };

    this.logger.log(
      `[ts=${event?.ts}] Creating a new comment on the ticket ${ticket.id}`,
    );

    if (userInQuestion instanceof CustomerContacts) {
      commentPayload.customerEmail = userInQuestion.slackProfileEmail;
    }

    // Create a new comment on the ticket
    const comment = await this.platformApiProvider.createNewComment(
      installation,
      commentPayload,
    );

    this.logger.log(
      `[ts=${event?.ts}] Created a new comment on the ticket ${ticket.id}`,
    );

    // Create comment thread mapping
    await this.commentThreadMappingsRepository.save({
      organization: installation.organization,
      platformCommentThreadId: comment.data.id,
      platformCommentTicketId: ticket.id,
      slackThreadId: event.ts,
      slackChannelId: event.channel,
    });

    return comment;
  }
}
