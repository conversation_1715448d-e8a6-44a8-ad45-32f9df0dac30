import { Inject, Injectable } from '@nestjs/common';
import { InjectRepository } from '@nestjs/typeorm';
import { Repository } from 'typeorm';
import { InstallationWithOrganization } from '../../../common/interfaces/common.interface';
import { SlackMessages } from '../../../database/entities';
import { CUSTOM_LOGGER_TOKEN, ILogger } from '../../../utils';

interface GetSlackMessageByPlatformTicketIdOpts {
  createIndependentIfNotFound?: boolean;
}

@Injectable()
export class SlackMessageCore {
  constructor(
    @Inject(CUSTOM_LOGGER_TOKEN) private readonly logger: ILogger,

    // Database Repositories
    @InjectRepository(SlackMessages)
    private readonly slackMessageRepository: Repository<SlackMessages>,
  ) {}

  /**
   * Get a slack message by platform ticket id
   */
  async getSlackMessageByPlatformTicketId(
    installation: InstallationWithOrganization,
    ticketId: string,
    opts: GetSlackMessageByPlatformTicketIdOpts,
  ) {
    const { createIndependentIfNotFound = false } = opts;

    let slackMessage: SlackMessages;
    slackMessage = await this.slackMessageRepository.findOne({
      where: {
        platformTicketId: ticketId,
        installation: { id: installation.id },
        organization: { id: installation.organization.id },
      },
    });

    // If the message is not found and the option to create an independent message is set, create a new message
    if (!slackMessage && createIndependentIfNotFound) {
      slackMessage = await this.slackMessageRepository.save({
        platformTicketId: ticketId,
        isIndependent: true,
        installation: { id: installation.id },
        organization: { id: installation.organization.id },
      });
    }

    return slackMessage;
  }
}
