export interface SlackActionPayload {
  type: string;
  actions?: Array<{
    action_id: string;
    value?: string;
    selected_option?: {
      value: string;
    };
    selected_options?: Array<{
      value: string;
    }>;
    selected_date?: string;
    selected_time?: string;
    selected_user?: string;
    selected_conversation?: string;
    selected_channel?: string;
  }>;
  view?: {
    id: string;
    type: string;
    callback_id: string;
    private_metadata?: string;
    blocks: Array<{
      type: string;
      block_id?: string;
      element?: {
        type: string;
        value?: string;
        selected_option?: {
          value: string;
        };
        selected_options?: Array<{
          value: string;
        }>;
        selected_date?: string;
      };
    }>;
  };
  client: {
    views: {
      update: (params: {
        view_id: string;
        view: {
          type: string;
          callback_id: string;
          private_metadata?: string;
          blocks: any[];
        };
      }) => Promise<any>;
    };
  };
}
