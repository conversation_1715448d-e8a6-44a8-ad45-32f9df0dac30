import { StringIndexed } from '@slack/bolt';
import { WebClient } from '@slack/web-api';
import { Installations } from '../../database/entities';

export interface ChannelSetupActionBody extends StringIndexed {
  actions: Array<{
    value?: string;
  }>;
  user: {
    id: string;
  };
  trigger_id: string;
}

export interface ChannelData {
  channelId: string;
  channelName: string;
}

export interface ChannelState {
  channelId: string;
  channelType: string;
  teamMappings: Array<{
    relationshipType: string;
    platformTeamId?: string;
    platformTeamUid?: string;
  }>;
  sharedTeamIds: string[];
}

export interface ChannelSetupConfig {
  retryDelayMs: number;
  maxRetries: number;
  modalConfig: {
    title: string;
    submitText: string;
    closeText: string;
  };
}

export interface Team {
  id: string;
  name: string;
  platformTeamId?: string;
  platformTeamUid?: string;
}

export interface IChannelSetupService {
  checkChannelState(
    channelId: string,
    installation: Installations,
  ): Promise<void>;
  fetchTeams(installation: Installations): Promise<Team[]>;
  openConfigurationModal(
    client: WebClient,
    installation: Installations,
    triggerId: string,
    channelData: ChannelData,
    teams: Team[],
  ): Promise<void>;
}
