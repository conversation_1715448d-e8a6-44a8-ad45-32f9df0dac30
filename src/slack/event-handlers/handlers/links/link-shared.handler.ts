import { Inject, Injectable } from '@nestjs/common';
import { encode } from 'html-entities';
import { Installations } from '../../../../database/entities';
import { ThenaPlatformApiProvider } from '../../../../external/provider/thena-platform-api.provider';
import { Ticket } from '../../../../platform/interfaces';
import { CUSTOM_LOGGER_TOKEN, ILogger } from '../../../../utils';
import { SlackEvent } from '../../../decorators';
import { SlackWebAPIService } from '../../../providers/slack-apis/slack-apis.service';
import { BaseSlackEventHandler, SlackEventMap } from '../../interface';

@Injectable()
@SlackEvent('link_shared')
export class SlackLinkSharedHandler extends BaseSlackEventHandler<'link_shared'> {
  eventType = 'link_shared' as const;

  constructor(
    @Inject(CUSTOM_LOGGER_TOKEN) private readonly logger: ILogger,

    // External services
    private readonly slackWebAPIService: SlackWebAPIService,
    private readonly platformApiProvider: ThenaPlatformApiProvider,
  ) {
    super();
  }

  canHandle(event: SlackEventMap['link_shared']): boolean {
    return event.event.type === 'link_shared';
  }

  async handle(e: SlackEventMap['link_shared']): Promise<void> {
    try {
      this.logger.log(
        `[SlackLinkSharedHandler] Handling 'link_shared' event for team ${e.context.installation.teamId} for user ${e.event.user} in channel ${e.event.channel} at ${e.event.event_ts}`,
      );

      const { event: linkSharedEvent, context } = e;
      const { installation } = context;

      const { channel, message_ts: messageTs, links } = linkSharedEvent;

      const unfurls: Record<string, any> = {};

      // Generate custom content based on the URL
      for (const link of links) {
        const unfurlContent = await this.generateUnfurlContent(
          installation,
          link.url,
        );
        unfurls[link.url] = unfurlContent;
      }

      // Unfurl the links
      await this.slackWebAPIService.unfurlLink(installation.botToken, {
        channel,
        ts: messageTs,
        unfurls,
      });
    } catch (error) {
      if (error instanceof Error) {
        this.logger.error(
          `[SlackLinkSharedHandler] Failed to handle 'link_shared' event for team ${e.context.installation.teamId} for user ${e.event.user} in channel ${e.event.channel} at ${e.event.event_ts}`,
          error.stack,
        );
      } else {
        console.error(
          `[SlackLinkSharedHandler] Failed to handle 'link_shared' event for team ${e.context.installation.teamId} for user ${e.event.user} in channel ${e.event.channel} at ${e.event.event_ts}`,
          error,
        );
      }
    }
  }

  async generateUnfurlContent(
    installation: Installations,
    url: string,
  ): Promise<Record<string, any>> {
    // Parse the URL
    const parsedUrl = new URL(url);

    // Get the value of the ticketId parameter from the query string
    const ticketId = parsedUrl.searchParams.get('ticketId');
    if (!ticketId) {
      return this.generateDefaultUnfurl(url);
    }

    // Get the ticket details from database
    const ticket = await this.platformApiProvider.getTicket(
      installation,
      ticketId,
    );

    return this.generateTicketDetails(ticket, url);
  }

  private generateTicketDetails(ticket: Ticket, ticketUrl: string) {
    const ticketData = {
      id: ticket.ticketId,
      title: `${ticket.teamIdentifier.toUpperCase()}-${ticket.ticketId} ${ticket.title}`,
      status: ticket.status,
      priority: ticket.priority,
      assignedAgent: ticket.assignedAgent,
      customerName: ticket.customerContactFirstName
        ? `${ticket.customerContactFirstName} ${ticket.customerContactLastName || ''}`
        : 'Unassigned',
      customerEmail: ticket.customerContactEmail || 'No email provided',
      createdAt: new Date(ticket.createdAt).toLocaleDateString('en-US', {
        month: 'short',
        day: 'numeric',
        year: 'numeric',
      }),
    };

    return {
      id: 1,
      color: '#2d1c9c',
      fallback: ticketData.title,
      blocks: [
        {
          type: 'section',
          block_id: 'OfxHE',
          text: {
            type: 'mrkdwn',
            text: `<${ticketUrl}|*${encode(ticketData.title)}*>`,
            verbatim: true,
          },
        },
        {
          type: 'context',
          block_id: 'xs0Tq',
          elements: [
            {
              type: 'mrkdwn',
              text: `*Status*  ${ticketData.status}`,
              verbatim: false,
            },
            {
              type: 'mrkdwn',
              text: `*Assignee*  ${ticketData.assignedAgent ?? 'Unassigned'}`,
              verbatim: false,
            },
            {
              type: 'mrkdwn',
              text: `*Priority*  ${ticketData.priority}`,
              verbatim: false,
            },
          ],
        },
        {
          type: 'context',
          block_id: 'jzQBw',
          elements: [
            {
              type: 'mrkdwn',
              text: `Updated at ${new Date().toLocaleString()}`,
              verbatim: false,
            },
          ],
        },
      ],
    };
  }

  private generateDefaultUnfurl(url: string) {
    return {
      blocks: [
        {
          type: 'section',
          text: {
            type: 'mrkdwn',
            text: '*Thena dashboard link*',
          },
        },
        {
          type: 'section',
          text: {
            type: 'mrkdwn',
            text: 'This link leads to the Thena dashboard. Click to view.',
          },
        },
        {
          type: 'context',
          elements: [
            {
              type: 'mrkdwn',
              text: `<${url}|Open in Thena>`,
            },
          ],
        },
      ],
    };
  }
}
