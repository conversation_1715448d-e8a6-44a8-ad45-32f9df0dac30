import { Inject, Injectable } from '@nestjs/common';
import { CUSTOM_LOGGER_TOKEN, ILogger } from '../../../../../utils';
import { SlackEventMap } from '../../../interface';
import { MessageChangedHandler } from './message-changed.handler';
import { MessageDeletedHandler } from './message-deleted.handler';

@Injectable()
export class SlackSubTypeHandler {
  constructor(
    @Inject(CUSTOM_LOGGER_TOKEN) private readonly logger: ILogger,

    // Handlers
    private readonly messageChangedHandler: MessageChangedHandler,
    private readonly messageDeletedHandler: MessageDeletedHandler,
  ) {}

  async handle(event: SlackEventMap['message']) {
    try {
      if (!('subtype' in event.event)) {
        this.logger.debug('No subtype found in message event, skipping...');
        return;
      }

      // Get the subtype
      const eventSubType = event.event.subtype;

      switch (eventSubType) {
        // Message changed
        case 'message_changed':
          this.logger.debug('Message changed event encountered!');
          await this.messageChangedHandler.handle(event);
          break;

        // Message deleted
        case 'message_deleted':
          this.logger.debug('Message deleted event encountered!');
          await this.messageDeletedHandler.handle(event);
          break;

        default:
          this.logger.debug(`Unknown subtype: ${eventSubType}, skipping...`);
          break;
      }

      // Return true if the event was handled
      return true;
    } catch (error) {
      if (error instanceof Error) {
        this.logger.error(
          `Error handling message event: ${error.message}`,
          error.stack,
        );
      } else {
        console.error('Error handling message event', error);
      }

      throw error;
    }
  }
}
