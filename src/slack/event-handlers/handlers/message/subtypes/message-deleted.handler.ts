import { Inject, Injectable } from '@nestjs/common';
import { InjectRepository } from '@nestjs/typeorm';
import { AllMessageEvents } from '@slack/types';
import { Repository } from 'typeorm';
import {
  CustomerContacts,
  Installations,
  SlackMessages,
  Users,
} from '../../../../../database/entities';
import { ChannelsRepository } from '../../../../../database/entities/channels/repositories';
import { SlackMessagesRepository } from '../../../../../database/entities/slack-messages/repositories/slack-messages.repository';
import { ThenaPlatformApiProvider } from '../../../../../external/provider/thena-platform-api.provider';
import { CommentMetadata } from '../../../../../platform/type-system/events';
import { CUSTOM_LOGGER_TOKEN, ILogger } from '../../../../../utils';
import { BaseSlackBlocksToHtml } from '../../../../../utils/parsers/slack/slack-blocks-to-html/base-slack-to-html.parser';
import { SlackEventMap } from '../../../interface';

@Injectable()
export class MessageDeletedHandler {
  constructor(
    @Inject(CUSTOM_LOGGER_TOKEN) private readonly logger: ILogger,

    // Repositories
    @InjectRepository(Users)
    private readonly usersRepository: Repository<Users>,
    @InjectRepository(CustomerContacts)
    private readonly customersRepository: Repository<CustomerContacts>,
    private readonly channelRepository: ChannelsRepository,
    private readonly slackMessagesRepository: SlackMessagesRepository,

    // External API Providers
    private readonly thenaPlatformApiProvider: ThenaPlatformApiProvider,

    // Parsers
    private readonly baseSlackBlocksToHtml: BaseSlackBlocksToHtml,
  ) {}

  async handle(e: SlackEventMap['message']) {
    const { event, context } = e;
    const { installation } = context;

    // Validate that the event has a subtype and that it is a message_changed event
    if (event?.subtype !== 'message_deleted') {
      const msg = `Subtype ${event.subtype} not supported, encountered for ${installation.teamId}, skipping...`;
      this.logger.debug(msg);
      throw new Error(msg);
    }

    // If the message is a bot message, skip it
    if (
      'previous_message' in event &&
      event.previous_message.subtype === 'bot_message'
    ) {
      this.logger.debug(
        `Bot message event received for ${installation.teamId}, skipping...`,
      );
      return;
    }

    // Get top-level message details for slack
    const { channel } = event;
    let ts: string | undefined;
    if ('deleted_ts' in event) {
      ts = event.deleted_ts;
    }

    const threadTs = await this.getThreadTs(event);

    // Get the slack channel
    const slackChannel = await this.channelRepository.findByCondition({
      where: { channelId: channel, installation: { id: installation.id } },
    });

    // If the channel is not found, throw an error
    if (!slackChannel) {
      const msg = `Channel not found for ${installation.teamId}, skipping...`;
      this.logger.debug(msg);
      throw new Error(msg);
    }

    this.logger.log(
      `Message deleted event received for ${installation.teamId}, channel: ${slackChannel.id}, threadTs: ${threadTs}, ts: ${ts}`,
    );

    // Get the related slack message
    const slackMessage = await this.getRelatedSlackMessage(
      installation,
      slackChannel.id,
      threadTs,
    );

    this.logger.log(
      `Related slack message found for ${installation.teamId}, slackMessage: ${slackMessage.id}`,
    );

    // Get the platform comment ID
    const platformCommentId = await this.getPlatformCommentId(
      installation,
      slackMessage,
      ts,
    );

    this.logger.log(
      `Platform comment ID found for ${installation.teamId}, platformCommentId: ${platformCommentId}`,
    );

    // Delete the comment on the platform
    await this.thenaPlatformApiProvider.deleteComment(
      installation,
      platformCommentId,
    );

    this.logger.log(
      `Comment deleted on the platform for ${installation.teamId}, platformCommentId: ${platformCommentId}`,
    );
  }

  /**
   * Get the platform comment ID
   * @param installation Installation
   * @param slackMessage Slack message
   * @param ts Thread timestamp
   * @returns Platform comment ID
   */
  private async getPlatformCommentId(
    installation: Installations,
    slackMessage: SlackMessages,
    ts: string,
  ) {
    // Get the platform comment ID
    const parentCommentId = slackMessage.platformCommentId;
    if (!parentCommentId) {
      const msg = `No platform comment ID found for ${installation.teamId}, skipping...`;
      this.logger.debug(msg);
      throw new Error(msg);
    }

    // If the threadTs is the same as the slack message timestamp, return the parent comment ID
    if (ts === slackMessage.slackMessageTs) {
      this.logger.debug(
        `Thread timestamp is the same as the slack message timestamp, returning parent comment ID: ${parentCommentId}`,
      );
      return parentCommentId;
    }

    // Get the thread comments on the platform thread
    const threads = await this.thenaPlatformApiProvider.getCommentThreads(
      installation,
      parentCommentId,
    );

    // Find the thread comment that matches the threadTs
    const commentFound = threads.find((th) => {
      const thMeta = th.metadata as CommentMetadata;
      const slackTs = thMeta?.external_sinks?.slack?.ts;
      return slackTs === ts;
    });

    // If no comment is found, throw an error
    if (!commentFound) {
      const msg = `No thread comment found for ${installation.teamId}, skipping...`;
      this.logger.debug(msg);
      throw new Error(msg);
    }

    return commentFound.id;
  }

  /**
   * Get the related slack message
   * @param installation Installation
   * @param channelId Channel ID
   * @param threadTs Thread timestamp
   * @returns Slack message
   */
  private async getRelatedSlackMessage(
    installation: Installations,
    channelId: string,
    threadTs: string,
  ) {
    // Validate that the threadTs is provided
    if (!threadTs) {
      const msg = `Thread timestamp not found, encountered for ${installation.teamId}, skipping...`;
      this.logger.debug(msg);
      throw new Error(msg);
    }

    const commonWhereClause = {
      channel: { id: channelId },
      installation: { id: installation.id },
    };

    // Get the slack message
    const slackMessage = await this.slackMessagesRepository.findByCondition({
      where: [
        // Check if its a direct message
        { ...commonWhereClause, slackMessageTs: threadTs },

        // Try to get the related top-level if this message is a threaded message
        { ...commonWhereClause, slackMessageThreadTs: threadTs },
      ],
    });

    // If a message is found, return it
    if (!slackMessage) {
      const msg = `No related slack message found for ${installation.teamId}, skipping...`;
      this.logger.debug(msg);
      throw new Error(msg);
    }

    return slackMessage;
  }

  /**
   * Get the thread timestamp
   * @param event Event
   * @returns Thread timestamp
   */
  private async getThreadTs(event: AllMessageEvents) {
    // Get the previous message
    let previousMessage: AllMessageEvents | undefined;
    if ('previous_message' in event) {
      previousMessage = event.previous_message;
    }

    // Get the thread timestamp
    let threadTs: string | undefined;
    if ('thread_ts' in previousMessage) {
      threadTs = previousMessage.thread_ts;
    }

    return threadTs;
  }
}
