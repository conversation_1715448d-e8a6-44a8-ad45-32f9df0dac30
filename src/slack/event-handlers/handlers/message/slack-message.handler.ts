import { Inject, Injectable } from '@nestjs/common';
import { EventDeduplicationService } from '../../../../common/redis/event-deduplication.service';
import { AuditLogOp, AuditLogVisibility } from '../../../../database/constants';
import { SlackAuditLogRepository } from '../../../../database/entities/slack-audit-logs/repositories/slack-audit-log.repository';
import { EmittableSlackEvents } from '../../../../external/provider/constants/platform-events.constants';
import { ThenaAppsPlatformApiProvider } from '../../../../external/provider/thena-apps-platform-api.provider';
import { CUSTOM_LOGGER_TOKEN, ILogger } from '../../../../utils/logger';
import { OnMessageHandler } from '../../../core/on-message/index';
import { SlackEvent } from '../../../decorators';
import { BaseSlack<PERSON>vent<PERSON><PERSON><PERSON>, SlackEventMap } from '../../interface';
import { SlackSubTypeHandler } from './subtypes';

@Injectable()
@SlackEvent(['message'])
export class SlackMessageHandler extends BaseSlackEventHandler<'message'> {
  eventType = 'message' as const;
  private readonly SLACK_STATUS_PREFIX = 'slack:event:status:';

  constructor(
    @Inject(CUSTOM_LOGGER_TOKEN) private readonly logger: ILogger,
    private readonly onMessageHandler: OnMessageHandler,

    private readonly slackAuditLogRepository: SlackAuditLogRepository,

    // External API Providers
    private readonly thenaAppsPlatformApiProvider: ThenaAppsPlatformApiProvider,

    // Handlers
    private readonly slackSubTypeHandler: SlackSubTypeHandler,

    // Event deduplication service
    private readonly eventDeduplicationService: EventDeduplicationService,
  ) {
    super();
  }

  canHandle(event: SlackEventMap['message']): boolean {
    return 'text' in event.event && typeof event.event.text === 'string';
  }

  async handle(e: SlackEventMap['message']): Promise<void> {
    const { event, context } = e || {};
    const { installation, organization } = context || {};

    // Check if this event has already been processed
    const eventId = event.ts;
    const eventType = 'message';
    const installationId = installation.id;
    const organizationId = organization.id;

    // Create a namespace to prevent collisions between different installations/organizations
    const namespace = `slack:${installationId}:${organizationId}`;

    // Use the EventDeduplicationService to process the event idempotently
    await this.eventDeduplicationService.processIdempotently(
      eventId,
      eventType,
      async () => {
        // Process the message
        await this.processMessage(e);
        return;
      },
      namespace,
      this.SLACK_STATUS_PREFIX,
    );
  }

  /**
   * Process a message event
   * @param e The message event
   */
  private async processMessage(e: SlackEventMap['message']): Promise<void> {
    const { event, context } = e || {};
    const { installation, organization } = context || {};

    try {
      // Post the event to the platform
      try {
        await this.thenaAppsPlatformApiProvider.postEventsToPlatform(
          installation.organization,
          { ...event, type: EmittableSlackEvents.MESSAGE },
        );
      } catch (error) {
        if (error instanceof Error) {
          this.logger.error(
            `[ts=${event?.ts}] Error posting message event to the platform: ${error.message}`,
          );
        } else {
          console.error(
            `[ts=${event?.ts}] Error posting message event to the platform`,
            error,
          );
        }
      }

      // Get the user
      let eventUser: string | null = null;
      if ('user' in event) {
        eventUser = event.user;
      }

      // Get the thread ts
      let threadTs: string | null = null;
      if ('thread_ts' in event) {
        threadTs = event.thread_ts;
      }

      this.logger.log(
        `[ts=${event?.ts}] Received message event from user ${eventUser} with ts: ${event.ts}`,
      );

      // Record the audit log
      await this.slackAuditLogRepository.recordAuditLog({
        eventTs: event.event_ts,
        activityPerformedBy: eventUser,
        activity: `Received message event from user ${eventUser} with ts: ${event.ts}`,
        description: `Received message event from user ${eventUser} with ts: ${event.ts} for Installation ${installation.name} (${installation.teamId})`,
        slackTs: event.ts,
        threadTs,
        externalId: event.ts,

        // Base
        op: AuditLogOp.INFO,
        visibility: AuditLogVisibility.ORGANIZATION,
        installation,
        organization,
      });

      const eventsToFallover = ['file_share'];
      const isNonSubTypeHandler = eventsToFallover.includes(event.subtype);

      // If the message was posted by an integration/bot, skip it
      if (
        'subtype' in event &&
        typeof event.subtype === 'string' &&
        !isNonSubTypeHandler
      ) {
        const eventHandled = await this.slackSubTypeHandler.handle(e);
        if (eventHandled) {
          return;
        }
      }

      // If message has text
      if ('text' in event) {
        await this.onMessageHandler.onMessage(e);
      }

      return;
    } catch (error) {
      if (error instanceof Error) {
        // Get the user
        let eventUser: string | null = null;
        if ('user' in event) {
          eventUser = event?.user;
        }

        // Get the thread ts
        let threadTs: string | null = null;
        if ('thread_ts' in event) {
          threadTs = event?.thread_ts;
        }

        // Record the audit log
        await this.slackAuditLogRepository.recordAuditLog({
          eventTs: event?.event_ts,
          activityPerformedBy: eventUser,
          activity: `Received message event from user ${eventUser} with ts: ${event?.ts}`,
          description: `Received message event from user ${eventUser} with ts: ${event?.ts} for Installation ${installation?.name} (${installation?.teamId})`,
          slackTs: event?.ts,
          threadTs,
          externalId: event?.ts,

          // Base
          op: AuditLogOp.INFO,
          visibility: AuditLogVisibility.ORGANIZATION,
          installation,
          organization,
        });

        this.logger.error(
          `[ts=${event?.ts}] Error handling message event: ${error.message}`,
          error.stack,
        );
      }

      // TODO: Add some block or something to let the user or us know about this
    }
  }
}
