import { Inject, Injectable } from '@nestjs/common';
import { AuditLogOp, AuditLogVisibility } from '../../../../database/constants';
import { TransactionService } from '../../../../database/common';
import { ChannelsRepository } from '../../../../database/entities/channels/repositories';
import { EmittableSlackEvents } from '../../../../external/provider/constants/platform-events.constants';
import { ThenaAppsPlatformApiProvider } from '../../../../external/provider/thena-apps-platform-api.provider';
import { CUSTOM_LOGGER_TOKEN, ILogger } from '../../../../utils';
import { SlackEvent } from '../../../decorators';
import { SlackEventMap } from '../../interface';
import { BaseSlackEventHandler } from '../../interface';
import { SlackAuditLogRepository } from '../../../../database/entities/slack-audit-logs/repositories/slack-audit-log.repository';

@Injectable()
@SlackEvent('channel_archive')
export class SlackChannelArchivedHandler extends BaseSlackEventHandler<'channel_archive'> {
  eventType = 'channel_archive' as const;

  constructor(
    @Inject(CUSTOM_LOGGER_TOKEN) private readonly logger: ILogger,

    private readonly transactionService: TransactionService,
    private readonly channelsRepository: ChannelsRepository,

    private readonly thenaAppsPlatformApiProvider: ThenaAppsPlatformApiProvider,
    private readonly slackAuditLogRepository: SlackAuditLogRepository,
  ) {
    super();
  }

  canHandle(event: SlackEventMap['channel_archive']): boolean {
    return event.event.type === 'channel_archive';
  }

  async handle(e: SlackEventMap['channel_archive']): Promise<void> {
    try {
      // Check if the channel property is present
      if (!('channel' in e.event) || !this.canHandle(e)) {
        throw new Error(
          'Invalid event received in the `channel_archive` handler',
        );
      }

      const { context, event } = e || {};
      const { channel } = event;
      const { installation, organization } = context || {};

      // Check if the channel exists in our database
      const channelExists = await this.channelsRepository.exists({
        where: { channelId: channel, installation: { id: installation.id } },
      });
      
      // Record this activity
      await this.slackAuditLogRepository.recordAuditLog({
        eventTs: event.event_ts,
        activityPerformedBy: event.user,
        activity: `Channel archive event received for channel id: ${channel}`,
        description: `Channel archive event received for channel id: ${channel} for Installation ${installation.name} (${installation.teamId})`,
        externalId: channel,

        // Base
        op: AuditLogOp.INFO,
        visibility: AuditLogVisibility.ORGANIZATION,
        installation,
        organization,
      });

      // If the channel does not exist, we won't do anything
      if (!channelExists) {
        this.logger.debug(
          `[ChannelArchiveHandler] The channel ${channel} was not found for installation ${installation.id}`,
        );

        return;
      }

      // Perform the update in a transaction
      await this.transactionService.runInTransaction(async (txnContext) => {
        // Update the channel name
        await this.channelsRepository.updateWithTxn(
          txnContext,
          { channelId: channel, installation: { id: installation.id } },
          { isArchived: true, isBotActive: false, isBotJoined: false },
        );

        // Record this activity after successful archive
        await this.slackAuditLogRepository.recordAuditLog({
          eventTs: event.event_ts,
          activityPerformedBy: event.user,
          activity: `Channel archived successfully by ${event.user} channel id: ${channel}`,
          description: `Channel archived successfully by ${event.user} channel id: ${channel} for Installation ${installation.name} (${installation.teamId})`,
          externalId: channel,

          // Base
          op: AuditLogOp.UPDATED,
          visibility: AuditLogVisibility.ORGANIZATION,
          installation,
          organization,
        });
      });

      // Post the event to the platform
      await this.thenaAppsPlatformApiProvider.postEventsToPlatform(
        installation.organization,
        {
          ...event,
          type: EmittableSlackEvents.CHANNEL_ARCHIVED,
        },
      );
    } catch (error) {
      if (error instanceof Error) {
        this.logger.error(
          `[ChannelArchivedHandler] Failed to handle channel archived event, ${error.message}`,
          error.stack,
        );

        // Record error audit log
        await this.slackAuditLogRepository.recordAuditLog({
          eventTs: e && 'event_ts' in e.event ? e.event.event_ts as string : null,
          activityPerformedBy: e?.event?.user,
          activity: `Failed to archive channel id: ${e?.event?.channel}`,
          description: `Failed to archive channel id: ${e?.event?.channel} for Installation ${e?.context?.installation?.name} (${e?.context?.installation?.teamId}). Error: ${error.message}`,
          externalId: e?.event?.channel,

          // Base
          op: AuditLogOp.ERROR,
          visibility: AuditLogVisibility.ORGANIZATION,
          installation: e?.context?.installation,
          organization: e?.context?.organization,
        });
      } else {
        console.error(
          `[ChannelArchivedHandler] Failed to handle channel archived event, ${error}`,
        );
      }
    }
  }
}
