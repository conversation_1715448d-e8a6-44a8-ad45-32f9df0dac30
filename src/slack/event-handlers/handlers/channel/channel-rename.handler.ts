import { Inject, Injectable } from '@nestjs/common';
import { cloneDeep } from 'lodash';
import { get } from 'lodash';
import { TransactionService } from '../../../../database/common';
import { AuditLogOp, AuditLogVisibility } from '../../../../database/constants';
import { Channels } from '../../../../database/entities';
import { Installations } from '../../../../database/entities';
import { ChannelsRepository } from '../../../../database/entities/channels/repositories';
import { SlackAuditLogRepository } from '../../../../database/entities/slack-audit-logs/repositories/slack-audit-log.repository';
import { UpdateAccount } from '../../../../external/provider/interfaces/accounts.interface';
import { UpdateCustomObjectRecord } from '../../../../external/provider/interfaces/custom-objects.interface';
import { UpdateCustomerContactDTO } from '../../../../external/provider/interfaces/customer-contacts.interface';
import { ThenaPlatformApiProvider } from '../../../../external/provider/thena-platform-api.provider';
import { CUSTOM_LOGGER_TOKEN, ILogger } from '../../../../utils';
import { SlackEvent } from '../../../decorators';
import { SlackWebAPIService } from '../../../providers/slack-apis/slack-apis.service';
import { SlackChannelService } from '../../../services/slack-channel.service';
import { BaseSlackEventHandler, SlackEventMap } from '../../interface';

@Injectable()
@SlackEvent('channel_rename')
export class SlackChannelRenameHandler extends BaseSlackEventHandler<'channel_rename'> {
  eventType = 'channel_rename' as const;

  constructor(
    @Inject(CUSTOM_LOGGER_TOKEN) private readonly logger: ILogger,

    // Database repositories
    private readonly transactionService: TransactionService,
    private readonly channelsRepository: ChannelsRepository,
    private readonly slackAuditLogRepository: SlackAuditLogRepository,

    private readonly slackChannelService: SlackChannelService,

    private readonly slackWebAPIService: SlackWebAPIService,
    private readonly thenaPlatformApiProvider: ThenaPlatformApiProvider,
  ) {
    super();
  }

  canHandle(event: SlackEventMap['channel_rename']): boolean {
    return event.event.type === 'channel_rename';
  }

  async handle(e: SlackEventMap['channel_rename']): Promise<void> {
    try {
      // Check if the channel property is present
      if (!('channel' in e.event) || !this.canHandle(e)) {
        throw new Error(
          'Invalid event received in the `channel_rename` handler',
        );
      }

      const { context, event } = e;
      const { channel } = event;
      const { installation, organization } = context;

      const newName = channel.name;

      // Record this activity when event is received
      await this.slackAuditLogRepository.recordAuditLog({
        eventTs: event.event_ts,
        activity: `Channel rename event received for channel id: ${channel.id}`,
        description: `Channel rename event received for channel id: ${channel.id} with new name "${newName}" for Installation ${installation.name} (${installation.teamId})`,
        externalId: channel.id,

        // Base
        op: AuditLogOp.INFO,
        visibility: AuditLogVisibility.ORGANIZATION,
        installation,
        organization,
      });

      // Check if the channel exists in our database
      const foundChannel = await this.channelsRepository.findByCondition({
        where: { channelId: channel.id, installation: { id: installation.id } },
      });
      // If the channel does not exist, we won't do anything
      if (!foundChannel) {
        this.logger.debug(
          `[ChannelRenameHandler] The channel ${channel.id} was not found for installation ${installation.id}`,
        );

        return;
      }

      let accountsToUpdate: Array<{ id: string; data: UpdateAccount }> = [];
      let accountCustomObjectRecordsToUpdate: UpdateCustomObjectRecord[] = [];
      let contactUpdates: Array<{
        id: string;
        data: UpdateCustomerContactDTO;
      }> = [];
      let contactCustomObjectRecordsToUpdate: UpdateCustomObjectRecord[] = [];

      if (foundChannel.isShared) {
        const customers =
          await this.slackChannelService.getAllCustomersFromChannel(
            installation,
            foundChannel,
          );

        const platformContactIds = customers.map(
          (c) => c.platformDump?.customerContactId,
        );

        const platformContactRecordIds = customers.flatMap(
          (c) => c.platformDump?.customObjectRecordIds,
        );

        const processContactsResult = await this.processContacts(
          installation,
          foundChannel,
          newName,
          platformContactIds,
          platformContactRecordIds,
        );

        contactUpdates = processContactsResult.contactUpdates;
        contactCustomObjectRecordsToUpdate =
          processContactsResult.customObjectRecordsToUpdate;

        // Get all accounts from channel platform dump
        const accountIds = foundChannel.platformDump.accounts.map(
          (a) => a.accountId,
        );

        // Get all custom object record IDs from channel platform dump
        const customObjectRecordIds =
          foundChannel.platformDump.accounts.flatMap(
            (a) => a.customObjectRecordIds,
          );

        const processAccountsResult = await this.processAccounts(
          installation,
          foundChannel,
          newName,
          accountIds,
          customObjectRecordIds,
        );

        accountsToUpdate = processAccountsResult.accountsToUpdate;
        accountCustomObjectRecordsToUpdate =
          processAccountsResult.customObjectRecordsToUpdate;
      }

      // Perform the update in a transaction
      await this.transactionService.runInTransaction(async (txnContext) => {
        // Update the channel name
        await this.channelsRepository.updateWithTxn(
          txnContext,
          { channelId: channel.id, installation: { id: installation.id } },
          { name: newName },
        );

        // Record this activity after successful update
        await this.slackAuditLogRepository.recordAuditLog({
          eventTs: event.event_ts,
          activity: `Channel renamed successfully from "${foundChannel.name}" to "${newName}" for channel id: ${channel.id}`,
          description: `Channel renamed successfully from "${foundChannel.name}" to "${newName}" for channel id: ${channel.id} for Installation ${installation.name} (${installation.teamId})`,
          externalId: channel.id,

          // Base
          op: AuditLogOp.UPDATED,
          visibility: AuditLogVisibility.ORGANIZATION,
          installation,
          organization,
        });
      });

      // Update the accounts
      try {
        await Promise.all(
          accountsToUpdate.map((account) =>
            this.thenaPlatformApiProvider.updateAccount(
              installation,
              account.id,
              account.data,
            ),
          ),
        );
      } catch (error) {
        this.logger.error(
          `[ChannelRenameHandler] Failed to update accounts: ${error}`,
        );
      }

      // Delete the custom object records
      try {
        await Promise.all(
          accountCustomObjectRecordsToUpdate.map((update) =>
            this.thenaPlatformApiProvider.updateCustomObjectRecord(
              installation,
              update,
            ),
          ),
        );
      } catch (error) {
        this.logger.error(
          `[ChannelRenameHandler] Failed to update account custom object records: ${error}`,
        );
      }

      // Update the contacts
      try {
        await Promise.all(
          contactUpdates.map((contact) =>
            this.thenaPlatformApiProvider.updateCustomerContact(
              installation,
              contact.id,
              contact.data,
            ),
          ),
        );
      } catch (error) {
        this.logger.error(
          `[ChannelRenameHandler] Failed to update contacts: ${error}`,
        );
      }

      // Update the custom object records
      try {
        await Promise.all(
          contactCustomObjectRecordsToUpdate.map((update) =>
            this.thenaPlatformApiProvider.updateCustomObjectRecord(
              installation,
              update,
            ),
          ),
        );
      } catch (error) {
        this.logger.error(
          `[ChannelRenameHandler] Failed to update customer contact custom object records: ${error}`,
        );
      }
    } catch (error) {
      if (error instanceof Error) {
        this.logger.error(
          `[ChannelRenameHandler] Failed to handle channel rename event, ${error.message}`,
          error.stack,
        );

        // Record error audit log
        await this.slackAuditLogRepository.recordAuditLog({
          eventTs: e && 'event_ts' in e.event ? e.event.event_ts as string : null,
          activity: `Failed to rename channel id: ${e?.event?.channel?.id}`,
          description: `Failed to rename channel id: ${e?.event?.channel?.id} for Installation ${e?.context?.installation?.name} (${e?.context?.installation?.teamId}). Error: ${error.message}`,
          externalId: e?.event?.channel?.id,

          // Base
          op: AuditLogOp.ERROR,
          visibility: AuditLogVisibility.ORGANIZATION,
          installation: e?.context?.installation,
          organization: e?.context?.organization,
        });
      } else {
        console.error(
          `[ChannelRenameHandler] Failed to handle channel rename event, ${error}`,
        );
      }
    }
  }

  private async processAccounts(
    installation: Installations,
    channel: Channels,
    newName: string,
    accountIds: string[],
    customObjectRecordIds: string[],
  ) {
    const accountsToUpdate: Array<{ id: string; data: UpdateAccount }> = [];
    const customObjectRecordsToUpdate: UpdateCustomObjectRecord[] = [];

    const accountCustomObjectId = get(
      installation.platformDump,
      'customObjects.accountCustomObjectId',
    );
    const slackChannelIdCustomFieldId = get(
      installation.platformDump,
      'customFields.slackChannelId',
    );
    const slackChannelNameCustomFieldId = get(
      installation.platformDump,
      'customFields.slackChannelName',
    );

    if (!accountIds.length) {
      this.logger.error(
        `[ChannelRenameHandler] No accounts found for channel ${channel.channelId}`,
      );

      return { accountsToUpdate, customObjectRecordsToUpdate };
    }

    // Get all accounts from the platform
    const accounts = await this.thenaPlatformApiProvider.getAccountsByIds(
      installation,
      accountIds,
    );

    // Prepare account updates
    for (const account of accounts) {
      const newMetadata = cloneDeep(account.metadata);

      if (get(newMetadata, 'sinks.slack.channels', [])) {
        for (const c of newMetadata.sinks.slack.channels) {
          if (c.channelId === channel.channelId) {
            c.channelName = newName;
          }
        }

        accountsToUpdate.push({
          id: account.id,
          data: { metadata: newMetadata },
        });
      }
    }

    // Get all custom object records from the platform
    const customObjectRecords =
      await this.thenaPlatformApiProvider.getCustomObjectRecordsByIds(
        installation,
        accountCustomObjectId,
        customObjectRecordIds,
      );

    for (const customObjectRecord of customObjectRecords) {
      const slackChannelCFValue = customObjectRecord.customFieldValues.find(
        (c) => c.customFieldId === slackChannelIdCustomFieldId,
      );

      if (
        !slackChannelCFValue ||
        !slackChannelCFValue.data ||
        !Array.isArray(slackChannelCFValue.data) ||
        slackChannelCFValue.data.length === 0
      ) {
        continue;
      }

      if (slackChannelCFValue.data[0].value === channel.channelId) {
        customObjectRecordsToUpdate.push({
          customObjectId: accountCustomObjectId,
          customObjectRecordId: customObjectRecord.id,
          customFieldValues: [
            {
              customFieldId: slackChannelNameCustomFieldId,
              data: [{ value: newName }],
            },
          ],
        });
      }
    }

    return { accountsToUpdate, customObjectRecordsToUpdate };
  }

  private async processContacts(
    installation: Installations,
    channel: Channels,
    newName: string,
    contactIds: string[],
    customObjectRecordIds: string[],
  ) {
    const contactUpdates: Array<{
      id: string;
      data: UpdateCustomerContactDTO;
    }> = [];
    const customObjectRecordsToUpdate: UpdateCustomObjectRecord[] = [];

    const contactCustomObjectId = get(
      installation.platformDump,
      'customObjects.contactCustomObjectId',
    );
    const slackChannelIdCustomFieldId = get(
      installation.platformDump,
      'customFields.slackChannelId',
    );
    const slackChannelNameCustomFieldId = get(
      installation.platformDump,
      'customFields.slackChannelName',
    );

    if (!contactIds.length) {
      this.logger.error(
        `[ChannelRenameHandler] No contacts found for channel ${channel.channelId}`,
      );

      return { contactUpdates, customObjectRecordsToUpdate };
    }

    // Get all platform contacts
    const platformContacts =
      await this.thenaPlatformApiProvider.getCustomerContactsByIds(
        installation,
        contactIds,
      );

    // Prepare contact updates
    for (const contact of platformContacts) {
      const newMetadata = cloneDeep(contact.metadata);

      if (get(newMetadata, 'sinks.slack.channels', [])) {
        for (const c of newMetadata.sinks.slack.channels) {
          if (c.channelId === channel.channelId) {
            c.channelName = newName;
          }
        }

        contactUpdates.push({
          id: contact.id,
          data: { metadata: newMetadata },
        });
      }
    }

    // Get all custom object records from the platform
    const customObjectRecords =
      await this.thenaPlatformApiProvider.getCustomObjectRecordsByIds(
        installation,
        contactCustomObjectId,
        customObjectRecordIds,
      );

    for (const customObjectRecord of customObjectRecords) {
      const slackChannelCFValue = customObjectRecord.customFieldValues.find(
        (c) => c.customFieldId === slackChannelIdCustomFieldId,
      );

      if (
        !slackChannelCFValue ||
        !slackChannelCFValue.data ||
        !Array.isArray(slackChannelCFValue.data) ||
        slackChannelCFValue.data.length === 0
      ) {
        continue;
      }

      if (slackChannelCFValue.data[0].value === channel.channelId) {
        customObjectRecordsToUpdate.push({
          customObjectId: contactCustomObjectId,
          customObjectRecordId: customObjectRecord.id,
          customFieldValues: [
            {
              customFieldId: slackChannelNameCustomFieldId,
              data: [{ value: newName }],
            },
          ],
        });
      }
    }

    return {
      contactUpdates,
      customObjectRecordsToUpdate,
    };
  }
}
