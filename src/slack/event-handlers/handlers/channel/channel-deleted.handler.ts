import { Inject, Injectable } from '@nestjs/common';
import { InjectRepository } from '@nestjs/typeorm';
import { cloneDeep, get } from 'lodash';
import { In, Repository } from 'typeorm';
import { TransactionService } from '../../../../database/common';
import { AuditLogOp, AuditLogVisibility } from '../../../../database/constants';
import {
  Channels,
  CustomerContacts,
  Installations,
} from '../../../../database/entities';
import { ChannelsRepository } from '../../../../database/entities/channels/repositories';
import { SlackAuditLogRepository } from '../../../../database/entities/slack-audit-logs/repositories/slack-audit-log.repository';
import { EmittableSlackEvents } from '../../../../external/provider/constants/platform-events.constants';
import { UpdateAccount } from '../../../../external/provider/interfaces';
import { UpdateCustomerContactDTO } from '../../../../external/provider/interfaces/customer-contacts.interface';
import { ThenaAppsPlatformApiProvider } from '../../../../external/provider/thena-apps-platform-api.provider';
import { ThenaPlatformApiProvider } from '../../../../external/provider/thena-platform-api.provider';
import { CUSTOM_LOGGER_TOKEN, ILogger } from '../../../../utils';
import { SlackEvent } from '../../../decorators';
import { SlackChannelService } from '../../../services/slack-channel.service';
import { BaseSlackEventHandler, SlackEventMap } from '../../interface';

@Injectable()
@SlackEvent('channel_deleted')
export class SlackChannelDeletedHandler extends BaseSlackEventHandler<'channel_deleted'> {
  eventType = 'channel_deleted' as const;

  constructor(
    @Inject(CUSTOM_LOGGER_TOKEN) private readonly logger: ILogger,

    private readonly transactionService: TransactionService,
    private readonly channelsRepository: ChannelsRepository,
    @InjectRepository(CustomerContacts)
    private readonly customerContactsRepository: Repository<CustomerContacts>,

    private readonly slackChannelService: SlackChannelService,
    private readonly slackAuditLogRepository: SlackAuditLogRepository,

    private readonly thenaPlatformApiProvider: ThenaPlatformApiProvider,
    private readonly thenaAppsPlatformApiProvider: ThenaAppsPlatformApiProvider,
  ) {
    super();
  }

  canHandle(event: SlackEventMap['channel_deleted']): boolean {
    return event.event.type === 'channel_deleted';
  }

  async handle(e: SlackEventMap['channel_deleted']): Promise<void> {
    try {
      // Check if the channel property is present
      if (!('channel' in e.event) || !this.canHandle(e)) {
        throw new Error(
          'Invalid event received in the `channel_deleted` handler',
        );
      }

      const { context, event } = e || {};
      const { channel } = event;
      const { installation, organization } = context || {};
      
      let eventTs: string | null = null;
      if ('event_ts' in event) {
        eventTs = event.event_ts as string;
      }

      // Record this activity
      await this.slackAuditLogRepository.recordAuditLog({
        eventTs,
        activity: `Channel deleted channel id: ${channel}`,
        description: `Channel deleted channel id: ${event.channel} for Installation ${installation.name} (${installation.teamId})`,
        externalId: channel,

        // Base
        op: AuditLogOp.INFO,
        visibility: AuditLogVisibility.ORGANIZATION,
        installation,
        organization,
      });

      // Find the channel in our database
      const foundChannel = await this.channelsRepository.findByCondition({
        where: {
          channelId: channel,
          installation: { id: installation.id },
        },
      });

      // We won't throw an error if the channel was not found in our workspace
      if (!foundChannel) {
        this.logger.debug(
          `[ChannelDeletedHandler] The deleted channel ${channel} was not found for installation ${installation.id}`,
        );

        return;
      }

      let accountsToUpdate: Array<{ id: string; data: UpdateAccount }> = [];
      let accountCustomObjectRecordsToDelete: string[] = [];
      let contactUpdates: Array<{
        id: string;
        data: UpdateCustomerContactDTO;
      }> = [];
      let existingCustomers: CustomerContacts[] = [];
      let contactCustomObjectRecordsToDelete: string[] = [];

      const accountCustomObjectId = get(
        installation.platformDump,
        'customObjects.accountCustomObjectId',
      );

      const contactCustomObjectId = get(
        installation.platformDump,
        'customObjects.contactCustomObjectId',
      );

      if (foundChannel.isShared) {
        const customers =
          await this.slackChannelService.getAllCustomersFromChannel(
            installation,
            foundChannel,
          );

        existingCustomers = await this.customerContactsRepository.find({
          where: {
            id: In(customers.map((c) => c.id)),
          },
          relations: ['channels'],
        });

        const platformContactIds = existingCustomers.map(
          (c) => c.platformDump?.customerContactId,
        );

        const platformContactRecordIds = existingCustomers.flatMap(
          (c) => c.platformDump?.customObjectRecordIds,
        );

        const processContactsResult = await this.processContacts(
          installation,
          foundChannel,
          platformContactIds,
          platformContactRecordIds,
        );

        contactUpdates = processContactsResult.contactUpdates;
        contactCustomObjectRecordsToDelete =
          processContactsResult.customObjectRecordsToDelete;

        // Get all accounts from channel platform dump
        const accountIds = foundChannel.platformDump.accounts.map(
          (a) => a.accountId,
        );

        // Get all custom object record IDs from channel platform dump
        const customObjectRecordIds =
          foundChannel.platformDump.accounts.flatMap(
            (a) => a.customObjectRecordIds,
          );

        const processAccountsResult = await this.processAccounts(
          installation,
          foundChannel,
          accountIds,
          customObjectRecordIds,
        );

        accountsToUpdate = processAccountsResult.accountsToUpdate;
        accountCustomObjectRecordsToDelete =
          processAccountsResult.customObjectRecordsToDelete;
      }

      // Update the channel to be deleted
      await this.transactionService.runInTransaction(async (txnContext) => {
        // Update platform dump to remove deleted custom object records
        const updatedPlatformDump = { ...foundChannel.platformDump };

        // Remove account custom object records
        if (accountCustomObjectRecordsToDelete.length > 0) {
          updatedPlatformDump.accounts = updatedPlatformDump.accounts.map(
            (account) => ({
              ...account,
              customObjectRecordIds: account.customObjectRecordIds.filter(
                (id) => !accountCustomObjectRecordsToDelete.includes(id),
              ),
            }),
          );
        }

        await this.channelsRepository.updateWithTxn(
          txnContext,
          { id: foundChannel.id },
          {
            slackDeletedAt: Date.now().toString(),
            isBotActive: false,
            isBotJoined: false,
            platformDump: updatedPlatformDump,
          },
        );

        // Remove channel from all customer contacts
        await Promise.all(
          existingCustomers.map((customer) => {
            customer.channels = customer.channels.filter(
              (c) => c.channelId !== foundChannel.channelId,
            );

            return this.customerContactsRepository.save(customer);
          }),
        );

        // Record this activity after successful deletion
        await this.slackAuditLogRepository.recordAuditLog({
          eventTs,
          activity: `Channel deleted successfully for channel id: ${channel}`,
          description: `Channel deleted successfully for channel id: ${channel} for Installation ${installation.name} (${installation.teamId})`,
          externalId: channel,

          // Base
          op: AuditLogOp.DELETED,
          visibility: AuditLogVisibility.ORGANIZATION,
          installation,
          organization,
        });

        // Post the event to the platform
        await this.thenaAppsPlatformApiProvider.postEventsToPlatform(
          installation.organization,
          {
            ...event,
            type: EmittableSlackEvents.CHANNEL_DELETED,
          },
        );
      });

      // Update the accounts
      try {
        await Promise.all(
          accountsToUpdate.map((account) =>
            this.thenaPlatformApiProvider.updateAccount(
              installation,
              account.id,
              account.data,
            ),
          ),
        );
      } catch (error) {
        this.logger.error(`Failed to update accounts: ${error}`);
      }

      // Update the contacts
      try {
        await Promise.all(
          contactUpdates.map((contact) =>
            this.thenaPlatformApiProvider.updateCustomerContact(
              installation,
              contact.id,
              contact.data,
            ),
          ),
        );
      } catch (error) {
        this.logger.error(`Failed to update contacts: ${error}`);
      }

      // Delete the custom object records
      try {
        await Promise.all(
          accountCustomObjectRecordsToDelete.map((id) =>
            this.thenaPlatformApiProvider.deleteCustomObjectRecord(
              installation,
              accountCustomObjectId,
              id,
            ),
          ),
        );
      } catch (error) {
        this.logger.error(
          `Failed to delete account custom object records: ${error}`,
        );
      }

      // Delete the contact custom object records
      try {
        await Promise.all(
          contactCustomObjectRecordsToDelete.map((id) =>
            this.thenaPlatformApiProvider.deleteCustomObjectRecord(
              installation,
              contactCustomObjectId,
              id,
            ),
          ),
        );
      } catch (error) {
        this.logger.error(
          `Failed to delete contact custom object records: ${error}`,
        );
      }

      return;
    } catch (error) {
      if (error instanceof Error) {
        this.logger.error(
          `[ChannelDeletedHandler] Failed to handle channel deleted event, ${error.message}`,
          error.stack,
        );

        // Record error audit log
        await this.slackAuditLogRepository.recordAuditLog({
          eventTs: e && 'event_ts' in e.event ? e.event.event_ts as string : null,
          activity: `Failed to delete channel id: ${e?.event?.channel}`,
          description: `Failed to delete channel id: ${e?.event?.channel} for Installation ${e?.context?.installation?.name} (${e?.context?.installation?.teamId}). Error: ${error.message}`,
          externalId: e?.event?.channel,

          // Base
          op: AuditLogOp.ERROR,
          visibility: AuditLogVisibility.ORGANIZATION,
          installation: e?.context?.installation,
          organization: e?.context?.organization,
        });
      } else {
        console.error(
          `[ChannelDeletedHandler] Failed to handle channel deleted event, ${error}`,
        );
      }
    }
  }

  private async processAccounts(
    installation: Installations,
    channel: Channels,
    accountIds: string[],
    customObjectRecordIds: string[],
  ) {
    const accountsToUpdate: Array<{ id: string; data: UpdateAccount }> = [];
    const customObjectRecordsToDelete: string[] = [];

    const accountCustomObjectId = get(
      installation.platformDump,
      'customObjects.accountCustomObjectId',
    );

    const slackChannelIdCustomFieldId = get(
      installation.platformDump,
      'customFields.slackChannelId',
    );

    if (!accountIds.length) {
      this.logger.error(
        `[ChannelDeletedHandler] No accounts found for channel ${channel.channelId}`,
      );

      return { accountsToUpdate, customObjectRecordsToDelete };
    }

    // Get all accounts from the platform
    const accounts = await this.thenaPlatformApiProvider.getAccountsByIds(
      installation,
      accountIds,
    );

    // Prepare account updates
    for (const account of accounts) {
      const newMetadata = cloneDeep(account.metadata);

      if (get(newMetadata, 'sinks.slack.channels', [])) {
        newMetadata.sinks.slack.channels =
          newMetadata.sinks.slack.channels.filter(
            (c) => c.channelId !== channel.channelId,
          );

        accountsToUpdate.push({
          id: account.id,
          data: { metadata: newMetadata },
        });
      }
    }

    // Get all custom object records from the platform
    const customObjectRecords =
      await this.thenaPlatformApiProvider.getCustomObjectRecordsByIds(
        installation,
        accountCustomObjectId,
        customObjectRecordIds,
      );

    for (const customObjectRecord of customObjectRecords) {
      const slackChannelCFValue = customObjectRecord.customFieldValues.find(
        (c) => c.customFieldId === slackChannelIdCustomFieldId,
      );

      if (
        !slackChannelCFValue ||
        !slackChannelCFValue.data ||
        !Array.isArray(slackChannelCFValue.data) ||
        slackChannelCFValue.data.length === 0
      ) {
        continue;
      }

      if (slackChannelCFValue.data[0].value === channel.channelId) {
        customObjectRecordsToDelete.push(customObjectRecord.id);
      }
    }

    return { accountsToUpdate, customObjectRecordsToDelete };
  }

  private async processContacts(
    installation: Installations,
    channel: Channels,
    contactIds: string[],
    customObjectRecordIds: string[],
  ) {
    const contactUpdates: Array<{
      id: string;
      data: UpdateCustomerContactDTO;
    }> = [];
    const customObjectRecordsToDelete: string[] = [];

    const contactCustomObjectId = get(
      installation.platformDump,
      'customObjects.contactCustomObjectId',
    );

    const slackChannelIdCustomFieldId = get(
      installation.platformDump,
      'customFields.slackChannelId',
    );

    if (!contactIds.length) {
      this.logger.error(
        `[ChannelDeletedHandler] No contacts found for channel ${channel.channelId}`,
      );

      return { contactUpdates, customObjectRecordsToDelete };
    }

    // Get all platform contacts
    const platformContacts =
      await this.thenaPlatformApiProvider.getCustomerContactsByIds(
        installation,
        contactIds,
      );

    // Prepare contact updates
    for (const contact of platformContacts) {
      const newMetadata = cloneDeep(contact.metadata);

      if (get(newMetadata, 'sinks.slack.channels', [])) {
        newMetadata.sinks.slack.channels =
          newMetadata.sinks.slack.channels.filter(
            (c) => c.channelId !== channel.channelId,
          );

        contactUpdates.push({
          id: contact.id,
          data: { metadata: newMetadata },
        });
      }
    }

    // Get all custom object records from the platform
    const customObjectRecords =
      await this.thenaPlatformApiProvider.getCustomObjectRecordsByIds(
        installation,
        contactCustomObjectId,
        customObjectRecordIds,
      );

    for (const customObjectRecord of customObjectRecords) {
      const slackChannelCFValue = customObjectRecord.customFieldValues.find(
        (c) => c.customFieldId === slackChannelIdCustomFieldId,
      );

      if (
        !slackChannelCFValue ||
        !slackChannelCFValue.data ||
        !Array.isArray(slackChannelCFValue.data) ||
        slackChannelCFValue.data.length === 0
      ) {
        continue;
      }

      if (slackChannelCFValue.data[0].value === channel.channelId) {
        customObjectRecordsToDelete.push(customObjectRecord.id);
      }
    }

    return { contactUpdates, customObjectRecordsToDelete };
  }
}
