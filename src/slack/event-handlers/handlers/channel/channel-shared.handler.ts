import { Inject, Injectable } from '@nestjs/common';
import { Team } from '@slack/web-api/dist/types/response/TeamInfoResponse';
import { cloneDeep, get, merge } from 'lodash';
import { TransactionService } from '../../../../database/common';
import { Channels, Installations } from '../../../../database/entities';
import { ChannelsRepository } from '../../../../database/entities/channels/repositories';
import {
  Account,
  CreateAccount,
} from '../../../../external/provider/interfaces';
import { ThenaPlatformApiProvider } from '../../../../external/provider/thena-platform-api.provider';
import { CUSTOM_LOGGER_TOKEN, ILogger } from '../../../../utils';
import { SlackEvent } from '../../../decorators';
import { SlackExternalUsersSyncJob } from '../../../processors/jobs';
import { SlackWebAPIService } from '../../../providers/slack-apis/slack-apis.service';
import { BaseSlackEventHandler, SlackEventMap } from '../../interface';

@Injectable()
@SlackEvent('channel_shared')
export class SlackChannelSharedHandler extends BaseSlackEventHandler<'channel_shared'> {
  eventType = 'channel_shared' as const;

  constructor(
    @Inject(CUSTOM_LOGGER_TOKEN) private readonly logger: ILogger,

    private readonly transactionService: TransactionService,
    private readonly channelsRepository: ChannelsRepository,

    private readonly slackWebApiService: SlackWebAPIService,
    private readonly platformApiProvider: ThenaPlatformApiProvider,
    private readonly slackExternalUsersSyncJob: SlackExternalUsersSyncJob,
  ) {
    super();
  }

  canHandle(event: SlackEventMap['channel_shared']): boolean {
    return event.event.type === 'channel_shared';
  }

  async handle(e: SlackEventMap['channel_shared']): Promise<void> {
    try {
      // Check if the channel property is present
      if (!('channel' in e.event) || !this.canHandle(e)) {
        throw new Error(
          'Invalid event received in the `channel_shared` handler',
        );
      }

      const { context, event } = e;
      const { channel } = event;
      const { installation } = context;

      // Get the conversation details from Slack WebAPI
      const conversationResponse =
        await this.slackWebApiService.getConversationInfo(
          installation.botToken,
          {
            channel: channel,
          },
        );

      // If the conversation is not found, throw an error
      if (!conversationResponse.ok) {
        this.logger.error(
          `[ChannelSharedHandler] Failed to get conversation info for channel ${channel}, ${conversationResponse.error}`,
        );

        throw new Error(conversationResponse.error);
      }

      const { channel: conversation } = conversationResponse;

      // Get the channel data from the database
      const channelData = await this.channelsRepository.findByCondition({
        where: {
          channelId: channel,
          installation: { id: installation.id },
        },
      });

      if (!channelData) {
        this.logger.error(
          `[ChannelSharedHandler] Failed to find channel data for channel ${channel}, ${installation.id}`,
        );

        throw new Error('Channel not found');
      }

      let updatedPlatformDump = channelData.platformDump;

      // Update the channel details
      await this.transactionService.runInTransaction(async (txnContext) => {
        await this.channelsRepository.updateWithTxn(
          txnContext,
          { channelId: channel, installation: { id: installation.id } },
          {
            isShared: true,
            channelDump: conversation as Record<string, any>,
            sharedTeamIds: Array.from(updatedSharedTeamIds),
            platformDump: updatedPlatformDump,
          },
        );
      });

      // Sync the channel members
      this.syncChannelMembers(installation, [channelData]);

      // Get connected team info
      const teamInfo = await this.slackWebApiService.getTeamInfo(
        installation.botToken,
        { team: event.connected_team_id },
      );
      if (!teamInfo.ok) {
        this.logger.error(
          `[ChannelSharedHandler] Failed to get team info for team ${event.connected_team_id}, ${teamInfo.error}`,
        );
        throw new Error(teamInfo.error);
      }
      const { team: connectedTeam } = teamInfo;

      const updatedSharedTeamIds = new Set(channelData.sharedTeamIds);
      updatedSharedTeamIds.add(event.connected_team_id);

      // Info for the account sync with platform
      const { accountToCreate, accountToUpdate } =
        await this.prepareForAccountSync(
          installation,
          connectedTeam,
          channelData,
        );

      const { customFields, customObjects } = installation.platformDump;

      // Update the channel details in transaction
      let newAccount: Account | null = null;

      if (accountToCreate) {
        // Create or update the account
        newAccount = await this.platformApiProvider.createAccount(
          installation,
          accountToCreate,
        );
      }
      if (accountToUpdate) {
        newAccount = await this.platformApiProvider.updateAccount(
          installation,
          accountToUpdate.id,
          accountToUpdate.data,
        );
      }

      // create a custom object record for the channel
      if (newAccount) {
        // create a custom object record for the channel
        const createdCustomObjectRecord =
          await this.platformApiProvider.createCustomObjectRecord(
            installation,
            {
              customObjectId: customObjects.accountCustomObjectId,
              customFieldValues: [
                {
                  customFieldId: customFields.accountId,
                  data: [{ value: newAccount?.id }],
                },
                {
                  customFieldId: customFields.slackChannelId,
                  data: [{ value: channelData.channelId }],
                },
                {
                  customFieldId: customFields.slackChannelName,
                  data: [{ value: channelData.name }],
                },
                {
                  customFieldId: customFields.slackTeamId,
                  data: [{ value: event.connected_team_id }],
                },
                {
                  customFieldId: customFields.slackTeamName,
                  data: [{ value: connectedTeam.name }],
                },
              ],
            },
          );

        // Update the platform dump
        const accountsData = get(updatedPlatformDump, 'accounts', []);

        if (
          accountsData.find((account) => account.accountId === newAccount.id)
        ) {
          updatedPlatformDump.accounts = accountsData.map((account) => {
            if (account.accountId === newAccount.id) {
              return {
                ...account,
                customObjectRecordIds: [
                  ...account.customObjectRecordIds,
                  createdCustomObjectRecord.id,
                ],
              };
            }

            return account;
          });
        } else {
          updatedPlatformDump.accounts = [
            ...accountsData,
            {
              accountId: newAccount.id,
              customObjectRecordIds: [createdCustomObjectRecord.id],
            },
          ];
        }

        updatedPlatformDump = {
          ...updatedPlatformDump,
          accounts: accountsData,
        };
      }
    } catch (error) {
      if (error instanceof Error) {
        this.logger.error(
          '[ChannelSharedHandler] Failed to handle channel shared event',
          error.stack,
        );
      } else {
        console.error(
          '[ChannelSharedHandler] Failed to handle channel shared event',
          error,
        );
      }
    }
  }

  private async syncChannelMembers(
    installation: Installations,
    channels: Channels[],
  ) {
    try {
      await this.slackExternalUsersSyncJob.execute(installation, channels);
    } catch (error) {
      if (error instanceof Error) {
        this.logger.error(
          `[ChannelSharedHandler] Failed to sync channel members for channel ${channels.map((c) => c.channelId).join(', ')}, ${installation.id}`,
          error.stack,
        );
      } else {
        console.error(
          `[ChannelSharedHandler] Failed to sync channel members for channel ${channels.map((c) => c.channelId).join(', ')}, ${installation.id}`,
          error,
        );
      }
    }
  }

  /**
   * Sync the account with the platform
   * @param installation The installation
   * @param team The team
   * @param channel The channel
   * @returns The account to create/update
   */
  private async prepareForAccountSync(
    installation: Installations,
    team: Team,
    channel: Channels,
  ) {
    // Get the existing account
    const existingAccounts =
      await this.platformApiProvider.getAccountsByDomains(installation, [
        team.domain,
      ]);

    if (existingAccounts.length === 1) {
      // Update the account
      const accountToUpdate = existingAccounts[0];
      let newMetadata = cloneDeep(accountToUpdate.metadata);

      if (get(newMetadata, 'sinks.slack.syncStatus', '') === 'success') {
        newMetadata.sinks.slack.channels.push({
          teamId: team.id,
          teamName: team.name,
          channelId: channel.channelId,
          channelName: channel.name,
        });

        newMetadata.sinks.slack.lastSyncedAt = new Date().toISOString();
        return {
          accountToUpdate: {
            id: accountToUpdate.id,
            data: { metadata: newMetadata },
          },
        };
      }

      newMetadata = merge(newMetadata, {
        sinks: {
          slack: {
            syncStatus: 'success',
            lastSyncedAt: new Date().toISOString(),
            channels: [
              {
                teamId: team.id,
                teamName: team.name,
                channelId: channel.channelId,
                channelName: channel.name,
              },
            ],
          },
        },
      });

      return {
        accountToCreate: null,
        accountToUpdate: {
          id: accountToUpdate.id,
          data: { metadata: newMetadata },
        },
      };
    }

    if (existingAccounts.length > 1) {
      // skip the account sync
      return {
        accountToCreate: null,
        accountsToUpdate: null,
      };
    }

    // Create the account
    const accountToCreate: CreateAccount = {
      name: team.name,
      primaryDomain: team.domain,
      logo: team.icon.image_102,
      source: 'slack',
      metadata: {
        sinks: {
          slack: {
            channels: [
              {
                teamId: team.id,
                teamName: team.name,
                channelId: channel.channelId,
                channelName: channel.name,
              },
            ],
          },
        },
      },
    };

    return {
      accountToCreate,
      accountsToUpdate: null,
    };
  }
}
