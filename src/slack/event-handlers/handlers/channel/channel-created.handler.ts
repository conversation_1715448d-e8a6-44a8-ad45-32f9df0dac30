import { Inject, Injectable } from '@nestjs/common';
import { TransactionService } from '../../../../database/common';
import { AuditLogOp, AuditLogVisibility } from '../../../../database/constants';
import { ChannelType } from '../../../../database/entities/channels/channels.entity';
import { ChannelsRepository } from '../../../../database/entities/channels/repositories';
import { SlackAuditLogRepository } from '../../../../database/entities/slack-audit-logs/repositories/slack-audit-log.repository';
import { EmittableSlackEvents } from '../../../../external/provider/constants/platform-events.constants';
import { ThenaAppsPlatformApiProvider } from '../../../../external/provider/thena-apps-platform-api.provider';
import {
  CUSTOM_LOGGER_TOKEN,
  ILogger,
  safeJsonStringify,
} from '../../../../utils';
import { SlackEvent } from '../../../decorators';
import { SlackWebAPIService } from '../../../providers/slack-apis/slack-apis.service';
import { BaseSlackEventHandler, SlackEventMap } from '../../interface';
@Injectable()
@SlackEvent('channel_created')
export class SlackChannelCreatedHandler extends BaseSlackEventHandler<'channel_created'> {
  eventType = 'channel_created' as const;

  constructor(
    @Inject(CUSTOM_LOGGER_TOKEN) private readonly logger: ILogger,

    // Utility Services
    private readonly transactionService: TransactionService,

    // Database Repositories
    private readonly channelsRepository: ChannelsRepository,
    private readonly slackAuditLogRepository: SlackAuditLogRepository,

    // External API Providers
    private readonly slackWebApiService: SlackWebAPIService,
    private readonly thenaAppsPlatformApiProvider: ThenaAppsPlatformApiProvider,
  ) {
    super();
  }

  canHandle(event: SlackEventMap['channel_created']): boolean {
    return event.event.type === 'channel_created';
  }

  async handle(e: SlackEventMap['channel_created']): Promise<void> {
    const { context, event } = e || {};
    const { installation, organization } = context || {};

    try {
      // Check if the channel property is present
      if (!('channel' in e.event)) {
        throw new Error(
          'Invalid event received in the `channel_created` handler',
        );
      }

      // Get the channel
      const channelId = e.event.channel?.id;
      if (!channelId) {
        throw new Error(
          `[ChannelCreatedHandler] Channel ID is not present in the event, found ${safeJsonStringify(e.event.channel)}`,
        );
      }

      // Record this activity
      await this.slackAuditLogRepository.recordAuditLog({
        eventTs: event.event_ts,
        activityPerformedBy: event.channel.creator,
        activity: `Channel event received for channel id: ${channelId}`,
        description: `Channel event received for channel id: ${channelId} for Installation ${installation.name} (${installation.teamId})`,
        externalId: channelId,

        // Base
        op: AuditLogOp.INFO,
        visibility: AuditLogVisibility.ORGANIZATION,
        installation,
        organization,
      });

      // Get the conversation details from Slack WebAPI
      const conversationResponse =
        await this.slackWebApiService.getConversationInfo(
          installation.botToken,
          {
            channel: channelId,
          },
        );

      // If the conversation is not found, throw an error
      if (!conversationResponse.ok) {
        this.logger.error(
          `[ChannelCreatedHandler] Failed to get conversation info for channel ${channelId}, ${conversationResponse.error}`,
        );

        throw new Error(conversationResponse.error);
      }

      const { channel } = conversationResponse;

      // Create this new channel
      await this.transactionService.runInTransaction(async (txnContext) => {
        await this.channelsRepository.upsertWithTxn(
          txnContext,
          {
            name: channel.name || channel.name_normalized,
            channelDump: channel as Record<string, any>,
            channelId: channel.id,
            slackCreatedAt: channel.created.toString(),
            isBotActive: false,
            isBotJoined: false,
            channelType: ChannelType.NOT_SETUP,
            isArchived: channel.is_archived,
            isPrivate: channel.is_private,
            isShared: channel.is_ext_shared,
            installation: { id: installation.id },
            organization: { id: organization.id },
          },
          { conflictPaths: ['channelId', 'installation'] },
        );

        // Record this activity
        await this.slackAuditLogRepository.recordAuditLog({
          eventTs: event.event_ts,
          activityPerformedBy: event.channel.creator,
          activity: `Channel created by ${event.channel.creator} channel id: ${channelId}`,
          description: `Channel created by ${event.channel.creator} channel id: ${channelId} for Installation ${installation.name} (${installation.teamId})`,
          externalId: channelId,

          // Base
          op: AuditLogOp.CREATED,
          visibility: AuditLogVisibility.ORGANIZATION,
          installation,
          organization,
        });
      });

      // Post the event to the platform
      await this.thenaAppsPlatformApiProvider.postEventsToPlatform(
        installation.organization,
        {
          ...event,
          type: EmittableSlackEvents.CHANNEL_CREATED,
        },
      );
    } catch (error) {
      if (error instanceof Error) {
        this.logger.error(
          `[ChannelCreatedHandler] Failed to handle channel created event, ${error.message}`,
          error.stack,
        );

        await this.slackAuditLogRepository.recordAuditLog({
          eventTs: e?.event?.event_ts,
          activityPerformedBy: e?.event?.channel?.creator,
          activity: `Channel created by ${e?.event?.channel?.creator} channel id: ${e?.event?.channel?.id}`,
          description: `Channel created by ${e?.event?.channel?.creator} channel id: ${e?.event?.channel?.id}`,
          externalId: e?.event?.channel?.id,

          // Base
          op: AuditLogOp.ERROR,
          visibility: AuditLogVisibility.ORGANIZATION,
          installation,
          organization,
        });
      } else {
        console.error(
          `[ChannelCreatedHandler] Failed to handle channel created event, ${error}`,
        );
      }
    }
  }
}
