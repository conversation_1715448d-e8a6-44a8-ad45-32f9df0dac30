import { Inject, Injectable } from '@nestjs/common';
import { Team } from '@slack/web-api/dist/types/response/TeamInfoResponse';
import { cloneDeep, get } from 'lodash';
import { TransactionService } from '../../../../database/common';
import { Channels, Installations } from '../../../../database/entities';
import { ChannelsRepository } from '../../../../database/entities/channels/repositories';
import { UpdateAccount } from '../../../../external/provider/interfaces';
import { ThenaPlatformApiProvider } from '../../../../external/provider/thena-platform-api.provider';
import { CUSTOM_LOGGER_TOKEN, ILogger } from '../../../../utils';
import { SlackEvent } from '../../../decorators';
import { SlackWebAPIService } from '../../../providers/slack-apis/slack-apis.service';
import { BaseSlackEventHandler, SlackEventMap } from '../../interface';

@Injectable()
@SlackEvent('channel_unshared')
export class SlackChannelUnsharedHandler extends BaseSlackEventHandler<'channel_unshared'> {
  eventType = 'channel_unshared' as const;

  constructor(
    @Inject(CUSTOM_LOGGER_TOKEN) private readonly logger: ILogger,

    private readonly transactionService: TransactionService,
    private readonly channelsRepository: ChannelsRepository,

    private readonly slackWebApiService: SlackWebAPIService,
    private readonly platformApiProvider: ThenaPlatformApiProvider,
  ) {
    super();
  }

  canHandle(event: SlackEventMap['channel_unshared']): boolean {
    return event.event.type === 'channel_unshared';
  }

  async handle(e: SlackEventMap['channel_unshared']): Promise<void> {
    try {
      // Check if the channel property is present
      if (!('channel' in e.event) || !this.canHandle(e)) {
        throw new Error(
          'Invalid event received in the `channel_unshared` handler',
        );
      }

      const { context, event } = e;
      const { channel } = event;
      const { installation } = context;

      // Get the conversation details from Slack WebAPI
      const conversationResponse =
        await this.slackWebApiService.getConversationInfo(
          installation.botToken,
          {
            channel: channel,
          },
        );

      // If the conversation is not found, throw an error
      if (!conversationResponse.ok) {
        this.logger.error(
          `[ChannelUnsharedHandler] Failed to get conversation info for channel ${channel}, ${conversationResponse.error}`,
        );

        throw new Error(conversationResponse.error);
      }

      const { channel: conversation } = conversationResponse;

      // Get the channel data from the database
      const channelData = await this.channelsRepository.findByCondition({
        where: {
          channelId: channel,
          installation: { id: installation.id },
        },
      });
      if (!channelData) {
        this.logger.error(
          `[ChannelSharedHandler] Failed to find channel data for channel ${channel}, ${installation.id}`,
        );

        throw new Error('Channel not found');
      }

      const updatedSharedTeamIds = new Set(channelData.sharedTeamIds);
      if (updatedSharedTeamIds.has(event.previously_connected_team_id)) {
        updatedSharedTeamIds.delete(event.previously_connected_team_id);
      }

      // Get previous shared team info
      const teamInfo = await this.slackWebApiService.getTeamInfo(
        installation.botToken,
        {
          team: event.previously_connected_team_id,
        },
      );
      if (!teamInfo.ok) {
        this.logger.error(
          `[ChannelUnsharedHandler] Failed to get team info for team ${event.previously_connected_team_id}, ${teamInfo.error}`,
        );
        throw new Error(teamInfo.error);
      }

      const { team: connectedTeam } = teamInfo;

      // Sync the account
      const { accountId, dataToUpdate } = await this.prepareForSyncAccount(
        installation,
        connectedTeam,
        channelData,
      );

      const accountCustomObjectId = get(
        installation.platformDump,
        'customObjects.accountCustomObjectId',
      );
      const accountsDump = get(channelData.platformDump, 'accounts', []);
      const account = accountsDump.find((a) => a.accountId === accountId);
      const customObjectRecordIds = get(account, 'customObjectRecordIds', []);

      // Update the channel details in transaction
      await this.transactionService.runInTransaction(async (txnContext) => {
        // Update the account
        if (accountId) {
          await this.platformApiProvider.updateAccount(
            installation,
            accountId,
            dataToUpdate,
          );
        }

        // Update platform dump to remove deleted custom object records and account
        const updatedPlatformDump = { ...channelData.platformDump };

        // Remove account custom object records
        if (customObjectRecordIds.length > 0) {
          // Delete the custom object records
          await Promise.all(
            customObjectRecordIds.map((customObjectRecordId: string) =>
              this.platformApiProvider.deleteCustomObjectRecord(
                installation,
                accountCustomObjectId,
                customObjectRecordId,
              ),
            ),
          );
        }

        // Remove the account entirely from platform dump
        if (
          accountId &&
          Array.isArray(updatedPlatformDump.accounts) &&
          updatedPlatformDump.accounts.length > 0
        ) {
          updatedPlatformDump.accounts = updatedPlatformDump.accounts.filter(
            (acc) => acc.accountId !== accountId,
          );
        }

        // Update the channel details
        await this.channelsRepository.updateWithTxn(
          txnContext,
          { channelId: channel, installation: { id: installation.id } },
          {
            isShared: false,
            channelDump: conversation as Record<string, any>,
            sharedTeamIds: Array.from(updatedSharedTeamIds),
            platformDump: updatedPlatformDump,
          },
        );
      });
    } catch (error) {
      if (error instanceof Error) {
        this.logger.error(
          '[ChannelUnsharedHandler] Failed to handle channel unshared event',
          error.stack,
        );
      } else {
        console.error(
          '[ChannelUnsharedHandler] Failed to handle channel unshared event',
          error,
        );
      }
    }
  }

  private async prepareForSyncAccount(
    installation: Installations,
    team: Team,
    channel: Channels,
  ): Promise<{
    accountId: string | null;
    dataToUpdate: UpdateAccount | null;
  }> {
    // Get the existing account
    const existingAccounts =
      await this.platformApiProvider.getAccountsByDomains(installation, [
        team.domain,
      ]);

    if (existingAccounts.length === 0) {
      this.logger.error(
        `[ChannelUnsharedHandler] No account found for team ${team.domain}`,
      );
      return {
        accountId: null,
        dataToUpdate: null,
      };
    }

    if (existingAccounts.length > 1) {
      this.logger.error(
        `[ChannelUnsharedHandler] Multiple accounts found for team ${team.domain}`,
      );
      return {
        accountId: null,
        dataToUpdate: null,
      };
    }

    const accountMetadata = cloneDeep(existingAccounts[0].metadata);
    if (get(accountMetadata, 'sinks.slack.syncStatus') === 'success') {
      accountMetadata.sinks.slack.channels =
        accountMetadata.sinks.slack.channels.filter(
          (c) => c.channelId !== channel.channelId,
        );
      accountMetadata.sinks.slack.lastSyncedAt = new Date().toISOString();
    }

    // Update the account
    return {
      accountId: existingAccounts[0].id,
      dataToUpdate: {
        metadata: accountMetadata,
      },
    };
  }
}
