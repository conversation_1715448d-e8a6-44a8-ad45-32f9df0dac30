import { SlackChannel<PERSON>rchived<PERSON><PERSON><PERSON> } from './channel-archived.handler';
import { SlackChannelCreatedHandler } from './channel-created.handler';
import { SlackChannelDeletedHandler } from './channel-deleted.handler';
import { SlackChannelLeftHandler } from './channel-left.handler';
import { SlackChannelRenameHandler } from './channel-rename.handler';
import { SlackChannelSharedHandler } from './channel-shared.handler';
import { SlackChannelUnarchivedHandler } from './channel-unarchived.handler';
import { SlackChannelUnsharedHandler } from './channel-unshared.handler';

export const slackChannelEventHandlers = [
  SlackChannel<PERSON>reatedHand<PERSON>,
  SlackChannelDeletedHandler,
  SlackChannelRenameHandler,
  SlackChannelArchivedHandler,
  SlackChannelUnarchivedHandler,
  SlackChannelShared<PERSON><PERSON><PERSON>,
  SlackChannelUnshared<PERSON><PERSON><PERSON>,
  SlackChannelLeftHandler,
];
