import { Inject, Injectable } from '@nestjs/common';
import { UsergroupsListResponse } from '@slack/web-api';
import { TransactionService } from '../../../../database/common';
import { Installations } from '../../../../database/entities';
import { SlackSubgroupsRepository } from '../../../../database/entities/subgroups/repositories/subgroups.repository';
import { CUSTOM_LOGGER_TOKEN, ILogger } from '../../../../utils';
import { SlackEvent } from '../../../decorators';
import { BaseSlackEventHandler, SlackEventMap } from '../../interface';

@Injectable()
@SlackEvent('subteam_updated')
export class SlackSubTeamUpdatedHandler extends BaseSlackEventHandler<'subteam_updated'> {
  eventType = 'subteam_updated' as const;

  constructor(
    @Inject(CUSTOM_LOGGER_TOKEN) private readonly logger: ILogger,

    private readonly transactionService: TransactionService,
    private readonly slackSubGroupsRepository: SlackSubgroupsRepository,
  ) {
    super();
  }

  canHandle(event: SlackEventMap['subteam_updated']): boolean {
    return event.event.type === 'subteam_updated';
  }

  async handle(e: SlackEventMap['subteam_updated']): Promise<void> {
    // Check if the channel property is present
    if (!('subteam' in e.event) || !this.canHandle(e)) {
      throw new Error(
        'Invalid event received in the `subteam_updated` handler',
      );
    }

    const { context, event } = e;
    const { subteam } = event;
    const { installation, organization } = context;

    try {
      this.logger.log(
        `Received 'subteam_updated' event for slack team ${event.subteam.team_id}, installation ${installation.id}, organization ${organization.id}`,
      );

      // Check if the subteam already exists in our database
      let subteamEntity = await this.slackSubGroupsRepository.findByCondition({
        where: {
          slackGroupId: subteam.id,
          installation: { id: installation.id },
          organization: { id: organization.id },
        },
      });

      // If the subteam does not exist, we won't do anything
      if (!subteamEntity) {
        this.logger.debug(
          `Subteam ${subteam.id} does not exist in our database`,
        );
        subteamEntity = await this.createSubteam(subteam, installation);
      }

      // Update the subteam in our database
      await this.transactionService.runInTransaction(async (txnContext) => {
        await this.slackSubGroupsRepository.updateWithTxn(
          txnContext,
          { id: subteamEntity.id, slackGroupId: subteam.id },
          {
            slackDeletedAt: subteam.date_delete,
            slackHandle: subteam.handle,
            description: subteam.description,
            isExternal: subteam.is_external,
            usersCount: subteam.user_count,
            updatedBy: { slackId: subteam.updated_by },
            users: subteam.users,
          },
        );
      });
    } catch (error) {
      if (error instanceof Error) {
        this.logger.error(
          `Error updating subteam ${subteam.id} in our database`,
          error.stack,
        );
      } else {
        console.error(error);
      }
    }
  }

  /**
   * @description
   * Creates a subteam in our database
   * @param subteam The subteam to create
   * @param installation The installation to create the subteam for
   * @returns The created subteam
   */
  private createSubteam(
    subteam: UsergroupsListResponse['usergroups'][0],
    installation: Installations,
  ) {
    return this.transactionService.runInTransaction(async (txnContext) => {
      return this.slackSubGroupsRepository.saveWithTxn(txnContext, {
        slackGroupId: subteam.id,
        slackGroupDump: subteam,
        slackHandle: subteam.handle,
        description: subteam.description,
        isExternal: subteam.is_external,
        usersCount: subteam.user_count,
        createdBy: { slackId: subteam.created_by },
        updatedBy: { slackId: subteam.updated_by },
        installation: { id: installation.id },
        organization: { id: installation.organization.id },
        users: subteam.users,
      });
    });
  }
}
