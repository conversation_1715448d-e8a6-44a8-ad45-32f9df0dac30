import { Inject, Injectable } from '@nestjs/common';
import { TransactionService } from '../../../../database/common';
import { SlackSubgroupsRepository } from '../../../../database/entities/subgroups/repositories/subgroups.repository';
import { CUSTOM_LOGGER_TOKEN, ILogger } from '../../../../utils';
import { SlackEvent } from '../../../decorators';
import { BaseSlackEventHandler, SlackEventMap } from '../../interface';

@Injectable()
@SlackEvent('subteam_created')
export class SlackSubTeamCreatedHandler extends BaseSlackEventHandler<'subteam_created'> {
  eventType = 'subteam_created' as const;

  constructor(
    @Inject(CUSTOM_LOGGER_TOKEN) private readonly logger: ILogger,

    private readonly transactionService: TransactionService,
    private readonly slackSubGroupsRepository: SlackSubgroupsRepository,
  ) {
    super();
  }

  canHandle(event: SlackEventMap['subteam_created']): boolean {
    return event.event.type === 'subteam_created';
  }

  async handle(e: SlackEventMap['subteam_created']): Promise<void> {
    // Check if the channel property is present
    if (!('subteam' in e.event) || !this.canHandle(e)) {
      throw new Error(
        'Invalid event received in the `subteam_created` handler',
      );
    }

    const { context, event } = e;
    const { subteam } = event;
    const { installation, organization } = context;

    try {
      this.logger.log(
        `Received 'subteam_created' event for slack team ${event.subteam.team_id}, installation ${installation.id}, organization ${organization.id}`,
      );

      // Check if the subteam already exists in our database
      const subteamExists = await this.slackSubGroupsRepository.exists({
        where: {
          slackGroupId: subteam.id,
          installation: { id: installation.id },
          organization: { id: organization.id },
        },
      });

      // If the subteam already exists, we won't do anything
      if (subteamExists) {
        this.logger.debug(
          `Subteam ${subteam.id} already exists in our database`,
        );
        return;
      }

      // Create the subteam in our database
      await this.transactionService.runInTransaction(async (txnContext) => {
        await this.slackSubGroupsRepository.saveWithTxn(txnContext, {
          slackGroupId: subteam.id,
          slackGroupDump: subteam,
          slackHandle: subteam.handle,
          description: subteam.description,
          isExternal: subteam.is_external,
          usersCount: subteam.user_count,
          createdBy: { slackId: subteam.created_by },
          updatedBy: { slackId: subteam.updated_by },
          installation: { id: installation.id },
          organization: { id: installation.organization.id },
          users: subteam.users,
        });
      });
    } catch (error) {
      if (error instanceof Error) {
        this.logger.error(
          `Error creating subteam ${subteam.id} in our database`,
          error.stack,
        );
      } else {
        console.error(
          `Error creating subteam ${subteam.id} in our database: ${error}`,
        );
      }
    }
  }
}
