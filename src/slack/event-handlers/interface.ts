import {
  SlackActionMiddlewareArgs,
  SlackCommandMiddlewareArgs,
  SlackEventMiddlewareArgs,
  SlackOptionsMiddlewareArgs,
  SlackViewMiddlewareArgs,
} from '@slack/bolt';
import type { EnrichedSlackArgsContext } from '../services/slack-action-discovery.service';

export const SlackEvents = {
  // Message events
  message: 'message',

  // Links events
  link_shared: 'link_shared',

  // Members events
  member_joined_channel: 'member_joined_channel',
  member_left_channel: 'member_left_channel',

  // Channels events
  channel_created: 'channel_created',
  channel_deleted: 'channel_deleted',
  channel_rename: 'channel_rename',
  channel_archive: 'channel_archive',
  channel_unarchive: 'channel_unarchive',
  channel_shared: 'channel_shared',
  channel_unshared: 'channel_unshared',
  channel_left: 'channel_left',

  // Subteams events
  subteam_created: 'subteam_created',
  subteam_updated: 'subteam_updated',

  // Reactions events
  reaction_added: 'reaction_added',
  reaction_removed: 'reaction_removed',
};

export type DecoratedSlackEventMiddlewareArgs<T extends string> =
  SlackEventMiddlewareArgs<T> & {
    context: EnrichedSlackArgsContext;
  };

export type DecoratedSlackCommandMiddlewareArgs = SlackCommandMiddlewareArgs & {
  context: EnrichedSlackArgsContext;
};

export type DecoratedSlackViewMiddlewareArgs = SlackViewMiddlewareArgs & {
  context: EnrichedSlackArgsContext;
};

export type DecoratedSlackActionMiddlewareArgs = SlackActionMiddlewareArgs & {
  context: EnrichedSlackArgsContext;
};

export type DecoratedSlackOptionsMiddlewareArgs = SlackOptionsMiddlewareArgs & {
  context: EnrichedSlackArgsContext;
};

export type SlackEventMap = {
  // Message events
  message: DecoratedSlackEventMiddlewareArgs<'message'>;

  // Links events
  link_shared: DecoratedSlackEventMiddlewareArgs<'link_shared'>;

  // Members events
  member_joined_channel: DecoratedSlackEventMiddlewareArgs<'member_joined_channel'>;
  member_left_channel: DecoratedSlackEventMiddlewareArgs<'member_left_channel'>;

  // Channel events
  channel_created: DecoratedSlackEventMiddlewareArgs<'channel_created'>;
  channel_deleted: DecoratedSlackEventMiddlewareArgs<'channel_deleted'>;
  channel_rename: DecoratedSlackEventMiddlewareArgs<'channel_rename'>;
  channel_archive: DecoratedSlackEventMiddlewareArgs<'channel_archive'>;
  channel_unarchive: DecoratedSlackEventMiddlewareArgs<'channel_unarchive'>;
  channel_shared: DecoratedSlackEventMiddlewareArgs<'channel_shared'>;
  channel_unshared: DecoratedSlackEventMiddlewareArgs<'channel_unshared'>;
  channel_left: DecoratedSlackEventMiddlewareArgs<'channel_left'>;

  // Subteams events
  subteam_created: DecoratedSlackEventMiddlewareArgs<'subteam_created'>;
  subteam_updated: DecoratedSlackEventMiddlewareArgs<'subteam_updated'>;

  // Reactions events
  reaction_added: DecoratedSlackEventMiddlewareArgs<'reaction_added'>;
  reaction_removed: DecoratedSlackEventMiddlewareArgs<'reaction_removed'>;
};

/**
 * Interface for Slack event handlers
 */
export interface ISlackEventHandler<
  T extends keyof SlackEventMap = keyof SlackEventMap,
> {
  /**
   * The type of event this handler can handle
   */
  eventType: T;

  /**
   * Whether this handler can handle the given event
   * @param event The event to check
   * @returns Whether this handler can handle the given event
   */
  canHandle(event: SlackEventMap[T]): boolean;

  /**
   * Handle the given event
   * @param event The event to handle
   */
  handle(event: SlackEventMap[T]): Promise<void>;
}

/**
 * @class
 * @abstract
 * @implements ISlackEventHandler
 * Base class for Slack event handlers
 */
export abstract class BaseSlackEventHandler<
  T extends keyof SlackEventMap = keyof SlackEventMap,
> implements ISlackEventHandler<T>
{
  /**
   * The type of event this handler can handle
   */
  abstract eventType: T;

  /**
   * Whether this handler can handle the given event
   * @param event The event to check
   * @returns Whether this handler can handle the given event
   */
  abstract canHandle(event: SlackEventMap[T]): boolean;

  /**
   * Handle the given event
   * @param event The event to handle
   */
  abstract handle(event: SlackEventMap[T]): Promise<void>;
}
