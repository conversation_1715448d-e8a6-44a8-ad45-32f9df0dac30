import { IsDateString, IsDecimal, IsIP, IsUrl } from 'class-validator';

/**
 * Check if value is a valid string
 * @param value - The value to check
 * @param options - Validation options
 * @returns True if the value is a string and meets the criteria
 */
export function isValidString(
  value: any,
  options: {
    minLength?: number;
    maxLength?: number;
    pattern?: string | RegExp;
  } = {},
): boolean {
  if (value === null || value === undefined || typeof value !== 'string') {
    return false;
  }

  const { minLength, maxLength, pattern } = options;

  // Check minimum length if specified
  if (minLength !== undefined && value.length < minLength) {
    return false;
  }

  // Check maximum length if specified
  if (maxLength !== undefined && value.length > maxLength) {
    return false;
  }

  // Check pattern if specified
  if (pattern) {
    const regex = typeof pattern === 'string' ? new RegExp(pattern) : pattern;
    return regex.test(value);
  }

  return true;
}

/**
 * Check if value is a valid email address
 * @param email - The email address to validate
 * @returns Object with isValid flag and specific errorMessage for validation failures
 */
export function isValidEmail(email: any): {
  isValid: boolean;
  errorMessage?: string;
} {
  // Basic validation checks
  if (!isValidString(email)) {
    return { isValid: false, errorMessage: 'Email address is required' };
  }

  const emailRegex = /^[^\s@]+@[^\s@]+\.[^\s@]+$/;
  if (!emailRegex.test(email)) {
    return {
      isValid: false,
      errorMessage: 'Please enter a valid email address',
    };
  }

  return { isValid: true };
}

/**
 * Check if value is a valid phone number
 * @param phone - The phone number to validate
 * @param options - Validation options
 * @returns True if the phone number is valid
 */
export function isValidPhoneNumber(
  phone: any,
  options: {
    allowInternational?: boolean;
    minLength?: number;
    maxLength?: number;
  } = {
    allowInternational: true,
    minLength: 7,
    maxLength: 20,
  },
): boolean {
  if (!isValidString(phone)) {
    return false;
  }

  const { allowInternational, minLength, maxLength } = options;

  // Basic length check
  if (phone.length < (minLength || 7) || phone.length > (maxLength || 20)) {
    return false;
  }

  // Remove all non-digit characters for further validation
  const digitsOnly = phone.replace(/\D/g, '');

  // Check if we have enough digits
  if (digitsOnly.length < 7) {
    return false;
  }

  // Different regex patterns based on options
  if (allowInternational) {
    // Improved phone regex that handles international formats
    const phoneRegex =
      /^(\+\d{1,3}[\s-]?)?\(?\d{3}\)?[\s.-]?\d{3}[\s.-]?\d{4}$|^\+?[\d\s-()]{7,20}$/;
    return phoneRegex.test(phone);
  }

  // US/North American format only
  const phoneRegex = /^\(?\d{3}\)?[\s.-]?\d{3}[\s.-]?\d{4}$/;
  return phoneRegex.test(phone);
}

/**
 * Check if value is a valid URL
 * @param url - The URL to validate
 * @param options - Validation options
 * @returns True if the URL is valid
 */
export function isValidUrl(
  url: any,
  options: {
    requireHttps?: boolean;
    allowedProtocols?: string[];
    requireTld?: boolean;
  } = {
    requireHttps: false,
    allowedProtocols: ['http:', 'https:'],
    requireTld: true,
  },
): boolean {
  if (!isValidString(url)) {
    return false;
  }

  try {
    const parsedUrl = new URL(url);

    // Check protocol
    const { requireHttps, allowedProtocols } = options;

    if (requireHttps && parsedUrl.protocol !== 'https:') {
      return false;
    }

    if (allowedProtocols && !allowedProtocols.includes(parsedUrl.protocol)) {
      return false;
    }

    // Check TLD (top-level domain)
    if (options.requireTld) {
      const hostnameParts = parsedUrl.hostname.split('.');
      if (
        hostnameParts.length < 2 ||
        hostnameParts[hostnameParts.length - 1] === ''
      ) {
        return false;
      }
    }

    return true;
  } catch {
    return false;
  }
}

/**
 * Check if value is a valid date
 * @param date - The date string to validate
 * @returns True if the date is valid
 */
export function isValidDate(date: any): boolean {
  if (!isValidString(date)) {
    return false;
  }

  const dateObj = new Date(date);
  return !Number.isNaN(dateObj.getTime());
}

/**
 * Check if value is a valid date and time
 * @param dateTime - The date and time string to validate
 * @returns True if the date and time is valid
 */
export function isValidDateTime(dateTime: any): boolean {
  if (!isValidString(dateTime)) {
    return false;
  }

  if (dateTime === '') {
    return true; // Allow empty string for non-mandatory fields
  }

  const dateObj = new Date(dateTime);
  return !Number.isNaN(dateObj.getTime());
}

/**
 * Check if value is a valid time (HH:MM format)
 * @param time - The time string to validate or Slack timepicker object
 * @param options - Validation options
 * @returns True if the time is valid
 */
export function isValidTime(
  time: any,
  options: {
    allowSlackFormat?: boolean;
  } = {
    allowSlackFormat: true,
  },
): boolean {
  // Handle Slack timepicker format which returns an object with selected_time
  if (
    options.allowSlackFormat &&
    time &&
    typeof time === 'object' &&
    'selected_time' in time
  ) {
    return isValidTime(time.selected_time, { allowSlackFormat: false });
  }

  if (!isValidString(time)) {
    return false;
  }

  // Standard HH:MM format (00:00 to 23:59)
  // Slack uses a strict HH:MM format with 2 digits for hours
  const timeRegex = /^([0-1][0-9]|2[0-3]):[0-5][0-9]$/;
  return timeRegex.test(time);
}

/**
 * Check if value is a valid regex pattern
 * @param pattern - The regex pattern to validate
 * @returns True if the pattern is a valid regex
 */
export function isValidRegex(pattern: any): boolean {
  if (!isValidString(pattern)) {
    return false;
  }

  try {
    new RegExp(pattern);
    return true;
  } catch {
    return false;
  }
}

/**
 * Check if value is a valid IP address (IPv4 or IPv6)
 * @param ip - The IP address to validate
 * @returns True if the IP address is valid
 */
export function isValidIpAddress(ip: any): boolean {
  if (!isValidString(ip)) {
    return false;
  }

  // IPv4 regex
  const ipv4Regex =
    /^(?:(?:25[0-5]|2[0-4][0-9]|[01]?[0-9][0-9]?)\.){3}(?:25[0-5]|2[0-4][0-9]|[01]?[0-9][0-9]?)$/;

  // Improved IPv6 regex that handles abbreviated forms
  const ipv6Regex =
    /^(([0-9a-fA-F]{1,4}:){7}[0-9a-fA-F]{1,4}|([0-9a-fA-F]{1,4}:){1,7}:|([0-9a-fA-F]{1,4}:){1,6}:[0-9a-fA-F]{1,4}|([0-9a-fA-F]{1,4}:){1,5}(:[0-9a-fA-F]{1,4}){1,2}|([0-9a-fA-F]{1,4}:){1,4}(:[0-9a-fA-F]{1,4}){1,3}|([0-9a-fA-F]{1,4}:){1,3}(:[0-9a-fA-F]{1,4}){1,4}|([0-9a-fA-F]{1,4}:){1,2}(:[0-9a-fA-F]{1,4}){1,5}|[0-9a-fA-F]{1,4}:((:[0-9a-fA-F]{1,4}){1,6})|:((:[0-9a-fA-F]{1,4}){1,7}|:)|fe80:(:[0-9a-fA-F]{0,4}){0,4}%[0-9a-zA-Z]+|::(ffff(:0{1,4})?:)?((25[0-5]|(2[0-4]|1?[0-9])?[0-9])\.){3}(25[0-5]|(2[0-4]|1?[0-9])?[0-9])|([0-9a-fA-F]{1,4}:){1,4}:((25[0-5]|(2[0-4]|1?[0-9])?[0-9])\.){3}(25[0-5]|(2[0-4]|1?[0-9])?[0-9]))$/;

  return ipv4Regex.test(ip) || ipv6Regex.test(ip);
}

/**
 * Check if value is a valid password
 * @param password - The password to validate
 * @param options - Validation options
 * @returns True if the password is valid
 */
export function isValidPassword(
  password: any,
  options: {
    minLength?: number;
    requireUppercase?: boolean;
    requireLowercase?: boolean;
    requireNumbers?: boolean;
    requireSpecialChars?: boolean;
    maxLength?: number;
  } = {
    minLength: 8,
    requireUppercase: true,
    requireLowercase: true,
    requireNumbers: true,
    requireSpecialChars: false,
    maxLength: 100,
  },
): boolean {
  if (!isValidString(password)) {
    return false;
  }

  const {
    minLength = 8,
    requireUppercase = true,
    requireLowercase = true,
    requireNumbers = true,
    requireSpecialChars = false,
    maxLength = 100,
  } = options;

  // Check length
  if (password.length < minLength) {
    return false;
  }

  if (maxLength !== undefined && password.length > maxLength) {
    return false;
  }

  // Check for uppercase letters
  if (requireUppercase && !/[A-Z]/.test(password)) {
    return false;
  }

  // Check for lowercase letters
  if (requireLowercase && !/[a-z]/.test(password)) {
    return false;
  }

  // Check for numbers
  if (requireNumbers && !/\d/.test(password)) {
    return false;
  }

  // Check for special characters
  if (
    requireSpecialChars &&
    !/[!@#$%^&*()_+\-=\[\]{};':"\\|,.<>\/?]/.test(password)
  ) {
    return false;
  }

  return true;
}

/**
 * Check if value contains valid coordinates (latitude,longitude)
 * @param coordinates - The coordinates string to validate
 * @returns True if the coordinates are valid
 */
export function isValidCoordinates(coordinates: any): boolean {
  if (!isValidString(coordinates)) {
    return false;
  }

  const coordRegex = /^-?\d+(\.\d+)?,\s*-?\d+(\.\d+)?$/;
  if (!coordRegex.test(coordinates)) {
    return false;
  }

  const [lat, lng] = coordinates.split(',').map(Number);
  return lat >= -90 && lat <= 90 && lng >= -180 && lng <= 180;
}

/**
 * URL validation class for class-validator package
 */
export class UrlValidation {
  @IsUrl()
  url: string;

  constructor(url?: string) {
    this.url = url || '';
  }

  /**
   * Validate if the URL is valid
   * @returns True if the URL is valid
   */
  isValid(): boolean {
    return isValidUrl(this.url);
  }
}

/**
 * Date validation class for class-validator package
 */
export class DateValidation {
  @IsDateString()
  date: string;

  constructor(date?: string) {
    this.date = date || '';
  }

  /**
   * Validate if the date is valid
   * @returns True if the date is valid
   */
  isValid(): boolean {
    return isValidDate(this.date);
  }
}

/**
 * Decimal validation class for class-validator package
 */
export class DecimalValidation {
  @IsDecimal()
  decimal: string;

  constructor(decimal?: string) {
    this.decimal = decimal || '';
  }

  /**
   * Validate if the decimal is valid
   * @returns True if the decimal is valid
   */
  isValid(): boolean {
    if (!isValidString(this.decimal)) {
      return false;
    }

    return !Number.isNaN(Number.parseFloat(this.decimal));
  }
}

/**
 * IP address validation class for class-validator package
 */
export class IpAddressValidation {
  @IsIP()
  ipAddress: string;

  constructor(ipAddress?: string) {
    this.ipAddress = ipAddress || '';
  }

  /**
   * Validate if the IP address is valid
   * @returns True if the IP address is valid
   */
  isValid(): boolean {
    return isValidIpAddress(this.ipAddress);
  }
}
