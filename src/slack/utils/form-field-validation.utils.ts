import { CustomFieldType } from '../constants/custom-field-types.enum';
import {
  isValidCoordinates,
  isValidDate,
  isValidDateTime,
  isValidEmail,
  isValidIpAddress,
  isValidPassword,
  isValidPhoneNumber,
  isValidRegex,
  isValidString,
  isValidTime,
  isValidUrl,
} from './field-validation.utils';

/**
 * Field interface for validation
 * This is a simplified version of the Field interface from conditional-form-builder.composite.ts
 * that includes only the properties needed for validation
 */
export interface Field {
  id: string;
  name: string;
  type: string;
  mandatoryOnCreation?: boolean;
  visibleToCustomer?: boolean;
  editableByCustomer?: boolean;
  accessibleInTicketCreationForm?: boolean;
  options?: { label: string; value: string }[];
  metadata?: {
    originalType?: string;
    regexPattern?: string;
    currencyCode?: string;
    maxFileSize?: number;
    allowedFileTypes?: string[];
    lookupType?: string;
    lookupApi?: string;
    [key: string]: any;
  };
}

/**
 * Interface for validation result
 */
export interface ValidationResult {
  isValid: boolean;
  errorMessage: string | null;
}

/**
 * Validate a field value based on its type
 * @param field The field definition
 * @param value The value to validate
 * @returns Validation result with isValid flag and error message if invalid
 */
export function validateFieldValue(field: Field, value: any): ValidationResult {
  // Skip validation for empty values on non-mandatory fields
  if (
    !field.mandatoryOnCreation &&
    (value === null ||
      value === undefined ||
      value === '' ||
      (Array.isArray(value) && value.length === 0))
  ) {
    return { isValid: true, errorMessage: null };
  }

  // Check if mandatory field is empty
  if (
    field.mandatoryOnCreation &&
    (value === null ||
      value === undefined ||
      value === '' ||
      (Array.isArray(value) && value.length === 0))
  ) {
    return { isValid: false, errorMessage: 'This field is required' };
  }

  // Get the original field type
  const fieldType = (field.metadata?.originalType?.toUpperCase() ||
    field.type.toUpperCase()) as CustomFieldType;

  // Validate based on field type
  switch (fieldType) {
    case CustomFieldType.SINGLE_LINE:
    case CustomFieldType.MULTI_LINE:
    case CustomFieldType.RICH_TEXT: {
      // Get validation options from field metadata
      const minLength = field.metadata?.minLength
        ? Number(field.metadata.minLength)
        : undefined;
      const maxLength = field.metadata?.maxLength
        ? Number(field.metadata.maxLength)
        : undefined;
      const pattern = field.metadata?.pattern as string | undefined;

      if (!isValidString(value, { minLength, maxLength, pattern })) {
        let errorMessage = `${field.name} must be a valid text`;

        // Provide more specific error messages based on what failed
        if (
          minLength !== undefined &&
          typeof value === 'string' &&
          value.length < minLength
        ) {
          errorMessage = `${field.name} must be at least ${minLength} characters`;
        } else if (
          maxLength !== undefined &&
          typeof value === 'string' &&
          value.length > maxLength
        ) {
          errorMessage = `${field.name} cannot exceed ${maxLength} characters`;
        } else if (pattern !== undefined && typeof value === 'string') {
          errorMessage = `${field.name} does not match the required format`;
        }

        return {
          isValid: false,
          errorMessage,
        };
      }
      break;
    }
    case CustomFieldType.EMAIL: {
      // Use the centralized email validation function
      const emailValidation = isValidEmail(value);
      if (!emailValidation.isValid) {
        return {
          isValid: false,
          errorMessage:
            emailValidation.errorMessage ||
            'Please enter a valid email address',
        };
      }
      break;
    }
    case CustomFieldType.SINGLE_CHOICE:
    case CustomFieldType.RADIO_BUTTON: {
      if (value && !field.options?.some((opt) => opt.value === value)) {
        return {
          isValid: false,
          errorMessage: `${field.name} has an invalid option selected`,
        };
      }
      break;
    }
    case CustomFieldType.MULTI_CHOICE: {
      if (value && Array.isArray(value)) {
        for (const val of value) {
          if (!field.options?.some((opt) => opt.value === val)) {
            return {
              isValid: false,
              errorMessage: `${field.name} has an invalid option selected`,
            };
          }
        }
      } else if (value && !Array.isArray(value)) {
        return {
          isValid: false,
          errorMessage: `${field.name} must be a list of values`,
        };
      }
      break;
    }
    case CustomFieldType.URL: {
      // Get validation options from field metadata
      const requireHttps =
        field.metadata?.requireHttps === 'true' ||
        field.metadata?.requireHttps === true;
      const allowedProtocols = field.metadata?.allowedProtocols
        ? (field.metadata.allowedProtocols as string)
            .split(',')
            .map((p) => p.trim())
        : undefined;

      if (value && !isValidUrl(value, { requireHttps, allowedProtocols })) {
        let errorMessage = `${field.name} must be a valid URL`;

        // Provide more specific error messages based on validation options
        if (requireHttps) {
          errorMessage = `${field.name} must be a secure URL (https)`;
        } else if (allowedProtocols && allowedProtocols.length > 0) {
          errorMessage = `${field.name} must use one of these protocols: ${allowedProtocols.join(', ')}`;
        }

        return {
          isValid: false,
          errorMessage,
        };
      }
      break;
    }
    case CustomFieldType.INTEGER: {
      if (value !== null && value !== '') {
        const num = Number(value);
        if (Number.isNaN(num) || !Number.isInteger(num)) {
          return {
            isValid: false,
            errorMessage: `${field.name} must be a valid integer`,
          };
        }
      }
      break;
    }
    case CustomFieldType.DECIMAL:
    case CustomFieldType.CURRENCY: {
      if (value !== null && value !== '') {
        const num = Number(value);
        if (Number.isNaN(num)) {
          return {
            isValid: false,
            errorMessage: `${field.name} must be a valid number`,
          };
        }
      }
      break;
    }
    case CustomFieldType.DATE: {
      if (value && !isValidDate(value)) {
        return {
          isValid: false,
          errorMessage: `${field.name} must be a valid date`,
        };
      }
      break;
    }
    case CustomFieldType.DATE_TIME: {
      if (value && !isValidDateTime(value)) {
        return {
          isValid: false,
          errorMessage: `${field.name} must be a valid date and time`,
        };
      }
      break;
    }
    case CustomFieldType.TIME: {
      // Handle both string time values and Slack timepicker objects
      if (value && !isValidTime(value, { allowSlackFormat: true })) {
        return {
          isValid: false,
          errorMessage: `${field.name} must be a valid time (HH:MM)`,
        };
      }
      break;
    }
    case CustomFieldType.PASSWORD: {
      // Get validation options from field metadata
      const minLength = field.metadata?.minLength
        ? Number(field.metadata.minLength)
        : undefined;
      const maxLength = field.metadata?.maxLength
        ? Number(field.metadata.maxLength)
        : undefined;
      const requireUppercase =
        field.metadata?.requireUppercase !== 'false' &&
        field.metadata?.requireUppercase !== false;
      const requireLowercase =
        field.metadata?.requireLowercase !== 'false' &&
        field.metadata?.requireLowercase !== false;
      const requireNumbers =
        field.metadata?.requireNumbers !== 'false' &&
        field.metadata?.requireNumbers !== false;
      const requireSpecialChars =
        field.metadata?.requireSpecialChars === 'true' ||
        field.metadata?.requireSpecialChars === true;

      if (
        value &&
        !isValidPassword(value, {
          minLength,
          maxLength,
          requireUppercase,
          requireLowercase,
          requireNumbers,
          requireSpecialChars,
        })
      ) {
        let errorMessage = `${field.name} must be a valid password`;

        // Build a more descriptive error message based on requirements
        const requirements = [];
        if (minLength) {
          requirements.push(`at least ${minLength} characters`);
        }
        if (requireUppercase) {
          requirements.push('uppercase letters');
        }
        if (requireLowercase) {
          requirements.push('lowercase letters');
        }
        if (requireNumbers) {
          requirements.push('numbers');
        }
        if (requireSpecialChars) {
          requirements.push('special characters');
        }

        if (requirements.length > 0) {
          errorMessage = `${field.name} must include ${requirements.join(', ')}`;
        }

        return {
          isValid: false,
          errorMessage,
        };
      }
      break;
    }
    case CustomFieldType.PHONE_NUMBER: {
      // Get validation options from field metadata
      const allowInternational =
        field.metadata?.allowInternational !== 'false' &&
        field.metadata?.allowInternational !== false;
      const minLength = field.metadata?.minLength
        ? Number(field.metadata.minLength)
        : undefined;
      const maxLength = field.metadata?.maxLength
        ? Number(field.metadata.maxLength)
        : undefined;

      if (
        value &&
        !isValidPhoneNumber(value, { allowInternational, minLength, maxLength })
      ) {
        let errorMessage = `${field.name} must be a valid phone number`;

        // Provide more specific error messages based on validation options
        if (!allowInternational) {
          errorMessage = `${field.name} must be a valid US phone number`;
        } else if (
          minLength !== undefined &&
          typeof value === 'string' &&
          value.replace(/\D/g, '').length < minLength
        ) {
          errorMessage = `${field.name} must have at least ${minLength} digits`;
        } else if (
          maxLength !== undefined &&
          typeof value === 'string' &&
          value.replace(/\D/g, '').length > maxLength
        ) {
          errorMessage = `${field.name} cannot exceed ${maxLength} digits`;
        }

        return {
          isValid: false,
          errorMessage,
        };
      }
      break;
    }
    case CustomFieldType.REGEX: {
      if (field.metadata && 'regexPattern' in field.metadata && value) {
        try {
          const regex = new RegExp(field.metadata.regexPattern as string);
          if (!regex.test(value)) {
            return {
              isValid: false,
              errorMessage: `${field.name} does not match the required pattern`,
            };
          }
        } catch (_) {
          return {
            isValid: false,
            errorMessage: `Invalid regex pattern for ${field.name}`,
          };
        }
      } else if (value && !isValidRegex(value)) {
        return {
          isValid: false,
          errorMessage: `${field.name} must be a valid regex pattern`,
        };
      }
      break;
    }
    case CustomFieldType.IP_ADDRESS: {
      if (value && !isValidIpAddress(value)) {
        return {
          isValid: false,
          errorMessage: `${field.name} must be a valid IP address`,
        };
      }
      break;
    }
    case CustomFieldType.TOGGLE:
    case CustomFieldType.BOOLEAN: {
      if (
        value !== null &&
        value !== undefined &&
        value !== true &&
        value !== false &&
        value !== 'true' &&
        value !== 'false'
      ) {
        return {
          isValid: false,
          errorMessage: `${field.name} must be a boolean value`,
        };
      }
      break;
    }
    case CustomFieldType.COORDINATES: {
      if (value && !isValidCoordinates(value)) {
        return {
          isValid: false,
          errorMessage: `${field.name} must be valid coordinates`,
        };
      }
      break;
    }
    case CustomFieldType.ADDRESS: {
      // Basic validation for address - can be enhanced
      if (value && (!isValidString(value) || value.length < 5)) {
        return {
          isValid: false,
          errorMessage: `${field.name} must be a valid address`,
        };
      }
      break;
    }
    case CustomFieldType.RATING: {
      // Assuming rating is a number between 1-5 or similar
      if (value !== null && value !== '') {
        const num = Number(value);
        if (Number.isNaN(num) || num < 0) {
          return {
            isValid: false,
            errorMessage: `${field.name} must be a valid rating value`,
          };
        }
      }
      break;
    }
    // For other types like LOOKUP, FILE_UPLOAD, CALCULATED, etc.
    // These typically need more complex validation that may depend on external data
    default:
      // No specific validation for this field type
      break;
  }

  return { isValid: true, errorMessage: null };
}
