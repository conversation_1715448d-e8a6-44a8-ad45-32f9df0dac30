import { Injectable } from '@nestjs/common';
import { SlackActionHandlerNotFoundError } from '../errors';
import type { SlackActionHandler } from '../interfaces';

@Injectable()
export class SlackActionRegistry {
  private actionHandlers = new Map<string, SlackActionHandler>();

  register(actionId: string, handler: SlackActionHandler) {
    this.actionHandlers.set(actionId, handler);
  }

  getHandler(actionId: string): SlackActionHandler {
    const handler = this.actionHandlers.get(actionId);
    if (!handler) {
      throw new SlackActionHandlerNotFoundError(actionId);
    }

    return handler;
  }
}
