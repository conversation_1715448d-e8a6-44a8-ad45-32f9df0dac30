import type { Provider } from '@nestjs/common';
import { ConfigKeys, ConfigService } from '../config/config.service';
import {
  SQSConsumerService,
  SQSProducerService,
  SQS_CONSUMER_CONFIG_TOKEN,
  SQS_PRODUCER_CONFIG_TOKEN,
} from '../utils/aws-utils/sqs';
import {
  SLACK_SQS_CONSUMER_SERVICE_TOKEN,
  SLACK_SQS_PRODUCER_SERVICE_TOKEN,
} from './constants';

export const slackProviders: Provider[] = [
  // Slack SQS Consumer Config
  {
    provide: SQS_CONSUMER_CONFIG_TOKEN,
    useFactory: (configService: ConfigService) => ({
      region: configService.get(ConfigKeys.AWS_REGION),
      queueUrl: configService.get(ConfigKeys.AWS_SLACK_EVENTS_FIFO_QUEUE_URL),
      credentials: {
        accessKeyId: configService.get(ConfigKeys.AWS_ACCESS_KEY),
        secretAccessKey: configService.get(ConfigKeys.AWS_SECRET_KEY),
      },
    }),
    inject: [ConfigService],
  },

  // Slack SQS Consumer Service
  {
    provide: SLACK_SQS_CONSUMER_SERVICE_TOKEN,
    useFactory: (configService: ConfigService) => {
      return new SQSConsumerService({
        region: configService.get(ConfigKeys.AWS_REGION),
        queueUrl: configService.get(ConfigKeys.AWS_SLACK_EVENTS_FIFO_QUEUE_URL),
        credentials: {
          accessKeyId: configService.get(ConfigKeys.AWS_ACCESS_KEY),
          secretAccessKey: configService.get(ConfigKeys.AWS_SECRET_KEY),
        },
      });
    },
    inject: [ConfigService],
  },

  // Slack SQS Producer Config
  {
    provide: SQS_PRODUCER_CONFIG_TOKEN,
    useFactory: (configService: ConfigService) => ({
      region: configService.get(ConfigKeys.AWS_REGION),
      queueUrl: configService.get(ConfigKeys.AWS_SLACK_EVENTS_FIFO_QUEUE_URL),
      credentials: {
        accessKeyId: configService.get(ConfigKeys.AWS_ACCESS_KEY),
        secretAccessKey: configService.get(ConfigKeys.AWS_SECRET_KEY),
      },
    }),
    inject: [ConfigService],
  },

  // Slack SQS Producer Service
  {
    provide: SLACK_SQS_PRODUCER_SERVICE_TOKEN,
    useFactory: (configService: ConfigService) => {
      return new SQSProducerService({
        region: configService.get(ConfigKeys.AWS_REGION),
        queueUrl: configService.get(ConfigKeys.AWS_SLACK_EVENTS_FIFO_QUEUE_URL),
        credentials: {
          accessKeyId: configService.get(ConfigKeys.AWS_ACCESS_KEY),
          secretAccessKey: configService.get(ConfigKeys.AWS_SECRET_KEY),
        },
      });
    },
    inject: [ConfigService],
  },
];
