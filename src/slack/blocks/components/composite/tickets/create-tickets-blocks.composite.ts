import { Injectable } from '@nestjs/common';
import { SlackCoreComponent } from '../../core';

@Injectable()
export class CreateTicketsBlocksComposite implements SlackCoreComponent {
  static readonly ACTION_IDS = {
    STATUS: 'update_status',
    PRIORITY: 'set_priority',
    TEAM: 'team_select',
  };

  static readonly BLOCK_ID = 'create_tickets_blocks';

  build(platformTeamUid: string, channelId?: string, formName?: string) {
    const blocks = [
      {
        type: 'header',
        text: {
          type: 'plain_text',
          text: formName || 'Create a ticket',
          emoji: true,
        },
      },
      {
        type: 'section',
        text: {
          type: 'mrkdwn',
          text: `Fill out the form below to create a new ticket for team ${platformTeamUid}:`,
        },
      },
      {
        type: 'divider',
      },
      {
        type: 'input',
        block_id: 'title_block',
        element: {
          type: 'plain_text_input',
          action_id: 'title_input',
          placeholder: {
            type: 'plain_text',
            text: 'Enter a descriptive title',
          },
        },
        label: {
          type: 'plain_text',
          text: 'Title',
          emoji: true,
        },
      },
      {
        type: 'input',
        block_id: 'team_select_block',
        element: {
          type: 'external_select',
          action_id: CreateTicketsBlocksComposite.ACTION_IDS.TEAM,
          placeholder: {
            type: 'plain_text',
            text: 'Select a team',
            emoji: true,
          },
          min_query_length: 0,
        },
        label: {
          type: 'plain_text',
          text: 'Team',
          emoji: true,
        },
      },
    ];

    // If the platform team uid is present, add the priority block
    // TODO: This has to be rendered dynamically
    // if (platformTeamUid) {
    //   blocks.push({
    //     type: 'input',
    //     block_id: 'priority_block',
    //     element: {
    //       type: 'external_select',
    //       action_id: CreateTicketsBlocksComposite.ACTION_IDS.PRIORITY,
    //       placeholder: {
    //         type: 'plain_text',
    //         text: 'Select priority level',
    //         emoji: true,
    //       },
    //       min_query_length: 0,
    //     },
    //     label: {
    //       type: 'plain_text',
    //       text: 'Priority',
    //       emoji: true,
    //     },
    //   });
    // }

    // Push the rest of the blocks
    blocks.push(...this.rest());

    return {
      blocks,
      private_metadata: channelId ? JSON.stringify({ channelId }) : undefined,
    };
  }

  private rest() {
    return [
      {
        type: 'input',
        block_id: 'requestor_email_block',
        element: {
          type: 'plain_text_input',
          action_id: 'requestor_email_input',
          placeholder: {
            type: 'plain_text',
            text: 'Enter requestor email',
          },
        },
        label: {
          type: 'plain_text',
          text: 'Requestor Email',
          emoji: true,
        },
      },
      {
        type: 'input',
        optional: true,
        block_id: 'description_block',
        element: {
          type: 'plain_text_input',
          multiline: true,
          action_id: 'description_input',
          placeholder: {
            type: 'plain_text',
            text: 'Provide additional details about the issue',
          },
        },
        label: {
          type: 'plain_text',
          text: 'Description',
          emoji: true,
        },
      },
    ];
  }
}
