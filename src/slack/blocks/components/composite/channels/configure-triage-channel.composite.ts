import { Injectable } from '@nestjs/common';
import { SlackCoreComponent } from '../../core';

@Injectable()
export class ConfigureTriageChannelComposite implements SlackCoreComponent {
  static readonly BLOCK_ID = 'configure_triage_channel';
  static readonly ACTION_IDS = { CHANNEL_SELECT: 'channel_select' };

  build() {
    return {
      blocks: [
        {
          type: 'header',
          text: {
            type: 'plain_text',
            text: 'Configure Triage Channel',
            emoji: true,
          },
        },
        {
          type: 'section',
          text: {
            type: 'mrkdwn',
            text: 'Select the channel that will be used to triage support requests:',
          },
        },
        {
          type: 'input',
          block_id: ConfigureTriageChannelComposite.BLOCK_ID,
          label: {
            type: 'plain_text',
            text: 'Search for a triage channel',
          },
          element: {
            type: 'external_select',
            action_id:
              ConfigureTriageChannelComposite.ACTION_IDS.CHANNEL_SELECT,
            placeholder: {
              type: 'plain_text',
              text: 'Search for a channel',
            },
            min_query_length: 0,
          },
        },
      ],
    };
  }
}
