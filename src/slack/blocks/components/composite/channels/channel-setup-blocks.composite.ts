import { Injectable } from '@nestjs/common';
import { ChannelType } from '../../../../../database/entities/channels/channels.entity';
import { SlackCoreComponent } from '../../core';

@Injectable()
export class ChannelSetupBlocks implements SlackCoreComponent {
  static readonly BLOCK_ID = 'channel_setup_form';
  static readonly ACTION_IDS = {
    TEAM_SELECT: 'team_select',
    CHANNEL_TYPE_SELECT: 'channel_type_select',
  };

  build(teams = []) {
    // Convert teams to Slack dropdown options format
    const teamOptions = teams.map((team) => ({
      text: {
        type: 'plain_text',
        text: team.name,
      },
      value: team.id,
    }));

    // Convert channel types to Slack dropdown options format
    const channelTypeOptions = [
      {
        text: {
          type: 'plain_text',
          text: 'Customer channel',
        },
        value: ChannelType.CUSTOMER_CHANNEL,
      },
      {
        text: {
          type: 'plain_text',
          text: 'Internal helpdesk',
        },
        value: ChannelType.INTERNAL_HELPDESK,
      },
      {
        text: {
          type: 'plain_text',
          text: 'Triage channel',
        },
        value: ChannelType.TRIAGE_CHANNEL,
      },
    ];

    return {
      blocks: [
        {
          type: 'header',
          text: {
            type: 'plain_text',
            text: 'Channel setup required 🎯',
            emoji: true,
          },
        },
        {
          type: 'section',
          text: {
            type: 'mrkdwn',
            text: "⚠️ This channel isn't configured yet. Please select a team and channel type to continue.",
          },
        },
        {
          type: 'input',
          block_id: `${ChannelSetupBlocks.BLOCK_ID}_team`,
          element: {
            type: 'static_select',
            action_id: ChannelSetupBlocks.ACTION_IDS.TEAM_SELECT,
            placeholder: {
              type: 'plain_text',
              text: 'Select a team',
            },
            options: teamOptions,
          },
          label: {
            type: 'plain_text',
            text: 'Team',
          },
        },
        {
          type: 'input',
          block_id: `${ChannelSetupBlocks.BLOCK_ID}_type`,
          element: {
            type: 'static_select',
            action_id: ChannelSetupBlocks.ACTION_IDS.CHANNEL_TYPE_SELECT,
            placeholder: {
              type: 'plain_text',
              text: 'Select a channel type',
            },
            options: channelTypeOptions,
          },
          label: {
            type: 'plain_text',
            text: 'Channel Type',
          },
        },
        {
          type: 'section',
          text: {
            type: 'mrkdwn',
            text: '*Channel Types:*',
          },
        },
        {
          type: 'section',
          text: {
            type: 'mrkdwn',
            text: '👥 *Customer channel*\nConvert messages from external domains into tickets in Thena.',
          },
        },
        {
          type: 'section',
          text: {
            type: 'mrkdwn',
            text: '⚙️ *Internal helpdesk*\nConvert messages into tickets in Thena.',
          },
        },
        {
          type: 'section',
          text: {
            type: 'mrkdwn',
            text: '🔔 *Triage channel*\nUsed to triage tickets and enable collaboration across internal teams.',
          },
        },
      ],
    };
  }
}
