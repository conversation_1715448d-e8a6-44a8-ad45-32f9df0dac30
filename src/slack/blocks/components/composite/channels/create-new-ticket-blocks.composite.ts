import { Injectable } from '@nestjs/common';
import { SlackCoreComponent } from '../../core';

@Injectable()
export class CreateNewTicketBlocks implements SlackCoreComponent {
  static readonly BLOCK_ID = 'create_new_ticket_form';
  static readonly ACTION_IDS = {
    CREATE_TICKET_FROM_REACTION: 'create_ticket_button_from_reaction',
  };

  build() {
    return {
      blocks: [
        {
          type: 'header',
          text: {
            type: 'plain_text',
            text: 'Create a Support Ticket',
            emoji: true,
          },
        },
        {
          type: 'section',
          text: {
            type: 'mrkdwn',
            text: '📝 To submit a support request, please fill out the ticket form with the necessary details.',
          },
        },
        {
          type: 'section',
          text: {
            type: 'mrkdwn',
            text: 'Creating a detailed ticket helps us address your issue more efficiently. Include any relevant information about your request.',
          },
        },
        {
          type: 'actions',
          block_id: CreateNewTicketBlocks.BLOCK_ID,
          elements: [
            {
              type: 'button',
              text: {
                type: 'plain_text',
                text: 'Create ticket',
                emoji: true,
              },
              style: 'primary',
              action_id:
                CreateNewTicketBlocks.ACTION_IDS.CREATE_TICKET_FROM_REACTION,
            },
          ],
        },
      ],
    };
  }
}
