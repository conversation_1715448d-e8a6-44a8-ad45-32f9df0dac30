import { Injectable } from '@nestjs/common';
import { SlackCoreComponent } from '../../core';

@Injectable()
export class SelectChannelTeamComposite implements SlackCoreComponent {
  static readonly BLOCK_ID = 'team_config_modal';
  static readonly ACTION_IDS = {
    TEAM_SELECT: 'team_select',
  };

  build() {
    return {
      blocks: [
        {
          type: 'header',
          text: {
            type: 'plain_text',
            text: 'Configure Team for Channel',
            emoji: true,
          },
        },
        {
          type: 'section',
          text: {
            type: 'mrkdwn',
            text: 'Select the team that will be associated with this channel:',
          },
        },
        {
          type: 'input',
          block_id: SelectChannelTeamComposite.BLOCK_ID,
          element: {
            type: 'external_select',
            action_id: SelectChannelTeamComposite.ACTION_IDS.TEAM_SELECT,
            placeholder: {
              type: 'plain_text',
              text: 'Search for a team',
            },
            min_query_length: 0,
          },
          label: {
            type: 'plain_text',
            text: 'Team',
          },
        },
      ],
    };
  }
}
