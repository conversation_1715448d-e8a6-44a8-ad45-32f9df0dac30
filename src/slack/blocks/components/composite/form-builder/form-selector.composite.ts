import { Injectable } from '@nestjs/common';
import { SlackCoreComponent } from '../../core';

export interface FormOption {
  id: string;
  name: string;
  description?: string;
  teamId?: string;
}

@Injectable()
export class FormSelectorComposite implements SlackCoreComponent {
  static readonly ACTION_ID = 'form_selector';
  static readonly BLOCK_ID = 'form_selector_block';

  /**
   * Build a form selector dropdown
   */
  build(data: {
    forms: FormOption[];
    messageText?: string;
    placeholderText?: string;
    channelId?: string;
  }) {
    const {
      forms,
      messageText = 'Please select a form to fill out',
      placeholderText = 'Select a form',
      channelId,
    } = data;

    if (!forms || forms.length === 0) {
      return {
        blocks: [
          {
            type: 'section',
            text: {
              type: 'mrkdwn',
              text: 'No forms are available at this time.',
            },
          },
        ],
        private_metadata: channelId ? JSON.stringify({ channelId }) : undefined,
      };
    }

    return {
      blocks: [
        {
          type: 'section',
          text: {
            type: 'mrkdwn',
            text: messageText || '',
          },
        },
        {
          type: 'actions',
          elements: [
            {
              type: 'static_select',
              action_id: FormSelectorComposite.ACTION_ID,
              options: forms.map((form) => ({
                text: {
                  type: 'plain_text',
                  text: form.name,
                  emoji: true,
                },
                value: form.id,
              })),
              placeholder: {
                type: 'plain_text',
                // text: placeholderText.replace(/form/gi, '').trim() || 'Select',
                text: placeholderText,
                emoji: true,
              },
            },
            {
              type: 'button',
              text: {
                type: 'plain_text',
                text: 'Continue',
                emoji: true,
              },
              action_id: 'form_continue',
              style: 'primary',
            },
          ],
        },
      ],
      private_metadata: channelId ? JSON.stringify({ channelId }) : undefined,
    };
  }
}
