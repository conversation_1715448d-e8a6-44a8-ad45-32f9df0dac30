# Conditional Form Builder for Slack Block Kit

This module provides a way to build dynamic forms in Slack using Block Kit, with conditional logic for showing/hiding and modifying fields based on user input.

## Usage Example

```typescript
import { ConditionalFormBuilderComposite, TargetFieldActionType, TargetFieldConditionsType } from './form-builder';

// Inject the component
@Injectable()
export class YourService {
  constructor(
    private readonly conditionalFormBuilder: ConditionalFormBuilderComposite,
  ) {}

  async buildDynamicForm(orgId: string) {
    // 1. Fetch form fields from API
    const formResponse = await this.yourApiService.getForm(orgId);
    
    // 2. Transform API fields to our format
    const fields = formResponse.data.results[0].fields.map(apiField => ({
      id: apiField.field,
      name: apiField.meta.name,
      description: '',
      mandatoryOnCreation: apiField.mandatoryOnCreation,
      mandatoryOnClose: apiField.mandatoryOnClose,
      visibleToCustomer: apiField.visibleToCustomer,
      editableByCustomer: apiField.editableByCustomer,
      fieldType: apiField.fieldType,
      isStandard: true,
      options: apiField.meta.options,
    }));
    
    // 3. Define conditional logic
    // Example: When Priority is set to "Urgent", make Description mandatory
    const conditions = new Map();
    
    const condition1 = {
      id: 'condition-1',
      triggerFieldId: 'SKZNJ0NJ10VBXC183R43RPHBRTHC6', // Priority field ID
      conditionType: TargetFieldConditionsType.EQUALS,
      value: 'Urgent',
      targetFields: [
        {
          id: 'action-1',
          type: TargetFieldActionType.MARK_MANDATORY,
          value: null,
          fieldId: 'SKZNJ0NJ105AJC3JNW57ZZRC0VR7W', // Description field ID
        }
      ],
    };
    
    conditions.set('condition-1', condition1);
    
    // 4. Define condition order
    const conditionOrder = ['condition-1'];
    
    // 5. Define initial values (if any)
    const values = {
      'SKZNJ0NJ10TM6EGPQXCKVFTW5YGKW': 'Initial title', // Title field
    };
    
    // 6. Build the form
    const blocks = this.conditionalFormBuilder.build({
      fields,
      conditions,
      conditionOrder,
      values,
    });
    
    return blocks;
  }
  
  // Handler for when a user interacts with the form
  handleInteraction(payload) {
    // Extract the current values from the payload
    const values = {};
    
    // For each block action, extract the value and update the values object
    // This is a simplified example - actual implementation would depend on Slack's payload structure
    if (payload.actions) {
      for (const action of payload.actions) {
        if (action.action_id.startsWith(ConditionalFormBuilderComposite.ACTION_PREFIX)) {
          const fieldId = action.action_id.replace(ConditionalFormBuilderComposite.ACTION_PREFIX, '');
          values[fieldId] = action.value;
        }
      }
    }
    
    // Rebuild the form with updated values to apply conditional logic
    return this.conditionalFormBuilder.build({
      fields: this.cachedFields, // You'd need to store these somewhere
      conditions: this.cachedConditions,
      conditionOrder: this.cachedConditionOrder,
      values,
    });
  }
}
```

## Features

1. **Field Types Support**:
   - Text inputs (single-line, multi-line)
   - Number inputs
   - Date/Time pickers
   - Toggles and checkboxes
   - Select menus (single and multi-select)

2. **Conditional Logic**:
   - Show/hide fields based on values of other fields
   - Make fields mandatory/optional
   - Apply different validation rules

3. **Condition Types**:
   - Equals/Not Equals
   - Contains/Not Contains
   - Greater Than/Less Than
   - Is Empty/Not Empty

## Integration with Form APIs

The component is designed to work with your existing form definition API. You'll need to transform the API response to match the expected format for fields, conditions, and values.

## Handling User Interaction

When users interact with the form, you'll need to:

1. Capture the updated values
2. Re-apply the conditional logic
3. Update the message blocks in Slack

See the usage example for a simplified implementation. 