import { Inject, Injectable } from '@nestjs/common';

import { isValidEmail } from '../../../../../slack/utils/field-validation.utils';
import { CUSTOM_LOGGER_TOKEN, ILogger } from '../../../../../utils/logger';
import { SlackCoreComponent } from '../../core';

/**
 * Enum of condition types that can be evaluated for fields
 */
export enum TargetFieldConditionsType {
  EQUALS = 'equals',
  NOT_EQUALS = 'not_equals',
  CONTAINS = 'contains',
  NOT_CONTAINS = 'not_contains',
  GREATER_THAN = 'greater_than',
  GREATER_THAN_EQUAL = 'greater_than_equal',
  LESS_THAN = 'less_than',
  LESS_THAN_EQUAL = 'less_than_equal',
  IS_EMPTY = 'is_empty',
  IS_NOT_EMPTY = 'is_not_empty',
}

/**
 * Actions that can be performed on target fields when conditions are met
 */
export enum TargetFieldActionType {
  ADD_FIELD = 'add_field',
  REMOVE_FIELD = 'remove_field',
  MARK_MANDATORY = 'mark_mandatory',
  MARK_NON_MANDATORY = 'mark_non_mandatory',
  FILL_VALUE = 'fill_value',
}

/**
 * Definition of a target field that will be affected by a condition
 */
export interface TargetField {
  id: string; // ID of the target field definition (often same as fieldId for simplicity)
  type: TargetFieldActionType;
  value: string | number | boolean | string[] | null; // Value for FILL_VALUE, otherwise often null
  fieldId?: string; // The ID of the actual form field to be affected
}

/**
 * Definition of a condition that will be evaluated
 */
export interface Condition {
  id: string;
  triggerFieldId: string;
  conditionType: TargetFieldConditionsType | ''; // Allow empty string if it signifies 'no condition'
  value: string | number | boolean | string[] | null;
  targetFields: TargetField[];
}

/**
 * Field data structure
 */
export interface Field {
  id: string;
  name: string;
  type: string; // e.g., 'text', 'email', 'number', 'date', 'time', 'datetime', 'select', 'multiselect', 'checkbox', 'radio', 'specialized'
  mandatoryOnCreation: boolean;
  mandatoryOnClose?: boolean;
  visibleToCustomer: boolean;
  editableByCustomer: boolean;
  accessibleInTicketCreationForm?: boolean;
  options?: Array<{
    label: string;
    value: string;
  }>;
  apiForOptions?: string;
  metadata?: {
    originalType?: string; // e.g., 'email', 'time', 'date', 'datetime' when type is 'specialized'
    currencyCode?: string;
    maxFileSize?: number;
    allowedFileTypes?: string[];
    lookupType?: string;
    lookupApi?: string;
    placeholder?: string;
    hintText?: string;
    checkboxLabel?: string;
    minLength?: number;
    maxLength?: number;
    minValue?: number | string; // Can be string for number_input min/max
    maxValue?: number | string; // Can be string for number_input min/max
  };
}

/**
 * Form values
 */
export interface FormValues {
  [fieldId: string]: string | number | boolean | string[] | null;
}

/**
 * Type for Slack Block elements
 */
interface SlackBlockElement {
  type: string;
  [key: string]: any;
}

/**
 * Type for Slack Block item
 */
interface SlackBlock {
  type: string;
  block_id?: string;
  [key: string]: any;
}

@Injectable()
export class ConditionalFormBuilderComposite implements SlackCoreComponent {
  static readonly ACTION_PREFIX = 'field_';

  constructor(@Inject(CUSTOM_LOGGER_TOKEN) private readonly logger: ILogger) {}

  /**
   * Build Slack Block Kit form with conditional fields
   */
  build(data: {
    fields: Field[];
    conditions: Condition[];
    conditionOrder: string[];
    values?: FormValues;
    name?: string;
  }): { blocks: SlackBlock[] } {
    const { fields, conditions, conditionOrder, values = {} } = data;

    // Ensure all fields have the required properties with default values
    const fieldsWithDefaults = this.applyDefaultFieldProperties(fields);

    // Apply conditions to determine which fields to display
    let fieldsToRender = [...fieldsWithDefaults];
    if (conditions.length > 0 && Object.keys(values).length > 0) {
      fieldsToRender = this.applyConditions(
        fieldsToRender,
        conditions,
        conditionOrder,
        values,
      );
    }

    // Filter out non-visible fields and fields not accessible in ticket creation form
    fieldsToRender = fieldsToRender.filter(
      (field) =>
        field.visibleToCustomer && field.accessibleInTicketCreationForm,
    );

    // Generate blocks for each field and flatten them
    const fieldBlocks = fieldsToRender
      .map((field) => this.renderField(field, values))
      .filter(Boolean)
      .flat();

    return { blocks: fieldBlocks };
  }

  /**
   * Apply default properties to field objects
   */
  private applyDefaultFieldProperties(fields: Field[]): Field[] {
    return fields.map((field) => ({
      ...field,
      visibleToCustomer: field.visibleToCustomer ?? true,
      editableByCustomer: field.editableByCustomer ?? true,
      accessibleInTicketCreationForm:
        field.accessibleInTicketCreationForm ?? true,
    }));
  }

  /**
   * Apply conditions to fields
   */
  private applyConditions(
    fields: Field[],
    conditions: Condition[],
    conditionOrder: string[],
    values: FormValues,
  ): Field[] {
    // Make a deep copy of fields to avoid modifying the original
    const updatedFields = JSON.parse(JSON.stringify(fields)) as Field[];

    // Process conditions in specified order
    for (const conditionId of conditionOrder) {
      const condition = conditions.find((c) => c.id === conditionId);
      if (!condition) {
        continue;
      }

      const isConditionMet = this.evaluateCondition(condition, values);

      if (isConditionMet) {
        this.applyActionsToTargetFields(condition.targetFields, updatedFields);
      }
    }

    // Ensure all fields have the required visibility properties
    return updatedFields.map((field) => ({
      ...field,
      visibleToCustomer: field.visibleToCustomer ?? true,
      editableByCustomer: field.editableByCustomer ?? true,
    }));
  }

  /**
   * Apply actions to target fields when conditions are met
   */
  private applyActionsToTargetFields(
    targetFields: TargetField[],
    fields: Field[],
  ): void {
    for (const targetField of targetFields) {
      const actualTargetFieldId = targetField.fieldId || targetField.id;
      const fieldIndex = fields.findIndex((f) => f.id === actualTargetFieldId);

      if (fieldIndex === -1) {
        this.logger.warn(
          `Target field ID "${actualTargetFieldId}" not found in fields list during condition application.`,
        );
        continue;
      }

      switch (targetField.type) {
        case TargetFieldActionType.ADD_FIELD:
          fields[fieldIndex].editableByCustomer = true;
          fields[fieldIndex].visibleToCustomer = true;
          break;
        case TargetFieldActionType.REMOVE_FIELD:
          fields[fieldIndex].editableByCustomer = false;
          fields[fieldIndex].visibleToCustomer = false;
          fields[fieldIndex].mandatoryOnCreation = false;
          if ('mandatoryOnClose' in fields[fieldIndex]) {
            fields[fieldIndex].mandatoryOnClose = false;
          }
          break;
        case TargetFieldActionType.MARK_MANDATORY:
          fields[fieldIndex].mandatoryOnCreation = true;
          if ('mandatoryOnClose' in fields[fieldIndex]) {
            fields[fieldIndex].mandatoryOnClose = true;
          }
          break;
        case TargetFieldActionType.MARK_NON_MANDATORY:
          fields[fieldIndex].mandatoryOnCreation = false;
          if ('mandatoryOnClose' in fields[fieldIndex]) {
            fields[fieldIndex].mandatoryOnClose = false;
          }
          break;
        case TargetFieldActionType.FILL_VALUE:
          this.logger.debug(
            `FILL_VALUE action encountered for target field ${actualTargetFieldId}. Value to fill: ${targetField.value}. This is generally handled by initial_value or external value management.`,
          );
          break;
      }
    }
  }

  /**
   * Evaluate if a condition is met based on form values
   */
  private evaluateCondition(condition: Condition, values: FormValues): boolean {
    const triggerValue = values[condition.triggerFieldId];
    const isEmptyCondition =
      condition.conditionType === TargetFieldConditionsType.IS_EMPTY ||
      condition.conditionType === TargetFieldConditionsType.IS_NOT_EMPTY;

    // Special handling for undefined trigger values
    if (triggerValue === undefined && !isEmptyCondition) {
      this.logger.debug(
        `Condition ${condition.id} on trigger ${condition.triggerFieldId}: triggerValue is undefined, condition not IS_EMPTY/IS_NOT_EMPTY. Returning false.`,
      );
      return false;
    }

    let result = false;

    switch (condition.conditionType) {
      case TargetFieldConditionsType.EQUALS:
        result = this.evaluateEqualsCondition(triggerValue, condition.value);
        break;

      case TargetFieldConditionsType.NOT_EQUALS:
        result = !this.evaluateEqualsCondition(triggerValue, condition.value);
        break;

      case TargetFieldConditionsType.CONTAINS:
        result = this.evaluateContainsCondition(triggerValue, condition.value);
        break;

      case TargetFieldConditionsType.NOT_CONTAINS:
        result = !this.evaluateContainsCondition(triggerValue, condition.value);
        break;

      case TargetFieldConditionsType.GREATER_THAN:
        result = Number(triggerValue) > Number(condition.value);
        break;

      case TargetFieldConditionsType.GREATER_THAN_EQUAL:
        result = Number(triggerValue) >= Number(condition.value);
        break;

      case TargetFieldConditionsType.LESS_THAN:
        result = Number(triggerValue) < Number(condition.value);
        break;

      case TargetFieldConditionsType.LESS_THAN_EQUAL:
        result = Number(triggerValue) <= Number(condition.value);
        break;

      case TargetFieldConditionsType.IS_EMPTY:
        result = this.isEmptyValue(triggerValue);
        break;

      case TargetFieldConditionsType.IS_NOT_EMPTY:
        result = !this.isEmptyValue(triggerValue);
        break;

      default:
        this.logger.warn(
          `Unknown condition type: ${condition.conditionType} for condition ID ${condition.id}`,
        );
        result = false;
    }

    this.logger.debug(
      `Condition ${condition.id} (trigger: ${condition.triggerFieldId}, ` +
        `type: ${condition.conditionType}, value: ${JSON.stringify(condition.value)}, ` +
        `triggerVal: ${JSON.stringify(triggerValue)}) evaluated to: ${result}`,
    );

    return result;
  }

  /**
   * Helper method to evaluate an equals condition
   */
  private evaluateEqualsCondition(
    triggerValue: any,
    conditionValue: any,
  ): boolean {
    if (Array.isArray(conditionValue)) {
      if (Array.isArray(triggerValue)) {
        return (
          conditionValue.length === triggerValue.length &&
          conditionValue.every((v) => triggerValue.includes(v)) &&
          triggerValue.every((v) =>
            Array.isArray(conditionValue)
              ? conditionValue.includes(v)
              : String(conditionValue).includes(String(v)),
          )
        );
      }
      return conditionValue.some((v) => v === triggerValue);
    }

    if (Array.isArray(triggerValue)) {
      return triggerValue.some((tv) => tv === conditionValue);
    }

    return triggerValue === conditionValue;
  }

  /**
   * Helper method to evaluate a contains condition
   */
  private evaluateContainsCondition(
    triggerValue: any,
    conditionValue: any,
  ): boolean {
    if (triggerValue === undefined || triggerValue === null) {
      return false;
    }

    const conditionStr = String(conditionValue || '').toLowerCase();

    if (Array.isArray(triggerValue)) {
      return triggerValue.some((v) =>
        String(v).toLowerCase().includes(conditionStr),
      );
    }

    return String(triggerValue).toLowerCase().includes(conditionStr);
  }

  /**
   * Helper method to check if a value is empty
   */
  private isEmptyValue(value: any): boolean {
    return (
      value === undefined ||
      value === null ||
      String(value).trim() === '' ||
      (Array.isArray(value) && value.length === 0)
    );
  }

  /**
   * Render a field with appropriate Slack Block Kit elements
   */
  private renderField(
    field: Field,
    values: FormValues = {},
  ): SlackBlock[] | null {
    const actionId = `${ConditionalFormBuilderComposite.ACTION_PREFIX}${field.id}`;
    const fieldValue = values[field.id];

    const fieldTypeLowercase = field.type.toLowerCase();
    const originalType = field.metadata?.originalType?.toLowerCase() || '';

    // Format field values based on their expected types
    const stringValue = String(fieldValue ?? '');
    const numberValue = this.convertToNumber(fieldValue);
    const booleanValue = this.convertToBoolean(fieldValue);
    const arrayValue = this.convertToStringArray(fieldValue);

    // Check for special datetime field by name
    if (
      field.name.toLowerCase().includes('datetime') &&
      fieldTypeLowercase !== 'datetime' &&
      fieldTypeLowercase !== 'date_time' &&
      originalType !== 'datetime' &&
      originalType !== 'date_time'
    ) {
      this.logger.debug(
        `Dispatching to renderDateTimeInput based on field name override for ${field.id}`,
      );
      return this.renderDateTimeInput(field, actionId, stringValue);
    }

    this.logger.debug(
      `Rendering field ${field.id} (${field.name}) type: ${fieldTypeLowercase}, ` +
        `originalType: ${originalType}, initialValue: ${JSON.stringify(fieldValue)}`,
    );

    // Check if this is an email field
    const isEmailField =
      fieldTypeLowercase === 'email' ||
      originalType === 'email' ||
      field.name.toLowerCase().includes('email') ||
      (fieldTypeLowercase === 'specialized' &&
        (field.name.toLowerCase().includes('requester') ||
          field.name.toLowerCase().includes('email')));

    if (isEmailField) {
      return this.renderEmailInput(field, actionId, stringValue);
    }

    // Select the rendering method based on field type
    if (fieldTypeLowercase === 'specialized' && originalType) {
      return this.renderSpecializedField(
        field,
        actionId,
        stringValue,
        originalType,
      );
    }

    // Standard field types
    return this.renderStandardField(
      field,
      actionId,
      { stringValue, numberValue, booleanValue, arrayValue },
      fieldTypeLowercase,
    );
  }

  /**
   * Convert a value to a number
   */
  private convertToNumber(value: any): number {
    if (typeof value === 'number') {
      return value;
    }
    const num = Number(value);
    return Number.isNaN(num) ? 0 : num;
  }

  /**
   * Convert a value to a boolean
   */
  private convertToBoolean(value: any): boolean {
    if (typeof value === 'boolean') {
      return value;
    }
    if (typeof value === 'string') {
      return value.toLowerCase() === 'true' || value === '1';
    }
    if (typeof value === 'number') {
      return value === 1;
    }
    return false;
  }

  /**
   * Convert a value to a string array
   */
  private convertToStringArray(value: any): string[] {
    if (Array.isArray(value)) {
      return value.map(String);
    }
    return value ? [String(value)] : [];
  }

  /**
   * Render specialized fields based on originalType
   */
  private renderSpecializedField(
    field: Field,
    actionId: string,
    value: string,
    originalType: string,
  ): SlackBlock[] {
    this.logger.debug(
      `Field ${field.id} is 'specialized'. Dispatching based on originalType: '${originalType}'`,
    );

    switch (originalType) {
      case 'email':
        return this.renderEmailInput(field, actionId, value);
      case 'time':
        return this.renderTimeInput(field, actionId, value);
      case 'date':
        return this.renderDateInput(field, actionId, value);
      case 'datetime':
      case 'date_time':
        return this.renderDateTimeInput(field, actionId, value);
      default:
        this.logger.debug(
          `Unhandled 'specialized' field ${field.id} with originalType: '${originalType}'. Defaulting to text input.`,
        );
        return this.renderTextInput(field, actionId, value);
    }
  }

  /**
   * Render standard field types
   */
  private renderStandardField(
    field: Field,
    actionId: string,
    values: {
      stringValue: string;
      numberValue: number;
      booleanValue: boolean;
      arrayValue: string[];
    },
    fieldType: string,
  ): SlackBlock[] {
    const { stringValue, numberValue, booleanValue, arrayValue } = values;

    switch (fieldType) {
      case 'text':
      case 'textarea':
      case 'string':
        return this.renderTextInput(field, actionId, stringValue);
      case 'choice':
      case 'select':
      case 'dropdown':
        return this.renderSelectInput(field, actionId, stringValue);
      case 'radio':
      case 'radio_button':
        return this.renderRadioInput(field, actionId, stringValue);
      case 'multiselect':
        return this.renderMultiSelectInput(field, actionId, arrayValue);
      case 'number':
        return this.renderNumberInput(field, actionId, numberValue);
      case 'date':
        return this.renderDateInput(field, actionId, stringValue);
      case 'time':
        return this.renderTimeInput(field, actionId, stringValue);
      case 'date_time':
      case 'datetime':
        return this.renderDateTimeInput(field, actionId, stringValue);
      case 'boolean':
      case 'checkbox':
        return this.renderCheckboxInput(field, actionId, booleanValue);
      default:
        this.logger.warn(
          `Unhandled field type '${fieldType}' for field ${field.id}. Defaulting to text input.`,
        );
        return this.renderTextInput(field, actionId, stringValue);
    }
  }

  /**
   * Create a standard input block with common properties
   */
  private createInputBlock(
    field: Field,
    actionId: string,
    element: SlackBlockElement,
    additionalBlocks: SlackBlock[] = [],
  ): SlackBlock[] {
    const blockId = `block_${field.id}`;
    const inputBlock: SlackBlock = {
      type: 'input',
      block_id: blockId,
      dispatch_action: true,
      element: { ...element, action_id: actionId },
      label: { type: 'plain_text', text: field.name, emoji: true },
      optional: !field.mandatoryOnCreation,
    };

    return [inputBlock, ...additionalBlocks];
  }

  /**
   * Create context block for hints and errors
   */
  private createContextBlock(
    blockId: string,
    text: string,
    isError = false,
  ): SlackBlock {
    return {
      type: 'context',
      block_id: `${blockId}_${isError ? 'error' : 'hint'}`,
      elements: [
        {
          type: 'mrkdwn',
          text: isError ? `:warning: *${text}*` : `_${text}_`,
        },
      ],
    };
  }

  /**
   * Render a text input field
   */
  private renderTextInput(
    field: Field,
    actionId: string,
    value: string,
  ): SlackBlock[] {
    return this.createInputBlock(field, actionId, {
      type: 'plain_text_input',
      initial_value: value,
      placeholder: {
        type: 'plain_text',
        text:
          field.metadata?.placeholder || `Enter ${field.name.toLowerCase()}`,
      },
      multiline: field.type.toLowerCase() === 'textarea',
      min_length: field.metadata?.minLength,
      max_length: field.metadata?.maxLength,
      dispatch_action_config: { trigger_actions_on: ['on_character_entered'] },
    });
  }

  /**
   * Render an email input field with validation
   */
  private renderEmailInput(
    field: Field,
    actionId: string,
    value: string,
  ): SlackBlock[] {
    // Add hint block with email format instructions
    const hintBlock = this.createContextBlock(
      `block_${field.id}`,
      field.metadata?.hintText ||
        'Must be a valid email format (e.g., <EMAIL>)',
    );

    // Check email validation
    const blocks = this.createInputBlock(
      field,
      actionId,
      {
        type: 'plain_text_input',
        initial_value: value,
        placeholder: {
          type: 'plain_text',
          text: field.metadata?.placeholder || 'Enter a valid email address',
        },
        dispatch_action_config: {
          trigger_actions_on: ['on_character_entered'],
        },
      },
      [hintBlock],
    );

    // Add error message if email is invalid
    if (value) {
      // Use the central validation utility
      const emailValidation = isValidEmail(value);
      if (!emailValidation.isValid) {
        this.logger.debug(
          `Invalid email format detected: ${value} - ${emailValidation.errorMessage}`,
        );

        const errorBlock = this.createContextBlock(
          `block_${field.id}`,
          emailValidation.errorMessage || 'Please enter a valid email address',
          true,
        );

        blocks.splice(1, 0, errorBlock);
      }
    }

    return blocks;
  }

  /**
   * Render a select input field
   */
  private renderSelectInput(
    field: Field,
    actionId: string,
    value: string,
  ): SlackBlock[] {
    const baseElementProps = {
      placeholder: {
        type: 'plain_text' as const,
        text:
          field.metadata?.placeholder || `Select ${field.name.toLowerCase()}`,
      },
    };

    // Handle external options via API
    if (field.apiForOptions) {
      return this.createInputBlock(field, actionId, {
        type: 'external_select',
        ...baseElementProps,
        min_query_length: 0,
        initial_option: value
          ? { text: { type: 'plain_text', text: value }, value }
          : undefined,
      });
    }

    // Handle static options
    const options =
      field.options?.map((opt) => ({
        text: { type: 'plain_text' as const, text: opt.label, emoji: true },
        value: opt.value,
      })) || [];

    let initialOptionObj = undefined;
    if (value) {
      const foundOption = options.find((opt) => opt.value === value);
      if (foundOption) {
        initialOptionObj = foundOption;
      } else {
        initialOptionObj = {
          text: { type: 'plain_text' as const, text: value, emoji: true },
          value,
        };
        this.logger.warn(
          `Initial value "${value}" for select field "${field.name}" not found in predefined options. Rendering with value as text.`,
        );
      }
    }

    return this.createInputBlock(field, actionId, {
      type: 'static_select',
      ...baseElementProps,
      initial_option: initialOptionObj,
      options,
    });
  }

  /**
   * Render a multi-select input field
   */
  private renderMultiSelectInput(
    field: Field,
    actionId: string,
    values: string[],
  ): SlackBlock[] {
    const baseElementProps = {
      placeholder: {
        type: 'plain_text' as const,
        text:
          field.metadata?.placeholder || `Select ${field.name.toLowerCase()}`,
      },
    };

    // Handle external options via API
    if (field.apiForOptions) {
      return this.createInputBlock(field, actionId, {
        type: 'multi_external_select',
        ...baseElementProps,
        min_query_length: 0,
      });
    }

    // Handle static options
    const allOptions =
      field.options?.map((opt) => ({
        text: { type: 'plain_text' as const, text: opt.label, emoji: true },
        value: opt.value,
      })) || [];

    // Find all valid initial options
    const initialOptions = values
      .map((val) => {
        const found = allOptions.find((opt) => opt.value === val);
        if (found) {
          return found;
        }

        this.logger.warn(
          `Initial value "${val}" for multi-select field "${field.name}" not found in predefined options. It will not be pre-selected.`,
        );
        return null;
      })
      .filter(Boolean) as any[];

    const elementProps: any = { ...baseElementProps, options: allOptions };
    if (initialOptions.length > 0) {
      elementProps.initial_options = initialOptions;
    }

    return this.createInputBlock(field, actionId, {
      type: 'multi_static_select',
      ...elementProps,
    });
  }

  /**
   * Render a number input field
   */
  private renderNumberInput(
    field: Field,
    actionId: string,
    value: number,
  ): SlackBlock[] {
    const isDecimalAllowed =
      ['currency', 'float', 'decimal', 'double'].includes(
        (field.metadata?.originalType || '').toLowerCase(),
      ) || field.type.toLowerCase() === 'float';

    return this.createInputBlock(field, actionId, {
      type: 'number_input',
      is_decimal_allowed: isDecimalAllowed,
      initial_value: value?.toString(),
      placeholder: {
        type: 'plain_text',
        text:
          field.metadata?.placeholder || `Enter ${field.name.toLowerCase()}`,
      },
      min_value: field.metadata?.minValue?.toString(),
      max_value: field.metadata?.maxValue?.toString(),
    });
  }

  /**
   * Render a date input field
   */
  private renderDateInput(
    field: Field,
    actionId: string,
    value: string,
  ): SlackBlock[] {
    const isValidDate = value && /^\d{4}-\d{2}-\d{2}$/.test(value);

    if (value && !isValidDate) {
      this.logger.warn(
        `Invalid initial_date format "${value}" for field "${field.name}". Expected YYYY-MM-DD. Field will have no initial date.`,
      );
    }

    return this.createInputBlock(field, actionId, {
      type: 'datepicker',
      initial_date: isValidDate ? value : undefined,
      placeholder: {
        type: 'plain_text',
        text:
          field.metadata?.placeholder || `Select ${field.name.toLowerCase()}`,
      },
    });
  }

  /**
   * Render a checkbox input field
   */
  private renderCheckboxInput(
    field: Field,
    actionId: string,
    value: boolean,
  ): SlackBlock[] {
    // Determine checkbox label text from options, metadata, or default
    let optionText = 'Yes';
    if (field.options?.length > 0 && field.options[0].label) {
      optionText = field.options[0].label;
    } else if (field.metadata?.checkboxLabel) {
      optionText = field.metadata.checkboxLabel;
    }

    const singleOption = [
      {
        text: { type: 'plain_text' as const, text: optionText, emoji: true },
        value: 'true',
      },
    ];

    const initialOptions = value ? singleOption : [];

    return this.createInputBlock(field, actionId, {
      type: 'checkboxes',
      options: singleOption,
      ...(initialOptions.length > 0 ? { initial_options: initialOptions } : {}),
    });
  }

  /**
   * Render a radio input field
   */
  private renderRadioInput(
    field: Field,
    actionId: string,
    value: string,
  ): SlackBlock[] {
    const options =
      field.options?.map((opt) => ({
        text: { type: 'plain_text' as const, text: opt.label, emoji: true },
        value: opt.value,
      })) || [];

    let initialOptionObj = undefined;
    if (value) {
      initialOptionObj = options.find((opt) => opt.value === value);
      if (!initialOptionObj && value.trim() !== '') {
        this.logger.warn(
          `Initial value "${value}" for radio field "${field.name}" not found in predefined options. It will not be pre-selected.`,
        );
      }
    }

    return this.createInputBlock(field, actionId, {
      type: 'radio_buttons',
      options,
      ...(initialOptionObj ? { initial_option: initialOptionObj } : {}),
    });
  }

  /**
   * Render a time input field
   */
  private renderTimeInput(
    field: Field,
    actionId: string,
    value: string,
  ): SlackBlock[] {
    this.logger.debug(
      `renderTimeInput called for field ${field.id} with value: ${value}`,
    );

    let processedInitialTime: string | undefined = undefined;

    if (value?.trim()) {
      if (/^\d{2}:\d{2}$/.test(value)) {
        processedInitialTime = value;
      } else {
        try {
          const date = new Date(value);
          if (!Number.isNaN(date.getTime())) {
            const hours = date.getUTCHours().toString().padStart(2, '0');
            const minutes = date.getUTCMinutes().toString().padStart(2, '0');
            processedInitialTime = `${hours}:${minutes}`;
            this.logger.debug(
              `Converted time from "${value}" to UTC HH:MM format: ${processedInitialTime} for field ${field.id}`,
            );
          } else {
            this.logger.warn(
              `Invalid date/time string for time input: "${value}" for field ${field.id}. Field will have no initial time.`,
            );
          }
        } catch (error) {
          this.logger.error(
            `Error parsing time value: "${value}" for field ${field.id}. Field will have no initial time.`,
            error instanceof Error ? error.message : String(error),
          );
        }
      }
    }

    return this.createInputBlock(field, actionId, {
      type: 'timepicker',
      ...(processedInitialTime ? { initial_time: processedInitialTime } : {}),
      placeholder: {
        type: 'plain_text',
        text:
          field.metadata?.placeholder || `Select ${field.name.toLowerCase()}`,
        emoji: true,
      },
    });
  }

  /**
   * Render a datetime input field
   */
  private renderDateTimeInput(
    field: Field,
    actionId: string,
    value: string,
  ): SlackBlock[] {
    this.logger.debug(
      `renderDateTimeInput called for field ${field.id} with value: ${value}`,
    );

    let initialTimestamp: number | undefined;

    if (value) {
      try {
        // Handle various datetime formats
        let dateObj = new Date(value);

        // Special handling for YYYY-MM-DD HH:MM:SS format without timezone
        if (
          typeof value === 'string' &&
          !value.includes('T') &&
          !value.endsWith('Z') &&
          !/[+-]\d{2}:\d{2}/.test(value)
        ) {
          if (/^\d{4}-\d{2}-\d{2} \d{2}:\d{2}:\d{2}$/.test(value)) {
            dateObj = new Date(`${value}Z`);
            this.logger.debug(
              `Appended 'Z' to datetime string "${value}" for UTC parsing.`,
            );
          }
        }

        if (!Number.isNaN(dateObj.getTime())) {
          initialTimestamp = Math.floor(dateObj.getTime() / 1000); // Convert to Unix timestamp (seconds)
          this.logger.debug(
            `Converted datetime "${value}" to Unix timestamp: ${initialTimestamp} ` +
              `(Date obj: ${dateObj.toISOString()}) for field ${field.id}`,
          );
        } else {
          this.logger.warn(
            `Invalid date format for datetime: "${value}" for field ${field.id}. Field will have no initial datetime.`,
          );
        }
      } catch (error) {
        this.logger.error(
          `Error parsing datetime value: "${value}" for field ${field.id}. Field will have no initial datetime.`,
          error instanceof Error ? error.message : String(error),
        );
      }
    }

    return this.createInputBlock(field, actionId, {
      type: 'datetimepicker',
      ...(initialTimestamp !== undefined
        ? { initial_datetime: initialTimestamp }
        : {}),
    });
  }
}
