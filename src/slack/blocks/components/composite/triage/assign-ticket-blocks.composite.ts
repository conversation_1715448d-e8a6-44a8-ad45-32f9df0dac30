import { Injectable } from '@nestjs/common';
import { SlackCoreComponent } from '../../core';

@Injectable()
export class SelectAssigneeComposite implements SlackCoreComponent {
  static readonly BLOCK_ID = 'assign_ticket_modal';
  static readonly ACTION_IDS = {
    ASSIGNEE_SELECT: 'assignee_select',
  };

  build() {
    return {
      blocks: [
        {
          type: 'input',
          block_id: SelectAssigneeComposite.BLOCK_ID,
          element: {
            type: 'external_select',
            action_id: SelectAssigneeComposite.ACTION_IDS.ASSIGNEE_SELECT,
            placeholder: {
              type: 'plain_text',
              text: 'Search for an assignee',
            },
            min_query_length: 0,
          },
          label: {
            type: 'plain_text',
            text: 'Assignee',
          },
        },
      ],
    };
  }
}
