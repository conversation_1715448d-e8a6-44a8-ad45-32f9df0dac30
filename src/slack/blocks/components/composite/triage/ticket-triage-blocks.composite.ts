import { Inject, Injectable } from '@nestjs/common';
import {
  ConfigKeys,
  ConfigService,
} from '../../../../../config/config.service';
import { Users } from '../../../../../database/entities';
import {
  CUSTOM_LOGGER_TOKEN,
  ILogger,
  SLACK_ACTION_NOOP,
} from '../../../../../utils';
import { SlackCoreComponent } from '../../core';
import { formatTimestamp } from '../../../../../utils/common';

interface Ticket {
  id: string;
  ticketId: string;
  ticketTeamId: string;
  priority: string;
  priorityId: string;
  status: string;
  statusId: string;
  permalink?: string;
  assignedAgent?: Partial<{
    id: string;
    name: string;
    email: string;
  }>;
  customer: {
    name?: string;
    email: string;
    company?: string;
  };
  subject: string;
  content: string;
  timestamp: string;
  archivedAt?: string;
  sentiment?: string;
  teamIdentifier?: string;
  updatedAt?: string;
}

interface SlackTriageData {
  ticket: Ticket;
  slackUserAssociated?: Users;
  isInternalThread?: boolean;
  isTriageMessage?: boolean;
  openedBy?: string;
}

/**
 * Creates Slack message blocks for ticket triage functionality
 */
@Injectable()
export class TriageMessageBlock implements SlackCoreComponent {
  constructor(
    private readonly configService: ConfigService,

    @Inject(CUSTOM_LOGGER_TOKEN) private readonly logger: ILogger,
  ) {}

  static readonly BLOCK_ID = 'support_triage_block';
  static readonly ACTION_IDS = {
    STATUS: 'update_status',
    PRIORITY: 'set_priority',
    ASSIGN: 'assign_ticket',
    LOOKING: 'looking_into_ticket',
    CLOSE: 'close_ticket',
    ARCHIVE: 'archive_ticket',
  };

  /**
   * Builds the Slack message blocks for ticket triage
   * @param data The ticket and related data
   * @returns Slack message blocks
   */
  build(data: SlackTriageData) {
    const { ticket, slackUserAssociated, isInternalThread, openedBy, isTriageMessage } = data;

    // Build the linked thread
    let linkedThread = '';
    if (isInternalThread) {
      linkedThread = `<${this.getTicketPermalink(ticket)}|this ticket>`;
    }
    else if (ticket.permalink) {
      linkedThread = `<${ticket.permalink}|This message>`;
    } else {
      linkedThread = `<${this.getTicketPermalink(ticket)}|This ticket>`;
    }

    // Build the head text
    let headText = `${ticket.ticketId}: ${linkedThread} is flagged as a ticket.`;
    
    if (isInternalThread) {
      linkedThread = `<${this.getTicketPermalink(ticket)}|${ticket.teamIdentifier}#${ticket.ticketId}>`;
      headText = `${linkedThread}: Internal thread opened by ${openedBy || 'a user'} for this ticket.`;
    } else if (slackUserAssociated && !isTriageMessage) {
      headText = `#${ticket.ticketId}: ${linkedThread} is flagged as a ticket. <@${slackUserAssociated.slackId}> is looking into it.`;
    }else if(ticket?.assignedAgent?.name && !isTriageMessage){
      headText =  `#${ticket.ticketId}: ${linkedThread} is flagged as a ticket. <@${slackUserAssociated.slackId}> is looking into it.`;
    }
    else if (ticket?.assignedAgent?.name && isTriageMessage) {
      // If the ticket is resolved, then we need to add a different head text
      headText = `#${ticket.teamIdentifier}-${ticket.ticketId}: ${linkedThread} is flagged as a ticket. _${ticket.sentiment}_ sentiment with _${ticket.priority}_ priority. \n\n <@${slackUserAssociated.slackId}> is assigned to the ticket.`;
    }else if(isTriageMessage){
      headText = `#${ticket.teamIdentifier}-${ticket.ticketId}: ${linkedThread} is flagged as a ticket. _${ticket.sentiment}_ sentiment with _${ticket.priority}_ priority.`;
    }
    if(ticket.status == "Closed") {

      headText = `#${ticket.teamIdentifier}-${ticket.ticketId}: ${linkedThread} has been closed ${formatTimestamp(ticket.updatedAt)}`;
    }

    // If the ticket is archived, then we need to add a different head text
    if (ticket.archivedAt) {
      headText = `#${ticket.ticketId}: ${linkedThread} is archived.`;
    }

    return {
      blocks: [
        {
          type: 'context',
          elements: [{ type: 'mrkdwn', text: headText }],
        },

        // Render the interactivity for the ticket
        ...this.renderInteractivity(ticket, isInternalThread),
      ],
    };
  }

  /**
   * Render the interactivity for the ticket
   * @param ticket Ticket
   * @param isInternalThread Whether this is an internal thread
   * @returns Interactivity blocks
   */
  private renderInteractivity(ticket: Ticket, isInternalThread: boolean) {
    // If the ticket is archived, then we don't need to render the interactivity
    if (ticket.archivedAt) {
      this.logger.log(
        `Ticket ${ticket.id} is archived, skipping interactivity`,
      );
      return [];
    }

    if (isInternalThread) {
      return [];
    }

    const ticketBoardUrl = this.getTicketPermalink(ticket);

    if(ticket.status == "Closed") {
      return [
        {
          type: 'actions',
          elements: [
            {
              type: 'external_select',
              placeholder: {
                type: 'plain_text',
                text: 'Status',
                emoji: true,
              },
              min_query_length: 0,
              initial_option: {
                text: {
                  type: 'plain_text',
                  text: ticket.status,
                },
                value: ticket.statusId,
              },
            action_id: TriageMessageBlock.ACTION_IDS.STATUS,

            },
       
          ],
        },
      ]
    }

    // Status and Priority Selects
    return [
      {
        type: 'actions',
        elements: [
          {
            type: 'external_select',
            placeholder: {
              type: 'plain_text',
              text: 'Status',
              emoji: true,
            },
          
            min_query_length: 0,
            initial_option: {
              text: {
                type: 'plain_text',
                text: ticket.status,
              },
              value: ticket.statusId,
            },
            action_id: TriageMessageBlock.ACTION_IDS.STATUS,
          },
          {
            type: 'external_select',
            placeholder: {
              type: 'plain_text',
              text: 'Priority',
              emoji: true,
            },
            min_query_length: 0,
            initial_option: {
              text: {
                type: 'plain_text',
                text: ticket.priority,
              },
              value: ticket.priorityId,
            },
            action_id: TriageMessageBlock.ACTION_IDS.PRIORITY,
          },
        ],
      },

      // Action Buttons
      {
        type: 'actions',
        elements: [
          // {
          //   type: 'button',
          //   text: {
          //     type: 'plain_text',
          //     text: 'Looking into it',
          //     emoji: false,
          //   },
          //   action_id: TriageMessageBlock.ACTION_IDS.LOOKING,
          // },
          {
            type: 'button',
            text: {
              type: 'plain_text',
              text: 'Assign',
              emoji: false,
            },
            action_id: TriageMessageBlock.ACTION_IDS.ASSIGN,
          },
          {
            type: 'button',
            text: {
              type: 'plain_text',
              text: 'Close',
              emoji: false,
            },
            style: 'primary',
            action_id: TriageMessageBlock.ACTION_IDS.CLOSE,
            confirm: {
              title: {
                type: 'plain_text',
                text: 'Close ticket',
              },
              text: {
                type: 'mrkdwn',
                text: 'This will mark the ticket as closed. Continue?',
              },
              confirm: {
                type: 'plain_text',
                text: 'Yes',
              },
              deny: {
                type: 'plain_text',
                text: 'No',
              },
            },
          },
          {
            type: 'button',
            text: {
              type: 'plain_text',
              text: 'Archive',
              emoji: false,
            },
            style: 'danger',
            action_id: TriageMessageBlock.ACTION_IDS.ARCHIVE,
            confirm: {
              title: {
                type: 'plain_text',
                text: 'Archive ticket',
              },
              text: {
                type: 'mrkdwn',
                text: 'Are you sure you wanna archive this ticket?',
              },
              confirm: {
                type: 'plain_text',
                text: 'Yes',
              },
              deny: {
                type: 'plain_text',
                text: 'No',
              },
            },
          },
          {
            type: 'button',
            text: {
              type: 'plain_text',
              text: 'View in Thena',
              emoji: false,
            },
            action_id: SLACK_ACTION_NOOP,
            url: ticketBoardUrl,
          },
        ],
      },
    ];
  }

  /**
   * Get the permalink for a ticket
   * @param ticket The ticket to get a permalink for
   * @returns The permalink URL
   */
  private getTicketPermalink(ticket: Ticket): string {
    const baseUIUrl = this.configService.get(ConfigKeys.THENA_WEB_URL);
    return `${baseUIUrl}/dashboard/${ticket.ticketTeamId}?ticketId=${ticket.id}`;
    
  }

  
}