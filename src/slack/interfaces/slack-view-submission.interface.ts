export interface ViewSubmissionPayload {
  type: 'view_submission';
  team: {
    id: string;
    domain: string;
  };
  user: {
    id: string;
    username: string;
    name: string;
    team_id: string;
  };
  api_app_id: string; // Often present
  token: string; // Often present (verification token, not auth token)
  trigger_id: string; // Often present
  view: {
    id: string;
    app_id: string; // Often present
    external_id?: string;
    app_installed_team_id?: string; // Often present
    bot_id?: string; // Often present
    root_view_id?: string; // Often present
    previous_view_id?: string; // Often present
    callback_id: string;
    title: {
      // Often present
      type: string;
      text: string;
      emoji?: boolean;
    };
    submit?: {
      // Often present
      type: string;
      text: string;
      emoji?: boolean;
    };
    close?: {
      // Often present
      type: string;
      text: string;
      emoji?: boolean;
    };
    state: {
      values: {
        [blockId: string]: {
          [actionId: string]: {
            type: string;
            value?: string | null; // For plain_text_input, number_input
            selected_date?: string | null; // For datepicker
            selected_time?: string | null; // For timepicker
            selected_datetime?: number | null; // For datetimepicker (Unix timestamp in seconds)
            selected_option?: {
              // For static_select, radio_buttons
              text: {
                type: string;
                text: string;
                emoji?: boolean;
              };
              value: string;
            } | null;
            selected_options?: Array<{
              // For multi_static_select, checkboxes
              text: {
                type: string;
                text: string;
                emoji?: boolean;
              };
              value: string;
            }> | null;
            // Add other types as needed, e.g., selected_conversation, selected_channel, etc.
          };
        };
      };
    };
    private_metadata?: string;
    hash: string; // Often present
  };
  response_urls?: Array<{
    // Present if response_url_enabled was true for a select menu
    block_id: string;
    action_id: string;
    channel_id: string;
    response_url: string;
  }>;
  is_enterprise_install?: boolean; // Often present
}
