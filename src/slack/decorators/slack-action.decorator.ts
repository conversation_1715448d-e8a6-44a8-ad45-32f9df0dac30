/**
 * Metadata key for the action ID.
 */
export const ACTION_METADATA_KEY = 'thena_slack:action_id';

/**
 * Decorator to mark a method as a Slack action handler.
 * @param actionId The ID of the action to handle.
 * @returns A decorator function that adds the action ID to the target method.
 */
export const SlackAction = (actionId: string | Array<string>) => {
  return (target: any) => {
    Reflect.defineMetadata(
      ACTION_METADATA_KEY,
      Array.isArray(actionId) ? actionId : [actionId],
      target,
    );
  };
};
