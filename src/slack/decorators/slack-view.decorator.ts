/**
 * Metadata key for the view ID.
 */
export const VIEW_METADATA_KEY = 'thena_slack:view_id';

/**
 * Decorator to mark a method as a Slack view handler.
 * @param viewId The ID of the view to handle.
 * @returns A decorator function that adds the view ID to the target method.
 */
export const SlackView = (viewId: string | Array<string>) => {
  return (target: any) => {
    Reflect.defineMetadata(
      VIEW_METADATA_KEY,
      Array.isArray(viewId) ? viewId : [viewId],
      target,
    );
  };
};
