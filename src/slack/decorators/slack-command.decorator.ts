/**
 * Metadata key for the command ID.
 */
export const COMMAND_METADATA_KEY = 'thena_slack:command';

/**
 * Decorator to mark a class as a Slack command handler.
 * @param command The command to handle.
 * @returns A decorator function that adds the command ID to the target class.
 */
export const SlackCommand = (command: string): ClassDecorator => {
  return (target: any) => {
    Reflect.defineMetadata(COMMAND_METADATA_KEY, command, target);
  };
};
