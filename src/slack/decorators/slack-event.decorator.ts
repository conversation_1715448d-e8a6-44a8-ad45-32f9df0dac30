/**
 * Metadata key for the event ID.
 */
export const EVENT_METADATA_KEY = 'thena_slack:event_id';

/**
 * Decorator to mark a method as a Slack event handler.
 * @param event The ID of the event to handle.
 * @returns A decorator function that adds the event ID to the target method.
 */
export const SlackEvent = (event: string | Array<string>) => {
  return (target: any) => {
    Reflect.defineMetadata(
      EVENT_METADATA_KEY,
      Array.isArray(event) ? event : [event],
      target,
    );
  };
};
