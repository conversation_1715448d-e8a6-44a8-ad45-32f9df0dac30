import { Channel } from '@slack/web-api/dist/types/response/ConversationsJoinResponse';
import {
  IsArray,
  IsBoolean,
  IsNotEmpty,
  IsOptional,
  IsString,
} from 'class-validator';

export class PostMessageDTO {
  @IsString()
  @IsNotEmpty()
  channel: string;

  @IsString()
  @IsNotEmpty()
  text: string;

  @IsString()
  @IsOptional()
  threadTs?: string;

  @IsArray()
  @IsOptional()
  blocks?: any;

  @IsBoolean()
  @IsOptional()
  unfurlLinks?: boolean;

  @IsBoolean()
  @IsOptional()
  unfurlMedia?: boolean;
}

export class PostMessageResponseDTO {
  ok: boolean;
  channel?: string;
  ts?: string;
  message?: {
    text?: string;
  };
}

export class UpdateMessageDTO {
  @IsString()
  @IsNotEmpty()
  channel: string;

  @IsString()
  @IsNotEmpty()
  text: string;

  @IsString()
  @IsNotEmpty()
  ts: string;

  @IsString()
  @IsOptional()
  blocks?: string;
}

export class UpdateMessageResponseDTO {
  ok: boolean;
  channel?: string;
  ts?: string;
  text?: string;
}
export class DeleteMessageDTO {
  @IsString()
  @IsNotEmpty()
  channel: string;

  @IsString()
  @IsNotEmpty()
  ts: string;
}

export class DeleteMessageResponseDTO {
  ok: boolean;
}

export class AddReactionDTO {
  @IsString()
  @IsNotEmpty()
  channel: string;

  @IsString()
  @IsNotEmpty()
  ts: string;

  @IsString()
  @IsNotEmpty()
  name: string;
}

export class AddReactionResponseDTO {
  ok: boolean;
}

export class RemoveReactionDTO {
  @IsString()
  @IsNotEmpty()
  channel: string;

  @IsString()
  @IsNotEmpty()
  ts: string;

  @IsString()
  @IsNotEmpty()
  name: string;
}

export class RemoveReactionResponseDTO {
  ok: boolean;
}

export class InviteUserToConversationDTO {
  @IsString()
  @IsNotEmpty()
  channel: string;

  @IsString()
  @IsNotEmpty()
  user: string;
}

export class InviteUserToConversationResponseDTO {
  ok: boolean;
}

export class KickUserFromConversationDTO {
  @IsString()
  @IsNotEmpty()
  channel: string;

  @IsString()
  @IsNotEmpty()
  user: string;
}

export class KickUserFromConversationResponseDTO {
  ok: boolean;
}

export class LeaveConversationDTO {
  @IsString()
  @IsNotEmpty()
  channel: string;
}

export class LeaveConversationResponseDTO {
  ok: boolean;
}

export class JoinConversationDTO {
  @IsString()
  @IsNotEmpty()
  channel: string;
}

export class JoinConversationResponseDTO {
  ok: boolean;
  channel?: Channel;
}
