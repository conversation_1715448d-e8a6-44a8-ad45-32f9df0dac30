import { Type } from 'class-transformer';
import {
  <PERSON><PERSON><PERSON>y,
  IsBoolean,
  IsN<PERSON>ber,
  IsOptional,
  IsString,
  Max,
  Min,
} from 'class-validator';

export class UpdateSettingsDTO {
  @IsNumber()
  @IsOptional()
  @Type(() => Number)
  @Min(0)
  @Max(1440)
  conversationWindow: number;

  @IsBoolean()
  @IsOptional()
  @Type(() => Boolean)
  automaticTickets: boolean;

  @IsBoolean()
  @IsOptional()
  @Type(() => Boolean)
  slashCommands: boolean;

  @IsBoolean()
  @IsOptional()
  @Type(() => Boolean)
  requireForm: boolean;

  @IsBoolean()
  @IsOptional()
  @Type(() => Boolean)
  thenaBotTaggingEnabled: boolean;

  @IsBoolean()
  @IsOptional()
  @Type(() => Boolean)
  ticketCommand: boolean;

  @IsBoolean()
  @IsOptional()
  @Type(() => Boolean)
  enableTicketCreationViaReaction: boolean;

  @IsBoolean()
  @IsOptional()
  @Type(() => Boolean)
  aiEnableExtendedThinking: boolean;

  @IsString()
  @IsOptional()
  @Type(() => String)
  aiModel: string;

  @IsNumber()
  @IsOptional()
  @Type(() => Number)
  aiTemperature: number;

  @IsNumber()
  @IsOptional()
  @Type(() => Number)
  aiMaxTokens: number;

  @IsArray()
  @IsOptional()
  @Type(() => String)
  selectedForms: string[];
}
