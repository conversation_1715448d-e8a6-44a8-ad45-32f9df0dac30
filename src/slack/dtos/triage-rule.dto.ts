import { Type } from 'class-transformer';
import {
  IsArray,
  IsBoolean,
  IsEnum,
  IsNotEmpty,
  IsOptional,
  IsString,
  ValidateIf,
  ValidateNested,
} from 'class-validator';
import { TriageCategory } from '../constants/triage-fields.constants';

export enum TriageOperator {
  EQUALS = '=',
  NOT_EQUALS = '!=',
  CONTAINS = 'contains',
  NOT_CONTAINS = 'not contains',
  IS_IN = 'in',
  IS_NOT_IN = 'not in',
}

export class TriageConditionDto {
  @IsEnum(TriageCategory)
  @IsNotEmpty()
  category: TriageCategory;

  @IsString()
  @IsNotEmpty()
  field: string;

  @IsEnum(TriageOperator)
  @IsNotEmpty()
  operator: TriageOperator;

  @IsString()
  @IsNotEmpty()
  value: string;
}

export class TriageRulesDto {
  @IsOptional()
  @IsArray()
  @ValidateNested({ each: true })
  @Type(() => TriageConditionDto)
  AND?: TriageConditionDto[];

  @IsOptional()
  @IsArray()
  @ValidateNested({ each: true })
  @Type(() => TriageConditionDto)
  OR?: TriageConditionDto[];
}

export class CommonTriageRuleDto {
  @IsBoolean()
  @IsOptional()
  isDefault?: boolean;
}

export class CreateTriageRuleDto extends CommonTriageRuleDto {
  @ValidateIf((o) => !o.isDefault)
  @ValidateNested()
  @Type(() => TriageRulesDto)
  triageRules?: TriageRulesDto;

  @IsArray()
  @IsString({ each: true })
  @IsNotEmpty({ each: true })
  channelIds: string[];
}

export class UpdateTriageRuleDto extends CommonTriageRuleDto {
  @IsBoolean()
  @IsOptional()
  isEnabled?: boolean;

  @ValidateIf((o) => !o.isDefault)
  @ValidateNested()
  @Type(() => TriageRulesDto)
  @IsOptional()
  triageRules?: TriageRulesDto;

  @IsArray()
  @IsString({ each: true })
  @IsOptional()
  channelIds: string[];
}
