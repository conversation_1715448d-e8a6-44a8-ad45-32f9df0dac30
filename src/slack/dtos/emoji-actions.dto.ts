import { <PERSON><PERSON><PERSON><PERSON>, <PERSON>NotEmpty, IsString } from 'class-validator';

export class MapEmojisActionDTO {
  @IsArray()
  @IsString({ each: true })
  @IsNotEmpty({ message: 'Emoji(s) to map against the action are required' })
  emojis: string[];

  @IsString()
  @IsNotEmpty({ message: 'Action to map against the emojis is required' })
  action: string;
}

export class UpdateEmojiActionDTO {
  @IsString()
  @IsNotEmpty({ message: 'Action to map against the emojis is required' })
  action: string;

  @IsArray()
  @IsString({ each: true })
  @IsNotEmpty({ message: 'Emoji(s) to map against the action are required' })
  emojis: string[];
}

export class UnmapEmojiActionDTO {
  @IsString()
  @IsNotEmpty({ message: 'Emoji to unmap from the action is required' })
  emoji: string;

  @IsString()
  @IsNotEmpty({ message: 'Action to unmap from the emoji is required' })
  action: string;
}
