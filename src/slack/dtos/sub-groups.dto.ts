import { IsNotEmpty, IsString } from 'class-validator';

export class MapSubGroupToSubTeamDTO {
  @IsString()
  @IsNotEmpty()
  slackSubGroupId: string;

  @IsString()
  @IsNotEmpty()
  platformSubGroupId: string;

  @IsString()
  @IsNotEmpty()
  platformTeamId: string;
}

export class UpdateSubGroupMappingDTO {
  @IsString()
  @IsNotEmpty()
  slackSubGroupId: string;

  @IsString()
  @IsNotEmpty()
  platformSubGroupId: string;
}
