import { Inject, Injectable } from '@nestjs/common';
import { Installations } from '../../database/entities';
import { TriageRules } from '../../database/entities/mappings/teams-triage-mappings.entity';
import { AnnotatorApiProvider } from '../../external/provider/annotator-api.provider';
import { CUSTOM_LOGGER_TOKEN, ILogger, SLACK_SENTRY_TAG } from '../../utils';
import { SentryService } from '../../utils/filters/sentry-alerts.filter';
import { TriageCategory } from '../constants/triage-fields.constants';
import { TriageOperator } from '../dtos/triage-rule.dto';
import { TriageFieldsService } from './triage-fields.service';

@Injectable()
export class TriageRuleEvaluatorService {
  constructor(
    @Inject(CUSTOM_LOGGER_TOKEN) private readonly logger: ILogger,
    private readonly triageFieldsService: TriageFieldsService,
    private readonly annotatorApiProvider: AnnotatorApiProvider,
    @Inject('Sentry') private readonly sentryService: SentryService,
  ) {}

  async validateField(
    category: TriageCategory,
    fieldPath: string,
    installation: Installations,
  ): Promise<boolean> {
    // Split field path to handle nested fields like "priority.name"
    const parts = fieldPath.split('.');
    const baseField = parts[0];

    // Get field mappings from Annotator API
    const fieldMappings = await this.triageFieldsService.getFieldMappings(
      category,
      installation,
      baseField,
    );

    // First check if the base field exists
    if (!(baseField in fieldMappings)) {
      return false;
    }

    // If we're dealing with a nested field, validate structure based on annotator API response
    if (parts.length > 1) {
      try {
        // Map category to entity type
        const entityType = this.mapCategoryToEntityType(category);
        if (!entityType) {
          return false;
        }

        // Get detailed field metadata from annotator API
        const metadata = await this.annotatorApiProvider.getEntityMetadata(
          installation,
          entityType,
          [baseField],
        );

        // Check if the base field exists in the metadata
        if (!metadata.fields[baseField]) {
          return false;
        }

        const baseFieldMetadata = metadata.fields[baseField];

        // Check if the base field has nested fields
        if (baseFieldMetadata.type === 'lookup' && baseFieldMetadata.fields) {
          // Get the nested property (e.g., "value" in "status.value")
          const nestedProp = parts[1];

          // Check if the nested property exists in the fields
          return nestedProp in baseFieldMetadata.fields;
        }

        // For other types of fields, check if they have a fields property
        if (baseFieldMetadata.fields && parts[1] in baseFieldMetadata.fields) {
          return true;
        }

        return false;
      } catch (error) {
        this.logger.error(
          `Error validating nested field ${fieldPath}: ${
            error instanceof Error ? error.message : 'Unknown error'
          }`,
          error instanceof Error ? error.stack : undefined,
        );

        // Report to Sentry
        this.sentryService.captureException(error, {
          tag: SLACK_SENTRY_TAG,
          name: '🚨 Error validating triage field',
          fieldPath,
          category,
          installationId: installation.id,
        });

        return false;
      }
    }

    // If it's a simple field (not nested), it's valid if it exists in mappings
    return true;
  }

  private mapCategoryToEntityType(category: TriageCategory): string | null {
    switch (category) {
      case TriageCategory.TICKET:
        return 'Ticket';
      case TriageCategory.ACCOUNT:
        return 'Account';
      case TriageCategory.CHANNEL:
        return null; // Channel metadata is not in Annotator
      default:
        return null;
    }
  }

  async validateFieldValue(
    category: TriageCategory,
    field: string,
    value: any,
    installation: Installations,
  ): Promise<boolean> {
    // Get base field type from mapping
    const baseField = field.split('.')[0];
    const fieldMappings = await this.triageFieldsService.getFieldMappings(
      category,
      installation,
      baseField,
    );

    const fieldType = fieldMappings[baseField];
    if (!fieldType) {
      return false;
    }

    // For nested fields, we validate the final value
    const actualValue =
      typeof value === 'object'
        ? this.getEntityFieldValue(value, field)
        : value;

    switch (fieldType) {
      case 'string':
        return typeof actualValue === 'string';
      case 'number':
        return (
          typeof actualValue === 'number' || !Number.isNaN(Number(actualValue))
        );
      case 'boolean':
        return (
          typeof actualValue === 'boolean' ||
          actualValue === 'true' ||
          actualValue === 'false'
        );
      case 'array':
        return Array.isArray(actualValue) || typeof actualValue === 'string';
      default:
        return false;
    }
  }

  private getEntityFieldValue(entity: any, fieldPath: string): any {
    // Handle nested paths like "priority.name"
    const parts = fieldPath.split('.');
    let value = entity;

    for (const part of parts) {
      if (value === null || value === undefined) {
        return undefined;
      }
      value = value[part];
    }

    return value;
  }

  async evaluateRules(rules: TriageRules, entity: any): Promise<boolean> {
    try {
      // Check AND conditions (all must match)
      if (rules.AND && rules.AND.length > 0) {
        for (const condition of rules.AND) {
          const matches = await this.evaluateCondition(condition, entity);
          if (!matches) {
            return false; // If any condition fails, the whole AND fails
          }
        }
        return true; // All AND conditions matched
      }

      // Check OR conditions (at least one must match)
      if (rules.OR && rules.OR.length > 0) {
        for (const condition of rules.OR) {
          const matches = await this.evaluateCondition(condition, entity);
          if (matches) {
            return true; // If any condition succeeds, the whole OR succeeds
          }
        }
        return false; // No OR conditions matched
      }

      // No conditions
      return false;
    } catch (error) {
      this.logger.error(
        `Error evaluating triage rules: ${
          error instanceof Error ? error.message : 'Unknown error'
        }`,
        error instanceof Error ? error.stack : undefined,
      );

      // Report to Sentry
      this.sentryService.captureException(error, {
        tag: SLACK_SENTRY_TAG,
        name: '🚨 Error evaluating triage rules',
        ruleCount: (rules.AND?.length || 0) + (rules.OR?.length || 0),
        entityType: entity?.ticket ? 'ticket' : entity?.account ? 'account' : 'channel',
      });

      return false;
    }
  }

  private async evaluateCondition(
    condition: any,
    entity: any,
  ): Promise<boolean> {
    const { category, field, operator, value: conditionValue } = condition;

    // Get the value for this field from the entity
    const entityCategory =
      category.toLowerCase() === 'ticket'
        ? entity.ticket
        : category.toLowerCase() === 'account'
          ? entity.account
          : entity.channel;

    if (!entityCategory) {
      return false;
    }

    const fieldValue = this.getEntityFieldValue(entityCategory, field);

    // If fieldValue is undefined or null, most comparisons should return false
    if (fieldValue == null) {
      // For NOT_EQUALS, null/undefined might be considered "not equal"
      return operator === TriageOperator.NOT_EQUALS;
    }

    // Handle array fields
    if (Array.isArray(fieldValue)) {
      const values = fieldValue;
      switch (operator) {
        case TriageOperator.CONTAINS:
          return values.some((v) =>
            String(v).toLowerCase().includes(conditionValue.toLowerCase()),
          );
        case TriageOperator.NOT_CONTAINS:
          return !values.some((v) =>
            String(v).toLowerCase().includes(conditionValue.toLowerCase()),
          );
        case TriageOperator.EQUALS:
          return values.some(
            (v) => String(v).toLowerCase() === conditionValue.toLowerCase(),
          );
        case TriageOperator.NOT_EQUALS:
          return !values.some(
            (v) => String(v).toLowerCase() === conditionValue.toLowerCase(),
          );
        case TriageOperator.IS_IN: {
          const inValues = conditionValue
            .toLowerCase()
            .split(',')
            .map((v: string) => v.trim());
          return values.some((v) => inValues.includes(String(v).toLowerCase()));
        }
        case TriageOperator.IS_NOT_IN: {
          const notInValues = conditionValue
            .toLowerCase()
            .split(',')
            .map((v: string) => v.trim());
          return !values.some((v) =>
            notInValues.includes(String(v).toLowerCase()),
          );
        }
      }
    }

    // Handle string/number/boolean fields
    const value = String(fieldValue).toLowerCase();
    const cond = conditionValue.toLowerCase();

    switch (operator) {
      case TriageOperator.EQUALS:
        return value === cond;
      case TriageOperator.NOT_EQUALS:
        return value !== cond;
      case TriageOperator.CONTAINS:
        return value.includes(cond);
      case TriageOperator.NOT_CONTAINS:
        return !value.includes(cond);
      case TriageOperator.IS_IN: {
        // Split conditionValue by commas and check if the field value is in the list
        const inValues = cond.split(',').map((v) => v.trim());
        return inValues.includes(value);
      }
      case TriageOperator.IS_NOT_IN: {
        // Split conditionValue by commas and check if the field value is not in the list
        const notInValues = cond.split(',').map((v) => v.trim());
        return !notInValues.includes(value);
      }
      default:
        return false;
    }
  }
}
