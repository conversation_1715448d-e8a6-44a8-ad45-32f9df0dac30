import {
  BadRequestException,
  Inject,
  Injectable,
  NotFoundException,
} from "@nestjs/common";
import { InjectRepository } from "@nestjs/typeorm";
import { TeamInfoResponse } from "@slack/web-api";
import {
  FindOptionsWhere,
  ILike,
  In,
  Not,
  Repository,
  SelectQueryBuilder,
} from "typeorm";
import { BotCtx } from "../../auth/interfaces";
import {
  Channels,
  CustomerContacts,
  Installations,
  Users,
} from "../../database/entities";
import { ChannelType } from "../../database/entities/channels/channels.entity";
import { ChannelsRepository } from "../../database/entities/channels/repositories";
import { TeamsRepository } from "../../database/entities/teams";
import { ThenaPlatformApiProvider } from "../../external/provider/thena-platform-api.provider";
import { CUSTOM_LOGGER_TOKEN } from "../../utils/logger";
import { ILogger } from "../../utils/logger";
import { SlackMessageCore } from "../core/messages/slack-message.core";
import { CoreTriageService } from "../core/messages/triage.core";
import { ConfigureTriageChannelDTO, CreateTriageThreadDTO } from "../dtos";
import { TriageChannelConfigHandler } from "../handlers/slack-views/triage-channel-config.handler";
import { SlackWebAPIService } from "../providers/slack-apis/slack-apis.service";
import {
  GetAllSlackChannelsQueryParams,
  SlackChannelAvailabilityTypes,
} from "../query-params";
import { TeamRelationshipType } from "../../database/entities/mappings";

@Injectable()
export class SlackChannelService {
  constructor(
    @Inject(CUSTOM_LOGGER_TOKEN) private readonly logger: ILogger,

    private readonly triageChannelConfigHandler: TriageChannelConfigHandler,

    // Core Services
    private readonly slackMessageCore: SlackMessageCore,
    private readonly coreTriageService: CoreTriageService,

    // Database Repositories
    @InjectRepository(CustomerContacts)
    private readonly customerContactsRepository: Repository<CustomerContacts>,

    @InjectRepository(Users)
    private readonly usersRepository: Repository<Users>,

    private readonly channelsRepository: ChannelsRepository,
    @InjectRepository(CustomerContacts)
    private readonly customerContactRepository: Repository<CustomerContacts>,

    // External Services
    private readonly slackWebAPIService: SlackWebAPIService,
    private readonly platformService: ThenaPlatformApiProvider,
    private readonly platformTeamRepository: TeamsRepository
  ) {}

  async configureTriageChannel(
    data: ConfigureTriageChannelDTO,
    botCtx: BotCtx
  ) {
    await this.triageChannelConfigHandler.handleViaApi(data, botCtx);
  }

  /**
   * Creates a new triage thread for a platform ticket
   */
  async createTriageThread(
    data: CreateTriageThreadDTO,
    channelId: string,
    botCtx: BotCtx
  ) {
    const { installation } = botCtx;
    const { platformTicketId, message, platformCommentId } = data;

    // Get the triage channel
    const tChannel = await this.channelsRepository.findByCondition({
      where: {
        channelId,
        installation: { id: installation.id },
        organization: { id: installation.organization.id },
      },
    });

    // Check if the triage channel exists
    if (!tChannel) {
      throw new NotFoundException("The provided triage channel was not found!");
    }

    // Get the slack message by platform ticket id, create a new one if it doesn't exist
    const slackMessage =
      await this.slackMessageCore.getSlackMessageByPlatformTicketId(
        installation,
        platformTicketId,
        { createIndependentIfNotFound: true }
      );

    // Get the ticket data
    const ticketData = await this.platformService.getTicket(
      installation,
      platformTicketId
    );

    // Check if the ticket data exists
    if (!ticketData) {
      throw new NotFoundException(
        "The provided platform ticket was not found!"
      );
    }

    let userToCommentAs: Users | null = null;
    const { commentAsEmail, commentAsName, commentAsAvatar } = data;
    if (commentAsEmail) {
      userToCommentAs = await this.usersRepository.findOne({
        where: {
          slackProfileEmail: commentAsEmail,
          installation: { id: installation.id },
          organization: { id: installation.organization.id },
        },
      });
    }

    // Get the customer contact by email
    const customerContact = await this.customerContactsRepository.findOne({
      where: {
        slackProfileEmail: ticketData.requestorEmail,
        installation: { id: installation.id },
        organization: { id: installation.organization.id },
      },
    });

    // Get the customer slack team
    const customerSlackTeam = customerContact?.userDump?.team_id ?? "";
    let slackTeamDetails: TeamInfoResponse | null = null;
    let slackTeamName: string | null = null;
    let slackTeamIconUrl: string | null = null;
    if (customerSlackTeam) {
      try {
        slackTeamDetails = await this.slackWebAPIService.getTeamInfo(
          installation.botToken,
          { team: customerSlackTeam }
        );

        // If the slack team details are not ok, log an error
        if (!slackTeamDetails.ok) {
          this.logger.error(
            `Error fetching slack team details for customer ${customerContact.slackId}`,
            slackTeamDetails.error
          );
        }

        // If the slack team details are ok, log the team name
        const { name, icon } = slackTeamDetails.team;
        const iconUrl = this.getIconUrl(icon);

        slackTeamName = name;
        slackTeamIconUrl = iconUrl;
      } catch (error) {
        if (error instanceof Error) {
          this.logger.error(
            `Error fetching slack team details for customer ${customerContact.slackId}`,
            error.stack
          );
        } else {
          console.error(
            `Error fetching slack team details for customer ${customerContact.slackId}`,
            error
          );
        }
      }
    }

    // Send the triage message to the channel
    await this.coreTriageService.sendTriageMessageToChannel(
      installation,
      {
        channel: tChannel,
        ticket: ticketData,
        slackMessage,
        returnExisting: false,
        message,
        slackTeamName,
        slackTeamIconUrl,
        platformCommentId,
        commentAs: userToCommentAs,
        commentAsName,
        commentAsAvatar,
        commentAsEmail,
      },
      true
    );
  }

  async getAllSlackChannelsByTeamId(
    botCtx: BotCtx,
    teamId: string,
    query: GetAllSlackChannelsQueryParams
  ) {
    const { organization } = botCtx;
    const {
      page,
      limit,
      availabilityType,
      searchQuery,
      showPrivateChannels,
      showExternalChannels,
      isBotActive,
    } = query;

    // Get the platform team
    const platformTeams = await this.platformTeamRepository.findAll({
      where: { uid: teamId, organization: { id: organization.id } },
      relations: { installation: true },
    });

    // Get the installations
    const installations = platformTeams.map((team) => team.installation);

    const whereClause: FindOptionsWhere<Channels> = {
      installation: { id: In(installations.map((i) => i.id)) },
      organization: { id: organization.id },
    };

    // If the user explicitly asks for configured channels
    if (availabilityType === SlackChannelAvailabilityTypes.configured) {
      whereClause.channelType = Not(
        In([ChannelType.NOT_SETUP, ChannelType.NOT_CONFIGURED])
      );
    } else if (availabilityType === SlackChannelAvailabilityTypes.available) {
      whereClause.channelType = Not(
        In([
          ChannelType.CUSTOMER_CHANNEL,
          ChannelType.TRIAGE_CHANNEL,
          ChannelType.INTERNAL_HELPDESK,
        ])
      );
    }

    // Add search query filter if provided
    if (searchQuery?.trim()) {
      whereClause.name = ILike(`%${searchQuery.trim()}%`);
    }

    // Add show private channels filter if provided
    if (showPrivateChannels === false) {
      whereClause.isPrivate = false;
    }

    // Add show external channels filter if provided
    if (showExternalChannels === false) {
      whereClause.isShared = false;
    }

    const { queryBuilder, privateChannelsCondition } =
      this.buildChannelsQueryBuilder(whereClause, {
        availabilityType,
        searchQuery,
        showExternalChannels,
        showPrivateChannels,
        isBotActive,
      });

    // Apply ordering and pagination
    queryBuilder
      .orderBy("channel.createdAt", "DESC")
      .skip((page - 1) * limit)
      .take(limit);

    const shouldIncludePrivateChannels =
      showPrivateChannels === true || isBotActive !== false;

    const [channels, total] = await Promise.all([
      queryBuilder.getMany(),

      // Get the total number of channels
      this.channelsRepository.count({
        where: [
          whereClause,
          ...(shouldIncludePrivateChannels ? [privateChannelsCondition] : []),
        ],
      }),
    ]);

    // Transform the response to include teams array
    const channelsWithTeams = channels.map((channel) => {
      // Group teams by uid to handle multiple installations
      const teamsByUid = (channel.platformTeamsToChannelMappings || []).reduce(
        (acc, mapping) => {
          const team = mapping.platformTeam;
          const existingTeam = acc.get(team.uid);

          if (existingTeam) {
            // Add installation to existing team
            existingTeam.installations.push({
              id: team.installation.id,
              teamId: team.installation.teamId,
              teamName: team.installation.teamName,
            });
          } else {
            // Create new team entry
            acc.set(team.uid, {
              id: team.id,
              uid: team.uid,
              installedBy: team.installedBy,
              relationshipType: mapping.relationshipType,
              installations: [
                {
                  id: team.installation.id,
                  teamId: team.installation.teamId,
                  teamName: team.installation.teamName,
                },
              ],
            });
          }
          return acc;
        },
        new Map()
      );

      // Convert Map to array and remove the mappings from the response
      const { platformTeamsToChannelMappings, installation, ...channelData } =
        channel;

      const teamToChannelRelationshipType = platformTeamsToChannelMappings.find(
        (platformTeamsToChannelMapping) =>
          platformTeamsToChannelMapping.platformTeam.uid == teamId &&
          platformTeamsToChannelMapping.relationshipType ==
            TeamRelationshipType.PRIMARY
      )
        ? TeamRelationshipType.PRIMARY
        : TeamRelationshipType.SECONDARY;

      return {
        ...channelData,
        relationshipType:
          availabilityType == SlackChannelAvailabilityTypes.configured
            ? teamToChannelRelationshipType
            : null,
        teamId: installation?.teamId || null,
        teams: Array.from(teamsByUid.values()),
      };
    });

    return {
      data: channelsWithTeams,
      meta: {
        page,
        limit,
        total,
        totalPages: Math.ceil(total / limit),
      },
    };
  }

  async getAllSlackChannels(
    botCtx: BotCtx,
    query: GetAllSlackChannelsQueryParams
  ) {
    const { installation, organization } = botCtx;
    const {
      page,
      limit,
      forOrganization,
      availabilityType,
      searchQuery,
      showExternalChannels,
      showBotAddedChannels,
      isBotActive,
      showPrivateChannels,
    } = query;

    // Validate pagination parameters
    if (limit > 100) {
      throw new BadRequestException("Limit cannot exceed 100");
    }

    if (page < 1) {
      throw new BadRequestException("Page must be greater than 0");
    }

    const whereClause: FindOptionsWhere<Channels> = {
      organization: { id: organization.id },
    };

    // If the user explicitly asks for channels for the organization
    // then we don't need to filter by installation
    if (forOrganization === false) {
      whereClause.installation = { id: installation.id };
    }

    // If the user explicitly asks for configured channels
    if (availabilityType === SlackChannelAvailabilityTypes.configured) {
      whereClause.channelType = Not(
        In([ChannelType.NOT_SETUP, ChannelType.NOT_CONFIGURED])
      );
    } else if (availabilityType === SlackChannelAvailabilityTypes.available) {
      whereClause.channelType = Not(
        In([
          ChannelType.CUSTOMER_CHANNEL,
          ChannelType.TRIAGE_CHANNEL,
          ChannelType.INTERNAL_HELPDESK,
        ])
      );
    }

    // If the user explicitly asks for bot added channels
    if (showBotAddedChannels === true) {
      whereClause.isBotJoined = true;
    }

    // Add search query filter if provided
    if (searchQuery?.trim()) {
      whereClause.name = ILike(`%${searchQuery.trim()}%`);
    }

    // Add show external channels filter if provided
    if (showExternalChannels === false) {
      whereClause.isShared = false;
    }

    const { queryBuilder, privateChannelsCondition } =
      this.buildChannelsQueryBuilder(whereClause, {
        availabilityType,
        searchQuery,
        showExternalChannels,
        showPrivateChannels,
        isBotActive,
      });

    // Apply ordering and pagination
    queryBuilder
      .orderBy("channel.createdAt", "DESC")
      .skip((page - 1) * limit)
      .take(limit);

    // Helper to determine if private channels should be included
    const shouldIncludePrivateChannels =
      showPrivateChannels === true || isBotActive !== false;

    const [channels, total] = await Promise.all([
      queryBuilder.getMany(),

      // Get the total number of channels
      this.channelsRepository.count({
        where: [
          whereClause,
          ...(shouldIncludePrivateChannels ? [privateChannelsCondition] : []),
        ],
      }),
    ]);

    // Transform the response to include teams array
    const channelsWithTeams = channels.map((channel) => {
      // Group teams by uid to handle multiple installations
      const teamsByUid = (channel.platformTeamsToChannelMappings || []).reduce(
        (acc, mapping) => {
          const team = mapping.platformTeam;
          const existingTeam = acc.get(team.uid);

          if (existingTeam) {
            // Add installation to existing team
            existingTeam.installations.push({
              id: team.installation.id,
              teamId: team.installation.teamId,
              teamName: team.installation.teamName,
            });
          } else {
            // Create new team entry
            acc.set(team.uid, {
              id: team.id,
              uid: team.uid,
              installedBy: team.installedBy,
              relationshipType: mapping.relationshipType,
              installations: [
                {
                  id: team.installation.id,
                  teamId: team.installation.teamId,
                  teamName: team.installation.teamName,
                },
              ],
            });
          }
          return acc;
        },
        new Map()
      );

      // Convert Map to array and remove the mappings from the response
      const { platformTeamsToChannelMappings, installation, ...channelData } =
        channel;
      return {
        ...channelData,
        teamId: installation?.teamId || null,
        teams: Array.from(teamsByUid.values()),
      };
    });

    return {
      data: channelsWithTeams,
      meta: {
        page,
        limit,
        total,
        totalPages: Math.ceil(total / limit),
      },
    };
  }

  /**
   * Helper method to build channel query with support for private channels
   */
  private buildChannelsQueryBuilder(
    whereClause: FindOptionsWhere<Channels>,
    options: {
      availabilityType?: SlackChannelAvailabilityTypes;
      searchQuery?: string;
      showExternalChannels?: boolean;
      showPrivateChannels?: boolean;
      isBotActive?: boolean;
    }
  ): {
    queryBuilder: SelectQueryBuilder<Channels>;
    privateChannelsCondition: FindOptionsWhere<Channels>;
  } {
    const { showPrivateChannels, isBotActive } = options;

    // Non-private channels are always included
    whereClause.isPrivate = false;

    // Create condition for private channels with bot active
    const privateChannelsCondition: FindOptionsWhere<Channels> = {
      isPrivate: true,
      isBotActive: true,
      installation: whereClause.installation,
      organization: whereClause.organization,
    };

    // Apply the same availabilityType filter to private channels
    if (whereClause.channelType) {
      privateChannelsCondition.channelType = whereClause.channelType;
    }

    // Apply name search if it exists
    if (whereClause.name) {
      privateChannelsCondition.name = whereClause.name;
    }

    // Apply isShared filter if it exists
    if (whereClause.isShared === false) {
      privateChannelsCondition.isShared = false;
    }

    // Apply isBotJoined filter if it exists
    if (whereClause.isBotJoined === true) {
      privateChannelsCondition.isBotJoined = true;
    }

    // Create query builder with base where clause
    const queryBuilder = this.channelsRepository
      .createQueryBuilder("channel")
      .leftJoinAndSelect("channel.installation", "channelInstallation")
      .leftJoinAndSelect("channel.platformTeamsToChannelMappings", "mappings")
      .leftJoinAndSelect("mappings.platformTeam", "team")
      .leftJoinAndSelect("team.installation", "installation")
      .where(whereClause);

    // Helper to determine if private channels should be included
    const shouldIncludePrivateChannels =
      showPrivateChannels === true || isBotActive !== false;

    // Include private channels if:
    // 1. User explicitly requests them with showPrivateChannels=true, OR
    // 2. isBotActive is not explicitly set to false
    if (shouldIncludePrivateChannels) {
      queryBuilder.orWhere(privateChannelsCondition);
    }

    return { queryBuilder, privateChannelsCondition };
  }

  private getIconUrl(icon: TeamInfoResponse["team"]["icon"]) {
    return (
      icon.image_original ??
      icon.image_230 ??
      icon.image_132 ??
      icon.image_102 ??
      icon.image_88 ??
      icon.image_68 ??
      icon.image_34 ??
      ""
    );
  }

  async getAllCustomersFromChannel(
    installation: Installations,
    channel: Channels
  ): Promise<CustomerContacts[]> {
    const channels = await this.channelsRepository.findWithRelations({
      where: { id: channel.id, installation: { id: installation.id } },
      relations: ["customerContacts"],
    });

    return channels.flatMap((c) => c.customerContacts);
  }
}
