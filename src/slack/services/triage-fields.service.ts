import { CACHE_MANAGER } from '@nestjs/cache-manager';
import { Inject, Injectable } from '@nestjs/common';
import { Cache } from 'cache-manager';
import { Installations } from '../../database/entities';
import { AnnotatorApiProvider } from '../../external/provider/annotator-api.provider';
import { CUSTOM_LOGGER_TOKEN, ILogger, SLACK_SENTRY_TAG } from '../../utils';
import { SentryService } from '../../utils/filters/sentry-alerts.filter';
import { TriageCategory } from '../constants/triage-fields.constants';

@Injectable()
export class TriageFieldsService {
  private readonly CACHE_TTL = 3600; // 1 hour in seconds

  constructor(
    @Inject(CUSTOM_LOGGER_TOKEN) private readonly logger: ILogger,
    @Inject(CACHE_MANAGER) private readonly cacheManager: Cache,
    private readonly annotatorApiProvider: Annotator<PERSON>piProvider,
    @Inject('Sentry') private readonly sentryService: SentryService,
  ) {}

  async getFieldMappings(
    category: TriageCategory,
    installation: Installations,
    baseField: string,
  ): Promise<Record<string, string>> {
    // const cacheKey = `triage_fields:${category}:${installation.organization.id}`;

    // Try to get from cache first
    // const cachedMappings =
    //   await this.cacheManager.get<Record<string, string>>(cacheKey);
    // if (cachedMappings) {
    //   return cachedMappings;
    // }

    // Map category to entity type
    const entityType = this.mapCategoryToEntityType(category);
    if (!entityType) {
      return this.getFallbackMappings(category);
    }

    try {
      // Fetch mappings from API
      const mappings = await this.annotatorApiProvider.getFieldTypeMappings(
        installation,
        entityType,
        baseField,
      );

      // Store in cache
      // await this.cacheManager.set(cacheKey, mappings, this.CACHE_TTL);

      return mappings;
    } catch (error) {
      this.logger.error(
        `Error fetching field mappings for ${category}: ${
          error instanceof Error ? error.message : 'Unknown error'
        }`,
        error instanceof Error ? error.stack : undefined,
      );

      // Report to Sentry
      this.sentryService.captureException(error, {
        tag: SLACK_SENTRY_TAG,
        name: '🚨 Error fetching field mappings',
        category,
        entityType: this.mapCategoryToEntityType(category),
        baseField,
        installationId: installation.id,
      });

      // Fallback to hardcoded mappings
      return this.getFallbackMappings(category);
    }
  }

  private mapCategoryToEntityType(category: TriageCategory): string | null {
    switch (category) {
      case TriageCategory.TICKET:
        return 'Ticket';
      case TriageCategory.ACCOUNT:
        return 'Account';
      case TriageCategory.CHANNEL:
        return null; // Channel metadata is not in Annotator
      default:
        return null;
    }
  }

  private getFallbackMappings(
    category: TriageCategory,
  ): Record<string, string> {
    // Use our current hardcoded mappings as fallback
    switch (category) {
      case TriageCategory.TICKET:
        return {
          title: 'string',
          description: 'string',
          priority: 'string',
          status: 'string',
          requestorEmail: 'string',
          assignee: 'string',
          reporter: 'string',
          labels: 'array',
          type: 'string',
        };
      case TriageCategory.ACCOUNT:
        return {
          name: 'string',
          tier: 'string',
          region: 'string',
          status: 'string',
          accountManager: 'string',
          supportLevel: 'string',
          tags: 'array',
        };
      case TriageCategory.CHANNEL:
        return {
          name: 'string',
          purpose: 'string',
          topic: 'string',
          memberCount: 'number',
          isPrivate: 'boolean',
          isShared: 'boolean',
          tags: 'array',
        };
      default:
        return {};
    }
  }
}
