import { ForbiddenException, Inject, Injectable } from '@nestjs/common';
import { WebClient } from '@slack/web-api';
import qs from 'qs';
import { BotCtx } from '../../auth/interfaces';
import { ConfigKeys, ConfigService } from '../../config/config.service';
import { TransactionService } from '../../database/common';
import { InstallationRepository } from '../../database/entities/installations/repositories';
import { UsersRepository } from '../../database/entities/users/repositories/users.repository';
import { ThenaPlatformApiProvider } from '../../external/provider/thena-platform-api.provider';
import { CUSTOM_LOGGER_TOKEN, ILogger } from '../../utils';
import { SLACK_AUTHORIZE_URL, SLACK_USER_SCOPES } from '../constants';

@Injectable()
export class AuthorizationService {
  constructor(
    @Inject(CUSTOM_LOGGER_TOKEN) private readonly logger: ILogger,

    // Services
    private readonly transactionService: TransactionService,
    private readonly configService: ConfigService,

    // Repositories
    private readonly userRepository: UsersRepository,
    private readonly installationRepository: InstallationRepository,

    // External Providers
    private readonly thenaPlatformApiProvider: ThenaPlatformApiProvider,
  ) {}

  /**
   * Get the authorize user URL
   * @returns The authorize user URL
   */
  async getAuthorizeUserURL(userEmail: string, botCtx: BotCtx) {
    const userScopes = SLACK_USER_SCOPES.join(',');
    const slackAuthUrl = SLACK_AUTHORIZE_URL;

    // Generate the state
    const state = encodeURIComponent(
      JSON.stringify({
        userEmail,
        teamId: botCtx.installation.teamId,
      }),
    );

    // Generate the query string
    const queryString: string = qs.stringify({
      user_scope: userScopes,
      redirect_uri: `${this.configService.get(ConfigKeys.SLACK_APP_BASE_URL)}/v1/slack/authorization/user/callback`,
      client_id: this.configService.get(ConfigKeys.SLACK_CLIENT_ID),
      team_id: botCtx.installation.teamId,
      user_email: userEmail,
    });

    // Generate the full URL
    const authURL = `${slackAuthUrl}?${queryString}&state=${state}`;

    return {
      url: authURL.toString(),
    };
  }

  /**
   * Authorize the user
   * @param code The code to authorize the user
   * @returns The authorized user
   */
  async authorizeUser(
    code: string,
    state: { userEmail: string; teamId: string },
  ) {
    // Initialize the Slack client
    const slackClient = new WebClient();

    // Call the Slack API to authorize the user
    const response = await slackClient.oauth.v2.access({
      client_id: this.configService.get(ConfigKeys.SLACK_CLIENT_ID),
      client_secret: this.configService.get(ConfigKeys.SLACK_CLIENT_SECRET),
      code,
      redirect_uri: `${this.configService.get(ConfigKeys.SLACK_APP_BASE_URL)}/v1/slack/authorization/user/callback`,
    });

    // If the response is not ok, throw an error
    if (!response.ok) {
      this.logger.error(
        `Failed to authorize user: ${response.error}`,
        response.error,
      );

      throw new Error(response.error);
    }

    // Get the team id
    const teamId = response.team.id;

    // If the team id is not the same as the state team id, throw an error
    if (teamId !== state.teamId) {
      this.logger.error(
        `Team id does not match the state team id: ${teamId} !== ${state.teamId}`,
      );

      throw new ForbiddenException('Forbidden resource');
    }

    // Get installations with the team id
    const installation = await this.installationRepository.findByCondition({
      where: { teamId }, // NOTE: Team ID is unique (idx)
      relations: { organization: true },
    });

    // If the installation is not found, throw an error
    if (!installation) {
      this.logger.error(`Installation not found for team id: ${teamId}`);
      throw new ForbiddenException('Forbidden resource');
    }

    // Get the user id and token
    const { id: slackUserId, access_token: userToken } = response.authed_user;

    // Query the user
    const encodedEmail = state.userEmail.replace(' ', '+');
    const authedUser = await this.userRepository.findByCondition({
      where: {
        installation: { id: installation.id },
        slackProfileEmail: encodedEmail,
        slackId: slackUserId,
      },
    });

    // If the authed user is not found, try to find them by slack id
    if (!authedUser) {
      this.logger.error(
        `[AuthedUser] User not found for slack user id: ${slackUserId}, ${state.userEmail}`,
      );
      throw new ForbiddenException(
        'User with this email was not found on Slack.',
      );
    }

    // If the authed user's slack id is not the same as the slack user id, throw an error
    if (authedUser.slackId !== slackUserId) {
      this.logger.error(
        `[AuthedUser] User mismatch for slack user id: ${slackUserId}, ${state.userEmail}`,
      );
      throw new ForbiddenException('Forbidden resource');
    }

    // Update the user's slack access token
    await this.transactionService.runInTransaction(async (txnContext) => {
      const updateResult = await this.userRepository.updateWithTxn(
        txnContext,
        { id: authedUser.id, installation: { id: installation.id } },
        { slackAccessToken: userToken },
      );

      // If the update result is 0, throw an error
      if (updateResult.affected === 0) {
        this.logger.error(`User not found for slack user id: ${slackUserId}`);
        throw new ForbiddenException('Forbidden resource');
      }

      // Set slack auth
      await this.thenaPlatformApiProvider.setSlackAuth(
        installation,
        state.userEmail,
      );
    });

    return true;
  }

  /**
   * Delete the user's authorization
   * @param userEmail The user's email
   * @returns The deleted user
   */
  async deleteUserAuthorization(userEmail: string, botCtx: BotCtx) {
    const { installation } = botCtx;

    // Get the user
    const user = await this.userRepository.findByCondition({
      where: {
        slackProfileEmail: userEmail,
        installation: { id: botCtx.installation.id },
      },
    });

    // If the user is not found, throw an error
    if (!user) {
      throw new ForbiddenException('Forbidden resource');
    }

    // Delete the user's authorization
    await this.transactionService.runInTransaction(async (txnContext) => {
      // Delete the user's authorization
      await this.userRepository.updateWithTxn(
        txnContext,
        { id: user.id, installation: { id: installation.id } },
        { slackAccessToken: null },
      );

      // Set slack auth
      await this.thenaPlatformApiProvider.setSlackAuth(
        installation,
        userEmail,
        true,
      );
    });
  }
}
