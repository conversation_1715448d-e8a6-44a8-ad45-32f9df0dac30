import { Inject, Injectable } from '@nestjs/common';
import { InjectRepository } from '@nestjs/typeorm';
import { TeamInfoResponse } from '@slack/web-api';
import { Repository } from 'typeorm';
import { BotCtx } from '../../auth/interfaces';
import {
  Channels,
  CustomerContacts,
  Installations,
} from '../../database/entities';
import { TeamTriageRuleMappingRepository } from '../../database/entities/mappings/repositories/teams-triage-mappings.repository';
import { TriageRules } from '../../database/entities/mappings/teams-triage-mappings.entity';
import { SlackMessages } from '../../database/entities/slack-messages';
import { TeamsRepository } from '../../database/entities/teams';
import { Person } from '../../database/interfaces/person.interface';
import { ThenaPlatformApiProvider } from '../../external/provider/thena-platform-api.provider';
import { Ticket } from '../../platform/interfaces';
import { CUSTOM_LOGGER_<PERSON>KE<PERSON>, ILogger, SLACK_SENTRY_TAG } from '../../utils';
import { SentryService } from '../../utils/filters/sentry-alerts.filter';
import { CoreTriageService } from '../core/messages';
import { SlackWebAPIService } from '../providers/slack-apis/slack-apis.service';
import { TriageRuleEvaluatorService } from './triage-rule-evaluator.service';

interface TriageEvaluationOptions {
  ticket: Partial<Ticket>;
  teamId: string;
  evaluationData: {
    ticket: Ticket;
    account: any;
    channel: Channels;
  };
}

export interface EvaluateAndSendTriageMessagesOptions {
  ticket: Partial<Ticket>;
  platformTeamId: string;
  sourceChannel?: Channels;
  slackMessage?: SlackMessages;
  customer?: Person;
}

@Injectable()
export class TriageEvaluationService {
  constructor(
    @Inject(CUSTOM_LOGGER_TOKEN) private readonly logger: ILogger,
    @Inject('Sentry') private readonly sentryService: SentryService,

    // Database Repositories
    private readonly triageRuleRepo: TeamTriageRuleMappingRepository,
    private readonly triageRuleEvaluator: TriageRuleEvaluatorService,
    private readonly platformTeamRepository: TeamsRepository,

    @InjectRepository(CustomerContacts)
    private readonly customerContactsRepository: Repository<CustomerContacts>,

    @InjectRepository(SlackMessages)
    private readonly slackMessagesRepository: Repository<SlackMessages>,

    // External Providers
    private readonly platformApiProvider: ThenaPlatformApiProvider,
    private readonly slackWebAPIService: SlackWebAPIService,

    // Utility Services
    private readonly coreTriageService: CoreTriageService,
  ) {}

  async evaluateAndSendTriageMessages(
    installation: Installations,
    options: EvaluateAndSendTriageMessagesOptions,
  ): Promise<void> {
    const { ticket, slackMessage, platformTeamId } = options;

    this.logger.log(
      `Evaluating triage messages for ticket ${ticket.id}, ${JSON.stringify(options)}`,
    );

    // Get the platform team
    const platformTeam = await this.platformTeamRepository.findByCondition({
      where: {
        uid: platformTeamId,
        installation: { id: installation.id },
        organization: { id: installation.organization.id },
      },
    });

    // If the platform team is not found, log an error and return
    if (!platformTeam) {
      this.logger.error(
        `Platform team not found for ticket ${ticket.id}, ${JSON.stringify(options)}`,
      );
      return;
    }

    this.logger.log(`Platform team found: ${platformTeam.id}`);

    // Get the entity data
    const evaluationData = await this.getEntityData(installation, ticket.id);

    // Evaluate the triage rules
    const triageChannels = await this.evaluateTriageRules(installation, {
      teamId: platformTeam.id,
      ticket,
      evaluationData,
    });

    if (triageChannels.length > 0) {
      await this.sendTriageMessages(installation, triageChannels, {
        ticket,
        slackMessage,
      });
    }
  }

  private async evaluateTriageRules(
    installation: Installations,
    options: TriageEvaluationOptions,
  ): Promise<Channels[]> {
    try {
      const { teamId, evaluationData } = options;

      const triageRules = await this.triageRuleRepo.findActiveRulesForTeam(
        teamId,
        { installation, organization: installation.organization } as BotCtx,
      );

      if (!triageRules?.length) {
        return [];
      }

      const defaultRule = triageRules.find((rule) => rule.isDefault);

      const matchedTriageChannels = new Set<Channels>();

      // Evaluate non-default rules first
      for (const rule of triageRules) {
        if (!rule.isDefault && rule.triageRules) {
          const matches = await this.triageRuleEvaluator.evaluateRules(
            rule.triageRules as TriageRules,
            evaluationData as any,
          );

          if (matches) {
            for (const channel of rule.triageChannels) {
              matchedTriageChannels.add(channel);
            }
          }
        }
      }

      // Use default rule if no other rules matched
      if (matchedTriageChannels.size === 0 && defaultRule) {
        for (const channel of defaultRule.triageChannels) {
          matchedTriageChannels.add(channel);
        }
      }

      return Array.from(matchedTriageChannels);
    } catch (error) {
      this.logger.error(
        `Error evaluating triage rules: ${error instanceof Error ? error.message : 'Unknown error'}`,
        error instanceof Error ? error.stack : undefined,
      );

      // Report to Sentry
      this.sentryService.captureException(error, {
        tag: SLACK_SENTRY_TAG,
        name: '🚨 Error evaluating triage rules',
        teamId: options.teamId,
        ticketId: options.ticket.id,
        installationId: installation.id,
      });

      return [];
    }
  }

  /**
   * Get entity data
   * @param installation Installation
   * @param ticketId Ticket ID
   */
  private async getEntityData(installation: Installations, ticketId: string) {
    const entityDetails = await this.platformApiProvider.getEntityDetails(
      installation,
      'ticketId',
      ticketId,
      'Ticket',
    );

    // Parse the entity details
    const ticket = entityDetails.data;

    // Get the channel from the slack message
    const slackMessage = await this.slackMessagesRepository.findOne({
      where: {
        platformTicketId: ticketId,
        installation: { id: installation.id },
        organization: { id: installation.organization.id },
      },
      relations: { channel: true },
    });

    // Get the channel from the slack message
    const channel = slackMessage?.channel;

    return {
      ticket,
      channel,
      account: ticket.account,
    };
  }

  private async sendTriageMessages(
    installation: Installations,
    triageChannels: Channels[],
    context: {
      ticket: Partial<Ticket>;
      slackMessage?: SlackMessages;
    },
  ): Promise<void> {
    const { ticket } = context;

    // Get the customer contact
    const customer = await this.customerContactsRepository.findOne({
      where: {
        slackProfileEmail: ticket.customerContactEmail,
        installation: { id: installation.id },
        organization: { id: installation.organization.id },
      },
    });

    let slackTeamName: string | null = null;
    let slackTeamIconUrl: string | null = null;
    if (customer) {
      const customerSlackTeam = customer.userDump?.team_id ?? '';
      let slackTeamDetails: TeamInfoResponse | null = null;

      try {
        slackTeamDetails = await this.slackWebAPIService.getTeamInfo(
          installation.botToken,
          {
            team: customerSlackTeam,
          },
        );

        // If the slack team details are not ok, log an error
        if (!slackTeamDetails.ok) {
          this.logger.error(
            ` Error fetching slack team details for customer ${customer.slackId}`,
            slackTeamDetails.error,
          );
        }

        // If the slack team details are ok, log the team name
        const { name, icon } = slackTeamDetails.team;
        const iconUrl = this.getIconUrl(icon);

        slackTeamName = name;
        slackTeamIconUrl = iconUrl;
      } catch (error) {
        if (error instanceof Error) {
          this.logger.error(
            `Error fetching slack team details for customer ${customer.slackId}`,
            error.stack,
          );
        } else {
          console.error(
            `Error fetching slack team details for customer ${customer.slackId}`,
            error,
          );
        }
      }
    }

    // Send the triage messages to the channels
    for (const triageChannel of triageChannels) {
      try {
        this.logger.log(
          `Sending triage message to channel ${JSON.stringify({
            channel: triageChannel,
            slackTeamName,
            slackTeamIconUrl,
            customer: {
              email: ticket.customerContactEmail,
              name: ticket.customerContactLastName
                ? `${ticket.customerContactFirstName} ${ticket.customerContactLastName}`
                : ticket.customerContactFirstName,
            },
          })}`,
        );

        await this.coreTriageService.sendTriageMessageToChannel(
          installation,
          {
            channel: triageChannel,
            slackMessage: context.slackMessage,
            ticket: context.ticket,
            returnExisting: true,
            slackTeamName,
            slackTeamIconUrl,
          },
          false,
        );
      } catch (error) {
        this.logger.error(
          `Error sending triage message to channel ${triageChannel.name}: ${error instanceof Error ? error.message : 'Unknown error'}`,
          error instanceof Error ? error.stack : undefined,
        );

        // Report to Sentry
        this.sentryService.captureException(error, {
          tag: SLACK_SENTRY_TAG,
          name: '🚨 Error sending triage message',
          channelId: triageChannel.channelId,
          channelName: triageChannel.name,
          ticketId: context.ticket.id,
          installationId: installation.id,
        });
      }
    }
  }

  private getIconUrl(icon: TeamInfoResponse['team']['icon']) {
    return (
      icon.image_original ??
      icon.image_230 ??
      icon.image_132 ??
      icon.image_102 ??
      icon.image_88 ??
      icon.image_68 ??
      icon.image_34 ??
      ''
    );
  }
}
