import {
  BadRequestException,
  ConflictException,
  Inject,
  Injectable,
  NotFoundException,
} from "@nestjs/common";
import { groupBy } from "lodash";
import { DeepPartial, FindOptionsWhere, In } from "typeorm";
import { BotCtx } from "../../auth/interfaces";
import { TransactionService } from "../../database/common";
import { ChannelType } from "../../database/entities/channels/channels.entity";
import { ChannelsRepository } from "../../database/entities/channels/repositories";
import { InstallationRepository } from "../../database/entities/installations/repositories";
import {
  PlatformTeamsToChannelMappings,
  TeamRelationshipType,
} from "../../database/entities/mappings";
import { TeamChannelMapsRepository } from "../../database/entities/mappings/repositories/team-channel-maps.repository";
import { SettingsRepository } from "../../database/entities/settings/repositories/settings.repository";
import { PlatformTeams, TeamsRepository } from "../../database/entities/teams";
import { CUSTOM_LOGGER_TOKEN, ILogger } from "../../utils";
import { ChannelsManagementService } from "../core";
import {
  AddTeamDTO,
  DisconnectTeamToChannelsDTO,
  MapTeamToChannelsDTO,
  MapTeamToSecondaryChannelsDTO,
} from "../dtos";
import { CommonQueryParamsToFetchEntityData } from "../query-params";
import { PromptsService } from "./prompts.service";
import { SettingsService } from "./settings.service";

@Injectable()
export class SlackTeamsService {
  constructor(
    @Inject(CUSTOM_LOGGER_TOKEN) private readonly logger: ILogger,

    // Utility Services
    private readonly transactionService: TransactionService,

    // Core Services
    private readonly channelsManagementService: ChannelsManagementService,
    private readonly settingsService: SettingsService,

    // Database Repositories
    private readonly teamsRepository: TeamsRepository,
    private readonly channelsRepository: ChannelsRepository,
    private readonly installationsRepository: InstallationRepository,
    private readonly teamChannelMapsRepository: TeamChannelMapsRepository,
    private readonly settingsRepository: SettingsRepository,
    private readonly promptsService: PromptsService
  ) {}

  /**
   * Get all teams
   * @param botCtx - The bot context
   * @param query - The query parameters
   * @returns The teams
   */
  public async getAllTeams(
    botCtx: BotCtx,
    query: CommonQueryParamsToFetchEntityData
  ) {
    const { installation, organization } = botCtx;
    const { page = 1, limit = 10, forOrganization = false } = query;

    // Validate pagination parameters
    if (limit > 100) {
      throw new BadRequestException("Limit cannot exceed 100");
    }

    if (page < 1) {
      throw new BadRequestException("Page must be greater than 0");
    }

    const whereClause: FindOptionsWhere<PlatformTeams> = {
      organization: { id: organization.id },
    };

    if (forOrganization === false) {
      whereClause.installation = { id: installation.id };
    }

    const teams = await this.teamsRepository.findAll({
      skip: (page - 1) * limit,
      take: limit,
      relations: { installation: true },
      where: whereClause,
      select: {
        id: true,
        uid: true,
        installedBy: true,
        createdAt: true,
        installation: {
          id: true,
          teamId: true,
          teamName: true,
        },
      },
    });

    // Get channel counts for all teams
    const channelsForTeams = await this.teamChannelMapsRepository.findAll({
      where: { platformTeam: { id: In(teams.map((t) => t.id)) } },
      relations: { platformTeam: true, channel: true },
      select: { platformTeam: { id: true }, channel: { id: true } },
    });

    const channelCountPerTeam = groupBy(channelsForTeams, "platformTeam.id");

    // Group teams by uid
    const groupedTeams = teams.reduce((acc, team) => {
      const existingTeam = acc.find((t) => t.uid === team.uid);

      if (existingTeam) {
        // Add installation to existing team
        existingTeam.installations.push({
          id: team.installation.id,
          teamId: team.installation.teamId,
          teamName: team.installation.teamName,
        });
      } else {
        // Create new team entry
        acc.push({
          id: team.id,
          uid: team.uid,
          installedBy: team.installedBy,
          createdAt: team.createdAt,
          channelCount: channelCountPerTeam[team.id]?.length ?? 0,
          installations: [
            {
              id: team.installation.id,
              teamId: team.installation.teamId,
              teamName: team.installation.teamName,
            },
          ],
        });
      }
      return acc;
    }, []);

    return {
      data: groupedTeams,
      meta: {
        page,
        limit,
        total: groupedTeams.length, // Update total to reflect unique teams
        totalPages: Math.ceil(groupedTeams.length / limit),
      },
    };
  }

  /**
   * Add a team to the database
   * @param data - The data to add the team
   * @param botCtx - The bot context
   * @returns The newly created team
   */
  public async addTeam(data: AddTeamDTO, botCtx: BotCtx) {
    const { installation, organization } = botCtx;
    const { teamId, installedBy, workspaces } = data;

    this.logger.log(`Adding team ${teamId} for ${installation.teamId}`);

    // Get the slack workspaces
    const slackWorkspaces = await this.installationsRepository.findAll({
      where: {
        teamId: In(workspaces),
        organization: { id: organization.id },
      },
    });

    // If the slack workspaces do not exist, throw an error
    if (slackWorkspaces.length !== workspaces.length) {
      throw new BadRequestException(
        "Some workspaces do not exist, please try again!"
      );
    }

    // Check if the team already exists
    const team = await this.teamsRepository.findByCondition({
      where: {
        uid: teamId,
        organization: { id: organization.id },
      },
    });

    // If the team already exists, throw an error
    if (team) {
      this.logger.log(`Team ${teamId} already exists`);
      throw new ConflictException("The provided team already exists!");
    }

    // Create the teams to save
    const teamSave = slackWorkspaces.map((ws) => ({
      uid: teamId,
      installedBy,
      installation: { id: ws.id },
      organization: { id: organization.id },
    }));

    // Prepare settings data before transaction
    const settingsData = await this.settingsService.prepareTeamSettings(
      teamSave as PlatformTeams[],
      botCtx
    );

    // Perform the actions in a transaction
    const newTeams = await this.transactionService.runInTransaction(
      async (txnContext) => {
        // Create new teams
        const teams = await this.teamsRepository.saveManyWithTxn(
          txnContext,
          teamSave
        );

        // Create settings using prepared data
        await this.settingsRepository.saveManyWithTxn(txnContext, settingsData);

        return teams;
      }
    );

    // Seed default prompts for the new team
    for (const team of newTeams) {
      await this.promptsService.seedDefaultPrompts(botCtx, team.id);
    }

    return newTeams[0];
  }

  /**
   * Map team channels to another team, making them secondary
   * @param data - The data to map the team to channels
   * @param botCtx - The bot context
   * @returns The mapped channels
   */

  public async mapTeamToSecondaryChannels(
    data: MapTeamToSecondaryChannelsDTO,
    botCtx: BotCtx
  ) {
    const { installation, organization } = botCtx;
    const { teamId, newTeamId, channelIds, channelType } = data;

    this.logger.log(
      `Mapping team ${teamId} to channels ${channelIds.join(", ")}`
    );

    const commonLookup = {
      installation: { id: installation.id },
      organization: { id: organization.id },
    };

    // Get both platform teams in parallel
    const [platformTeam, newPlatformTeam] = await Promise.all([
      this.teamsRepository.findByCondition({
        where: { uid: teamId, ...commonLookup },
      }),
      this.teamsRepository.findByCondition({
        where: { uid: newTeamId, ...commonLookup },
      }),
    ]);

    // If the platform team does not exist, throw an error
    if (!platformTeam) {
      this.logger.debug(`Platform team ${teamId} does not exist`);
      throw new BadRequestException(
        "The provided team does not exist, have you provided the Slack app access to this team?"
      );
    }

    if (!newPlatformTeam) {
      this.logger.debug(`Platform new team ${newTeamId} does not exist`);
      throw new BadRequestException(
        "The new team does not exist, have you provided the Slack app access to this team?"
      );
    }

    // Check if the channels exist
    const channels = await this.channelsRepository.findAll({
      where: { channelId: In(channelIds), ...commonLookup },
    });

    // If the channels do not exist, throw an error
    if (channelIds.length !== channels.length) {
      this.logger.error(
        `Some channels do not exist: ${channelIds.filter(
          (id) => !channels.some((c) => c.channelId === id)
        )}`
      );

      throw new BadRequestException(
        "Some channels do not exist, please try again!"
      );
    }

    const existingMappings = await this.teamChannelMapsRepository.findAll({
      where: {
        channel: { id: In(channels.map((c) => c.id)) },
        platformTeam: { id: platformTeam.id },
        relationshipType: TeamRelationshipType.PRIMARY,
        ...commonLookup,
      },
      relations: { channel: true },
    });

    // Save the team channel maps
    await this.transactionService.runInTransaction(async (txnContext) => {
      // Map the channels to this team
      await this.teamChannelMapsRepository.updateWithTxn(
        txnContext,
        {
          id: In(existingMappings.map((existingMapping) => existingMapping.id)),
        },
        {
          platformTeam: { id: newPlatformTeam.id },
        }
      );

      // Update the channels to be joined
      await this.channelsRepository.updateWithTxn(
        txnContext,
        { id: In(channels.map((c) => c.id)) },
        {
          isBotJoined: true,
          isBotActive: true,
          channelType: channelType as ChannelType,
        }
      );
    });
  }

  /**
   * Map a team to channels
   * @param data - The data to map the team to channels
   * @param botCtx - The bot context
   * @returns The mapped channels
   */
  public async mapTeamToChannels(data: MapTeamToChannelsDTO, botCtx: BotCtx) {
    const { installation, organization } = botCtx;
    const { teamId, channelIds, channelType } = data;

    this.logger.log(
      `Mapping team ${teamId} to channels ${channelIds.join(", ")}`
    );

    const commonLookup = {
      installation: { id: installation.id },
      organization: { id: organization.id },
    };

    // Get the platform team
    const platformTeam = await this.teamsRepository.findByCondition({
      where: { uid: teamId, ...commonLookup },
    });

    // If the platform team does not exist, throw an error
    if (!platformTeam) {
      this.logger.debug(`Platform team ${teamId} does not exist`);
      throw new BadRequestException(
        "The provided team does not exist, have you provided the Slack app access to this team?"
      );
    }

    // Check if the channels exist
    const channels = await this.channelsRepository.findAll({
      where: { channelId: In(channelIds), ...commonLookup },
    });

    // If the channels do not exist, throw an error
    if (channelIds.length !== channels.length) {
      this.logger.error(
        `Some channels do not exist: ${channelIds.filter(
          (id) => !channels.some((c) => c.channelId === id)
        )}`
      );

      throw new BadRequestException(
        "Some channels do not exist, please try again!"
      );
    }

    this.logger.log(
      `Joining channels ${channels.map((c) => c.channelId).join(", ")}`
    );

    // Join the channels, the function will NEVER throw and should generally
    // not be awaited this can happen in the background
    this.channelsManagementService.joinChannels(installation, channels, {
      channelType: channelType as ChannelType,
    });

    // For customer channels, check existing mappings to determine relationship type
    const mappedChannels: Array<DeepPartial<PlatformTeamsToChannelMappings>> =
      [];

    const existingMappings = await this.teamChannelMapsRepository.findAll({
      where: {
        channel: { id: In(channels.map((c) => c.id)) },
        platformTeam: { id: platformTeam.id },
        ...commonLookup,
      },
      relations: { channel: true },
    });

    // Create a Set of channel IDs that already have mappings for faster lookup
    const existingMappingChannelIds = new Set(
      existingMappings.map((mapping) => mapping.channel.id)
    );

    // Filter channels that don't have existing mappings and map them in one operation
    const newMappedChannels = channels
      .filter((channel) => !existingMappingChannelIds.has(channel.id))
      .map((channel) => ({
        channel,
        platformTeam,
        relationshipType: TeamRelationshipType.PRIMARY,
        ...commonLookup,
      }));

    // Add the new mappings to the result array
    mappedChannels.push(...newMappedChannels);

    // Save the team channel maps
    await this.transactionService.runInTransaction(async (txnContext) => {
      // Map the channels to this team
      await this.teamChannelMapsRepository.saveManyWithTxn(
        txnContext,
        mappedChannels
      );

      // Update the channels to be joined
      await this.channelsRepository.updateWithTxn(
        txnContext,
        { id: In(channels.map((c) => c.id)) },
        {
          isBotJoined: true,
          isBotActive: true,
          channelType: channelType as ChannelType,
        }
      );
    });
  }

  /**
   * Remove a team from channels
   * @param data - The data to map the team to channels
   * @param botCtx - The bot context
   * @returns The mapped channels
   */
  public async disconnectTeamToChannels(
    data: DisconnectTeamToChannelsDTO,
    botCtx: BotCtx
  ) {
    const { installation, organization } = botCtx;
    const { teamId, channelIds } = data;

    this.logger.log(
      `Disconnect team ${teamId} to channels ${channelIds.join(", ")}`
    );

    const commonLookup = {
      installation: { id: installation.id },
      organization: { id: organization.id },
    };

    // Get the platform team
    const platformTeam = await this.teamsRepository.findByCondition({
      where: { uid: teamId, ...commonLookup },
    });

    // If the platform team does not exist, throw an error
    if (!platformTeam) {
      this.logger.debug(`Platform team ${teamId} does not exist`);
      throw new BadRequestException(
        "The provided team does not exist, have you provided the Slack app access to this team?"
      );
    }

    // Check if the channels exist
    const channels = await this.channelsRepository.findAll({
      where: { channelId: In(channelIds), ...commonLookup },
    });

    // If the channels do not exist, throw an error
    if (channelIds.length !== channels.length) {
      this.logger.error(
        `Some channels do not exist: ${channelIds.filter(
          (id) => !channels.some((c) => c.channelId === id)
        )}`
      );

      throw new BadRequestException(
        "Some channels do not exist, please try again!"
      );
    }

    // Check for the mappings of team and channel
    const teamChannelMappings = await this.teamChannelMapsRepository.findAll({
      where: { channel: { channelId: In(channelIds), ...commonLookup } },
    });

    this.logger.log(
      `Disconnecting channels ${channels.map((c) => c.channelId).join(", ")}`
    );

    // Disconnect the channels, the function will NEVER throw and should generally
    // not be awaited this can happen in the background
    this.channelsManagementService.disconnectChannels(installation, channels);

    // Remove the team channel maps
    await this.transactionService.runInTransaction(async (txnContext) => {
      // Disconnect the channels to this team
      await this.teamChannelMapsRepository.removeManyWithTxn(
        txnContext,
        teamChannelMappings
      );

      // Update the channels to be joined
      await this.channelsRepository.updateWithTxn(
        txnContext,
        { id: In(channels.map((c) => c.id)) },
        {
          isBotJoined: true,
          isBotActive: false,
          channelType: ChannelType.NOT_CONFIGURED,
        }
      );
    });
  }

  /**
   * Disconnect a team from the database
   * @param teamId - The id of the team to disconnect
   * @param botCtx - The bot context
   */
  public async disconnectTeam(teamId: string, botCtx: BotCtx) {
    const { installation, organization } = botCtx;

    this.logger.log(`Disconnecting team ${teamId} for ${organization.uid}`);

    // Get the platform team
    const platformTeam = await this.teamsRepository.findByCondition({
      where: {
        uid: teamId,
        installation: { id: installation.id },
        organization: { id: organization.id },
      },
    });

    // If the platform team does not exist, throw an error
    if (!platformTeam) {
      this.logger.debug(`Platform team ${teamId} does not exist`);
      throw new BadRequestException(
        "The provided team does not exist, have you provided the Slack app access to this team?"
      );
    }

    this.logger.log(`Disconnecting team ${teamId} from ${installation.teamId}`);

    // Disconnect the team from the channels
    await this.transactionService.runInTransaction(async (txnContext) => {
      // Remove the team from the database
      await this.teamsRepository.removeWithTxn(txnContext, platformTeam);
    });
  }

  /**
   * Get the platform team
   * @param teamId - The id of the team
   * @param botCtx - The bot context
   * @returns The platform team
   */
  public async getPlatformTeam(teamId: string, botCtx: BotCtx) {
    const { installation, organization } = botCtx;

    // Get the platform team
    const platformTeam = await this.teamsRepository.findByCondition({
      where: {
        uid: teamId,
        installation: { id: installation.id },
        organization: { id: organization.id },
      },
    });

    // If the platform team does not exist, throw an error
    if (!platformTeam) {
      throw new NotFoundException("The provided team does not exist");
    }

    return platformTeam;
  }
}
