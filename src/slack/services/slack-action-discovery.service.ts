import { Inject, Injectable, type OnModuleInit } from '@nestjs/common';
import { DiscoveryService } from '@nestjs/core';
import {
  Context,
  SlackActionMiddlewareArgs,
  SlackCommandMiddlewareArgs,
  SlackEventMiddlewareArgs,
  SlackOptionsMiddlewareArgs,
  SlackViewMiddlewareArgs,
  StringIndexed,
} from '@slack/bolt';
import { cloneDeep } from 'lodash';
import { Installations, Organizations, Users } from '../../database/entities';
import { InstallationRepository } from '../../database/entities/installations/repositories';
import { WebApiClient } from '../../utils';
import { SQSConsumerService, SQSMessage } from '../../utils/aws-utils/sqs';
import { safeJsonStringify } from '../../utils/external/safe-json-stringify.utils';
import { CUSTOM_LOGGER_TOKEN, ILogger } from '../../utils/logger';
import { SLACK_SQS_CONSUMER_SERVICE_TOKEN } from '../constants';
import { SlackAppManagementService } from '../core';
import { VIEW_METADATA_KEY } from '../decorators';
import { ACTION_METADATA_KEY } from '../decorators/slack-action.decorator';
import { COMMAND_METADATA_KEY } from '../decorators/slack-command.decorator';
import { EVENT_METADATA_KEY } from '../decorators/slack-event.decorator';
import { OPTIONS_METADATA_KEY } from '../decorators/slack-options.decorator';
import { DecoratedSlackActionMiddlewareArgs } from '../event-handlers';
import { FormFieldActionHandler } from '../handlers/slack-actions/form-handlers/form-field-action.handler';
import { SlackService } from './slack.service';

export interface EnrichedSlackArgsContext extends Context {
  user: Users;
  client: WebApiClient;
  installation: Installations;
  organization: Organizations;
}

@Injectable()
export class SlackActionDiscoveryService implements OnModuleInit {
  constructor(
    @Inject(CUSTOM_LOGGER_TOKEN) private readonly logger: ILogger,
    private readonly discoveryService: DiscoveryService,
    private readonly slackService: SlackService,
    private readonly slackAppManagementService: SlackAppManagementService,
    private readonly installationRepository: InstallationRepository,
    @Inject(SLACK_SQS_CONSUMER_SERVICE_TOKEN)
    private readonly sqsConsumerService: SQSConsumerService,
    private readonly formFieldActionHandler: FormFieldActionHandler,
  ) {}

  onModuleInit() {
    // Register Slack handlers
    this.registerActions();

    // Register Slack options
    this.registerOptions();

    // Register Slack commands
    this.registerCommands();

    // Start the consumer
    this.startSlackEventsConsumer();
  }

  /**
   * Enrich the Slack context with the installation and Slack WebAPI client
   * @param args The Slack action middleware arguments
   * @returns The enriched Slack action middleware arguments
   */
  private async enrichSlackContext(
    args:
      | SlackActionMiddlewareArgs
      | SlackCommandMiddlewareArgs
      | SlackOptionsMiddlewareArgs
      | SlackEventMiddlewareArgs
      | SlackViewMiddlewareArgs,
  ) {
    if (!('context' in args)) {
      return Promise.resolve(args);
    }

    const { context } = args;
    const slackContext = cloneDeep(context) as EnrichedSlackArgsContext;

    // Create a new Slack WebAPI Client
    const slackClient = new WebApiClient(slackContext.botToken);

    // Get the installation
    const installation = await this.installationRepository.findByCondition({
      where: { teamId: slackContext.teamId },
      relations: ['organization'],
    });

    // If no installation is found, log an error and return the original args
    if (!installation) {
      this.logger.error(
        `No installation found for team ${slackContext.teamId}`,
      );

      return args;
    }

    // Attach populated data to the context
    slackContext.client = slackClient;
    slackContext.installation = installation;
    slackContext.organization = installation.organization;

    // If the user is present, get the user
    if ('userId' in slackContext) {
      const slackUser = await this.slackAppManagementService.getAndUpsertUser({
        token: slackContext.botToken,
        userId: slackContext.userId,
        installationId: installation.id,
        organizationId: installation.organization.id,
        upsert: false,
      });

      slackContext.user = slackUser;
    }

    // Replace the context in the args
    args.context = slackContext;

    return args;
  }

  /**
   * Register Slack `action` and `view` handlers
   */
  private registerActions() {
    const providers = this.discoveryService.getProviders();
    const bolt = this.slackService.getBolt();

    // Add a global catch-all view handler
    bolt.view({ callback_id: /.*/ }, async (args) => {
      this.logger.log(
        `Catch-all view handler received submission: ${args.view.callback_id}`,
        'SlackActionDiscoveryService',
      );

      try {
        // Always acknowledge immediately to prevent timeouts
        await args.ack();
        this.logger.log(
          'Successfully acknowledged view submission via catch-all handler',
          'SlackActionDiscoveryService',
        );

        // We're now just using the catch-all for acknowledgment
        // The specific handler will process the submission to avoid duplication
        this.logger.log(
          'View will be handled by its specific handler',
          'SlackActionDiscoveryService',
        );

        // Don't directly call handlers here - just leave this to the specific handler
        // This prevents duplicate messages from being sent
      } catch (error) {
        this.logger.error(
          'Error in catch-all view handler',
          safeJsonStringify(error, {
            fallback: 'Unknown error in catch-all view handler',
            handleCircular: true,
            maxDepth: 5,
          }),
          'SlackActionDiscoveryService',
        );
      }
    });

    // Register block action handler for form fields
    bolt.action({ block_id: /block_.+/ }, async (args) => {
      try {
        // Acknowledge the action
        await args.ack();

        // Enrich the context
        const enrichedArgs = (await this.enrichSlackContext(
          args,
        )) as DecoratedSlackActionMiddlewareArgs;

        // Handle the block action
        await this.formFieldActionHandler.handle(enrichedArgs);
      } catch (error) {
        this.logger.error(
          'Error handling block action:',
          safeJsonStringify(error, {
            fallback: 'Unknown error in block action',
            handleCircular: true,
            maxDepth: 5,
          }),
        );
      }
    });

    for (const wrapper of providers) {
      const { instance, metatype } = wrapper;
      if (!instance || !metatype) {
        continue;
      }

      // Get the action ID from the metadata
      const actionId: string = Reflect.getMetadata(
        ACTION_METADATA_KEY,
        metatype,
      );

      const viewId: string = Reflect.getMetadata(VIEW_METADATA_KEY, metatype);

      // Register Slack View Handlers
      if (viewId && Array.isArray(viewId) && viewId.length > 0) {
        // Filter out null/undefined values
        const filteredViewIds = viewId.filter(Boolean);

        for (const id of filteredViewIds) {
          this.logger.log(
            `Registering specific view handler for callback_id: "${id}" with class ${metatype.name}`,
            'SlackActionDiscoveryService',
          );

          // Register with specific ID - this should be registered AFTER the catch-all
          bolt.view(
            { callback_id: id, type: 'view_submission' },
            async (args) => {
              this.logger.log(
                `Received view submission for ID: "${id}" from ${metatype.name}`,
                'SlackActionDiscoveryService',
              );

              try {
                // The acknowledgment is already handled by the catch-all handler
                this.logger.log(
                  `Processing view submission for ID: "${id}"`,
                  'SlackActionDiscoveryService',
                );

                const enrichedArgs = await this.enrichSlackContext(args);
                this.logger.log(
                  `Calling handler class ${metatype.name} for view ID: "${id}"`,
                  'SlackActionDiscoveryService',
                );

                // Execute the handler
                await instance.handle(enrichedArgs);
                this.logger.log(
                  `Handler executed successfully for view ID: "${id}"`,
                  'SlackActionDiscoveryService',
                );
              } catch (error) {
                this.logger.error(
                  `Error in view handler for ID "${id}"`,
                  safeJsonStringify(error, {
                    fallback: `Unknown error in view handler for ID "${id}"`,
                    handleCircular: true,
                    maxDepth: 5,
                  }),
                  'SlackActionDiscoveryService',
                );
              }
            },
          );
        }
      }

      // Register Slack Action Handlers
      if (actionId && Array.isArray(actionId) && actionId.length > 0) {
        // Filter out null/undefined values
        const filteredActionIds = actionId.filter(Boolean);

        for (const id of filteredActionIds) {
          bolt.action(id, async (args) => {
            try {
              // Acknowledge immediately
              await args.ack();

              // Enrich and handle for all action types
              const enrichedArgs = await this.enrichSlackContext(args);
              await instance.handle(enrichedArgs);
            } catch (error) {
              this.logger.error(
                `Error in ${id} handler: ${error}`,
                error instanceof Error ? error.stack : String(error),
                'SlackActionDiscoveryService',
              );
            }
          });
        }
      }
    }

    this.logger.log('Registered Slack actions and views handlers');
  }

  /**
   * Register Slack `options` handlers
   */
  private registerOptions() {
    const providers = this.discoveryService.getProviders();
    const bolt = this.slackService.getBolt();

    for (const wrapper of providers) {
      const { instance, metatype } = wrapper;
      if (!instance || !metatype) {
        continue;
      }

      const actionId = Reflect.getMetadata(OPTIONS_METADATA_KEY, metatype);

      if (actionId && Array.isArray(actionId) && actionId.length > 0) {
        for (const id of actionId) {
          if (!id) {
            this.logger.warn(
              `Skipping registration for null or undefined options ID in ${metatype.name}`,
              'SlackActionDiscoveryService',
            );
            continue;
          }

          bolt.options(id, async (args) => {
            const enrichedArgs = await this.enrichSlackContext(args);
            const options = await instance.handle(enrichedArgs);

            return args.ack(options);
          });
        }
      } else if (actionId) {
        bolt.options(actionId, async (args) => {
          const enrichedArgs = await this.enrichSlackContext(args);
          const options = await instance.handle(enrichedArgs);

          return args.ack(options);
        });
      }
    }

    this.logger.log('Registered Slack options handlers');
  }

  /**
   * Register Slack `slash` commands
   */
  private registerCommands() {
    const providers = this.discoveryService.getProviders();
    const bolt = this.slackService.getBolt();

    for (const wrapper of providers) {
      const { instance, metatype } = wrapper;
      if (!instance || !metatype) {
        continue;
      }

      // Get the command ID from the metadata
      const commandId = Reflect.getMetadata(COMMAND_METADATA_KEY, metatype);

      if (commandId && Array.isArray(commandId) && commandId.length > 0) {
        for (const id of commandId) {
          if (!id) {
            this.logger.warn(
              `Skipping registration for null or undefined command ID in ${metatype.name}`,
              'SlackActionDiscoveryService',
            );
            continue;
          }

          bolt.command(id, async (args) => {
            // Acknowledge the command
            await args.ack();

            const enrichedArgs = await this.enrichSlackContext(args);
            return instance.handle(enrichedArgs);
          });
        }
      } else if (commandId) {
        bolt.command(commandId, async (args) => {
          // Acknowledge the command
          await args.ack();

          const enrichedArgs = await this.enrichSlackContext(args);
          return instance.handle(enrichedArgs);
        });
      }
    }

    this.logger.log('Registered Slack command handlers');
  }

  /**
   * Start the Slack SQS Consumer
   */
  private startSlackEventsConsumer() {
    // Start the Slack SQS Consumer
    this.sqsConsumerService.startConsumer(async (msg) => {
      this.logger.log(`Received event: ${msg.id}`);
      await this.handleSlackEvent(msg);
    });
  }

  /**
   * Handle a Slack event from SQS
   * @param sqsMessage The SQS message
   */
  private async handleSlackEvent(sqsMessage: SQSMessage) {
    try {
      const { message, messageAttributes } = sqsMessage;

      // Try and parse the message from SQS
      let parsedMessage: SlackEventMiddlewareArgs & { context: StringIndexed };
      try {
        if (typeof message === 'string') {
          parsedMessage = JSON.parse(message);
        } else {
          parsedMessage = message as SlackEventMiddlewareArgs & {
            context: StringIndexed;
          };
        }
      } catch (error) {
        this.logger.error(`Error parsing Slack event: ${error}`);
      }

      // If the message is not parsed, throw an error
      if (!parsedMessage) {
        throw new Error('Failed to parse Slack event');
      }

      const {
        event_id: eventId,
        event_name: eventName,
        event_identifier: eventIdentifier,
      } = messageAttributes;

      // If the event attributes are missing, throw an error
      if (!eventId || !eventName || !eventIdentifier) {
        throw new Error(
          `Missing required event attributes: ${JSON.stringify(messageAttributes)}`,
        );
      }

      // If the event identifier is not a string, throw an error
      if (typeof eventIdentifier !== 'string') {
        throw new Error(
          `Invalid event identifier: ${eventIdentifier}, expected a string`,
        );
      }

      // Get the installation
      const installation = await this.installationRepository.findByCondition({
        where: { teamId: parsedMessage.context.teamId },
      });

      // If no installation is found, log an error and return
      if (!installation) {
        this.logger.error(
          `No installation found for team ${parsedMessage.context.teamId}`,
        );

        return;
      }

      // If the installation is disconnected, log an error and return
      if (installation.disconnected) {
        this.logger.error(`Installation ${installation.id} is disconnected`);

        return;
      }

      // Reflect and handle the event
      await this.reflectAndHandleEvent(parsedMessage, eventIdentifier);
    } catch (error) {
      if (error instanceof Error) {
        this.logger.error(`Error handling Slack event: ${error.message}`);
      } else {
        console.error(error);
      }

      throw error;
    }
  }

  /**
   * Reflect and handle a Slack event
   * @param args The Slack event middleware arguments
   * @param eventIdentifier The event identifier
   */
  private async reflectAndHandleEvent(
    args: SlackEventMiddlewareArgs,
    eventIdentifier?: string,
  ) {
    // Get the target event type
    const targetEventType = eventIdentifier ?? args.event.type;

    // Get the providers with matching metadata directly
    const provider = this.discoveryService.getProviders().find((wrapper) => {
      const { metatype } = wrapper;
      if (!metatype) {
        return false;
      }

      // Get the event IDs from the metadata
      const eventIds: string[] = Reflect.getMetadata(
        EVENT_METADATA_KEY,
        metatype,
      );

      // Return true if the event ID matches the target event type
      return eventIds?.includes(targetEventType);
    });

    // If no provider is found, log a warning and return
    if (!provider || !provider.instance) {
      this.logger.warn(
        `No provider found for event: ${targetEventType}, skipping...`,
      );

      return;
    }

    // Enrich the context
    const enrichedArgs = await this.enrichSlackContext(args);

    // If the context is not present, log an error and return
    if (!('context' in enrichedArgs)) {
      this.logger.error(
        `No context found for event: ${targetEventType}, skipping...`,
      );

      return;
    }

    // If the installation is not present, log an error and return
    const context = enrichedArgs.context as EnrichedSlackArgsContext;
    if (!context.installation) {
      this.logger.error(
        `No installation found for event: ${targetEventType}, skipping...`,
      );

      return;
    }

    try {
      // Handle the event
      await provider.instance.handle(enrichedArgs);
    } catch (error) {
      this.logger.error(`Error handling event: ${error}`);
      throw error;
    }
  }
}
