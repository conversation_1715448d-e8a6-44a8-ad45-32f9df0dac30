import { Injectable } from '@nestjs/common';
import { Inject } from '@nestjs/common';
import { AiService } from '../../ai/ai.service';
import { Installations } from '../../database/entities';
import { PlatformTeams } from '../../database/entities/teams/teams.entity';
import { CUSTOM_LOGGER_TOKEN, ILogger } from '../../utils';

export interface TicketContentResult {
  title: string;
  description: string;
}

/**
 * Service for generating ticket content using AI
 * This service centralizes AI-related operations for ticket generation
 */
@Injectable()
export class AiTicketGeneratorService {
  constructor(
    @Inject(CUSTOM_LOGGER_TOKEN) private readonly logger: ILogger,
    private readonly aiService: AiService,
  ) {}

  /**
   * Set the AI provider and model based on the model name
   * @param aiModel The AI model name
   */
  public setAiProviderAndModel(aiModel: string): void {
    // Set the active provider based on the model
    if (
      aiModel === 'claude-3-7-sonnet-20250219' ||
      aiModel === 'claude-3-5-haiku-20241022'
    ) {
      this.aiService.setActiveProvider('claude');
      this.aiService.setActiveModel(aiModel);
    } else if (aiModel === 'gpt-4o' || aiModel === 'o3-mini-2025-01-31') {
      this.aiService.setActiveProvider('openai');
      this.aiService.setActiveModel(aiModel);
    } else {
      // Default to OpenAI GPT-o3-mini
      this.aiService.setActiveProvider('openai');
      this.aiService.setActiveModel('o3-mini-2025-01-31');
    }
  }

  /**
   * Generate ticket title and description using AI
   * @param messageContent The message content to generate from
   * @param platformTeam The platform team
   * @param installation The installation
   * @param fallbackTitle The fallback title if AI generation fails
   * @param fallbackDescription The fallback description if AI generation fails
   * @returns The generated title and description
   */
  public async generateTicketContent(
    messageContent: string,
    platformTeam: PlatformTeams,
    installation: Installations,
    aiModel: string,
    fallbackTitle: string,
    fallbackDescription: string,
  ): Promise<TicketContentResult> {
    try {
      this.logger.debug(`Using AI model ${aiModel} for ticket generation`);

      // Set the AI provider and model
      this.setAiProviderAndModel(aiModel);

      // Load team prompts
      await this.aiService.loadTeamPrompts(
        platformTeam.id,
        installation.id,
        installation.organization.id,
      );

      // Generate title and description using AI
      const [titleResult, descriptionResult] = await Promise.allSettled([
        this.aiService.generateTicketTitle(
          messageContent,
          platformTeam.id,
          installation.id,
          installation.organization.id,
        ),
        this.aiService.generateTicketDescription(
          messageContent,
          platformTeam.id,
          installation.id,
          installation.organization.id,
        ),
      ]);

      // Use AI-generated title or fall back to provided fallback
      const title =
        titleResult.status === 'fulfilled' ? titleResult.value : fallbackTitle;

      // Use AI-generated description or fall back to provided fallback
      const description =
        descriptionResult.status === 'fulfilled'
          ? descriptionResult.value
          : fallbackDescription;

      return { title, description };
    } catch (error) {
      this.logger.error(
        `Error generating ticket content with AI: ${
          error instanceof Error ? error.message : String(error)
        }`,
      );
      return { title: fallbackTitle, description: fallbackDescription };
    }
  }
}
