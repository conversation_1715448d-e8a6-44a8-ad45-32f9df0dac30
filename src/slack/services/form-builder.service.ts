import { Inject, Injectable } from '@nestjs/common';
import { Installations } from '../../database/entities';
import { ThenaPlatformApiProvider } from '../../external/provider/thena-platform-api.provider';
import { ILogger } from '../../utils';
import { CUSTOM_LOGGER_TOKEN } from '../../utils';
import {
  Condition,
  Field,
  TargetField,
  TargetFieldActionType,
  TargetFieldConditionsType,
} from '../blocks/components/composite/form-builder/conditional-form-builder.composite';
import { FormOption } from '../blocks/components/composite/form-builder/form-selector.composite';

interface ApiFormField {
  field: string;
  fieldType: string;
  defaultValue: any;
  mandatoryOnClose: boolean;
  visibleToCustomer: boolean;
  editableByCustomer: boolean;
  mandatoryOnCreation: boolean;
  meta: {
    name: string;
    options?: Array<{ id: string; value: string }>;
    type: string;
    aiForOptions: any;
    accessibleInTicketCreationForm: boolean;
    currencyCode?: string;
    maxFileSize?: number;
    allowedFileTypes?: string[];
    lookupType?: string;
    lookupApi?: string;
  };
}

interface ApiFormCondition {
  targetFields: Array<{
    id: string;
    type: string;
    value: any;
  }>;
  conditionType: string;
  triggerFieldId: string;
  triggerFieldValue: any;
}

@Injectable()
export class FormBuilderService {
  constructor(
    @Inject(CUSTOM_LOGGER_TOKEN) private readonly logger: ILogger,
    private readonly platformApiProvider: ThenaPlatformApiProvider,
  ) {}

  /**
   * Get all forms for an organization/team
   * @param installation The installation
   * @param teamId The team ID
   * @param selectedFormIds Optional array of form IDs to filter by
   * @returns Array of form options
   */
  async getForms(
    installation: Installations,
    teamId: string,
    selectedFormIds?: string[],
  ): Promise<FormOption[]> {
    try {
      // Use the existing ThenaPlatformApiProvider to fetch forms
      const formsResponse = await this.platformApiProvider.fetchFormsForTeam(
        installation,
        teamId,
      );

      if (!formsResponse || !formsResponse.results) {
        return [];
      }

      // Map forms to our format
      let forms = formsResponse.results.map((form) => ({
        id: form.id,
        name: form.name,
        description: form.description || '',
        teamId: form.teamId,
      }));

      // Filter by selected form IDs if provided
      if (selectedFormIds && selectedFormIds.length > 0) {
        this.logger.debug(
          `Filtering forms by selected IDs: ${JSON.stringify(selectedFormIds)}`,
        );
        // Use a Set for O(1) lookups and convert IDs to strings for type-safe comparison
        const allowedIds = new Set(selectedFormIds.map(String));
        forms = forms.filter((form) => allowedIds.has(String(form.id)));
        this.logger.debug(`Filtered to ${forms.length} forms`);
      }

      return forms;
    } catch (error) {
      if (error instanceof Error) {
        this.logger.error(
          `Error fetching forms: ${error.message}`,
          error.stack,
        );
      } else {
        console.error(error);
      }
      return [];
    }
  }

  /**
   * Process a single form from the API response
   */
  async processForm(form: any): Promise<{
    fields: Field[];
    conditions: Map<string, Condition>;
    conditionOrder: string[];
  } | null> {
    try {
      if (!form) {
        return null;
      }

      const fields = this.mapApiFieldsToFields(form.fields);
      const { conditions, conditionOrder } = this.mapApiConditionsToConditions(
        form.conditions || [],
      );

      return { fields, conditions, conditionOrder };
    } catch (error) {
      if (error instanceof Error) {
        this.logger.error(
          `Error processing form: ${error.message}`,
          error.stack,
        );
      } else {
        console.error(error);
      }
      return null;
    }
  }

  /**
   * Get form by ID from a list of forms
   * @param installation The installation
   * @param formId The form ID to fetch
   * @param teamId The team ID
   * @param selectedFormIds Optional array of form IDs to filter by
   * @returns Form data or null if not found
   */
  async getFormById(
    installation: Installations,
    formId: string,
    teamId: string,
    selectedFormIds?: string[],
  ): Promise<{
    fields: Field[];
    conditions: Condition[];
    conditionOrder: string[];
    name: string;
  } | null> {
    try {
      // Check if we have selected form IDs and if the requested form is in the list
      if (selectedFormIds && selectedFormIds.length > 0) {
        // Use Set for O(1) lookup and String conversion for type safety
        const allowedIds = new Set(selectedFormIds.map(String));
        if (!allowedIds.has(String(formId))) {
          this.logger.debug(
            `Form ${formId} is not in the selected forms list: ${JSON.stringify(selectedFormIds)}`,
          );
          return null;
        }
      }

      // Get all forms for the team
      const formsResponse = await this.platformApiProvider.fetchFormsForTeam(
        installation,
        teamId,
      );

      if (!formsResponse || !formsResponse.results) {
        return null;
      }

      // Find the form with the matching ID
      const form = formsResponse.results.find((f) => f.id === formId);

      if (!form) {
        return null;
      }

      const result = await this.processForm(form);
      if (!result) {
        return null;
      }

      // Convert Map to array for the builder
      const conditionsArray = Array.from(result.conditions.values());

      return {
        fields: result.fields,
        conditions: conditionsArray,
        conditionOrder: result.conditionOrder,
        name: form.name,
      };
    } catch (error) {
      if (error instanceof Error) {
        this.logger.error(
          `Error fetching form by ID: ${error.message}`,
          error.stack,
        );
      } else {
        console.error(error);
      }
      return null;
    }
  }

  /**
   * Transform API fields to our expected format
   */
  private mapApiFieldsToFields(apiFields: ApiFormField[]): Field[] {
    return apiFields.map((apiField) => {
      // Map field type to our internal type
      let fieldType = apiField.meta.type || apiField.fieldType;

      // Handle specialized field types
      switch (fieldType.toLowerCase()) {
        case 'rich_text':
        case 'email':
        case 'phone_number':
        case 'url':
        case 'decimal':
        case 'currency':
        case 'date_time':
        case 'time':
        case 'toggle':
        case 'file_upload':
        case 'lookup':
        case 'coordinates':
          // These are handled as specialized types
          fieldType = 'specialized';
          break;
        case 'single_choice':
          fieldType = 'choice';
          break;
        case 'multi_choice':
          fieldType = 'multiselect';
          break;
        case 'boolean':
          fieldType = 'checkbox';
          break;
      }

      return {
        id: apiField.field,
        name: apiField.meta.name,
        type: fieldType,
        mandatoryOnCreation: apiField.mandatoryOnCreation,
        visibleToCustomer: apiField.visibleToCustomer,
        editableByCustomer: apiField.editableByCustomer,
        accessibleInTicketCreationForm:
          apiField.meta.accessibleInTicketCreationForm,
        options: apiField.meta.options?.map((opt) => ({
          label: opt.value,
          value: opt.id,
        })),
        apiForOptions: apiField.meta.aiForOptions,
        // Add metadata for specialized fields
        metadata: {
          originalType: apiField.meta.type || apiField.fieldType,
          ...(apiField.meta.type === 'currency' && {
            currencyCode: apiField.meta.currencyCode,
          }),
          ...(apiField.meta.type === 'file_upload' && {
            maxFileSize: apiField.meta.maxFileSize,
            allowedFileTypes: apiField.meta.allowedFileTypes,
          }),
          ...(apiField.meta.type === 'lookup' && {
            lookupType: apiField.meta.lookupType,
            lookupApi: apiField.meta.lookupApi,
          }),
        },
      };
    });
  }

  /**
   * Transform API conditions to our expected format
   */
  private mapApiConditionsToConditions(apiConditions: ApiFormCondition[]): {
    conditions: Map<string, Condition>;
    conditionOrder: string[];
  } {
    const conditions = new Map<string, Condition>();
    const conditionOrder: string[] = [];

    apiConditions.forEach((apiCondition, index) => {
      const conditionId = `condition-${index + 1}`;

      // Map condition type
      let conditionType: TargetFieldConditionsType;
      switch (apiCondition.conditionType) {
        case 'equals':
          conditionType = TargetFieldConditionsType.EQUALS;
          break;
        case 'not_equals':
          conditionType = TargetFieldConditionsType.NOT_EQUALS;
          break;
        case 'contains':
          conditionType = TargetFieldConditionsType.CONTAINS;
          break;
        case 'not_contains':
          conditionType = TargetFieldConditionsType.NOT_CONTAINS;
          break;
        case 'greater_than':
          conditionType = TargetFieldConditionsType.GREATER_THAN;
          break;
        case 'greater_than_equal':
          conditionType = TargetFieldConditionsType.GREATER_THAN_EQUAL;
          break;
        case 'less_than':
          conditionType = TargetFieldConditionsType.LESS_THAN;
          break;
        case 'less_than_equal':
          conditionType = TargetFieldConditionsType.LESS_THAN_EQUAL;
          break;
        case 'is_empty':
          conditionType = TargetFieldConditionsType.IS_EMPTY;
          break;
        case 'is_not_empty':
          conditionType = TargetFieldConditionsType.IS_NOT_EMPTY;
          break;
        default:
          conditionType = TargetFieldConditionsType.EQUALS;
      }

      // Map target fields
      const targetFields: TargetField[] = apiCondition.targetFields.map(
        (targetField, tfIndex) => {
          // Map target field action type
          let actionType: TargetFieldActionType;
          switch (targetField.type) {
            case 'add_field':
              actionType = TargetFieldActionType.ADD_FIELD;
              break;
            case 'remove_field':
              actionType = TargetFieldActionType.REMOVE_FIELD;
              break;
            case 'mark_mandatory':
              actionType = TargetFieldActionType.MARK_MANDATORY;
              break;
            case 'mark_non_mandatory':
              actionType = TargetFieldActionType.MARK_NON_MANDATORY;
              break;
            case 'fill_value':
              actionType = TargetFieldActionType.FILL_VALUE;
              break;
            default:
              actionType = TargetFieldActionType.MARK_MANDATORY;
          }

          return {
            id: `action-${tfIndex + 1}`,
            type: actionType,
            value: targetField.value,
            fieldId: targetField.id,
          };
        },
      );

      // Create condition
      const condition: Condition = {
        id: conditionId,
        triggerFieldId: apiCondition.triggerFieldId,
        conditionType,
        value: apiCondition.triggerFieldValue,
        targetFields,
      };

      conditions.set(conditionId, condition);
      conditionOrder.push(conditionId);
    });

    return { conditions, conditionOrder };
  }

  /**
   * Get options for a field from an external API
   */
  async getFieldOptions(
    installation: Installations,
    apiEndpoint: string,
    _teamId: string,
    searchValue?: string,
  ): Promise<Array<{ label: string; value: string }>> {
    try {
      // Use the proxy method from ThenaPlatformApiProvider
      const response = await this.platformApiProvider.proxy(
        installation.organization,
        'GET',
        `${apiEndpoint}?search=${encodeURIComponent(searchValue || '')}`,
      );

      if (!response.ok) {
        this.logger.error('Error fetching field options: API request failed');
        return [];
      }

      const data = await response.json();
      return data.data || [];
    } catch (error) {
      this.logger.error(
        'Error fetching field options:',
        error instanceof Error ? error.message : 'Unknown error',
      );
      return [];
    }
  }
}
