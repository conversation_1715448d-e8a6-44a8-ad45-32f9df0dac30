import {
  BadRequestException,
  Inject,
  Injectable,
  NotFoundException,
} from '@nestjs/common';
import { InjectRepository } from '@nestjs/typeorm';
import { Repository } from 'typeorm';
import { DEFAULT_PROMPTS } from '../../ai/constants/default-prompts';
import { PromptType } from '../../ai/constants/prompt-types.enum';
import { BotCtx } from '../../auth/interfaces';
import { Prompts } from '../../database/entities/prompts/prompts.entity';
import { PlatformTeams } from '../../database/entities/teams';
import { CUSTOM_LOGGER_TOKEN, ILogger } from '../../utils';

@Injectable()
export class PromptsService {
  constructor(
    @InjectRepository(Prompts)
    private readonly promptsRepository: Repository<Prompts>,

    @InjectRepository(PlatformTeams)
    private readonly teamsRepository: Repository<PlatformTeams>,

    @Inject(CUSTOM_LOGGER_TOKEN) private readonly logger: ILogger,
  ) {}

  /**
   * Get all prompts for an organization and installation (without teamId)
   */
  async getPrompts(botCtx: BotCtx) {
    try {
      const { installation, organization } = botCtx;

      const prompts = await this.promptsRepository.find({
        where: {
          installation: { id: installation.id },
          organization: { id: organization.id },
        },
        relations: ['platformTeam'],
      });

      return prompts;
    } catch (error) {
      if (error instanceof Error) {
        this.logger.error(`Error fetching prompts: ${error.message}`);
      } else {
        console.error(error);
      }

      throw error;
    }
  }

  /**
   * Get prompts for a specific team
   */
  async getPromptsByTeamId(botCtx: BotCtx, teamId: string) {
    try {
      const { installation, organization } = botCtx;

      const prompts = await this.promptsRepository.find({
        where: {
          installation: { id: installation.id },
          organization: { id: organization.id },
          platformTeam: { uid: teamId },
        },
        relations: ['platformTeam'],
      });

      return prompts;
    } catch (error) {
      if (error instanceof Error) {
        this.logger.error(
          `Error fetching prompts by team ID: ${error.message}`,
        );
      } else {
        console.error(error);
      }

      throw error;
    }
  }

  /**
   * Get a specific prompt by ID
   */
  async getPromptById(botCtx: BotCtx, promptId: string) {
    try {
      const { installation, organization } = botCtx;

      const prompt = await this.promptsRepository.findOne({
        where: {
          id: promptId,
          installation: { id: installation.id },
          organization: { id: organization.id },
        },
        relations: ['platformTeam'],
      });

      if (!prompt) {
        throw new NotFoundException(`Prompt with ID ${promptId} not found`);
      }

      return prompt;
    } catch (error) {
      if (error instanceof Error) {
        this.logger.error(`Error fetching prompt by ID: ${error.message}`);
      } else {
        console.error(error);
      }

      throw error;
    }
  }

  /**
   * Create a new prompt
   */
  async createPrompt(botCtx: BotCtx, promptData: Partial<Prompts>) {
    try {
      const { installation, organization } = botCtx;

      const newPrompt = this.promptsRepository.create({
        ...promptData,
        installation,
        organization,
      });

      return await this.promptsRepository.save(newPrompt);
    } catch (error) {
      if (error instanceof Error) {
        this.logger.error(`Error creating prompt: ${error.message}`);
      } else {
        console.error(error);
      }

      throw error;
    }
  }

  /**
   * Update an existing prompt
   */
  async updatePrompt(
    botCtx: BotCtx,
    promptId: string,
    promptData: Partial<Prompts>,
  ) {
    try {
      const { installation, organization } = botCtx;

      // Check if the prompt exists
      const existingPrompt = await this.promptsRepository.findOne({
        where: {
          id: promptId,
          installation: { id: installation.id },
          organization: { id: organization.id },
        },
      });

      if (!existingPrompt) {
        throw new NotFoundException(`Prompt with ID ${promptId} not found`);
      }

      // Update the prompt
      await this.promptsRepository.update({ id: promptId }, { ...promptData });

      // Return the updated prompt
      return await this.getPromptById(botCtx, promptId);
    } catch (error) {
      if (error instanceof Error) {
        this.logger.error(`Error updating prompt: ${error.message}`);
      } else {
        console.error(error);
      }

      throw error;
    }
  }

  /**
   * Delete a prompt
   */
  async deletePrompt(botCtx: BotCtx, promptId: string) {
    try {
      const { installation, organization } = botCtx;

      // Check if the prompt exists
      const existingPrompt = await this.promptsRepository.findOne({
        where: {
          id: promptId,
          installation: { id: installation.id },
          organization: { id: organization.id },
        },
      });

      if (!existingPrompt) {
        throw new NotFoundException(`Prompt with ID ${promptId} not found`);
      }

      // Soft delete the prompt
      await this.promptsRepository.softDelete(promptId);

      return { success: true, message: 'Prompt deleted successfully' };
    } catch (error) {
      if (error instanceof Error) {
        this.logger.error(`Error deleting prompt: ${error.message}`);
      } else {
        console.error(error);
      }

      throw error;
    }
  }

  /**
   * Set a prompt as default for a platform team
   */
  async setDefaultPrompt(
    botCtx: BotCtx,
    promptId: string,
    platformTeamId: string,
  ) {
    try {
      const { installation, organization } = botCtx;

      // First, find the prompt we want to set as default
      const prompt = await this.promptsRepository.findOne({
        where: {
          id: promptId,
          installation: { id: installation.id },
          organization: { id: organization.id },
        },
        relations: ['platformTeam'],
      });

      if (!prompt) {
        throw new NotFoundException(`Prompt with ID ${promptId} not found`);
      }

      // Check if the prompt belongs to the specified platform team
      if (prompt.platformTeam?.id.toString() !== platformTeamId) {
        throw new BadRequestException(
          'Prompt does not belong to the specified platform team',
        );
      }

      // Unset default for all other prompts in this platform team
      await this.promptsRepository.update(
        {
          platformTeam: { id: platformTeamId },
          installation: { id: installation.id },
          organization: { id: organization.id },
          isDefault: true,
        },
        { isDefault: false },
      );

      // Set this prompt as default
      await this.promptsRepository.update(
        { id: promptId },
        { isDefault: true },
      );

      return await this.getPromptById(botCtx, promptId);
    } catch (error) {
      if (error instanceof Error) {
        this.logger.error(`Error setting default prompt: ${error.message}`);
      } else {
        console.error(error);
      }

      throw error;
    }
  }

  /**
   * Seeds default prompts for a team if none exist
   * @param botCtx The bot context
   * @param teamId The team ID to seed prompts for
   * @returns The created default prompt, or null if prompts already exist
   */
  async seedDefaultPrompts(
    botCtx: BotCtx,
    teamId: string,
  ): Promise<Prompts | null> {
    try {
      const { installation, organization } = botCtx;

      // Check if the team exists
      const team = await this.teamsRepository.findOne({
        where: { id: teamId },
      });

      if (!team) {
        throw new NotFoundException(`Team with ID ${teamId} not found`);
      }

      // Check if the team already has prompts
      const existingPrompts = await this.promptsRepository.find({
        where: {
          platformTeam: { id: teamId },
          installation: { id: installation.id },
          organization: { id: organization.id },
        },
      });

      if (existingPrompts.length > 0) {
        this.logger.log(
          `Team ${teamId} already has ${existingPrompts.length} prompts, skipping seed`,
        );
        return null;
      }

      // Create default prompts for the team
      const defaultPrompt = this.promptsRepository.create({
        name: `${team.uid} Default Prompts`,
        prompts: {
          ticket_detection: DEFAULT_PROMPTS[PromptType.TICKET_DETECTION],
          sentiment_analysis: DEFAULT_PROMPTS[PromptType.SENTIMENT_ANALYSIS],
          urgency_detection: DEFAULT_PROMPTS[PromptType.URGENCY_DETECTION],
          custom_fields: DEFAULT_PROMPTS[PromptType.CUSTOM_FIELDS],
          title_generation: DEFAULT_PROMPTS[PromptType.TITLE_GENERATION],
          description_generation:
            DEFAULT_PROMPTS[PromptType.DESCRIPTION_GENERATION],
        },
        platformTeam: { id: teamId },
        installation: { id: installation.id },
        organization: { id: organization.id },
        isDefault: true,
        isEnabled: true,
      });

      this.logger.log(`Creating default prompt for team ${teamId}`);
      return await this.promptsRepository.save(defaultPrompt);
    } catch (error) {
      if (error instanceof Error) {
        this.logger.error(
          `Error seeding default prompts for team ${teamId}: ${error.message}`,
        );
      } else {
        console.error(error);
      }

      throw error;
    }
  }

  /**
   * Seeds default prompts for all teams
   * @param botCtx The bot context
   * @returns The number of teams that had prompts created
   */
  async seedDefaultPromptsForAllTeams(
    botCtx: BotCtx,
  ): Promise<{ seededCount: number; skippedCount: number }> {
    try {
      const { organization } = botCtx;

      // Get all teams for this organization
      const teams = await this.teamsRepository.find({
        where: {
          organization: { id: organization.id },
        },
      });

      let seededCount = 0;
      let skippedCount = 0;

      for (const team of teams) {
        const result = await this.seedDefaultPrompts(
          botCtx,
          team.id.toString(),
        );
        if (result) {
          seededCount++;
        } else {
          skippedCount++;
        }
      }

      this.logger.log(
        `Seeded default prompts for ${seededCount} teams, skipped ${skippedCount} teams`,
      );
      return { seededCount, skippedCount };
    } catch (error) {
      if (error instanceof Error) {
        this.logger.error(
          `Error seeding default prompts for all teams: ${error.message}`,
        );
      } else {
        console.error(error);
      }

      throw error;
    }
  }
}
