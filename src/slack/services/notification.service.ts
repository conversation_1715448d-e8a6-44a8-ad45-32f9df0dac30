import { Inject, Injectable } from '@nestjs/common';
import { WebClient } from '@slack/web-api';
import { ConfigKeys, ConfigService } from '../../config/config.service';
import { CUSTOM_LOGGER_TOKEN, ILogger } from '../../utils';

// Define types for better type safety and code readability
interface TicketData {
  id: string;
  title: string;
  teamIdentifier?: string;
  ticketId?: number | string;
}

// Define message payload type
interface MessagePayload {
  channel: string;
  text: string;
  blocks: Array<{
    type: string;
    text: {
      type: string;
      text: string;
    };
  }>;
}

@Injectable()
export class NotificationService {
  constructor(
    @Inject(CUSTOM_LOGGER_TOKEN) private readonly logger: ILogger,
    private readonly configService: ConfigService,
  ) {}

  /**
   * Build a ticket URL with proper safeguards against undefined values
   * @param ticketId The ticket ID for the URL
   * @param teamId The team ID for the URL, may fall back to ticketData.teamIdentifier
   * @param ticketData Additional ticket data that may contain a teamIdentifier
   * @returns The fully formed and safe ticket URL
   */
  private buildTicketUrl(
    ticketId: string,
    teamId?: string,
    ticketData?: { teamIdentifier?: string },
  ): string {
    // Get platform URL for each request to support dynamic config changes
    const platformUrl = this.configService.get(ConfigKeys.THENA_WEB_URL);

    // Ensure teamId is defined and safely encoded
    const safeTeamId = encodeURIComponent(
      teamId ?? ticketData?.teamIdentifier ?? '',
    );

    return `${platformUrl}/dashboard/${safeTeamId}?ticketId=${ticketId}`;
  }

  /**
   * Format ticket identifier for display
   * @param ticketData The ticket data
   * @param teamId The team ID (optional)
   * @returns Formatted ticket identifier string
   */
  private formatTicketIdentifier(
    ticketData: TicketData,
    teamId?: string,
  ): string {
    const team = ticketData.teamIdentifier || teamId || '';

    // Use ticketId if available, otherwise safely extract from id
    let ticketNumber: string | number = '';
    if (ticketData.ticketId !== undefined) {
      ticketNumber = ticketData.ticketId;
    } else if (ticketData.id?.includes('-')) {
      ticketNumber = ticketData.id.split('-')[0];
    } else {
      ticketNumber = ticketData.id;
    }

    return `${team}#${ticketNumber}`;
  }

  /**
   * Create a message payload for ticket notifications
   * @param channelId The channel ID where the message should be sent
   * @param ticketUrl The URL to the ticket
   * @param ticketIdentifier The formatted ticket identifier
   * @param message The message to display
   * @returns The formatted message payload
   */
  private createMessagePayload(
    channelId: string,
    ticketUrl: string,
    ticketIdentifier: string,
    message: string,
  ): MessagePayload {
    if (!channelId) {
      throw new Error('Channel ID is required for creating message payload');
    }

    return {
      channel: channelId,
      text: message,
      blocks: [
        {
          type: 'section',
          text: {
            type: 'mrkdwn',
            text: `<${ticketUrl}|${ticketIdentifier}>: ${message}`,
          },
        },
      ],
    };
  }

  /**
   * Send a message to Slack as an ephemeral message
   * @param client The Slack WebClient instance
   * @param payload The message payload
   * @param userId The user ID who should receive the message
   * @param logSpan The logging span identifier
   */
  private async sendEphemeralMessage(
    client: WebClient,
    payload: MessagePayload,
    userId: string,
    logSpan: string,
  ): Promise<void> {
    if (!userId) {
      this.logger.error(
        `${logSpan} User ID is required for ephemeral messages`,
      );
      throw new Error('User ID is required for ephemeral messages');
    }

    this.logger.debug(`${logSpan} Sending as ephemeral message`);
    this.logger.log(
      `${logSpan} Sending ephemeral message to user ${userId} in channel ${payload.channel}`,
    );

    await client.chat.postEphemeral({
      ...payload,
      user: userId,
    });

    this.logger.log(
      `${logSpan} Ephemeral message sent successfully to user ${userId}`,
    );
  }

  /**
   * Send a message to Slack in a thread
   * @param client The Slack WebClient instance
   * @param payload The message payload
   * @param threadTs The thread timestamp
   * @param logSpan The logging span identifier
   */
  private async sendThreadMessage(
    client: WebClient,
    payload: MessagePayload,
    threadTs: string,
    logSpan: string,
  ): Promise<void> {
    if (!threadTs) {
      this.logger.error(
        `${logSpan} Thread timestamp is required for thread messages`,
      );
      throw new Error('Thread timestamp is required for thread messages');
    }

    this.logger.debug(`${logSpan} Sending in thread: ${threadTs}`);
    this.logger.log(
      `${logSpan} Sending message as thread reply in channel ${payload.channel}, thread ${threadTs}`,
    );

    await client.chat.postMessage({
      ...payload,
      thread_ts: threadTs,
    });

    this.logger.log(
      `${logSpan} Thread message sent successfully in thread ${threadTs}`,
    );
  }

  /**
   * Send a ticket notification with specified message
   * @param client The Slack WebClient instance
   * @param channelId The channel ID where the message should be sent
   * @param userId The user ID who should receive the message
   * @param ticketData The ticket data
   * @param message The message to display
   * @param threadTs Optional thread timestamp
   * @param teamId Optional team ID
   * @param logSpan The logging span identifier
   */
  private async sendTicketNotification(
    client: WebClient,
    channelId: string,
    userId: string,
    ticketData: TicketData,
    message: string,
    threadTs?: string,
    teamId?: string,
    logSpan?: string,
  ): Promise<void> {
    const span = logSpan || 'NotificationService.sendTicketNotification';

    try {
      // Validate required parameters
      if (!channelId) {
        throw new Error('Channel ID is required');
      }

      if (!ticketData || !ticketData.id) {
        throw new Error('Valid ticket data with ID is required');
      }

      this.logger.debug(
        `${span} Preparing notification for ticket ${ticketData.id} in channel ${channelId}`,
      );

      const ticketUrl = this.buildTicketUrl(ticketData.id, teamId, ticketData);
      const ticketIdentifier = this.formatTicketIdentifier(ticketData, teamId);

      this.logger.debug(`${span} Generated ticket URL: ${ticketUrl}`);
      this.logger.debug(
        `${span} Channel ID: ${channelId}, Ticket ID: ${ticketData.id}`,
      );

      const messagePayload = this.createMessagePayload(
        channelId,
        ticketUrl,
        ticketIdentifier,
        message,
      );

      // Send as thread message or ephemeral message based on parameters
      if (threadTs) {
        await this.sendThreadMessage(client, messagePayload, threadTs, span);
      } else if (userId) {
        await this.sendEphemeralMessage(client, messagePayload, userId, span);
      } else {
        throw new Error('Either userId or threadTs must be provided');
      }
    } catch (error) {
      this.logger.error(`${span} Error sending notification: ${error}`);
      throw error;
    }
  }

  /**
   * Send a message to a user with ticket creation confirmation
   * @param client The Slack WebClient instance
   * @param channelId The channel ID where the message should be sent
   * @param userId The user ID who should receive the message
   * @param ticketData The ticket data
   * @param threadTs Optional thread timestamp
   * @param teamId Optional team ID
   */
  async sendTicketCreationConfirmation(
    client: WebClient,
    channelId: string,
    userId: string,
    ticketData: TicketData,
    threadTs?: string,
    teamId?: string,
  ): Promise<void> {
    const LOG_SPAN = 'NotificationService.sendTicketCreationConfirmation';

    await this.sendTicketNotification(
      client,
      channelId,
      userId,
      ticketData,
      'Ticket created successfully',
      threadTs,
      teamId,
      LOG_SPAN,
    );
  }

  /**
   * Send a message to a user indicating that a ticket already exists
   * @param client The Slack WebClient instance
   * @param channelId The channel ID where the message should be sent
   * @param userId The user ID who should receive the message
   * @param ticketData The ticket data
   * @param threadTs Optional thread timestamp
   * @param teamId Optional team ID
   */
  async sendTicketAlreadyExistsNotification(
    client: WebClient,
    channelId: string,
    userId: string,
    ticketData: TicketData,
    threadTs?: string,
    teamId?: string,
  ): Promise<void> {
    const LOG_SPAN = 'NotificationService.sendTicketAlreadyExistsNotification';

    await this.sendTicketNotification(
      client,
      channelId,
      userId,
      ticketData,
      'ℹ️ Ticket already exists',
      threadTs,
      teamId,
      LOG_SPAN,
    );
  }
}
