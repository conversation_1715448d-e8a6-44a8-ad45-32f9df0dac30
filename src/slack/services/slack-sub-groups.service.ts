import { Inject, Injectable, NotFoundException } from '@nestjs/common';
import { FindOptionsWhere, ILike } from 'typeorm';
import { BotCtx } from '../../auth/interfaces';
import { TransactionService } from '../../database/common';
import { SlackSubgroups } from '../../database/entities';
import { SubGroupsMapsRepository } from '../../database/entities/mappings/repositories/sub-groups-maps.repository';
import { SlackSubgroupsRepository } from '../../database/entities/subgroups/repositories/subgroups.repository';
import { TeamsRepository } from '../../database/entities/teams';
import { CUSTOM_LOGGER_TOKEN, ILogger } from '../../utils';
import {
  MapSubGroupToSubTeamDTO,
  UpdateSubGroupMappingDTO,
} from '../dtos/sub-groups.dto';
import { SearchQueryParams } from '../query-params';

@Injectable()
export class SlackSubGroupsService {
  constructor(
    @Inject(CUSTOM_LOGGER_TOKEN) private readonly logger: ILogger,

    // Utility Services
    private readonly transactionService: TransactionService,

    // Database Repositories
    private readonly teamsRepository: TeamsRepository,
    private readonly subGroupsRepository: SlackSubgroupsRepository,
    private readonly subGroupMappingsRepository: SubGroupsMapsRepository,
  ) {}

  /**
   * Fetches all sub groups for a workspace
   */
  async getAllSubGroupsForWorkspace(query: SearchQueryParams, botCtx: BotCtx) {
    const { installation, organization } = botCtx;

    // Define the where clause
    const whereClause: FindOptionsWhere<SlackSubgroups> = {
      installation: { id: installation.id },
      organization: { id: organization.id },
    };

    // If a search query is provided, add it to the where clause
    if (query.searchQuery) {
      whereClause.slackHandle = ILike(`%${query.searchQuery}%`);
    }

    const subGroups = await this.subGroupsRepository.findAll({
      where: whereClause,
    });

    return subGroups;
  }

  /**
   * Fetches all sub group mappings for a workspace
   */
  async getAllSubGroupMappings(botCtx: BotCtx) {
    const { installation, organization } = botCtx;

    // Fetch all mappings
    const mappings = await this.subGroupMappingsRepository.findAll({
      where: {
        installation: { id: installation.id },
        organization: { id: organization.id },
      },
    });

    return mappings;
  }

  /**
   * Creates a mapping between a slack sub group and a platform sub team
   */
  async createMapping(data: MapSubGroupToSubTeamDTO, botCtx: BotCtx) {
    const { installation, organization } = botCtx;
    const { slackSubGroupId, platformSubGroupId, platformTeamId } = data;

    // Find the slack sub group
    const slackSubGroup = await this.subGroupsRepository.findByCondition({
      where: {
        slackGroupId: slackSubGroupId,
        installation: { id: installation.id },
        organization: { id: organization.id },
      },
    });

    // If the slack sub group is not found, throw an error
    if (!slackSubGroup) {
      throw new NotFoundException('Slack sub group not found');
    }

    // Find the platform team
    const platformTeam = await this.teamsRepository.findByCondition({
      where: {
        uid: platformTeamId,
        installation: { id: installation.id },
        organization: { id: organization.id },
      },
    });

    // If the platform team is not found, throw an error
    if (!platformTeam) {
      throw new NotFoundException('Platform team not found');
    }

    // Create mapping with platform's sub group
    await this.transactionService.runInTransaction(async (txnContext) => {
      await this.subGroupMappingsRepository.saveWithTxn(txnContext, {
        subGroup: { id: slackSubGroup.id },
        platformSubTeam: platformSubGroupId,
        platformTeam: { id: platformTeam.id },
        installation: { id: installation.id },
        organization: { id: organization.id },
      });
    });

    return slackSubGroup;
  }

  /**
   * Updates a mapping between a slack sub group and a platform sub team
   */
  async updateMapping(
    id: string,
    data: UpdateSubGroupMappingDTO,
    botCtx: BotCtx,
  ) {
    const { installation, organization } = botCtx;
    const { slackSubGroupId, platformSubGroupId } = data;

    // Find the mapping
    const mapping = await this.subGroupMappingsRepository.findByCondition({
      where: {
        id,
        installation: { id: installation.id },
        organization: { id: organization.id },
      },
    });

    // If the mapping is not found, throw an error
    if (!mapping) {
      throw new NotFoundException('Mapping not found');
    }

    // Update the mapping
    await this.transactionService.runInTransaction(async (txnContext) => {
      await this.subGroupMappingsRepository.updateWithTxn(
        txnContext,
        { id },
        {
          subGroup: { id: slackSubGroupId },
          platformSubTeam: platformSubGroupId,
        },
      );
    });

    // Fetch the updated mapping
    const updatedMapping =
      await this.subGroupMappingsRepository.findByCondition({
        where: { id },
      });

    return updatedMapping;
  }

  /**
   * Fetches all sub group mappings with details for a workspace
   */
  async getAllMappedSubGroupsAndTeams(botCtx: BotCtx) {
    const { installation, organization } = botCtx;

    // Fetch all mappings with related entities
    const mappings = await this.subGroupMappingsRepository.findAll({
      where: {
        installation: { id: installation.id },
        organization: { id: organization.id },
      },
      relations: {
        subGroup: true,
        platformTeam: true,
      },
    });

    // Transform the data to include necessary details
    const mappedData = await Promise.all(
      mappings.map(async (mapping) => {
        // Get the subgroup details
        const subGroup = mapping.subGroup;

        // Get the platform team details
        const platformTeam = mapping.platformTeam;

        return {
          id: mapping.id,
          slackSubGroup: {
            id: subGroup.id,
            slackGroupId: subGroup.slackGroupId,
            slackHandle: subGroup.slackHandle,
            description: subGroup.description,
            usersCount: subGroup.usersCount,
          },
          platformTeam: {
            id: platformTeam.id,
            uid: platformTeam.uid,
          },
          platformSubTeamId: mapping.platformSubTeam,
          createdAt: mapping.createdAt,
          updatedAt: mapping.updatedAt,
        };
      }),
    );

    return mappedData;
  }

  /**
   * Deletes a mapping between a slack sub group and a platform sub team
   */
  async deleteMapping(id: string, botCtx: BotCtx) {
    const { installation, organization } = botCtx;

    // Find the mapping
    const mapping = await this.subGroupMappingsRepository.findByCondition({
      where: {
        id,
        installation: { id: installation.id },
        organization: { id: organization.id },
      },
    });

    // If the mapping is not found, throw an error
    if (!mapping) {
      throw new NotFoundException('Mapping not found');
    }

    // Delete the mapping
    await this.transactionService.runInTransaction(async (txnContext) => {
      await this.subGroupMappingsRepository.removeWithTxn(txnContext, mapping);
    });

    return { id, deleted: true };
  }
}
