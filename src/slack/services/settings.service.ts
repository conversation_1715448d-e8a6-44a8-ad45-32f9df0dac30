import {
  BadRequestException,
  Injectable,
  NotFoundException,
} from '@nestjs/common';
import { In, QueryFailedError } from 'typeorm';
import { DeepPartial } from 'typeorm';
import { BotCtx } from '../../auth/interfaces';
import { TransactionContext, TransactionService } from '../../database/common';
import { SettingType } from '../../database/entities/settings/interfaces/slack-app-settings.interface';
import { SettingsSchemasRepository } from '../../database/entities/settings/repositories/setting-schemas.repository';
import { SettingsRepository } from '../../database/entities/settings/repositories/settings.repository';
import { Settings } from '../../database/entities/settings/settings.entity';
import { TeamsRepository } from '../../database/entities/teams/repositories/teams.repository';
import { PlatformTeams } from '../../database/entities/teams/teams.entity';
import { UpdateSettingsDTO } from '../dtos/settings.dto';

@Injectable()
export class SettingsService {
  constructor(
    private readonly settingsRepository: SettingsRepository,
    private readonly teamsRepository: TeamsRepository,
    private readonly settingsSchemasRepository: SettingsSchemasRepository,
    private readonly transactionService: TransactionService,
  ) {}

  /**
   * Convert string value to appropriate type based on setting type
   */
  private convertValueByType(value: string, type: SettingType): any {
    if (value === null || value === undefined) {
      return null;
    }

    switch (type) {
      case SettingType.BOOLEAN:
        return value.toLowerCase() === 'true';

      case SettingType.NUMBER:
        return Number(value);

      case SettingType.ARRAY:
      case SettingType.ARRAY_OF_STRINGS:
      case SettingType.ARRAY_OF_NUMBERS:
      case SettingType.JSONB:
        try {
          const parsed = JSON.parse(value);
          if (type === SettingType.ARRAY_OF_NUMBERS) {
            return parsed.map(Number);
          }
          return parsed;
        } catch {
          return null;
        }

      default:
        return value;
    }
  }

  /**
   * Get default settings from schema with proper type conversion
   */
  private async getDefaultSettings(): Promise<Record<string, any>> {
    const schemas = await this.settingsSchemasRepository.findAll();
    return schemas.reduce((acc, schema) => {
      if (schema.defaultValue !== null) {
        acc[schema.settingKey] = this.convertValueByType(
          schema.defaultValue,
          schema.settingType,
        );
      }
      return acc;
    }, {});
  }

  /**
   * Get default settings from schema and prepare settings entities
   * @param teams - Array of teams
   * @param botCtx - Bot context
   */
  async prepareTeamSettings(
    teams: PlatformTeams[],
    botCtx: BotCtx,
  ): Promise<DeepPartial<Settings>[]> {
    // Get default settings once
    const defaultSettings = await this.getDefaultSettings();

    // Prepare settings entities for bulk insert
    return teams.map((team) => ({
      settings: defaultSettings,
      platformTeam: team,
      organization: { id: botCtx.organization.id },
      installation: { id: botCtx.installation.id },
    }));
  }

  /**
   * Initialize settings for one or more teams
   * @param teamIds - Single team ID or array of team IDs
   * @param botCtx - Bot context
   * @param txnContext - Optional transaction context
   */
  async initializeTeamSettings(
    teamIds: string | string[],
    botCtx: BotCtx,
    txnContext?: TransactionContext,
  ): Promise<Settings | Settings[]> {
    const ids = Array.isArray(teamIds) ? teamIds : [teamIds];

    // Function to perform initialization
    const performInitialization = async (txnContext: TransactionContext) => {
      // Get teams in bulk
      const teams = await this.teamsRepository.findAll({
        where: {
          uid: In(ids),
          organization: { id: botCtx.organization.id },
        },
      });

      if (!teams.length) {
        throw new NotFoundException('No teams found for the provided IDs');
      }

      const settingsToCreate = await this.prepareTeamSettings(teams, botCtx);

      // Bulk create settings
      const createdSettings = await this.settingsRepository.saveManyWithTxn(
        txnContext,
        settingsToCreate,
      );

      return Array.isArray(teamIds) ? createdSettings : createdSettings[0];
    };

    // If transaction context is provided, use it
    if (txnContext) {
      return performInitialization(txnContext);
    }

    // Otherwise, create new transaction
    return this.transactionService.runInTransaction(performInitialization);
  }

  /**
   * Convert snake_case object keys to camelCase
   */
  private toCamelCase(obj: Record<string, any>): Record<string, any> {
    return Object.entries(obj).reduce((acc, [key, value]) => {
      const camelKey = key.replace(/_([a-z])/g, (_, letter) =>
        letter.toUpperCase(),
      );
      acc[camelKey] = value;
      return acc;
    }, {});
  }

  /**
   * Format settings response
   */
  private formatSettingsResponse(settings: Settings) {
    return {
      ...this.toCamelCase(settings.settings),
      createdAt: settings.createdAt,
      updatedAt: settings.updatedAt,
      platformTeamId: settings.platformTeam.uid,
    };
  }

  /**
   * Get settings for a team
   */
  async getTeamSettings(teamId: string, botCtx: BotCtx) {
    const settings = await this.settingsRepository.findByCondition({
      where: {
        platformTeam: { uid: teamId },
        installation: { id: botCtx.installation.id },
        organization: { id: botCtx.organization.id },
      },
      relations: ['platformTeam'],
    });

    if (!settings) {
      throw new NotFoundException(`Settings not found for team ${teamId}`);
    }

    return this.formatSettingsResponse(settings);
  }

  /**
   * Update settings for a team
   */
  async updateSettings(
    teamId: string,
    updateDto: UpdateSettingsDTO,
    botCtx: BotCtx,
  ) {
    try {
      // Get existing settings or initialize new ones
      let settings = await this.settingsRepository.findByCondition({
        where: {
          platformTeam: { uid: teamId },
          installation: { id: botCtx.installation.id },
          organization: { id: botCtx.organization.id },
        },
        relations: ['platformTeam'],
      });

      if (!settings) {
        // Initialize settings for single team
        const initialized = await this.initializeTeamSettings(teamId, botCtx);
        // Since initializeTeamSettings can return array, ensure we get single result
        settings = Array.isArray(initialized) ? initialized[0] : initialized;
      }

      // Convert camelCase to snake_case and filter out undefined values
      const settingsToSave = Object.entries(updateDto).reduce(
        (acc, [key, value]) => {
          // Skip undefined values
          if (value !== undefined) {
            const snakeKey = key.replace(
              /[A-Z]/g,
              (letter) => `_${letter.toLowerCase()}`,
            );
            acc[snakeKey] = value;
          }
          return acc;
        },
        {},
      );
      // Merge with existing settings
      const mergedSettings = {
        ...settings.settings,
        ...settingsToSave,
      };

      // Update settings in transaction
      await this.transactionService.runInTransaction(async (txnContext) => {
        await this.settingsRepository.updateWithTxn(
          txnContext,
          { id: settings.id },
          { settings: mergedSettings },
        );
      });

      const updatedSettings = await this.settingsRepository.findByCondition({
        where: { id: settings.id },
        relations: ['platformTeam'],
      });

      return this.formatSettingsResponse(updatedSettings);
    } catch (error) {
      if (error instanceof QueryFailedError) {
        throw new BadRequestException('Invalid settings values provided');
      }

      throw error;
    }
  }

  /**
   * Reset a specific setting to its default value
   */
  async deleteSetting(teamId: string, settingKey: string, botCtx: BotCtx) {
    try {
      const settings = await this.settingsRepository.findByCondition({
        where: {
          platformTeam: { uid: teamId },
          installation: { id: botCtx.installation.id },
          organization: { id: botCtx.organization.id },
        },
        relations: ['platformTeam'],
      });

      if (!settings) {
        throw new NotFoundException('Settings not found for team');
      }

      // Convert camelCase to snake_case for database key
      const dbKey = settingKey.replace(
        /[A-Z]/g,
        (letter) => `_${letter.toLowerCase()}`,
      );

      // Get the schema for this setting
      const schema = await this.settingsSchemasRepository.findByCondition({
        where: { settingKey: dbKey },
      });

      if (!schema) {
        throw new BadRequestException(`Unknown setting: ${settingKey}`);
      }

      // Get the default value with proper type conversion
      const defaultValue = this.convertValueByType(
        schema.defaultValue,
        schema.settingType,
      );

      // Update the setting with default value
      const updatedSettings = {
        ...settings.settings,
        [dbKey]: defaultValue,
      };

      await this.settingsRepository.save({
        ...settings,
        settings: updatedSettings,
      });

      const refreshedSettings = await this.settingsRepository.findByCondition({
        where: { id: settings.id },
        relations: ['platformTeam'],
      });

      return this.formatSettingsResponse(refreshedSettings);
    } catch (error) {
      if (error instanceof QueryFailedError) {
        throw new BadRequestException('Invalid setting key');
      }
      throw error;
    }
  }

  /**
   * Reset settings to empty state
   */
  async resetSettings(teamId: string, botCtx: BotCtx) {
    const settings = await this.settingsRepository.findByCondition({
      where: {
        platformTeam: { uid: teamId },
        installation: { id: botCtx.installation.id },
        organization: { id: botCtx.organization.id },
      },
      relations: ['platformTeam'],
    });

    if (!settings) {
      throw new NotFoundException('Settings not found for team');
    }

    const updatedSettings = await this.settingsRepository.save({
      ...settings,
      settings: {},
    });

    return this.formatSettingsResponse(updatedSettings);
  }
}
