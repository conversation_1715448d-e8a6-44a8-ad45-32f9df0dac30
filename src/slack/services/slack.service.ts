import { Inject, Injectable } from '@nestjs/common';
import { InjectRepository } from '@nestjs/typeorm';
import { App, ExpressReceiver, LogLevel } from '@slack/bolt';
import { Response as ExpressResponse } from 'express';
import { groupBy } from 'lodash';
import { In, Repository } from 'typeorm';
import { BotCtx } from '../../auth/interfaces';
import { ConfigKeys, ConfigService } from '../../config/config.service';
import { TransactionService } from '../../database/common';
import { Organizations } from '../../database/entities';
import { ChannelsRepository } from '../../database/entities/channels/repositories';
import { InstallationRepository } from '../../database/entities/installations/repositories';
import { CUSTOM_LOGGER_TOKEN, ILogger } from '../../utils';
import { InstallForOrganizationParams } from '../interfaces';
import { SLACK_SCOPES } from '../metadata';
import { TypeORMInstallationStore } from '../stores/installation.store';

@Injectable()
export class SlackService {
  private bolt: App;
  private receiver: ExpressReceiver;

  constructor(
    private readonly configService: ConfigService,
    private readonly transactionService: TransactionService,
    private readonly installationStore: TypeORMInstallationStore,

    @Inject(CUSTOM_LOGGER_TOKEN) private readonly logger: ILogger,

    @InjectRepository(Organizations)
    private readonly organizationRepository: Repository<Organizations>,
    private readonly channelsRepository: ChannelsRepository,
    private readonly installationRepository: InstallationRepository,
  ) {
    // Express receiver
    this.receiver = new ExpressReceiver({
      // Slack app config
      clientId: this.configService.get(ConfigKeys.SLACK_CLIENT_ID),
      stateSecret: this.configService.get(ConfigKeys.SLACK_STATE_SECRET),
      clientSecret: this.configService.get(ConfigKeys.SLACK_CLIENT_SECRET),
      signingSecret: this.configService.get(ConfigKeys.SLACK_SIGNING_SECRET),

      // Slack scopes
      scopes: SLACK_SCOPES,

      // Stores installation in the database
      installationStore: this.installationStore,

      // Installation options
      installerOptions: {
        directInstall: true,
        stateVerification: false,
        redirectUriPath: '/slack/oauth_redirect',
        callbackOptions: {
          success: (installation, _opts, _req, res) => {
            this.logger.log(
              `Installation success for ${installation.team.id}, ${installation.team.name}`,
            );

            return (res as ExpressResponse).redirect(
              `${this.configService.get(
                ConfigKeys.THENA_WEB_URL,
              )}/organization/settings/sources`,
            );
          },
          failure: () => {
            // TODO: Handle failure in installation
            console.log('failure');
          },
        },
        installPathOptions: {
          beforeRedirection: (req, _res, options) => {
            // Set metadata before redirecting
            options.metadata = JSON.stringify({
              orgId: (req as any).query.orgId,
              installingUser: (req as any).query.installingUser,
            });

            return Promise.resolve(true);
          },
        },
      },

      // Redirect URI
      redirectUri: `${this.configService.get(
        ConfigKeys.SLACK_APP_BASE_URL,
      )}/slack/oauth_redirect`,
    });

    // Bolt app
    this.bolt = new App({
      receiver: this.receiver,
      logLevel: LogLevel.ERROR,

      // ---- Developer options
      developerMode: true,
      socketMode: false,
    });
  }

  /**
   * Get the Bolt app
   * @returns The Bolt app
   */
  public use() {
    return this.receiver.app;
  }

  /**
   * Get the workspaces with the number of channels installed
   * @param botCtx The bot context
   * @returns The workspaces
   */
  async getWorkspaces(botCtx: BotCtx, salt?: string) {
    const { installations } = botCtx;

    let shouldReturnBotToken = false;
    if (salt === this.configService.get(ConfigKeys.PLATFORM_SALT)) {
      shouldReturnBotToken = true;
    }

    // Get all channels for the installations
    const channels = await this.channelsRepository.findAll({
      where: { installation: In(installations.map((i) => i.id)) },
      relations: { installation: true },
      select: {
        id: true,
        installation: {
          id: true,
          teamId: true,
          teamName: true,
          installingUserId: true,
          createdAt: true,
          botToken: shouldReturnBotToken,
          status: true,
        },
      },
    });

    // Group channels by installation
    const channelsByInstallation = groupBy(channels, 'installation.teamId');

    // Map the installations to the workspaces
    return installations.map((installation) => {
      const returnPayload: Record<string, any> = {
        id: installation.id,
        name: installation.teamName,
        teamId: installation.teamId,
        teamInfo: installation.teamInfo,
        status: installation.status,
        installedBy: installation.installingUserId,
        installedAt: new Date(installation.createdAt).toISOString(),
        channelsCount:
          (channelsByInstallation[installation.teamId] || [])?.length ?? 0,
      };

      // If the salt matches, return the bot token
      if (shouldReturnBotToken) {
        returnPayload.botToken = installation.botToken;
      }

      return returnPayload;
    });
  }

  /**
   * Disconnect a workspace
   * @param installationId The installation id
   * @param botCtx The bot context
   */
  async disconnectWorkspace(botCtx: BotCtx) {
    const { installation } = botCtx;

    // If the installation is not found, throw an error
    if (!installation) {
      throw new Error('Installation not found');
    }

    // Disconnect the workspace
    await this.transactionService.runInTransaction(async (txnContext) => {
      await this.installationRepository.updateWithTxn(
        txnContext,
        { id: installation.id },
        {
          disconnected: true,
          disconnectedOn: new Date(),
        },
      );
    });
  }

  /**
   * Installs the Slack app for an organization.
   * @param data The installation data.
   */
  async installForOrganization(data: InstallForOrganizationParams) {
    // Fetch the organization
    let organization: Organizations;
    organization = await this.organizationRepository.findOne({
      where: { uid: data.orgId },
    });

    // If the organization does not exist, create it
    if (!organization) {
      organization = await this.organizationRepository.save({
        uid: data.orgId,
        installingUserId: data.userId,
      });
    } else {
      // If the organization exists, update the installing user id
      await this.organizationRepository.update(organization.id, {
        installingUserId: data.userId,
      });
    }

    return organization;
  }

  getBolt() {
    return this.bolt;
  }
}
