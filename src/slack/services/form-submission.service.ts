import { BadRequestException, Inject, Injectable } from '@nestjs/common';
import { validateSync } from 'class-validator';
import { CUSTOM_LOGGER_TOKEN, ILogger } from '../../utils';
import { Field } from '../blocks/components/composite/form-builder/conditional-form-builder.composite';
import { CustomFieldType } from '../constants/custom-field-types.enum';
import { ViewSubmissionPayload } from '../interfaces/slack-view-submission.interface';
import {
  DateValidation,
  DecimalValidation,
  IpAddressValidation,
  UrlValidation,
  isValidCoordinates,
  isValidDate,
  isValidDateTime,
  isValidEmail,
  isValidPassword,
  isValidPhoneNumber,
  isValidRegex,
  isValidString,
  isValidTime,
  isValidUrl,
} from '../utils/field-validation.utils';

export interface FormFieldSubmission {
  field: {
    id: string;
    name: string;
    type: string;
    mandatoryOnCreation: boolean;
    metadata?: Field['metadata'];
  };
  value: string | number | boolean | string[] | null;
  rawValue: any; // Original value from Slack
}

@Injectable()
export class FormSubmissionService {
  constructor(@Inject(CUSTOM_LOGGER_TOKEN) private readonly logger: ILogger) {}

  /**
   * Process form submission and extract values
   */
  processSubmission(
    payload: ViewSubmissionPayload,
    formFields: Field[],
  ): FormFieldSubmission[] {
    try {
      const stateValues = payload.view.state.values;
      const submissions: FormFieldSubmission[] = [];

      const fieldsMap = new Map(formFields.map((field) => [field.id, field]));

      for (const [blockId, block] of Object.entries(stateValues)) {
        const fieldId = blockId.replace('block_', '');
        const field = fieldsMap.get(fieldId);

        if (!field) {
          this.logger.warn(`Field not found for block ID: ${blockId}`);
          continue;
        }

        const [, action] = Object.entries(block)[0];
        const processedValue = this.processFieldValue(field, action as any); // Cast action to any for new properties

        if (processedValue !== undefined) {
          // Undefined means skip (e.g. if field was part of a combined datetime)
          submissions.push({
            field: {
              id: field.id,
              name: field.name,
              type: field.type,
              mandatoryOnCreation: field.mandatoryOnCreation,
              metadata: field.metadata,
            },
            value: processedValue,
            rawValue: action,
          });
        }
      }
      return submissions;
    } catch (error) {
      if (error instanceof Error) {
        this.logger.error('Error processing form submission:', error.message);
      } else {
        this.logger.error('Error processing form submission:', String(error)); // Cast to string
      }
      throw new Error('Failed to process form submission'); // Or rethrow original error
    }
  }

  /**
   * Process field value based on type
   */
  private processFieldValue(
    field: Field,
    action: {
      type: string;
      value?: string;
      selected_date?: string;
      selected_time?: string;
      selected_datetime?: number;
      selected_date_time?: number;
      selected_option?: { value: string };
      selected_options?: Array<{ value: string }>;
    },
  ): string | number | boolean | string[] | null {
    try {
      if (!action || !action.type) {
        return null;
      }

      const originalType =
        field.metadata?.originalType?.toLowerCase() || field.type.toLowerCase();

      switch (originalType) {
        case 'text':
        case 'textarea':
        case 'string':
        case 'rich_text': {
          return action.value || null;
        }
        case 'email': {
          return action.value || null;
        }
        case 'phone_number': {
          return action.value || null;
        }
        case 'url': {
          return action.value || null;
        }
        case 'choice':
        case 'select':
        case 'dropdown':
        case 'single_choice': {
          if (
            action.type === 'static_select' ||
            action.type === 'external_select'
          ) {
            return action.selected_option?.value || null;
          }
          return null;
        }
        case 'radio':
        case 'radio_button': {
          if (action.type === 'radio_buttons') {
            return action.selected_option?.value || null;
          }
          return null;
        }
        case 'multiselect':
        case 'multi_choice': {
          if (
            action.type === 'multi_static_select' ||
            action.type === 'multi_external_select'
          ) {
            return action.selected_options?.map((opt) => opt.value) || [];
          }
          return [];
        }
        case 'number':
        case 'integer': {
          if (
            action.value === undefined ||
            action.value === null ||
            action.value === ''
          ) {
            // More robust check for empty
            return null;
          }
          const num = Number.parseInt(action.value);
          return Number.isNaN(num) ? null : num;
        }
        case 'decimal':
        case 'currency': {
          if (
            action.value === undefined ||
            action.value === null ||
            action.value === ''
          ) {
            return null;
          }
          const num = Number.parseFloat(action.value);
          return Number.isNaN(num) ? null : num;
        }
        case 'date': {
          return action.selected_date || null;
        }
        case 'time': {
          // For time fields, return the selected_time directly from the timepicker
          // This ensures we're getting the HH:MM format that Slack provides
          if (action.selected_time) {
            this.logger.debug(
              `Processing time field with value: ${action.selected_time}`,
            );
            return action.selected_time;
          }
          return null;
        }
        case 'date_time':
        case 'datetime': {
          return this.handleDateTimeField(field, action);
        }
        case 'boolean':
        case 'checkbox':
        case 'toggle': {
          if (action.type === 'checkboxes') {
            return action.selected_options?.length > 0;
          }
          return false;
        }
        case 'file_upload': {
          return action.value || null;
        }
        case 'lookup': {
          return action.value || null;
        }
        case 'coordinates': {
          return action.value || null;
        }
        default: {
          this.logger.warn(
            `Unknown field type: ${originalType} for field ${field.type}`,
          ); // Use originalType
          return action.value || null;
        }
      }
    } catch (error) {
      if (error instanceof Error) {
        this.logger.error(`Error processing field ${field.id}:`, error.message);
      } else {
        this.logger.error(`Error processing field ${field.id}:`, String(error)); // Cast to string
      }
      return null;
    }
  }

  /**
   * Process date time field based on the action type
   */
  private handleDateTimeField(
    field: Field,
    action: {
      type: string;
      selected_date?: string;
      selected_time?: string;
      selected_datetime?: number;
      selected_date_time?: number;
    },
  ): string | null {
    this.logger.debug(
      `handleDateTimeField called for field ${field.id} with action type: ${action.type}`,
    );
    this.logger.debug(
      `Action data: ${JSON.stringify({
        selected_date: action.selected_date,
        selected_time: action.selected_time,
        selected_datetime: action.selected_datetime,
        selected_date_time: action.selected_date_time,
      })}`,
    );

    if (action.type === 'datetimepicker') {
      const timestamp = action.selected_date_time || action.selected_datetime;
      this.logger.debug(
        `Processing datetimepicker with timestamp: ${timestamp}`,
      );

      if (timestamp === undefined || timestamp === null) {
        this.logger.debug(
          `No timestamp provided for datetimepicker field ${field.id}`,
        );
        return '';
      }
      try {
        // Convert Unix timestamp to UTC date
        const date = new Date(timestamp * 1000);
        const isoString = date.toISOString().split('.')[0];
        this.logger.debug(
          `Converted timestamp ${timestamp} to UTC: ${isoString} for field ${field.id}`,
        );
        this.logger.debug(
          `UTC Date details: ISO=${date.toISOString()}, UTC String=${date.toUTCString()}`,
        );
        return isoString;
      } catch (error) {
        this.logger.error(
          `Error converting timestamp for field ${field.id}:`,
          error instanceof Error ? error.message : String(error),
        );
        return '';
      }
    }

    const date = action.selected_date || null;
    const time = action.selected_time || null;

    this.logger.debug(
      `Processing date: ${date}, time: ${time} for field ${field.id}`,
    );

    let result = '';

    try {
      if (date && time) {
        // Create date in UTC by explicitly setting the time in UTC
        this.logger.debug(
          `Combining date ${date} and time ${time} with UTC timezone`,
        );
        const dateTimeString = `${date}T${time}:00Z`;
        this.logger.debug(`Combined datetime string: ${dateTimeString}`);

        const dateObj = new Date(dateTimeString);
        result = dateObj.toISOString().split('.')[0];

        this.logger.debug(
          `Combined date and time in UTC: ${result}, UTC String: ${dateObj.toUTCString()}`,
        );
      } else if (date) {
        // Set time to midnight UTC
        this.logger.debug(`Using date ${date} with midnight UTC time`);
        const dateTimeString = `${date}T00:00:00Z`;
        const dateObj = new Date(dateTimeString);
        result = dateObj.toISOString().split('.')[0];

        this.logger.debug(
          `Date with midnight UTC: ${result}, UTC String: ${dateObj.toUTCString()}`,
        );
      } else if (time) {
        // Use today's date with the specified time in UTC
        const today = new Date().toISOString().split('T')[0];
        this.logger.debug(
          `Using today's date ${today} with time ${time} in UTC`,
        );

        const dateTimeString = `${today}T${time}:00Z`;
        const dateObj = new Date(dateTimeString);
        result = dateObj.toISOString().split('.')[0];

        this.logger.debug(
          `Today with specified time in UTC: ${result}, UTC String: ${dateObj.toUTCString()}`,
        );
      }
    } catch (error) {
      this.logger.error(
        `Error processing date/time for field ${field.id}:`,
        error instanceof Error ? error.message : String(error),
      );
      return null;
    }

    if (result) {
      this.logger.debug(`Final UTC datetime: ${result} for field ${field.id}`);
    } else {
      this.logger.debug(`No datetime result produced for field ${field.id}`);
    }

    return result || null;
  }

  /**
   * Validate submission values against field requirements
   */
  validateSubmission(submissions: FormFieldSubmission[]): string[] {
    const errors: string[] = [];

    for (const submission of submissions) {
      const { field, value } = submission;
      const originalType =
        field.metadata?.originalType?.toLowerCase() || field.type.toLowerCase();

      if (
        field.mandatoryOnCreation &&
        (value === null ||
          value === '' ||
          (Array.isArray(value) && value.length === 0))
      ) {
        errors.push('This field is required');
        continue;
      }

      // Skip validation for non-mandatory empty fields
      if (
        !field.mandatoryOnCreation &&
        (value === null ||
          value === '' ||
          (Array.isArray(value) && value.length === 0))
      ) {
        continue;
      }

      switch (originalType) {
        case 'number':
        case 'integer': {
          if (value !== null && typeof value !== 'number') {
            errors.push(`${field.name} must be a number`);
          }
          break;
        }
        case 'decimal':
        case 'currency': {
          if (value !== null && typeof value !== 'number') {
            errors.push(`${field.name} must be a decimal number`);
          }
          break;
        }
        case 'email': {
          if (value !== null && !isValidEmail(value as string)) {
            errors.push('Please enter a valid email address');
          }
          break;
        }
        case 'phone_number': {
          if (value !== null && !isValidPhoneNumber(value as string)) {
            errors.push(`${field.name} must be a valid phone number`);
          }
          break;
        }
        case 'url': {
          if (value !== null && !isValidUrl(value as string)) {
            errors.push(`${field.name} must be a valid URL`);
          }
          break;
        }
        case 'multiselect':
        case 'multi_choice': {
          if (value !== null && !Array.isArray(value)) {
            errors.push(`${field.name} must be a list of values`);
          }
          break;
        }
        case 'boolean':
        case 'checkbox':
        case 'toggle': {
          if (value !== null && typeof value !== 'boolean') {
            errors.push(`${field.name} must be a boolean value`);
          }
          break;
        }
        case 'date': {
          if (value !== null && !isValidDate(value as string)) {
            errors.push(`${field.name} must be a valid date`);
          }
          break;
        }
        case 'date_time':
        case 'datetime': {
          if (
            value !== null &&
            value !== '' &&
            !isValidDateTime(value as string)
          ) {
            errors.push(`${field.name} must be a valid date and time`);
          }
          break;
        }
        case 'time': {
          // Handle both string time values and Slack timepicker objects
          if (
            value !== null &&
            !isValidTime(value, { allowSlackFormat: true })
          ) {
            errors.push(`${field.name} must be a valid time (HH:MM)`);
          }
          break;
        }
        case 'file_upload': {
          // Placeholder, actual file validation is complex and often server-side after upload
          break;
        }
        case 'coordinates': {
          if (value !== null && !isValidCoordinates(value as string)) {
            errors.push(`${field.name} must be valid coordinates`);
          }
          break;
        }
      }
    }
    return errors;
  }

  /**
   * Convert submissions to a simple key-value object with field name as key
   */
  toKeyValueName(submissions: FormFieldSubmission[]): Record<string, any> {
    return submissions.reduce(
      (acc, { field, value }) => {
        acc[field.name] = value;
        return acc;
      },
      {} as Record<string, any>,
    );
  }

  /**
   * Convert submissions to a simple key-value object with field ID as key
   */
  toKeyValueById(submissions: FormFieldSubmission[]): Record<string, any> {
    return submissions.reduce(
      (acc, { field, value }) => {
        acc[field.id] = value;
        return acc;
      },
      {} as Record<string, any>,
    );
  }

  /**
   * Validates a field value against its type and constraints
   */
  validateFieldValue(
    field: Field,
    values: any, // Expects an array of values as per Final code logic
    isTicketClosing?: boolean,
    needsSingleValue?: boolean, // Default behavior: most fields are single value unless specified
  ): void {
    const fieldType = (field.metadata?.originalType?.toUpperCase() ||
      field.type.toUpperCase()) as CustomFieldType;

    // Mandatory checks
    const isValueEmpty = (v: any) =>
      v === undefined ||
      v === null ||
      v === '' ||
      (Array.isArray(v) && v.length === 0);

    // Check if the first value (or the value itself if not an array) is empty
    const effectiveValue = Array.isArray(values) ? values[0] : values;

    if (
      isTicketClosing &&
      (field as any).mandatoryOnClose === true &&
      isValueEmpty(effectiveValue)
    ) {
      throw new BadRequestException(
        `Field ${field.name || field.id} cannot be empty when closing.`,
      );
    }
    if (
      !isTicketClosing &&
      field.mandatoryOnCreation === true &&
      isValueEmpty(effectiveValue)
    ) {
      throw new BadRequestException(
        `Field ${field.name || field.id} cannot be empty on creation.`,
      );
    }

    if (values === undefined || values === null) {
      return; // If not mandatory and no value, nothing to validate
    }

    // Ensure `values` is an array for consistent processing, even if it's a single value from a non-array source.
    const valuesArray = Array.isArray(values) ? values : [values];

    // If the original value was truly empty and not mandatory, skip further type validation.
    if (
      isValueEmpty(effectiveValue) &&
      !(
        (isTicketClosing && (field as any).mandatoryOnClose) ||
        (!isTicketClosing && field.mandatoryOnCreation)
      )
    ) {
      return;
    }

    const singleValueCheck = (vals: any[]) => {
      if (vals.length > 1) {
        throw new BadRequestException(
          `Field ${field.name || field.id} expects a single value, but received multiple.`,
        );
      }
    };

    // Default to single value for most types unless it's a known multi-value type
    const isImplicitlySingleValue = ![
      CustomFieldType.MULTI_CHOICE,
      CustomFieldType.CHECKBOX,
      // CustomFieldType.FILE_UPLOAD, // File upload can be multiple
    ].includes(fieldType);

    if (
      needsSingleValue === true ||
      (needsSingleValue === undefined && isImplicitlySingleValue)
    ) {
      singleValueCheck(valuesArray);
    }

    for (const value of valuesArray) {
      // Skip validation for genuinely empty, non-mandatory values
      if (
        isValueEmpty(value) &&
        !(
          (isTicketClosing && (field as any).mandatoryOnClose) ||
          (!isTicketClosing && field.mandatoryOnCreation)
        )
      ) {
        continue;
      }
      // For mandatory fields, an empty string might still need to pass certain validations if not caught by `isValueEmpty`
      // or if specific type (e.g. Date) expects non-empty string.

      switch (fieldType) {
        case CustomFieldType.SINGLE_LINE:
        case CustomFieldType.RICH_TEXT: // Final code treats RICH_TEXT like MULTI_LINE for string validation
        case CustomFieldType.MULTI_LINE: {
          if (!isValidString(value)) {
            throw new BadRequestException(`Field ${field.name} invalid string`);
          }
          break;
        }
        case CustomFieldType.EMAIL: {
          const validation = isValidEmail(value);
          if (!validation.isValid) {
            throw new BadRequestException(
              validation.errorMessage || 'Please enter a valid email address',
            );
          }
          break;
        }
        case CustomFieldType.SINGLE_CHOICE:
        case CustomFieldType.RADIO_BUTTON: {
          if (!field.options?.some((o) => o.value === value)) {
            throw new BadRequestException(`Field ${field.name} invalid option`);
          }
          break;
        }
        case CustomFieldType.MULTI_CHOICE:
        case CustomFieldType.CHECKBOX: {
          // This loop iterates through selected values for MULTI_CHOICE.
          // For CHECKBOX, if 'value' here is a boolean, this check is fine.
          // If 'value' is an array of selected checkbox values, then the outer loop handles it.
          if (!field.options?.some((o) => o.value === value)) {
            throw new BadRequestException(
              `Field ${field.name} has invalid option: ${value}`,
            );
          }
          break;
        }
        case CustomFieldType.URL: {
          const urlValidation = new UrlValidation();
          urlValidation.url = value;
          if (validateSync(urlValidation).length > 0) {
            throw new BadRequestException(`Field ${field.name} invalid URL`);
          }
          break;
        }
        case CustomFieldType.INTEGER: {
          if (
            Number.isNaN(Number.parseInt(value, 10)) ||
            Number.parseInt(value, 10).toString() !== String(value)
          ) {
            throw new BadRequestException(
              `Field ${field.name} invalid integer`,
            );
          }
          break;
        }
        case CustomFieldType.DATE: // Date and DateTime use same validation class in Final
        case CustomFieldType.DATE_TIME: {
          const dateValidation = new DateValidation();
          dateValidation.date = value;
          if (
            value === '' &&
            !(
              (isTicketClosing && (field as any).mandatoryOnClose) ||
              (!isTicketClosing && field.mandatoryOnCreation)
            )
          ) {
            // Empty is valid if not mandatory
          } else if (validateSync(dateValidation).length > 0) {
            this.logger.error(
              `DATE_TIME validation errors for ${field.name} (value: '${value}'): ${JSON.stringify(validateSync(dateValidation))}`,
            );
            throw new BadRequestException(
              `Field ${field.name} must be a valid date/datetime`,
            );
          }
          break;
        }
        case CustomFieldType.CURRENCY:
        case CustomFieldType.DECIMAL: {
          const decimalValidation = new DecimalValidation();
          decimalValidation.decimal = value;
          if (validateSync(decimalValidation).length > 0) {
            throw new BadRequestException(
              `Field ${field.name} invalid decimal`,
            );
          }
          break;
        }
        case CustomFieldType.PASSWORD: {
          if (!isValidPassword(value)) {
            throw new BadRequestException(
              `Field ${field.name} invalid password`,
            );
          }
          break;
        }
        case CustomFieldType.PHONE_NUMBER: {
          if (!isValidPhoneNumber(value)) {
            throw new BadRequestException(`Field ${field.name} invalid phone`);
          }
          break;
        }
        case CustomFieldType.REGEX: {
          if (
            field.metadata &&
            'regexPattern' in field.metadata &&
            !new RegExp(field.metadata.regexPattern as string).test(value)
          ) {
            throw new BadRequestException(
              `Field ${field.name} does not match pattern`,
            );
          }

          if (
            !isValidRegex(value) &&
            !(field.metadata && 'regexPattern' in field.metadata)
          ) {
            // Generic regex check if no pattern
            throw new BadRequestException(
              `Field ${field.name} invalid regex string`,
            );
          }
          break;
        }
        case CustomFieldType.IP_ADDRESS: {
          const ipAddressValidation = new IpAddressValidation();
          ipAddressValidation.ipAddress = value;
          if (validateSync(ipAddressValidation).length > 0) {
            throw new BadRequestException(`Field ${field.name} invalid IP`);
          }
          break;
        }
        case CustomFieldType.TOGGLE: // Final code maps TOGGLE to BOOLEAN
        case CustomFieldType.BOOLEAN: {
          if (
            value !== true &&
            value !== false &&
            String(value) !== 'true' &&
            String(value) !== 'false'
          ) {
            throw new BadRequestException(
              `Field ${field.name} invalid boolean (true/false)`,
            );
          }
          break;
        }
        // LOOKUP, FILE_UPLOAD, etc. have minimal client-side validation in Final code example
        default:
          this.logger.warn(
            `No specific validation for ${fieldType} field ${field.name}`,
          );
      }
    }
  }
  // --- End: New methods from Final Code ---
}
