import { BullModule } from '@nestjs/bullmq';
import { Module } from '@nestjs/common';
import { DiscoveryService } from '@nestjs/core';
import { TypeOrmModule } from '@nestjs/typeorm';
import { Queue } from 'bullmq';
import { AiModule } from '../ai/ai.module';
import { CommonModule } from '../common/common.module';
import { ConfigModule } from '../config/config.module';
import { ConfigKeys, ConfigService } from '../config/config.service';
import { TransactionService } from '../database/common';
import {
  Bots,
  CommentConversationMappings,
  GroupedSlackMessages,
  Prompts,
  SettingsSchemas,
  SlackAuditLog,
  SlackEmojis,
} from '../database/entities';
import { BotsRepository } from '../database/entities/bots/repositories';
import { Channels } from '../database/entities/channels/channels.entity';
import { ChannelsRepository } from '../database/entities/channels/repositories/channels.repository';
import { CustomerContacts } from '../database/entities/customer-contacts/customer-contacts.entity';
import { SlackEmojisRepository } from '../database/entities/emojis/repository/slack-emojis.repository';
import { Installations } from '../database/entities/installations/installations.entity';
import { InstallationRepository } from '../database/entities/installations/repositories';
import {
  ChannelTriageMappings,
  CommentThreadMappings,
  PlatformTeamsToChannelMappings,
} from '../database/entities/mappings';
import { CommentConversationMapsRepository } from '../database/entities/mappings/repositories/comment-conversation-maps.repository';
import { CommentThreadMapsRepository } from '../database/entities/mappings/repositories/comment-thread-maps.repository';
import { SubGroupsMapsRepository } from '../database/entities/mappings/repositories/sub-groups-maps.repository';
import { TeamChannelMapsRepository } from '../database/entities/mappings/repositories/team-channel-maps.repository';
import { TeamTriageRuleMappingRepository } from '../database/entities/mappings/repositories/teams-triage-mappings.repository';
import { TriageMapsRepository } from '../database/entities/mappings/repositories/triage-maps.repository';
import { SubTeamToSubGroupsMapping } from '../database/entities/mappings/sub-team-to-sub-groups-mappings.entity';
import { TeamTriageRuleMapping } from '../database/entities/mappings/teams-triage-mappings.entity';
import { Organizations } from '../database/entities/organizations/organizations.entity';
import { PromptsRepository } from '../database/entities/prompts/repositories/prompts.repository';
import { SettingsSchemasRepository } from '../database/entities/settings/repositories/setting-schemas.repository';
import { SettingsRepository } from '../database/entities/settings/repositories/settings.repository';
import { Settings } from '../database/entities/settings/settings.entity';
import { SlackAuditLogRepository } from '../database/entities/slack-audit-logs/repositories/slack-audit-log.repository';
import { GroupedSlackMessagesRepository } from '../database/entities/slack-messages/repositories/grouped-slack-messages.repository';
import { SlackMessagesRepository } from '../database/entities/slack-messages/repositories/slack-messages.repository';
import { SlackTriageMessagesRepository } from '../database/entities/slack-messages/repositories/slack-triage-messages.repository';
import { SlackMessages } from '../database/entities/slack-messages/slack-messages.entity';
import { SlackTriageMessages } from '../database/entities/slack-messages/slack-triage-messages.entity';
import { SlackSubgroupsRepository } from '../database/entities/subgroups/repositories/subgroups.repository';
import { SlackSubgroups } from '../database/entities/subgroups/subgroups.entity';
import { PlatformTeams, TeamsRepository } from '../database/entities/teams';
import { UsersRepository } from '../database/entities/users/repositories/users.repository';
import { Users } from '../database/entities/users/users.entity';
import { ExternalModule } from '../external/external.module';
import { AnnotatorApiProvider } from '../external/provider/annotator-api.provider';
import { ThenaAppsPlatformApiProvider } from '../external/provider/thena-apps-platform-api.provider';
import { BaseSlackBlocksToHtml } from '../utils/parsers/slack/slack-blocks-to-html/base-slack-to-html.parser';
import { SlackToHTMLRichTextParser } from '../utils/parsers/slack/slack-blocks-to-html/slack-to-html-rich-text.parser';
import { slackBlocks } from './blocks';
import { FormSelectorComposite } from './blocks/components/composite/form-builder';
import { ConditionalFormBuilderComposite } from './blocks/components/composite/form-builder';
import { slackCommands } from './commands';
import {
  SLACK_QUEUE_NAME,
  SLACK_WORKSPACE_SYNC_QUEUE,
} from './constants/slack-queue.constants';
import { SlackActivitiesController } from './controllers/activities.controller';
import { AuthorizationController } from './controllers/authorization.controller';
import { SlackChannelController } from './controllers/configurations.controller';
import { FormBuilderController } from './controllers/form-builder.controller';
import { PromptsController } from './controllers/prompts.controller';
import { SettingsController } from './controllers/settings.controller';
import { SlackInteractionsController } from './controllers/slack-interactions.controller';
import { SlackSyncController } from './controllers/slack-sync.controller';
import { SlackController } from './controllers/slack.controller';
import { SlackSubGroupsController } from './controllers/sub-groups.controller';
import { TeamsController } from './controllers/teams.controller';
import { TriageRulesController } from './controllers/triage-rules.controller';
import {
  ChannelsManagementService,
  SettingsCore,
  SlackAppManagementService,
  coreHandlers,
} from './core';
import { ConversationGroupingService } from './core/conversation-grouping';
import { ForMessageFactory } from './core/factories/for-message.factory';
import { CoreTriageService } from './core/messages';
import { SlackMessageCore } from './core/messages/slack-message.core';
import { OnMessageHandler } from './core/on-message';
import { slackEventHandlers } from './event-handlers';
import { slackChannelEventHandlers } from './event-handlers/handlers/channel';
import { slackLinkEventHandlers } from './event-handlers/handlers/links';
import { slackMemberEventHandlers } from './event-handlers/handlers/members';
import { SlackSubTypeHandler } from './event-handlers/handlers/message/subtypes';
import { MessageChangedHandler } from './event-handlers/handlers/message/subtypes/message-changed.handler';
import { MessageDeletedHandler } from './event-handlers/handlers/message/subtypes/message-deleted.handler';
import { slackReactionEventHandlers } from './event-handlers/handlers/reactions';
import { slackSubTeamEventHandlers } from './event-handlers/handlers/subteams';
import {
  slackActionHandlers,
  slackOptionsHandlers,
  slackViewHandlers,
} from './handlers';
import { slackActions } from './handlers/slack-actions';
import { FormFieldActionHandler } from './handlers/slack-actions/form-handlers/form-field-action.handler';
import { FormSubmissionHandler } from './handlers/slack-actions/form-handlers/form-submission.handler';
import {
  SlackAccountsSyncJob,
  SlackChannelsSyncJob,
  SlackEmojiSyncJob,
  SlackExternalUsersSyncJob,
  SlackSubgroupsSyncJob,
  SlackUsersSyncJob,
} from './processors/jobs';
import { WorkspaceSyncProcessor } from './processors/workspace-sync.processor';
import { SlackWebAPIService } from './providers/slack-apis/slack-apis.service';
import { SlackRateLimiterService } from './providers/slack-apis/slack-rate-limiter.service';
import { SlackActionRegistry } from './registry';
import { AiTicketGeneratorService } from './services/ai-ticket-generator.service';
import { AuthorizationService } from './services/authorization.service';
import { ChannelSetupService } from './services/channel-setup.service';
import { FormBuilderService } from './services/form-builder.service';
import { FormSubmissionService } from './services/form-submission.service';
import { NotificationService } from './services/notification.service';
import { PromptsService } from './services/prompts.service';
import { SettingsService } from './services/settings.service';
import { SlackActionDiscoveryService } from './services/slack-action-discovery.service';
import { SlackChannelService } from './services/slack-channel.service';

import { SlackSubGroupsService } from './services/slack-sub-groups.service';
import { SlackService } from './services/slack.service';
import { SlackTeamsService } from './services/teams.service';
import { TriageEvaluationService } from './services/triage-evaluation.service';
import { TriageFieldsService } from './services/triage-fields.service';
import { TriageRuleEvaluatorService } from './services/triage-rule-evaluator.service';
import { TriageRulesService } from './services/triage-rules.service';
import { slackProviders } from './slack-common.providers';
import { TypeORMInstallationStore } from './stores/installation.store';

@Module({
  imports: [
    CommonModule,
    AiModule,
    ConfigModule,

    // BullMQ for Queue Management
    BullModule.registerQueueAsync({
      name: SLACK_QUEUE_NAME,
      imports: [ConfigModule],
      inject: [ConfigService],
      useFactory: (configService: ConfigService) => ({
        redis: {
          host: configService.get(ConfigKeys.REDIS_HOST),
          port: Number.parseInt(configService.get(ConfigKeys.REDIS_PORT)),
          username: configService.get(ConfigKeys.REDIS_USERNAME),
          password: configService.get(ConfigKeys.REDIS_PASSWORD),
        },
        defaultJobOptions: {
          removeOnComplete: {
            age: 24 * 3600,
            count: 1000,
          },
          removeOnFail: { age: 24 * 3600 },
        },
      }),
    }),

    // Register the workspace sync queue
    BullModule.registerQueueAsync({
      name: SLACK_WORKSPACE_SYNC_QUEUE,
      imports: [ConfigModule],
      inject: [ConfigService],
      useFactory: (configService: ConfigService) => ({
        redis: {
          host: configService.get(ConfigKeys.REDIS_HOST),
          port: Number.parseInt(configService.get(ConfigKeys.REDIS_PORT)),
          username: configService.get(ConfigKeys.REDIS_USERNAME),
          password: configService.get(ConfigKeys.REDIS_PASSWORD),
        },
        defaultJobOptions: {
          removeOnComplete: {
            age: 24 * 3600,
            count: 1000,
          },
          removeOnFail: { age: 24 * 3600 },
        },
      }),
    }),

    // Register the typeorm module
    TypeOrmModule.forFeature([
      Users,
      UsersRepository,

      // Channels
      Channels,
      ChannelsRepository,

      // Slack Triage Messages
      SlackTriageMessages,
      SlackTriageMessagesRepository,

      // Triage Maps
      ChannelTriageMappings,
      TriageMapsRepository,
      TeamChannelMapsRepository,

      // Teams
      PlatformTeams,
      TeamsRepository,

      // Prompts
      Prompts,
      PromptsRepository,

      // Subgroups
      SlackSubgroups,
      SlackSubgroupsRepository,

      // Slack Messages
      SlackMessages,
      SlackMessagesRepository,

      // Settings
      Settings,
      SettingsRepository,

      // Slack Emojis
      SlackEmojis,
      SlackEmojisRepository,

      // Setting Schemas
      SettingsSchemas,
      SettingsSchemasRepository,

      // Triage Rules
      TeamTriageRuleMapping,
      TeamTriageRuleMappingRepository,

      // Installations
      Installations,
      InstallationRepository,

      // Subgroups Maps
      SubTeamToSubGroupsMapping,
      SubGroupsMapsRepository,

      // Slack Audit Logs
      SlackAuditLog,
      SlackAuditLogRepository,

      // Bots
      Bots,
      BotsRepository,

      // Slack Emojis
      SlackEmojis,
      SlackEmojisRepository,

      // Comment Threads
      CommentThreadMappings,
      CommentThreadMapsRepository,

      // Comment Conversations
      CommentConversationMappings,
      CommentConversationMapsRepository,

      // Grouped Slack Messages
      GroupedSlackMessages,
      GroupedSlackMessagesRepository,

      SlackMessages,
      Organizations,
      CustomerContacts,
      CommentThreadMappings,
      ChannelTriageMappings,
      PlatformTeamsToChannelMappings,
    ]),

    // Register the external module
    ExternalModule,
  ],
  providers: [
    PromptsRepository,
    DiscoveryService,
    TransactionService,
    TeamChannelMapsRepository,
    SlackService,
    InstallationRepository,
    SlackTeamsService,
    SettingsCore,
    TeamTriageRuleMappingRepository,
    GroupedSlackMessagesRepository,
    SlackMessagesRepository,
    SlackSubgroupsRepository,
    BotsRepository,
    CoreTriageService,
    SlackChannelService,
    SlackMessageCore,
    TriageRuleEvaluatorService,
    CommentConversationMapsRepository,
    SlackSubGroupsService,
    CommentThreadMapsRepository,
    ...slackProviders,

    // Add ChannelSetupService
    ChannelSetupService,

    // Slack Message Handlers
    SlackSubTypeHandler,
    MessageChangedHandler,
    MessageDeletedHandler,

    // Parsers
    SlackToHTMLRichTextParser,
    BaseSlackBlocksToHtml,

    // Repositories
    ChannelsRepository,
    SlackEmojisRepository,
    TriageMapsRepository,
    SlackTriageMessagesRepository,
    SlackEmojisRepository,
    TeamsRepository,
    SettingsRepository,
    SettingsSchemasRepository,
    SlackSubgroupsRepository,
    SubGroupsMapsRepository,
    UsersRepository,
    SlackAuditLogRepository,

    // Bolt installation store
    TypeORMInstallationStore,

    SlackActionDiscoveryService,
    SlackActionRegistry,

    // Utility Services
    ChannelsManagementService,
    SlackAppManagementService,

    // BullMQ Jobs
    SlackChannelsSyncJob,
    SlackUsersSyncJob,
    SlackExternalUsersSyncJob,
    SlackSubgroupsSyncJob,
    SlackAccountsSyncJob,
    SlackEmojiSyncJob,

    // BullMQ Processors
    WorkspaceSyncProcessor,

    // Evaluation Services
    TriageEvaluationService,

    // Factories
    ForMessageFactory,

    {
      provide: SlackRateLimiterService,
      useFactory: (queue: Queue) => {
        return new SlackRateLimiterService(queue, {
          requestsPerSecond: 1,
          maxConcurrent: 1,
        });
      },
      inject: [`BullQueue_${SLACK_QUEUE_NAME}`],
    },
    SlackWebAPIService,
    AuthorizationService,
    ThenaAppsPlatformApiProvider,
    SettingsService,
    TriageRulesService,
    PromptsService,

    ...coreHandlers,
    ...slackBlocks,
    ...slackEventHandlers,
    ...slackActionHandlers,
    ...slackActions,
    ...slackCommands,
    ...slackViewHandlers,
    ...slackOptionsHandlers,
    ...slackLinkEventHandlers,

    // Handlers for `channel_*` events
    ...slackChannelEventHandlers,

    // Handlers for `member_*` events
    ...slackMemberEventHandlers,

    // Handlers for `subteam_*` events
    ...slackSubTeamEventHandlers,

    // Handlers for `reaction_*` events
    ...slackReactionEventHandlers,

    ConversationGroupingService,

    AnnotatorApiProvider,
    TriageFieldsService,

    FormSelectorComposite,
    ConditionalFormBuilderComposite,
    FormBuilderService,

    FormFieldActionHandler,
    FormSubmissionHandler,

    FormSubmissionService,

    NotificationService,

    AiTicketGeneratorService,
  ],
  controllers: [
    SlackController,
    SettingsController,
    SlackSubGroupsController,
    TriageRulesController,
    TeamsController,
    SlackInteractionsController,
    SlackChannelController,
    SlackActivitiesController,
    PromptsController,
    AuthorizationController,
    FormBuilderController,
    SlackSyncController,
  ],
  exports: [
    SlackWebAPIService,
    CoreTriageService,
    OnMessageHandler,
    TriageEvaluationService,
    ConversationGroupingService,
    FormSelectorComposite,
    ConditionalFormBuilderComposite,
    FormBuilderService,
    FormSubmissionService,
    NotificationService,
  ],
})
export class SlackModule {}
