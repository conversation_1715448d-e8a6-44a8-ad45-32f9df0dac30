import { InjectQueue } from '@nestjs/bullmq';
import { Inject, Injectable } from '@nestjs/common';
import { InjectRepository } from '@nestjs/typeorm';
import {
  Installation,
  InstallationQuery,
  InstallationStore,
} from '@slack/bolt';
import { Queue } from 'bullmq';
import { Repository } from 'typeorm';
import { Installations, Organizations } from '../../database/entities';
import { CUSTOM_LOGGER_TOKEN, ILogger } from '../../utils/logger';
import { SLACK_WORKSPACE_SYNC_QUEUE } from '../constants/slack-queue.constants';
import { SlackWebAPIService } from '../providers/slack-apis/slack-apis.service';

@Injectable()
export class TypeORMInstallationStore implements InstallationStore {
  constructor(
    @Inject(CUSTOM_LOGGER_TOKEN) private readonly logger: ILogger,

    @InjectRepository(Installations)
    private readonly installationsRepository: Repository<Installations>,
    @InjectRepository(Organizations)
    private readonly organizationsRepository: Repository<Organizations>,

    // Slack API Service
    private readonly slackWebAPIService: SlackWebAPIService,

    @InjectQueue(SLACK_WORKSPACE_SYNC_QUEUE)
    private readonly workspaceSyncQueue: Queue,
  ) {}

  /**
   * Store the installation in the database
   * @param installation The installation to store
   */
  async storeInstallation(installation: Installation) {
    try {
      // Check if the installation is already stored
      const existingInstallation = await this.installationsRepository.findOne({
        where: { teamId: installation.team?.id },
        relations: ['organization'],
      });

      // Parse the installation metadata
      const parsedMetadata = JSON.parse(installation.metadata as string);

      // If the installation is already stored, we don't need to store it again
      if (existingInstallation) {
        if (existingInstallation.organization.uid !== parsedMetadata?.orgId) {
          this.logger.error(
            `Organization for installation ${installation.team?.id} does not match!`,
          );
          return;
        }

        // Schedule a job to sync the workspace
        await this.workspaceSyncQueue.add('workspace-installed', {
          installationId: existingInstallation.id,
        });

        const installingUserId = parsedMetadata?.installingUser;
        await this.installationsRepository.update(existingInstallation.id, {
          botToken: installation.bot.token,
          installingUserId,
        });

        console.warn('Installation already stored');
        return;
      }

      // Get the orgId from the installation metadata
      const orgId = parsedMetadata?.orgId;
      const installingUserId = parsedMetadata?.installingUser;
      if (!orgId) {
        throw new Error('No orgId found in installation metadata');
      }

      // Get the organization from the database
      const organization = await this.organizationsRepository.findOne({
        where: { uid: orgId },
      });

      // If the organization does not exist, throw an error
      if (!organization) {
        throw new Error('Organization not found');
      }

      const teamInfoResponse = await this.slackWebAPIService.getTeamInfo(
        installation.bot.token,
      );

      let teamInfo: Record<string, any> = installation.team;
      if (teamInfoResponse.ok) {
        teamInfo = teamInfoResponse.team;
      }

      // Store the installation in the database
      const installationData = {
        teamId: installation.team.id,
        installationDump: installation,
        teamName: installation.team.name,
        enterpriseId: installation.enterprise?.id || null,
        botToken: installation.bot.token,
        botSlackId: installation.bot.id,
        botSlackUserId: installation.bot?.userId,
        teamInfo,
        installingUserId,
        installingUserName: '',
        installingUserSlackId: installation.user.id,
        name: installation.team.name,
        organization,
      };

      // Save the installation to the database
      const savedInstallation =
        await this.installationsRepository.save(installationData);

      // Schedule a job to sync the workspace
      await this.workspaceSyncQueue.add('workspace-installed', {
        installationId: savedInstallation.id,
      });

      return;
    } catch (error) {
      if (error instanceof Error) {
        this.logger.error(
          `Error storing installation: ${error.message}`,
          error.stack,
        );
      } else {
        console.error('Failed to store installation', error);
      }

      return;
    }
  }

  /**
   * Fetch the installation from the database
   * @param query The query to fetch the installation
   * @returns The installation
   */
  async fetchInstallation(query: InstallationQuery<boolean>) {
    try {
      // Fetch the installation from the database
      const installation = await this.installationsRepository.findOne({
        where: { teamId: query.teamId },
        select: ['installationDump'],
      });

      // If the installation is not found, throw an error
      if (!installation) {
        throw new Error('No installation found');
      }

      // Return the installation
      return installation.installationDump;
    } catch (error) {
      if (error instanceof Error) {
        this.logger.error(
          `Error fetching installation: ${error.message}`,
          error.stack,
        );
      } else {
        console.error('Failed to fetch installation', error);
      }

      return;
    }
  }

  /**
   * Delete the installation from the database
   * @param query The query to delete the installation
   */
  async deleteInstallation(query: InstallationQuery<boolean>) {
    try {
      await this.installationsRepository.softDelete({ teamId: query.teamId });
    } catch (error) {
      if (error instanceof Error) {
        this.logger.error(
          `Error deleting installation: ${error.message}`,
          error.stack,
        );
      } else {
        console.error('Failed to delete installation', error);
      }

      return;
    }
  }
}