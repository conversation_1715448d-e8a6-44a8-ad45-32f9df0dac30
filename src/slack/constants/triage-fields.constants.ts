export enum TriageCategory {
  TICKET = 'ticket',
  ACCOUNT = 'account',
  CHANNEL = 'channel',
}

export const TRIAGE_FIELD_MAPPINGS = {
  [TriageCategory.TICKET]: {
    title: 'string',
    description: 'string',
    priority: 'string',
    status: 'string',
    requestorEmail: 'string',
    assignee: 'string',
    reporter: 'string',
    labels: 'array',
    type: 'string',
  },
  [TriageCategory.ACCOUNT]: {
    name: 'string',
    tier: 'string',
    region: 'string',
    status: 'string',
    accountManager: 'string',
    supportLevel: 'string',
    tags: 'array',
  },
  [TriageCategory.CHANNEL]: {
    name: 'string',
    purpose: 'string',
    topic: 'string',
    memberCount: 'number',
    isPrivate: 'boolean',
    isShared: 'boolean',
    tags: 'array',
  },
} as const;

export type TriageFieldType = typeof TRIAGE_FIELD_MAPPINGS;
