/**
 * @event channel_join
 * Triggered when a user joins a channel/conversation in slack.
 *
 * @see https://api.slack.com/events/message/channel_join
 */
export const CHANNEL_JOIN_SUBTYPE = 'channel_join';

/**
 * @event channel_leave
 * Triggered when a user leaves a channel/conversation in slack.
 *
 * @see https://api.slack.com/events/message/channel_leave
 */
export const CHANNEL_LEAVE_SUBTYPE = 'channel_leave';

/**
 * @event group_join
 * Triggered when a user joins a private group in slack.
 *
 * @see https://api.slack.com/events/message/group_join
 */
export const GROUP_JOIN_SUBTYPE = 'group_join';

/**
 * @event group_leave
 * Triggered when a user leaves a private group in slack.
 *
 * @see https://api.slack.com/events/message/group_leave
 */
export const GROUP_LEAVE_SUBTYPE = 'group_leave';
