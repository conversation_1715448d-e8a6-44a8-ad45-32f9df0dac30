import {
  Body,
  Controller,
  Delete,
  Get,
  HttpException,
  Inject,
  InternalServerErrorException,
  Param,
  Patch,
  Query,
  UseGuards,
} from '@nestjs/common';
import { GetBotCtx } from '../../auth/decorators';
import { AuthGuard } from '../../auth/guards';
import { BotCtx } from '../../auth/interfaces';
import { CUSTOM_LOGGER_TOKEN, ILogger } from '../../utils';
import {
  MapSubGroupToSubTeamDTO,
  UpdateSubGroupMappingDTO,
} from '../dtos/sub-groups.dto';
import { SearchQueryParams } from '../query-params';
import { SlackSubGroupsService } from '../services/slack-sub-groups.service';

@Controller('v1/slack/sub-groups')
@UseGuards(AuthGuard)
export class SlackSubGroupsController {
  constructor(
    @Inject(CUSTOM_LOGGER_TOKEN) private readonly logger: ILogger,

    // Services
    private readonly slackSubGroupsService: SlackSubGroupsService,
  ) {}

  @Get('/')
  async getAllSubGroupsForWorkspace(
    @Query() query: SearchQueryParams,
    @GetBotCtx() botCtx: BotCtx,
  ) {
    try {
      const subGroups =
        await this.slackSubGroupsService.getAllSubGroupsForWorkspace(
          query,
          botCtx,
        );

      return { data: subGroups, ok: true };
    } catch (error) {
      if (error instanceof HttpException) {
        throw error;
      }

      // Log the error
      if (error instanceof Error) {
        this.logger.error(
          `Error fetching slack sub groups: ${error.message}`,
          error.stack,
        );
      } else {
        console.error(error);
      }

      throw new InternalServerErrorException(
        'An error occurred while fetching slack sub groups.',
      );
    }
  }

  @Get('/mappings')
  async getAllSubGroupMappings(@GetBotCtx() botCtx: BotCtx) {
    try {
      const subGroups =
        await this.slackSubGroupsService.getAllSubGroupMappings(botCtx);

      return { data: subGroups, ok: true };
    } catch (error) {
      if (error instanceof HttpException) {
        throw error;
      }

      // Log the error
      if (error instanceof Error) {
        this.logger.error(
          `Error fetching slack sub groups: ${error.message}`,
          error.stack,
        );
      } else {
        console.error(error);
      }

      throw new InternalServerErrorException(
        'An error occurred while fetching slack sub groups.',
      );
    }
  }

  @Patch('/mappings/:id')
  async updateMapping(
    @GetBotCtx() botCtx: BotCtx,
    @Param('id') id: string,
    @Body() body: UpdateSubGroupMappingDTO,
  ) {
    try {
      const results = await this.slackSubGroupsService.updateMapping(
        id,
        body,
        botCtx,
      );

      return { data: results, ok: true };
    } catch (error) {
      if (error instanceof HttpException) {
        throw error;
      }

      // Log the error
      if (error instanceof Error) {
        this.logger.error(
          `Error creating slack sub group mapping: ${error.message}`,
          error.stack,
        );
      } else {
        console.error(error);
      }

      throw new InternalServerErrorException(
        'An error occurred while mapping slack sub groups.',
      );
    }
  }

  @Patch('/')
  async createMapping(
    @GetBotCtx() botCtx: BotCtx,
    @Body() body: MapSubGroupToSubTeamDTO,
  ) {
    try {
      const results = await this.slackSubGroupsService.createMapping(
        body,
        botCtx,
      );

      return { data: results, ok: true };
    } catch (error) {
      if (error instanceof HttpException) {
        throw error;
      }

      // Log the error
      if (error instanceof Error) {
        this.logger.error(
          `Error creating slack sub group mapping: ${error.message}`,
          error.stack,
        );
      } else {
        console.error(error);
      }

      throw new InternalServerErrorException(
        'An error occurred while mapping slack sub groups.',
      );
    }
  }

  @Get('/mapped-teams')
  async getAllMappedSubGroupsAndTeams(@GetBotCtx() botCtx: BotCtx) {
    try {
      const mappedData =
        await this.slackSubGroupsService.getAllMappedSubGroupsAndTeams(botCtx);

      return { data: mappedData, ok: true };
    } catch (error) {
      if (error instanceof HttpException) {
        throw error;
      }

      // Log the error
      if (error instanceof Error) {
        this.logger.error(
          `Error fetching mapped slack sub groups and platform teams: ${error.message}`,
          error.stack,
        );
      } else {
        console.error(error);
      }

      throw new InternalServerErrorException(
        'An error occurred while fetching mapped slack sub groups and platform teams.',
      );
    }
  }

  @Delete('/mappings/:id')
  async deleteMapping(@GetBotCtx() botCtx: BotCtx, @Param('id') id: string) {
    try {
      const result = await this.slackSubGroupsService.deleteMapping(id, botCtx);

      return { data: result, ok: true };
    } catch (error) {
      if (error instanceof HttpException) {
        throw error;
      }

      // Log the error
      if (error instanceof Error) {
        this.logger.error(
          `Error deleting slack sub group mapping: ${error.message}`,
          error.stack,
        );
      } else {
        console.error(error);
      }

      throw new InternalServerErrorException(
        'An error occurred while deleting slack sub group mapping.',
      );
    }
  }
}
