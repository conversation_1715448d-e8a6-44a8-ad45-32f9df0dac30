import {
  BadRequestException,
  Controller,
  Delete,
  Get,
  HttpException,
  Inject,
  InternalServerErrorException,
  Param,
  Response,
  UseGuards,
} from '@nestjs/common';
import { Response as ExpressResponse } from 'express';
import { GetBotCtx } from '../../auth/decorators';
import { NoSlackTeam } from '../../auth/decorators/no-slack-team.decorator';
import { AuthGuard } from '../../auth/guards';
import { BotCtx } from '../../auth/interfaces';
import { CUSTOM_LOGGER_TOKEN, ILogger } from '../../utils';
import { SlackService } from '../services/slack.service';

@Controller('v1/slack')
export class SlackController {
  constructor(
    @Inject(CUSTOM_LOGGER_TOKEN) private readonly logger: ILogger,
    private readonly slackService: SlackService,
  ) {}

  @Get('/install/:orgId/:userId')
  async installForOrganization(
    @Param('orgId') orgId: string,
    @Param('userId') userId: string,
    @Response() res: ExpressResponse,
  ) {
    // If no orgId is provided, throw an error
    if (!orgId) {
      throw new BadRequestException('Organization ID is required');
    }

    // If no user ID is provided, throw an error
    if (!userId) {
      throw new BadRequestException('User ID is required');
    }

    // Install the Slack app for the organization
    const organization = await this.slackService.installForOrganization({
      orgId,
      userId,
    });

    // Redirect to the Slack app
    res.redirect(
      `/slack/install?orgId=${organization.uid}&installingUser=${userId}`,
    );
  }

  @UseGuards(AuthGuard)
  @NoSlackTeam()
  @Get('/workspaces')
  async getWorkspaces(
    @GetBotCtx() botCtx: BotCtx,
    @Param('__sa_k') salt: string, // This is a salt if provided and matches what we have will return bot token
  ) {
    try {
      const { installations } = botCtx;
      if (!Array.isArray(installations)) {
        throw new BadRequestException(
          'No installations found, please do not add an installation id when fetching all the workspaces.',
        );
      }

      // If no installations are found, throw an error
      if (installations.length === 0) {
        throw new BadRequestException('No installations found');
      }

      const workspaces = await this.slackService.getWorkspaces(botCtx, salt);
      return workspaces;
    } catch (error) {
      if (error instanceof Error) {
        this.logger.error(
          `Error fetching workspaces: ${error.message}`,
          error.stack,
        );
      } else {
        console.error('Failed to fetch workspace', error);
      }

      if (error instanceof HttpException) {
        throw error;
      }

      throw new InternalServerErrorException('Something went wrong!');
    }
  }

  @UseGuards(AuthGuard)
  @NoSlackTeam()
  @Delete('/workspaces/disconnect')
  async disconnectWorkspace(@GetBotCtx() botCtx: BotCtx) {
    try {
      await this.slackService.disconnectWorkspace(botCtx);
    } catch (error) {
      if (error instanceof Error) {
        this.logger.error(`Error disconnecting workspace: ${error.message}`);
      } else {
        console.error(error);
      }

      // If the error is an HTTP exception, throw it
      if (error instanceof HttpException) {
        throw error;
      }

      throw new InternalServerErrorException('Something went wrong!');
    }
  }
}
