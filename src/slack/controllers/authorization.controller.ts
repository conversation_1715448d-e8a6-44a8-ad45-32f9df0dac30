import {
  BadRequestException,
  Controller,
  Delete,
  ForbiddenException,
  Get,
  HttpException,
  Inject,
  InternalServerErrorException,
  Query,
  Response,
  UseGuards,
} from '@nestjs/common';
import { Response as ExpressResponse } from 'express';
import { GetBotCtx } from '../../auth/decorators';
import { AuthGuard } from '../../auth/guards';
import { BotCtx } from '../../auth/interfaces';
import { ConfigKeys, ConfigService } from '../../config/config.service';
import { CUSTOM_LOGGER_TOKEN, ILogger } from '../../utils';
import { AuthorizationService } from '../services/authorization.service';

@Controller('v1/slack/authorization')
export class AuthorizationController {
  constructor(
    @Inject(CUSTOM_LOGGER_TOKEN) private readonly logger: ILogger,

    // Services
    private readonly configService: ConfigService,
    private readonly authorizationService: AuthorizationService,
  ) {}

  @Get('/user')
  @UseGuards(AuthGuard)
  async getAuthorizeUserURL(
    @Query('userEmail') userEmail: string,
    @GetBotCtx() botCtx: BotCtx,
  ) {
    try {
      // Validate that the user email is provided
      if (!userEmail) {
        throw new BadRequestException('User email is required!');
      }

      // Get the authorize user URL
      const result = await this.authorizationService.getAuthorizeUserURL(
        userEmail,
        botCtx,
      );

      return result;
    } catch (error) {
      if (error instanceof HttpException) {
        throw error;
      }

      // Log the error
      if (error instanceof Error) {
        this.logger.error(
          `Failed to get authorize user URL: ${error.message}`,
          error.stack,
        );
      } else {
        console.error(error);
      }

      throw new InternalServerErrorException(
        'Failed to get authorize user URL',
      );
    }
  }

  @Get('/user/callback')
  async authorizeUser(
    @Query('code') code: string,
    @Query('state') state: string,
    @Response() res: ExpressResponse,
  ) {
    try {
      if (!code) {
        throw new BadRequestException('Code is required!');
      }

      // Parse the state
      const parsedState = JSON.parse(state);

      // Authorize the user
      const result = await this.authorizationService.authorizeUser(
        code,
        parsedState,
      );

      // If the result is false, throw an error
      if (!result) {
        throw new ForbiddenException('Forbidden resource');
      }

      // Redirect to the organization settings page
      res.redirect(
        `${this.configService.get(ConfigKeys.THENA_WEB_URL)}/auth-close`,
      );
    } catch (error) {
      // Log the error
      if (error instanceof Error) {
        this.logger.error(
          `Failed to authorize user: ${error.message}`,
          error.stack,
        );
      } else {
        console.error(error);
      }

      if (error instanceof HttpException) {
        throw error;
      }

      throw new InternalServerErrorException('Failed to authorize user');
    }
  }

  @Delete('/user')
  @UseGuards(AuthGuard)
  async deleteUserAuthorization(
    @Query('userEmail') userEmail: string,
    @GetBotCtx() botCtx: BotCtx,
  ) {
    try {
      // Delete the user's authorization
      const result = await this.authorizationService.deleteUserAuthorization(
        userEmail,
        botCtx,
      );

      return result;
    } catch (error) {
      if (error instanceof Error) {
        this.logger.error(
          `Failed to delete user authorization: ${error.message}`,
          error.stack,
        );
      } else {
        console.error('Failed to delete user authorization', error);
      }

      if (error instanceof HttpException) {
        throw error;
      }

      throw new InternalServerErrorException(
        'Failed to delete user authorization',
      );
    }
  }
}
