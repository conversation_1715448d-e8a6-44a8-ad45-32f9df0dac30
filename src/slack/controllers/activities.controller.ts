import {
  Body,
  Controller,
  HttpCode,
  HttpStatus,
  InternalServerErrorException,
  Post,
} from '@nestjs/common';
import { UseGuards } from '@nestjs/common';
import { Inject } from '@nestjs/common';
import { ChatPostMessageResponse } from '@slack/web-api';
import { GetBotCtx } from '../../auth/decorators';
import { NoSlackTeam } from '../../auth/decorators/no-slack-team.decorator';
import { AuthGuard } from '../../auth/guards';
import { BotCtx } from '../../auth/interfaces';
import { ILogger } from '../../utils';
import { CUSTOM_LOGGER_TOKEN } from '../../utils';
import {
  AddReactionDTO,
  AddReactionResponseDTO,
  DeleteMessageDTO,
  DeleteMessageResponseDTO,
  InviteUserToConversationDTO,
  InviteUserToConversationResponseDTO,
  JoinConversationDTO,
  JoinConversationResponseDTO,
  KickUserFromConversationDTO,
  KickUserFromConversationResponseDTO,
  LeaveConversationDTO,
  LeaveConversationResponseDTO,
  PostMessageDTO,
  PostMessageResponseDTO,
  RemoveReactionDTO,
  RemoveReactionResponseDTO,
  UpdateMessageDTO,
  UpdateMessageResponseDTO,
} from '../dtos/activities.dto';
import { SlackWebAPIService } from '../providers/slack-apis/slack-apis.service';

@Controller('v1/slack/activities')
@UseGuards(AuthGuard)
export class SlackActivitiesController {
  constructor(
    @Inject(CUSTOM_LOGGER_TOKEN) private readonly logger: ILogger,
    private readonly slackWebApiService: SlackWebAPIService,
  ) {}

  @NoSlackTeam()
  @Post('/post-message')
  @HttpCode(HttpStatus.OK)
  async postMessage(
    @Body() body: PostMessageDTO,
    @GetBotCtx() botCtx: BotCtx,
  ): Promise<Array<PostMessageResponseDTO>> {
    const { installation, installations } = botCtx;
    const {
      channel: channelId,
      text,
      threadTs,
      blocks,
      unfurlLinks = true,
      unfurlMedia = true,
    } = body;

    try {
      // If an installation is provided via `x-slack-id` then we'll fire only for that team
      if (installation?.botToken) {
        const response = await this.slackWebApiService.sendMessage(
          installation.botToken,
          {
            channel: channelId,
            text,
            thread_ts: threadTs,
            blocks,
            unfurl_links: unfurlLinks,
            unfurl_media: unfurlMedia,
          },
        );

        return [
          {
            ok: response.ok,
            channel: response.channel,
            ts: response.ts,
            message: response.message,
          },
        ];
      }

      // Fanout to all installations
      const responses: Array<ChatPostMessageResponse> = [];
      for (const ins of installations) {
        try {
          const res = await this.slackWebApiService.sendMessage(ins.botToken, {
            channel: channelId,
            text,
            thread_ts: threadTs,
            blocks,
            unfurl_links: unfurlLinks,
            unfurl_media: unfurlMedia,
          });

          responses.push(res);
        } catch (error) {
          if (error instanceof Error) {
            this.logger.error(
              `Error sending message to channel ${channelId}: ${error.message}`,
            );

            responses.push({
              ok: false,
              channel: channelId,
              ts: threadTs,
              error: error.message,
            });
          } else {
            console.error(
              `Error sending message to channel ${channelId}`,
              error,
            );
          }
        }
      }

      // Format the responses
      const formattedResponses = responses.map((res) => ({
        ok: res.ok,
        channel: res.channel,
        ts: res.ts,
        message: res.message,
      }));

      return formattedResponses;
    } catch (error) {
      this.logger.error(
        `Error sending message to channel ${channelId}: ${error}`,
      );

      throw new InternalServerErrorException('Something went wrong');
    }
  }

  @Post('/update-message')
  async updateMessage(
    @Body() body: UpdateMessageDTO,
    @GetBotCtx() botCtx: BotCtx,
  ): Promise<UpdateMessageResponseDTO> {
    const { channel: channelId, text, ts, blocks } = body;
    try {
      const response = await this.slackWebApiService.updateMessage(
        botCtx.installation.botToken,
        {
          channel: channelId,
          text,
          ts,
          blocks: JSON.parse(blocks),
        },
      );
      return response;
    } catch (error) {
      this.logger.error(
        `Error updating message in channel ${channelId}: ${error}`,
      );
      throw new InternalServerErrorException('Something went wrong');
    }
  }

  @Post('/delete-message')
  async deleteMessage(
    @Body() body: DeleteMessageDTO,
    @GetBotCtx() botCtx: BotCtx,
  ): Promise<DeleteMessageResponseDTO> {
    const { channel: channelId, ts } = body;
    try {
      const response = await this.slackWebApiService.deleteMessage(
        botCtx.installation.botToken,
        {
          channel: channelId,
          ts,
        },
      );
      return response;
    } catch (error) {
      this.logger.error(
        `Error deleting message in channel ${channelId}: ${error}`,
      );
      throw new InternalServerErrorException('Something went wrong');
    }
  }

  @Post('/add-reaction')
  async addReaction(
    @Body() body: AddReactionDTO,
    @GetBotCtx() botCtx: BotCtx,
  ): Promise<AddReactionResponseDTO> {
    const { channel, ts, name } = body;
    try {
      const response = await this.slackWebApiService.addReactionToMessage(
        botCtx.installation.botToken,
        {
          channel,
          timestamp: ts,
          name,
        },
      );
      return response;
    } catch (error) {
      this.logger.error(
        `Error adding reaction to message in channel ${channel}: ${error}`,
      );
      throw new InternalServerErrorException('Something went wrong');
    }
  }

  @Post('/remove-reaction')
  async removeReaction(
    @Body() body: RemoveReactionDTO,
    @GetBotCtx() botCtx: BotCtx,
  ): Promise<RemoveReactionResponseDTO> {
    const { channel, ts, name } = body;
    try {
      const response = await this.slackWebApiService.removeReactionFromMessage(
        botCtx.installation.botToken,
        {
          channel,
          timestamp: ts,
          name,
        },
      );
      return response;
    } catch (error) {
      this.logger.error(
        `Error removing reaction from message in channel ${channel}: ${error}`,
      );
      throw new InternalServerErrorException('Something went wrong');
    }
  }

  @Post('/invite-user-to-conversation')
  async inviteUserToConversation(
    @Body() body: InviteUserToConversationDTO,
    @GetBotCtx() botCtx: BotCtx,
  ): Promise<InviteUserToConversationResponseDTO> {
    const { channel, user } = body;
    try {
      const response = await this.slackWebApiService.inviteUserToConversation(
        botCtx.installation.botToken,
        {
          channel,
          users: user,
        },
      );
      return response;
    } catch (error) {
      this.logger.error(
        `Error inviting user to conversation in channel ${channel}: ${error}`,
      );
      throw new InternalServerErrorException('Something went wrong');
    }
  }

  @Post('/kick-user-from-conversation')
  async kickUserFromConversation(
    @Body() body: KickUserFromConversationDTO,
    @GetBotCtx() botCtx: BotCtx,
  ): Promise<KickUserFromConversationResponseDTO> {
    const { channel, user } = body;
    try {
      const response = await this.slackWebApiService.kickUserFromConversation(
        botCtx.installation.botToken,
        {
          channel,
          user,
        },
      );
      return response;
    } catch (error) {
      this.logger.error(
        `Error kicking user from conversation in channel ${channel}: ${error}`,
      );
      throw new InternalServerErrorException('Something went wrong');
    }
  }

  @Post('/join-conversation')
  async joinConversation(
    @Body() body: JoinConversationDTO,
    @GetBotCtx() botCtx: BotCtx,
  ): Promise<JoinConversationResponseDTO> {
    const { channel } = body;
    try {
      const response = await this.slackWebApiService.joinConversation(
        botCtx.installation.botToken,
        {
          channel,
        },
      );
      return response;
    } catch (error) {
      this.logger.error(
        `Error joining conversation in channel ${channel}: ${error}`,
      );
      throw new InternalServerErrorException('Something went wrong');
    }
  }

  @Post('/leave-conversation')
  async leaveConversation(
    @Body() body: LeaveConversationDTO,
    @GetBotCtx() botCtx: BotCtx,
  ): Promise<LeaveConversationResponseDTO> {
    const { channel } = body;
    try {
      const response = await this.slackWebApiService.leaveConversation(
        botCtx.installation.botToken,
        {
          channel,
        },
      );
      return response;
    } catch (error) {
      this.logger.error(
        `Error leaving conversation in channel ${channel}: ${error}`,
      );
      throw new InternalServerErrorException('Something went wrong');
    }
  }
}
