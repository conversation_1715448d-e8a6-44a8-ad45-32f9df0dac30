import {
  Body,
  Controller,
  Delete,
  Get,
  HttpException,
  Inject,
  InternalServerErrorException,
  Param,
  Patch,
  Post,
  UseGuards,
} from '@nestjs/common';
import { GetBotCtx } from '../../auth/decorators';
import { AuthGuard } from '../../auth/guards/auth.guard';
import { BotCtx } from '../../auth/interfaces/context.interface';
import { CUSTOM_LOGGER_TOKEN } from '../../utils';
import { ILogger } from '../../utils';
import {
  CreateTriageRuleDto,
  UpdateTriageRuleDto,
} from '../dtos/triage-rule.dto';
import { TriageRulesService } from '../services/triage-rules.service';

@Controller('v1/slack/triage')
@UseGuards(AuthGuard)
export class TriageRulesController {
  constructor(
    @Inject(CUSTOM_LOGGER_TOKEN) private readonly logger: ILogger,
    private readonly triageRulesService: TriageRulesService,
  ) {}

  @Post('/:teamId')
  async createRule(
    @Param('teamId') teamId: string,
    @Body() createDto: CreateTriageRuleDto,
    @GetBotCtx() botCtx: BotCtx,
  ) {
    try {
      return this.triageRulesService.createRule(teamId, createDto, botCtx);
    } catch (error) {
      if (error instanceof HttpException) {
        throw error;
      }

      if (error instanceof Error) {
        this.logger.error(
          `Error creating triage rule: ${error.message}`,
          error.stack,
        );
      } else {
        console.error('Error creating triage rule:', error);
      }

      throw new InternalServerErrorException('Failed to create triage rule');
    }
  }

  @Get('/:teamId')
  async getRules(@Param('teamId') teamId: string, @GetBotCtx() botCtx: BotCtx) {
    try {
      return this.triageRulesService.getTeamRules(teamId, botCtx);
    } catch (error) {
      if (error instanceof HttpException) {
        throw error;
      }

      if (error instanceof Error) {
        this.logger.error(
          `Error getting triage rules: ${error.message}`,
          error.stack,
        );
      } else {
        console.error('Error getting triage rules:', error);
      }

      throw new InternalServerErrorException('Failed to get triage rules');
    }
  }

  @Patch('/:ruleId')
  async updateRule(
    @Param('ruleId') ruleId: string,
    @Body() updateDto: UpdateTriageRuleDto,
    @GetBotCtx() botCtx: BotCtx,
  ) {
    try {
      return this.triageRulesService.updateRule(ruleId, updateDto, botCtx);
    } catch (error) {
      if (error instanceof HttpException) {
        throw error;
      }

      if (error instanceof Error) {
        this.logger.error(
          `Error updating triage rule: ${error.message}`,
          error.stack,
        );
      } else {
        console.error('Error updating triage rule:', error);
      }

      throw new InternalServerErrorException('Failed to update triage rule');
    }
  }

  @Delete('/:ruleId')
  async deleteRule(
    @Param('ruleId') ruleId: string,
    @GetBotCtx() botCtx: BotCtx,
  ) {
    try {
      return this.triageRulesService.deleteRule(ruleId, botCtx);
    } catch (error) {
      if (error instanceof HttpException) {
        throw error;
      }

      if (error instanceof Error) {
        this.logger.error(
          `Error deleting triage rule: ${error.message}`,
          error.stack,
        );
      } else {
        console.error('Error deleting triage rule:', error);
      }

      throw new InternalServerErrorException('Failed to delete triage rule');
    }
  }
}
