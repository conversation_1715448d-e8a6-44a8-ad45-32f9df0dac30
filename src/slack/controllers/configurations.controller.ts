import {
  BadRequestException,
  Body,
  Controller,
  Get,
  HttpException,
  Inject,
  InternalServerErrorException,
  Param,
  Post,
  Query,
  UseGuards,
} from '@nestjs/common';
import { GetBotCtx } from '../../auth/decorators';
import { NoSlackTeam } from '../../auth/decorators/no-slack-team.decorator';
import { AuthGuard } from '../../auth/guards';
import { BotCtx } from '../../auth/interfaces';
import { CUSTOM_LOGGER_TOKEN, ILogger } from '../../utils';
import { ConfigureTriageChannelDTO, CreateTriageThreadDTO } from '../dtos';
import { GetAllSlackChannelsQueryParams } from '../query-params';
import { SlackChannelService } from '../services/slack-channel.service';

@Controller('v1/slack/channel')
@UseGuards(AuthGuard)
export class SlackChannelController {
  constructor(
    @Inject(CUSTOM_LOGGER_TOKEN) private readonly logger: ILogger,
    private readonly slackChannelService: SlackChannelService,
  ) {}

  @Get('/:teamId')
  @NoSlackTeam()
  async getAllSlackChannelsByTeamId(
    @Param('teamId') teamId: string,
    @Query() query: GetAllSlackChannelsQueryParams,
    @GetBotCtx() botCtx: BotCtx,
  ) {
    try {
      this.logger.log(
        `Fetching all slack channels for team ${teamId} with query ${JSON.stringify(
          query,
        )}`,
      );

      const results =
        await this.slackChannelService.getAllSlackChannelsByTeamId(
          botCtx,
          teamId,
          query,
        );

      this.logger.log(
        `Fetched ${results.data.length} slack channels for team ${teamId}`,
      );

      return results;
    } catch (error) {
      if (error instanceof HttpException) {
        throw error;
      }

      // Log the error
      if (error instanceof Error) {
        this.logger.error(
          `Error fetching slack channels: ${error.message}`,
          error.stack,
        );
      } else {
        console.error(error);
      }

      throw new InternalServerErrorException(
        'An error occurred while fetching slack channels',
      );
    }
  }

  @Get('/')
  async getAllSlackChannels(
    @Query() query: GetAllSlackChannelsQueryParams,
    @GetBotCtx() botCtx: BotCtx,
  ) {
    try {
      this.logger.log(
        `Fetching all slack channels for team ${botCtx.installation.teamId} with query ${JSON.stringify(
          query,
        )}`,
      );

      const results = await this.slackChannelService.getAllSlackChannels(
        botCtx,
        query,
      );

      this.logger.log(
        `Fetched ${results.data.length} slack channels for team ${botCtx.installation.teamId}`,
      );

      return results;
    } catch (error) {
      if (error instanceof HttpException) {
        throw error;
      }

      // Log the error
      if (error instanceof Error) {
        this.logger.error(
          `Error fetching slack channels: ${error.message}`,
          error.stack,
        );
      } else {
        console.error(error);
      }

      throw new InternalServerErrorException(
        'An error occurred while fetching slack channels',
      );
    }
  }

  @Post('/configure-triage-channel')
  async configureTriageChannel(
    @Body() data: ConfigureTriageChannelDTO,
    @GetBotCtx() botCtx: BotCtx,
  ) {
    await this.slackChannelService.configureTriageChannel(data, botCtx);
  }

  @Post('/thread/:channelId')
  async createTriageThread(
    @Param('channelId') channelId: string,
    @Body() data: CreateTriageThreadDTO,
    @GetBotCtx() botCtx: BotCtx,
  ) {
    try {
      // Validate that the channel ID is provided
      if (!channelId) {
        throw new BadRequestException('Channel ID is required');
      }

      // Create the triage thread
      const result = await this.slackChannelService.createTriageThread(
        data,
        channelId,
        botCtx,
      );

      return { ok: true, data: result };
    } catch (error) {
      if (error instanceof HttpException) {
        throw error;
      }

      // Log the error
      if (error instanceof Error) {
        this.logger.error(
          `Error creating triage thread: ${error.message}`,
          error.stack,
        );
      } else {
        console.error(error);
      }

      throw new InternalServerErrorException(
        'An error occurred while creating a triage thread',
      );
    }
  }
}
