import {
  BadRequestException,
  Body,
  Controller,
  Delete,
  Get,
  HttpException,
  Inject,
  InternalServerErrorException,
  Param,
  Post,
  Put,
  UseGuards,
} from '@nestjs/common';
import { GetBotCtx } from '../../auth/decorators';
import { AuthGuard } from '../../auth/guards';
import { BotCtx } from '../../auth/interfaces';
import { Prompts } from '../../database/entities/prompts/prompts.entity';
import { CUSTOM_LOGGER_TOKEN, ILogger } from '../../utils';
import { PromptsService } from '../services/prompts.service';

@Controller('v1/prompts')
export class PromptsController {
  constructor(
    @Inject(CUSTOM_LOGGER_TOKEN) private readonly logger: ILogger,
    private readonly promptsService: PromptsService,
  ) {}

  @UseGuards(AuthGuard)
  @Get()
  async getPrompts(@GetBotCtx() botCtx: BotCtx) {
    try {
      const data = await this.promptsService.getPrompts(botCtx);
      return {
        ok: true,
        data,
      };
    } catch (error) {
      if (error instanceof HttpException) {
        throw error;
      }

      if (error instanceof Error) {
        this.logger.error(`Error fetching prompts: ${error.message}`);
      } else {
        console.error(error);
      }

      throw new InternalServerErrorException('Failed to fetch prompts');
    }
  }

  @UseGuards(AuthGuard)
  @Get(':id')
  async getPromptById(@GetBotCtx() botCtx: BotCtx, @Param('id') id: string) {
    try {
      const data = await this.promptsService.getPromptById(botCtx, id);
      return {
        ok: true,
        data,
      };
    } catch (error) {
      if (error instanceof HttpException) {
        throw error;
      }

      if (error instanceof Error) {
        this.logger.error(`Error fetching prompt: ${error.message}`);
      } else {
        console.error(error);
      }

      throw new InternalServerErrorException('Failed to fetch prompt');
    }
  }

  @UseGuards(AuthGuard)
  @Post()
  async createPrompt(
    @GetBotCtx() botCtx: BotCtx,
    @Body() promptData: Partial<Prompts>,
  ) {
    try {
      // Validate required fields
      if (!promptData.name || !promptData.prompts) {
        throw new BadRequestException('Name and prompts are required fields');
      }

      // Validate that all required prompt fields are provided
      const requiredFields = [
        'ticket_detection',
        'sentiment_analysis',
        'urgency_detection',
        'custom_fields',
        'title_generation',
        'description_generation',
      ];

      for (const field of requiredFields) {
        if (!promptData.prompts[field]) {
          throw new BadRequestException(`Prompt field "${field}" is required`);
        }
      }

      const data = await this.promptsService.createPrompt(botCtx, promptData);
      return {
        ok: true,
        data,
      };
    } catch (error) {
      if (error instanceof HttpException) {
        throw error;
      }

      if (error instanceof Error) {
        this.logger.error(`Error creating prompt: ${error.message}`);
      } else {
        console.error(error);
      }

      throw new InternalServerErrorException('Failed to create prompt');
    }
  }

  @UseGuards(AuthGuard)
  @Put(':id')
  async updatePrompt(
    @GetBotCtx() botCtx: BotCtx,
    @Param('id') id: string,
    @Body() promptData: Partial<Prompts>,
  ) {
    try {
      // If prompts field is provided, validate all required fields
      if (promptData.prompts) {
        const requiredFields = [
          'ticket_detection',
          'sentiment_analysis',
          'urgency_detection',
          'custom_fields',
          'title_generation',
          'description_generation',
        ];

        for (const field of requiredFields) {
          if (!promptData.prompts[field]) {
            throw new BadRequestException(
              `Prompt field "${field}" is required`,
            );
          }
        }
      }

      const data = await this.promptsService.updatePrompt(
        botCtx,
        id,
        promptData,
      );
      return {
        ok: true,
        data,
      };
    } catch (error) {
      if (error instanceof HttpException) {
        throw error;
      }

      if (error instanceof Error) {
        this.logger.error(`Error updating prompt: ${error.message}`);
      } else {
        console.error(error);
      }

      throw new InternalServerErrorException('Failed to update prompt');
    }
  }

  @UseGuards(AuthGuard)
  @Delete(':id')
  async deletePrompt(@GetBotCtx() botCtx: BotCtx, @Param('id') id: string) {
    try {
      const data = await this.promptsService.deletePrompt(botCtx, id);
      return {
        ok: true,
        data,
      };
    } catch (error) {
      if (error instanceof HttpException) {
        throw error;
      }

      if (error instanceof Error) {
        this.logger.error(`Error deleting prompt: ${error.message}`);
      } else {
        console.error(error);
      }

      throw new InternalServerErrorException('Failed to delete prompt');
    }
  }

  @UseGuards(AuthGuard)
  @Post(':id/set-default/:teamId')
  async setDefaultPrompt(
    @GetBotCtx() botCtx: BotCtx,
    @Param('id') id: string,
    @Param('teamId') teamId: string,
  ) {
    try {
      const data = await this.promptsService.setDefaultPrompt(
        botCtx,
        id,
        teamId,
      );
      return {
        ok: true,
        data,
      };
    } catch (error) {
      if (error instanceof HttpException) {
        throw error;
      }

      if (error instanceof Error) {
        this.logger.error(`Error setting default prompt: ${error.message}`);
      } else {
        console.error(error);
      }

      throw new InternalServerErrorException('Failed to set default prompt');
    }
  }

  @UseGuards(AuthGuard)
  @Post('seed/:teamId')
  async seedDefaultPrompts(
    @GetBotCtx() botCtx: BotCtx,
    @Param('teamId') teamId: string,
  ) {
    try {
      const data = await this.promptsService.seedDefaultPrompts(botCtx, teamId);
      return {
        ok: true,
        data,
        message: data
          ? 'Default prompts created for team'
          : 'Team already has prompts, no action taken',
      };
    } catch (error) {
      if (error instanceof HttpException) {
        throw error;
      }

      if (error instanceof Error) {
        this.logger.error(`Error seeding default prompts: ${error.message}`);
      } else {
        console.error(error);
      }

      throw new InternalServerErrorException('Failed to seed default prompts');
    }
  }

  @UseGuards(AuthGuard)
  @Post('seed-all')
  async seedDefaultPromptsForAllTeams(@GetBotCtx() botCtx: BotCtx) {
    try {
      const data =
        await this.promptsService.seedDefaultPromptsForAllTeams(botCtx);
      return {
        ok: true,
        data,
        message: `Created default prompts for ${data.seededCount} teams, ${data.skippedCount} teams already had prompts`,
      };
    } catch (error) {
      if (error instanceof HttpException) {
        throw error;
      }

      if (error instanceof Error) {
        this.logger.error(
          `Error seeding default prompts for all teams: ${error.message}`,
        );
      } else {
        console.error(error);
      }

      throw new InternalServerErrorException(
        'Failed to seed default prompts for all teams',
      );
    }
  }

  @UseGuards(AuthGuard)
  @Get('teams/:teamId')
  async getPromptsByTeamId(
    @GetBotCtx() botCtx: BotCtx,
    @Param('teamId') teamId: string,
  ) {
    if (!teamId || teamId.trim() === '') {
      throw new BadRequestException('Team ID is required');
    }
    try {
      const data = await this.promptsService.getPromptsByTeamId(botCtx, teamId);
      return {
        ok: true,
        data,
      };
    } catch (error) {
      if (error instanceof HttpException) {
        throw error;
      }

      if (error instanceof Error) {
        this.logger.error(
          `Error fetching prompts by team ID: ${error.message}`,
        );
      } else {
        console.error(error);
      }

      throw new InternalServerErrorException(
        'Failed to fetch prompts by team ID',
      );
    }
  }
}
