import {
  Controller,
  HttpException,
  Inject,
  InternalServerErrorException,
  Param,
  Post,
  UseGuards,
} from '@nestjs/common';
import { GetBotCtx } from '../../auth/decorators';
import { AuthGuard } from '../../auth/guards';
import { BotCtx } from '../../auth/interfaces';
import { CUSTOM_LOGGER_TOKEN, ILogger } from '../../utils';
import {
  SlackExternalUsersSyncJob,
  SlackUsersSyncJob,
} from '../processors/jobs';

export enum SlackSyncType {
  EXTERNAL_USERS = 'external-users',
  INTERNAL_USERS = 'internal-users',
}

@UseGuards(AuthGuard)
@Controller('v1/slack/sync')
export class SlackSyncController {
  constructor(
    @Inject(CUSTOM_LOGGER_TOKEN) private readonly logger: ILogger,

    // Jobs
    private readonly slackExternalUsersSyncJob: SlackExternalUsersSyncJob,
    private readonly slackUsersSyncJob: SlackUsersSyncJob,
  ) {}

  @Post('/:syncType')
  async sync(
    @Param('syncType') syncType: SlackSyncType,
    @GetBotCtx() botCtx: BotCtx,
  ) {
    try {
      const { installation } = botCtx;

      // Sync the external users
      if (syncType === SlackSyncType.EXTERNAL_USERS) {
        this.slackExternalUsersSyncJob.execute(installation).catch((error) => {
          this.logger.error(error.message, error.stack);
        });
      }
      // Sync the internal users
      else if (syncType === SlackSyncType.INTERNAL_USERS) {
        this.slackUsersSyncJob.execute(installation).catch((error) => {
          this.logger.error(error.message, error.stack);
        });
      }
    } catch (error) {
      if (error instanceof Error) {
        this.logger.error(error.message, error.stack);
      } else {
        console.error(`Failed to sync slack: ${syncType}`, error);
      }

      // If the error is an HTTP exception, throw it
      if (error instanceof HttpException) {
        throw error;
      }

      // Otherwise, throw a generic error
      throw new InternalServerErrorException(
        `Failed to sync provided slack type: ${syncType}`,
      );
    }
  }
}
