import { Body, Controller, Inject, Post, Res } from '@nestjs/common';
import { Response } from 'express';
import { InstallationRepository } from '../../database/entities/installations/repositories';
import { CUSTOM_LOGGER_TOKEN, ILogger } from '../../utils';
import { ConditionalFormBuilderComposite } from '../blocks/components/composite/form-builder';
import { SlackWebAPIService } from '../providers/slack-apis/slack-apis.service';
import { FormBuilderService } from '../services/form-builder.service';

@Controller('v1/slack/form-builder')
export class FormBuilderController {
  constructor(
    @Inject(CUSTOM_LOGGER_TOKEN) private readonly logger: ILogger,
    private readonly formBuilderService: FormBuilderService,
    private readonly formBuilder: ConditionalFormBuilderComposite,
    private readonly slackApiService: SlackWebAPIService,
    private readonly installationRepository: InstallationRepository,
  ) {}

  /**
   * Handle form selection from the dropdown
   */
  @Post('/select-form')
  async handleFormSelection(@Body() payload: any, @Res() res: Response) {
    // Immediately acknowledge the request to avoid timeout
    res.status(200).send();

    try {
      this.logger.debug('Handling form selection');

      // Parse the payload from Slack
      const parsedPayload =
        typeof payload === 'string' ? JSON.parse(payload) : payload;

      // Extract necessary information from the payload
      const { actions, trigger_id, team, channel } = parsedPayload;

      if (!trigger_id) {
        this.logger.error('Missing trigger_id in payload');
        return;
      }

      if (!channel?.id) {
        this.logger.error('Missing channel ID in payload');
        return;
      }

      // First, get the installation for this team
      const installation = await this.installationRepository.findByCondition({
        where: { teamId: team.id },
      });

      if (!installation) {
        this.logger.error('Installation not found for this workspace');
        return;
      }

      // Find which action was triggered
      const formSelectionAction = actions?.find(
        (action) =>
          action.action_id === 'form_continue' ||
          action.block_id === 'form_selector_block',
      );

      if (!formSelectionAction) {
        this.logger.error('No form was selected');
        return;
      }

      // Extract form ID from the action
      let formId: string;
      if (formSelectionAction.action_id === 'form_continue') {
        // Get the form ID from the select input
        const selectValue =
          parsedPayload.state?.values?.form_selector_block?.form_selector
            ?.selected_option?.value;
        formId = selectValue || formSelectionAction.value;
      } else {
        // Direct selection
        formId = formSelectionAction.selected_option.value;
      }

      if (!formId) {
        this.logger.error('No form was selected');
        return;
      }

      // Extract teamId from the installation
      const teamId = installation.teamId;

      if (!teamId) {
        this.logger.error('Team context not found');
        return;
      }

      // Fetch the selected form
      const formData = await this.formBuilderService.getFormById(
        installation,
        formId,
        teamId,
      );

      if (!formData) {
        this.logger.error('Selected form not found');
        return;
      }

      // Build the form using our conditional form builder
      const { fields, conditions, conditionOrder } = formData;

      const formBlocks = this.formBuilder.build({
        fields,
        conditions,
        conditionOrder,
        values: {}, // Initial values
      });

      // Open a modal with the form
      await this.slackApiService.openView(installation.botToken, {
        trigger_id,
        view: {
          type: 'modal',
          callback_id: 'form_submission_modal',
          private_metadata: JSON.stringify({
            formId,
            teamId,
            channelId: channel.id,
          }),
          title: {
            type: 'plain_text',
            text: 'Create ticket',
            emoji: true,
          },
          submit: {
            type: 'plain_text',
            text: 'Submit',
            emoji: true,
          },
          close: {
            type: 'plain_text',
            text: 'Cancel',
            emoji: true,
          },
          blocks: formBlocks.blocks,
        },
      });
    } catch (error) {
      if (error instanceof Error) {
        this.logger.error(
          `Error handling form selection: ${error.message}`,
          error.stack,
        );
      } else {
        this.logger.error('Error handling form selection', String(error));
      }
    }
  }

  /**
   * Handle form submission from modal
   */
  @Post('/submit-form')
  async handleFormSubmission(@Body() payload: any, @Res() res: Response) {
    // Immediately acknowledge the request
    res.status(200).send();

    try {
      this.logger.debug('Handling form submission');

      // Parse the payload from Slack
      const parsedPayload =
        typeof payload === 'string' ? JSON.parse(payload) : payload;

      // This will be a view_submission event
      const { view, user } = parsedPayload;

      if (!view) {
        this.logger.error('Missing view in payload');
        return;
      }

      // Extract form values from the state
      const values = {};
      const formBlocks = view.state?.values;

      // Process each block to extract values
      if (formBlocks) {
        for (const [_blockId, blockData] of Object.entries(formBlocks)) {
          for (const [actionId, actionData] of Object.entries(blockData)) {
            if (actionId.startsWith('field_')) {
              const fieldId = actionId.replace('field_', '');

              // Handle different types of inputs
              if ('value' in actionData) {
                values[fieldId] = actionData.value;
              } else if ('selected_option' in actionData) {
                values[fieldId] = actionData.selected_option?.value;
              } else if ('selected_options' in actionData) {
                values[fieldId] = actionData.selected_options?.map(
                  (opt) => opt.value,
                );
              } else if ('selected_date' in actionData) {
                values[fieldId] = actionData.selected_date;
              } else if ('selected_time' in actionData) {
                values[fieldId] = actionData.selected_time;
              } else if ('selected_user' in actionData) {
                values[fieldId] = actionData.selected_user;
              } else if ('selected_conversation' in actionData) {
                values[fieldId] = actionData.selected_conversation;
              } else if ('selected_channel' in actionData) {
                values[fieldId] = actionData.selected_channel;
              }
            }
          }
        }
      }

      // Get metadata to know which form was submitted
      const metadata = view.private_metadata
        ? JSON.parse(view.private_metadata)
        : {};

      const { teamId } = metadata;

      // Get installation for this team
      const installation = await this.installationRepository.findByCondition({
        where: { teamId },
      });

      if (!installation) {
        this.logger.error('Installation not found for this workspace');
        return;
      }

      // TODO: Process the form values and create a ticket
      this.logger.debug('Form values received', JSON.stringify(values));

      // Send a DM to the user confirming the form submission
      await this.slackApiService.postMessage(installation.botToken, {
        channel: user.id,
        text: '✅ Your form has been submitted successfully! A new ticket has been created.',
      });
    } catch (error) {
      if (error instanceof Error) {
        this.logger.error(
          `Error handling form submission: ${error.message}`,
          error.stack,
        );
      } else {
        this.logger.error('Error handling form submission', String(error));
      }
    }
  }

  /**
   * Handle interactive components (dropdowns, checkboxes, etc.)
   */
  @Post('/interact')
  async handleInteraction(@Body() payload: any, @Res() res: Response) {
    try {
      // Immediately acknowledge the request
      res.status(200).send();

      this.logger.debug(
        'Raw interaction payload',
        typeof payload === 'string'
          ? payload
          : JSON.stringify(payload, null, 2),
      );

      // Parse the payload from Slack
      const parsedPayload =
        typeof payload === 'string' ? JSON.parse(payload) : payload;

      // Check the type of interaction
      const { type, actions, view, team } = parsedPayload;

      this.logger.debug(
        'Parsed interaction details',
        JSON.stringify(
          {
            type,
            hasActions: !!actions,
            hasView: !!view,
            actionIds: actions?.map((a) => a.action_id),
            viewType: view?.type,
            teamId: team?.id,
          },
          null,
          2,
        ),
      );

      // For block actions in a modal view
      if (type === 'block_actions' && view?.type === 'modal') {
        const values = {};

        // Process the current action
        if (actions && actions.length > 0) {
          const action = actions[0];
          this.logger.debug(
            'Processing action',
            JSON.stringify({ action }, null, 2),
          );

          if (action.action_id.startsWith('field_')) {
            const fieldId = action.action_id.replace('field_', '');
            this.logger.debug(
              `Processing field ${fieldId}`,
              JSON.stringify({ action }, null, 2),
            );

            // Extract the value based on the action type
            if ('value' in action) {
              values[fieldId] = action.value;
              this.logger.debug(
                `Field ${fieldId} value set to: ${action.value}`,
              );
            } else if ('selected_option' in action) {
              values[fieldId] = action.selected_option?.value;
            } else if ('selected_options' in action) {
              values[fieldId] = action.selected_options?.map(
                (opt) => opt.value,
              );
            } else if ('selected_date' in action) {
              values[fieldId] = action.selected_date;
            } else if ('selected_time' in action) {
              values[fieldId] = action.selected_time;
            } else if ('selected_user' in action) {
              values[fieldId] = action.selected_user;
            } else if ('selected_conversation' in action) {
              values[fieldId] = action.selected_conversation;
            } else if ('selected_channel' in action) {
              values[fieldId] = action.selected_channel;
            }
          }
        }

        // Also get existing values from state.values
        const formBlocks = view.state?.values;
        if (formBlocks) {
          for (const [blockId, blockData] of Object.entries(formBlocks)) {
            this.logger.debug(
              `Processing block ${blockId}`,
              JSON.stringify({ blockData }, null, 2),
            );

            for (const [actionId, actionData] of Object.entries(blockData)) {
              if (actionId.startsWith('field_')) {
                const fieldId = actionId.replace('field_', '');

                // Only add if not already set by the current action
                if (!values[fieldId]) {
                  this.logger.debug(
                    `Processing existing field ${fieldId}`,
                    JSON.stringify({ actionData }, null, 2),
                  );

                  // Handle different types of inputs
                  if ('value' in actionData) {
                    values[fieldId] = actionData.value;
                    this.logger.debug(
                      `Existing field ${fieldId} value set to: ${actionData.value}`,
                    );
                  } else if ('selected_option' in actionData) {
                    values[fieldId] = actionData.selected_option?.value;
                  } else if ('selected_options' in actionData) {
                    values[fieldId] = actionData.selected_options?.map(
                      (opt) => opt.value,
                    );
                  } else if ('selected_date' in actionData) {
                    values[fieldId] = actionData.selected_date;
                  } else if ('selected_time' in actionData) {
                    values[fieldId] = actionData.selected_time;
                  } else if ('selected_user' in actionData) {
                    values[fieldId] = actionData.selected_user;
                  } else if ('selected_conversation' in actionData) {
                    values[fieldId] = actionData.selected_conversation;
                  } else if ('selected_channel' in actionData) {
                    values[fieldId] = actionData.selected_channel;
                  }
                }
              }
            }
          }
        }

        this.logger.debug(
          'All field values',
          JSON.stringify({ values }, null, 2),
        );

        // Get metadata to reapply conditions
        const metadata = view.private_metadata
          ? JSON.parse(view.private_metadata)
          : {};
        const { formId, teamId } = metadata;

        this.logger.debug(
          'Form metadata',
          JSON.stringify({ formId, teamId }, null, 2),
        );

        if (formId && teamId) {
          const installation =
            await this.installationRepository.findByCondition({
              where: { teamId: team.id },
            });

          if (installation) {
            // Get the form definition
            const formData = await this.formBuilderService.getFormById(
              installation,
              formId,
              teamId,
            );

            if (formData) {
              this.logger.debug(
                'Form data retrieved',
                JSON.stringify(
                  {
                    fieldCount: formData.fields.length,
                    conditionCount: formData.conditions.length,
                    values,
                  },
                  null,
                  2,
                ),
              );

              // Apply conditions and update the view
              const { fields, conditions, conditionOrder } = formData;

              const updatedBlocks = this.formBuilder.build({
                fields,
                conditions,
                conditionOrder,
                values, // Current values for conditional logic
              });

              // Update the modal with new blocks based on conditions
              await this.slackApiService.updateView(installation.botToken, {
                view_id: view.id,
                hash: view.hash,
                view: {
                  ...view,
                  blocks: updatedBlocks.blocks,
                },
              });

              this.logger.debug('View updated successfully');
            }
          }
        }
      }
    } catch (error) {
      if (error instanceof Error) {
        this.logger.error(
          `Error handling interaction: ${error.message}`,
          error.stack,
        );
      } else {
        this.logger.error('Error handling interaction', String(error));
      }
    }
  }
}
