import {
  Body,
  Controller,
  Delete,
  Get,
  Param,
  Post,
  Put,
  UseGuards,
} from '@nestjs/common';
import { GetBotCtx } from '../../auth/decorators';
import { AuthGuard } from '../../auth/guards';
import { BotCtx } from '../../auth/interfaces';
import { UpdateSettingsDTO } from '../dtos/settings.dto';
import { SettingsService } from '../services/settings.service';

@Controller('v1/slack/settings')
@UseGuards(AuthGuard)
export class SettingsController {
  constructor(private readonly settingsService: SettingsService) {}

  @Get('/teams/:teamId')
  async getTeamSettings(
    @Param('teamId') teamId: string,
    @GetBotCtx() botCtx: BotCtx,
  ) {
    return this.settingsService.getTeamSettings(teamId, botCtx);
  }

  @Put('/teams/:teamId')
  async updateSettings(
    @Param('teamId') teamId: string,
    @Body() updateDto: UpdateSettingsDTO,
    @GetBotCtx() botCtx: BotCtx,
  ) {
    return this.settingsService.updateSettings(teamId, updateDto, botCtx);
  }

  @Delete('/teams/:teamId/settings/:key')
  async deleteSetting(
    @Param('teamId') teamId: string,
    @Param('key') settingKey: string,
    @GetBotCtx() botCtx: BotCtx,
  ) {
    return this.settingsService.deleteSetting(teamId, settingKey, botCtx);
  }

  @Post('/teams/:teamId/reset')
  async resetSettings(
    @Param('teamId') teamId: string,
    @GetBotCtx() botCtx: BotCtx,
  ) {
    return this.settingsService.resetSettings(teamId, botCtx);
  }
}
