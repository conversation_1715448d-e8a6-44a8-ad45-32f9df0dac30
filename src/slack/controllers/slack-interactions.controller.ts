import { Body, Controller, Post } from '@nestjs/common';
import { SlackActionRegistry } from '../registry';

@Controller('v1/slack/interactions')
export class SlackInteractionsController {
  constructor(private readonly actionRegistry: SlackActionRegistry) {}

  @Post()
  async handleInteraction(@Body() body: { payload: string }) {
    const payload = JSON.parse(body.payload);
    const blockId = payload.actions[0].block_id;

    const handler = this.actionRegistry.getHandler(blockId);
    await handler.handle(payload);
  }
}
