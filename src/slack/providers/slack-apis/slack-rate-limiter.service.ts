import { InjectQueue } from '@nestjs/bullmq';
import { Injectable } from '@nestjs/common';
import type { Queue } from 'bullmq';
import { AbstractRateLimiter, type RateLimiterOptions } from '../../../utils';
import { SLACK_QUEUE_NAME } from '../../constants/slack-queue.constants';
import { SLACK_METHOD_CONFIG } from './slack-method.config';

export interface SlackRateLimitHeaders {
  'retry-after'?: string;
  'x-rate-limit-limit'?: string;
  'x-rate-limit-remaining'?: string;
  'x-rate-limit-reset'?: string;
}

export interface SlackApiResponse {
  ok: boolean;
  error?: string;
  headers?: SlackRateLimitHeaders;
  response_metadata?: {
    warnings?: string[];
  };
}

@Injectable()
export class SlackRateLimiterService extends AbstractRateLimiter {
  /**
   * Map rate limits for each tier and request type
   */
  private readonly rateLimitMap: Map<
    string,
    { limit: number; remaining: number; reset: number }
  > = new Map();

  constructor(
    @InjectQueue(SLACK_QUEUE_NAME) queue: Queue,
    options: RateLimiterOptions,
  ) {
    super(queue, {
      ...options,
      requestsPerSecond: options.requestsPerSecond ?? 1,
      retryAttempts: options.retryAttempts || 3,
      retryDelay: options.retryDelay || 1000,
      maxConcurrent: options.maxConcurrent || 1,
      methodConfig: SLACK_METHOD_CONFIG,
    });
  }

  /**
   * Schedule a function to be executed with rate limiting.
   * @param fn The function to execute.
   * @returns The result of the function execution.
   */
  async schedule<T>(fn: () => Promise<T>, method?: string): Promise<T> {
    // Schedule the function
    const result = await super.schedule(fn, method);

    // Update the rate limits
    if (typeof result === 'object' && 'headers' in result && result.headers) {
      this.updateRateLimits(result.headers as SlackRateLimitHeaders);
    }

    return result;
  }

  /**
   * Check if the error is a rate limit error.
   * @param error The error to check.
   * @returns True if the error is a rate limit error, false otherwise.
   */
  isRateLimited(error: any): boolean {
    // Check if the error is a rate limit error
    if (error?.response?.status === 429) {
      return true;
    }

    // Check if the error is a rate limit error
    if (error?.data?.error === 'rate_limited') {
      return true;
    }

    // Check if the error is a rate limit error
    const rateLimitErrors = ['too_many_requests', 'ratelimited'];
    return rateLimitErrors.includes(error?.data?.error);
  }

  /**
   * Update the rate limits.
   * @param headers The headers to update the rate limits from.
   */
  private updateRateLimits(headers: SlackRateLimitHeaders): void {
    const tier = 'default';

    const limit = Number.parseInt(headers['x-rate-limit-limit'] || '0');
    const remaining = Number.parseInt(headers['x-rate-limit-remaining'] || '0');
    const reset = Number.parseInt(headers['x-rate-limit-reset'] || '0');

    // Handle the retry-after header
    if (headers['retry-after']) {
      const retryAfter = Number.parseInt(headers['retry-after']);
      const currentTime = Math.floor(Date.now() / 1000);

      this.rateLimitMap.set(tier, {
        limit: 0,
        remaining: 0,
        reset: currentTime + retryAfter + 2, // Add 2 second to the retry-after value
      });

      console.log(
        `Rate limit exceeded. Retry after ${retryAfter} seconds.`,
        this.rateLimitMap,
      );

      // We should return here as retry-after takes precedence
      return;
    }

    // Update the rate limits
    if (limit && reset) {
      // If reset time is in the past, don't update
      const currentTime = Math.floor(Date.now() / 1000);
      if (reset < currentTime) {
        return;
      }

      const currentLimits = this.rateLimitMap.get(tier);

      // Only update if we don't have limits yet or if this is a newer reset time
      if (!currentLimits || reset > currentLimits.reset) {
        this.rateLimitMap.set(tier, {
          limit,
          remaining,
          reset,
        });
      } else {
        // If we have current limits, only update the remaining count if it's lower
        if (currentLimits && remaining < currentLimits.remaining) {
          this.rateLimitMap.set(tier, {
            ...currentLimits,
            remaining,
          });
        }
      }
    }
  }
}
