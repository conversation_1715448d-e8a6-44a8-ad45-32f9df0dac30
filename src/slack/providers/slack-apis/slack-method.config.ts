import {
  SLACK_RATE_LIMIT_CONFIG,
  c__delete,
  c__getPermalink,
  c__postEphemeral,
  c__postMessage,
  c__unfurl,
  c__update,
  con__conversations__history,
  con__conversations__info,
  con__conversations__invite,
  con__conversations__join,
  con__conversations__kick,
  con__conversations__leave,
  con__conversations__list,
  con__conversations__members,
  con__conversations__replies,
  e__listEmojis,
  r__reactions__add,
  r__reactions__remove,
  t__team__info,
  u__users__info,
  u__users__list,
  ug__usergroups__list,
} from './slack.constants';

export const SLACK_METHOD_CONFIG = {
  // Tier 4 APIs
  [c__postMessage]: SLACK_RATE_LIMIT_CONFIG.tier_4,
  [c__postEphemeral]: SLACK_RATE_LIMIT_CONFIG.tier_4,
  [u__users__info]: SLACK_RATE_LIMIT_CONFIG.tier_4,
  [con__conversations__members]: SLACK_RATE_LIMIT_CONFIG.tier_4,

  // Tier 3 APIs
  [c__update]: SLACK_RATE_LIMIT_CONFIG.tier_3,
  [c__delete]: SLACK_RATE_LIMIT_CONFIG.tier_3,
  [con__conversations__join]: SLACK_RATE_LIMIT_CONFIG.tier_3,
  [con__conversations__leave]: SLACK_RATE_LIMIT_CONFIG.tier_3,
  [con__conversations__info]: SLACK_RATE_LIMIT_CONFIG.tier_3,
  [con__conversations__history]: SLACK_RATE_LIMIT_CONFIG.tier_3,
  [con__conversations__invite]: SLACK_RATE_LIMIT_CONFIG.tier_3,
  [con__conversations__kick]: SLACK_RATE_LIMIT_CONFIG.tier_3,
  [t__team__info]: SLACK_RATE_LIMIT_CONFIG.tier_3,
  [ug__usergroups__list]: SLACK_RATE_LIMIT_CONFIG.tier_3,
  [r__reactions__add]: SLACK_RATE_LIMIT_CONFIG.tier_3,
  [c__unfurl]: SLACK_RATE_LIMIT_CONFIG.tier_3,
  [con__conversations__replies]: SLACK_RATE_LIMIT_CONFIG.tier_3,

  // Tier 2 APIs
  [con__conversations__list]: SLACK_RATE_LIMIT_CONFIG.tier_2,
  [u__users__list]: SLACK_RATE_LIMIT_CONFIG.tier_2,
  [r__reactions__remove]: SLACK_RATE_LIMIT_CONFIG.tier_2,
  [e__listEmojis]: SLACK_RATE_LIMIT_CONFIG.tier_2,

  // Tier Special APIs
  [c__getPermalink]: SLACK_RATE_LIMIT_CONFIG.tier_special,
};
