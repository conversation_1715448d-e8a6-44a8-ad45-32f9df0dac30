/**
 * @tier tier_4
 * @apiMethod chat.postMessage
 * @description Post a message to a channel
 * @see https://api.slack.com/methods/chat.postMessage
 */
export const c__postMessage = 'chat.postMessage';

/**
 * @tier tier_3
 * @apiMethod chat.unfurl
 * @description Unfurl a link
 * @see https://api.slack.com/methods/chat.unfurl
 */
export const c__unfurl = 'chat.unfurl';

/**
 * @tier tier_2
 * @apiMethod emoji.list
 * @description List all emojis in a workspace
 * @see https://api.slack.com/methods/emoji.list
 */
export const e__listEmojis = 'emoji.list';

/**
 * @tier tier_3
 * @apiMethod chat.update
 * @description Update a message
 * @see https://api.slack.com/methods/chat.update
 */
export const c__update = 'chat.update';

/**
 * @tier tier_3
 * @apiMethod chat.delete
 * @description Delete a message
 * @see https://api.slack.com/methods/chat.delete
 */
export const c__delete = 'chat.delete';

/**
 * @tier tier_4
 * @apiMethod chat.postEphemeral
 * @description Post an ephemeral message to a user
 * @see https://api.slack.com/methods/chat.postEphemeral
 */
export const c__postEphemeral = 'chat.postEphemeral';

/**
 * @tier tier_2
 * @apiMethod usergroups.list
 * @description List all user groups in a workspace
 * @see https://api.slack.com/methods/usergroups.list
 */
export const ug__usergroups__list = 'usergroups.list';

/**
 * @tier tier_special
 * @apiMethod chat.getPermalink
 * @description Get a permalink to a message
 * @see https://api.slack.com/methods/chat.getPermalink
 */
export const c__getPermalink = 'chat.getPermalink';

/**
 * @tier tier_2
 * @apiMethod conversations.list
 * @description List conversations in a channel
 * @see https://api.slack.com/methods/conversations.list
 */
export const con__conversations__list = 'conversations.list';

/**
 * @tier tier_3
 * @apiMethod conversations.info
 * @description Get information about a conversation
 * @see https://api.slack.com/methods/conversations.info
 */
export const con__conversations__info = 'conversations.info';

/**
 * @tier tier_3
 * @apiMethod conversations.history
 * @description Get the history of a conversation
 * @see https://api.slack.com/methods/conversations.history
 */
export const con__conversations__history = 'conversations.history';

/**
 * @tier tier_3
 * @apiMethod conversations.replies
 * @description Get the replies to a message
 * @see https://api.slack.com/methods/conversations.replies
 */
export const con__conversations__replies = 'conversations.replies';

/**
 * @tier tier_4
 * @apiMethod conversations.members
 * @description Get the members of a conversation
 * @see https://api.slack.com/methods/conversations.members
 */
export const con__conversations__members = 'conversations.members';

/**
 * @tier tier_3
 * @apiMethod conversations.join
 * @description Join a conversation
 * @see https://api.slack.com/methods/conversations.join
 */
export const con__conversations__join = 'conversations.join';

/**
 * @tier tier_3
 * @apiMethod conversations.leave
 * @description Leave a conversation
 * @see https://api.slack.com/methods/conversations.leave
 */
export const con__conversations__leave = 'conversations.leave';

/**
 * @tier tier_3
 * @apiMethod conversations.invite
 * @description Invite a user to a conversation
 * @see https://api.slack.com/methods/conversations.invite
 */
export const con__conversations__invite = 'conversations.invite';

/**
 * @tier tier_3
 * @apiMethod conversations.kick
 * @description Kick a user from a conversation
 * @see https://api.slack.com/methods/conversations.kick
 */
export const con__conversations__kick = 'conversations.kick';

/**
 * @tier tier_2
 * @apiMethod users.list
 * @description List users in a workspace
 * @see https://api.slack.com/methods/users.list
 */
export const u__users__list = 'users.list';

/**
 * @tier tier_4
 * @apiMethod users.info
 * @description Get information about a user
 * @see https://api.slack.com/methods/users.info
 */
export const u__users__info = 'users.info';

/**
 * @tier tier_4
 * @apiMethod team.info
 * @description Get information about the team
 * @see https://api.slack.com/methods/team.info
 */
export const t__team__info = 'team.info';

/**
 * @tier tier_3
 * @apiMethod reactions.add
 * @description Add a reaction to a message
 * @see https://api.slack.com/methods/reactions.add
 */
export const r__reactions__add = 'reactions.add';

/**
 * @tier tier_2
 * @apiMethod reactions.remove
 * @description Remove a reaction from a message
 * @see https://api.slack.com/methods/reactions.remove
 */
export const r__reactions__remove = 'reactions.remove';

// Rate limit configuration
export const SLACK_RATE_LIMIT_CONFIG = {
  default: {
    requestsPerSecond: 1,
    maxConcurrent: 1,
  },
  tier_4: {
    requestsPerSecond: 3, // ~180 Per Minute, allows for bursts
    maxConcurrent: 10, // 10 concurrent requests
  },
  tier_3: {
    requestsPerSecond: 1.5, // ~90 per minute, allows for bursts
    maxConcurrent: 5, // Allow multiple concurrent requests for bursts
  },
  tier_2: {
    requestsPerSecond: 0.5, // 30 per minute, allowing for occasional bursts
    maxConcurrent: 5, // Allow multiple concurrent requests for bursts
  },
  tier_special: {
    requestsPerSecond: 3, // ~180 per minute, allowing for occasional bursts
    maxConcurrent: 10, // Allow only 10 concurrent request
  },
};
