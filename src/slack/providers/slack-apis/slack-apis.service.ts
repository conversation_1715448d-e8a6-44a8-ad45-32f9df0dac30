import { Inject, Injectable } from '@nestjs/common';
import {
  ChatDeleteArguments,
  ChatGetPermalinkArguments,
  ChatPostEphemeralArguments,
  ChatPostMessageArguments,
  ChatUnfurlArguments,
  ChatUpdateArguments,
  ConversationsHistoryArguments,
  ConversationsInfoArguments,
  ConversationsInviteArguments,
  ConversationsJoinArguments,
  ConversationsKickArguments,
  ConversationsLeaveArguments,
  ConversationsListArguments,
  ConversationsMembersArguments,
  ConversationsRepliesArguments,
  ReactionsAddArguments,
  ReactionsRemoveArguments,
  TeamInfoArguments,
  UsergroupsListArguments,
  UsersInfoArguments,
  UsersListArguments,
  ViewsOpenArguments,
  ViewsUpdateArguments,
  WebClient,
} from '@slack/web-api';
import { CUSTOM_LOGGER_TOKEN, ILogger, WebApiClient } from '../../../utils';
import { SentryService } from '../../../utils/filters/sentry-alerts.filter';
import { SlackRateLimiterService } from './slack-rate-limiter.service';
import {
  c__delete,
  c__getPermalink,
  c__postEphemeral,
  c__postMessage,
  c__unfurl,
  c__update,
  con__conversations__history,
  con__conversations__info,
  con__conversations__invite,
  con__conversations__join,
  con__conversations__kick,
  con__conversations__leave,
  con__conversations__list,
  con__conversations__members,
  con__conversations__replies,
  e__listEmojis,
  r__reactions__add,
  r__reactions__remove,
  t__team__info,
  u__users__info,
  u__users__list,
  ug__usergroups__list,
} from './slack.constants';

const SENTRY_TAG = 'slack-web-api-service';

// Add constants for the new methods
const v__views__open = 'v__views__open';
const v__views__update = 'v__views__update';

@Injectable()
export class SlackWebAPIService {
  constructor(
    @Inject(CUSTOM_LOGGER_TOKEN) private readonly logger: ILogger,
    private readonly slackRateLimiterService: SlackRateLimiterService,
    @Inject('Sentry') private readonly sentryService: SentryService,
  ) {}

  /**
   * @description List all user groups in a workspace, calls the usergroups.list method from the Slack API, WebClient {@link client.usergroups.list}
   * @see https://api.slack.com/methods/usergroups.list
   * @param token The Slack token
   * @param opts The options for the user groups
   * @returns The response from the Slack API
   */
  listUserGroups(token: string, opts?: UsergroupsListArguments) {
    const client = new WebApiClient(token);

    return this.slackRateLimiterService.schedule(
      () => client.usergroups.list(opts),
      ug__usergroups__list,
    );
  }

  /**
   * @description List all emojis in a workspace, calls the emoji.list method from the Slack API, WebClient {@link client.emoji.list}
   * @see https://api.slack.com/methods/emoji.list
   * @param token The Slack token
   * @returns The response from the Slack API
   */
  listWorkspaceEmojis(token: string) {
    const client = new WebApiClient(token);

    return this.slackRateLimiterService.schedule(
      () => client.emoji.list(),
      e__listEmojis,
    );
  }
  /**
   * @description Get information about the team, calls the team.info method from the Slack API, WebClient {@link client.team.info}
   * @see https://api.slack.com/methods/team.info
   * @param token The Slack token
   * @param opts The options for the team
   * @returns The response from the Slack API
   */
  getTeamInfo(token: string, opts?: TeamInfoArguments) {
    const client = new WebApiClient(token);

    return this.slackRateLimiterService.schedule(
      () => client.team.info(opts),
      t__team__info,
    );
  }

  /**
   * @description Unfurl a link, calls the chat.unfurl method from the Slack API, WebClient {@link client.chat.unfurl}
   * @see https://api.slack.com/methods/chat.unfurl
   * @param token The Slack token
   * @param opts The options for the unfurl
   * @returns The response from the Slack API
   */
  unfurlLink(token: string, opts: ChatUnfurlArguments) {
    const client = new WebApiClient(token);

    return this.slackRateLimiterService.schedule(
      () => client.chat.unfurl(opts),
      c__unfurl,
    );
  }

  /**
   * @description Send a message to a channel, calls the chat.postMessage method from the Slack API, WebClient {@link client.chat.postMessage}
   * @see https://api.slack.com/methods/chat.postMessage
   * @param token The Slack token
   * @param opts The options for the message
   * @returns The response from the Slack API
   */
  sendMessage(token: string, opts: ChatPostMessageArguments) {
    const client = new WebApiClient(token);

    try {
      return this.slackRateLimiterService.schedule(
        () => client.chat.postMessage(opts),
        c__postMessage,
      );
    } catch (error) {
      this.sentryService.captureException(error, {
        tag: SENTRY_TAG,
      });

      throw error;
    }
  }

  /**
   * @description Join a conversation or channel, calls the conversations.join method from the Slack API, WebClient {@link client.conversations.join}
   * @see https://api.slack.com/methods/conversations.join
   * @param token The Slack token
   * @param opts The options for the conversation
   * @returns The response from the Slack API
   */
  joinConversation(token: string, opts: ConversationsJoinArguments) {
    const client = new WebApiClient(token);

    return this.slackRateLimiterService.schedule(
      () => client.conversations.join(opts),
      con__conversations__join,
    );
  }

  /**
   * @description Leave a conversation or channel, calls the conversations.leave method from the Slack API, WebClient {@link client.conversations.leave}
   * @see https://api.slack.com/methods/conversations.leave
   * @param token The Slack token
   * @param opts The options for the conversation
   * @returns The response from the Slack API
   */
  leaveConversation(token: string, opts: ConversationsLeaveArguments) {
    const client = new WebApiClient(token);

    return this.slackRateLimiterService.schedule(
      () => client.conversations.leave(opts),
      con__conversations__leave,
    );
  }

  /**
   * @description Send an ephemeral message to a user, calls the chat.postEphemeral method from the Slack API, WebClient {@link client.chat.postEphemeral}
   * @see https://api.slack.com/methods/chat.postEphemeral
   * @param token The Slack token
   * @param opts The options for the ephemeral message
   * @returns The response from the Slack API
   */
  sendEphemeral(token: string, opts: ChatPostEphemeralArguments) {
    const client = new WebApiClient(token);

    return this.slackRateLimiterService.schedule(
      () => client.chat.postEphemeral(opts),
      c__postEphemeral,
    );
  }

  /**
   * @description Update a message, calls the chat.update method from the Slack API, WebClient {@link client.chat.update}
   * @see https://api.slack.com/methods/chat.update
   * @param token The Slack token
   * @param opts The options for the message
   * @returns The response from the Slack API
   */
  updateMessage(token: string, opts: ChatUpdateArguments) {
    const client = new WebApiClient(token);

    return this.slackRateLimiterService.schedule(
      () => client.chat.update(opts),
      c__update,
    );
  }

  /**
   * @description Delete a message, calls the chat.delete method from the Slack API, WebClient {@link client.chat.delete}
   * @see https://api.slack.com/methods/chat.delete
   */
  deleteMessage(token: string, opts: ChatDeleteArguments) {
    const client = new WebApiClient(token);

    return this.slackRateLimiterService.schedule(
      () => client.chat.delete(opts),
      c__delete,
    );
  }

  /**
   * @description List conversations/channels in a slack team, calls the conversations.list method from the Slack API, WebClient {@link client.conversations.list}
   * @see https://api.slack.com/methods/conversations.list
   * @param token The Slack token
   * @param opts The options for the conversations
   * @returns The response from the Slack API
   */
  listConversations(token: string, opts: ConversationsListArguments) {
    const client = new WebClient(token);

    return this.slackRateLimiterService.schedule(
      () => client.conversations.list(opts),
      con__conversations__list,
    );
  }

  /**
   * @description List users in a workspace, calls the users.list method from the Slack API, WebClient {@link client.users.list}
   * @see https://api.slack.com/methods/users.list
   * @param token The Slack token
   * @param opts The options for the users
   * @returns The response from the Slack API
   */
  listUsers(token: string, opts: UsersListArguments) {
    const client = new WebApiClient(token);

    return this.slackRateLimiterService.schedule(
      () => client.users.list(opts),
      u__users__list,
    );
  }

  /**
   * @description Get information about a user, calls the users.info method from the Slack API, WebClient {@link client.users.info}
   * @see https://api.slack.com/methods/users.info
   * @param token The Slack token
   * @param opts The options for the user
   * @returns The response from the Slack API
   */
  getUserInfo(token: string, opts: UsersInfoArguments) {
    const client = new WebApiClient(token);

    return this.slackRateLimiterService.schedule(
      () => client.users.info(opts),
      u__users__info,
    );
  }

  /**
   * @description Get a permalink to a message, calls the chat.getPermalink method from the Slack API, WebClient {@link client.chat.getPermalink}
   * @see https://api.slack.com/methods/chat.getPermalink
   * @param token The Slack token
   * @param opts The options for the permalink
   * @returns The response from the Slack API
   */
  getPermalink(token: string, opts: ChatGetPermalinkArguments) {
    const client = new WebApiClient(token);

    return this.slackRateLimiterService.schedule(
      () => client.chat.getPermalink(opts),
      c__getPermalink,
    );
  }

  /**
   * @description Get the members of a conversation, calls the conversations.members method from the Slack API, WebClient {@link client.conversations.members}
   * @see https://api.slack.com/methods/conversations.members
   * @param token The Slack token
   * @param opts The options for the conversation
   * @returns The response from the Slack API
   */
  getConversationMembers(token: string, opts: ConversationsMembersArguments) {
    const client = new WebApiClient(token);

    return this.slackRateLimiterService.schedule(
      () => client.conversations.members(opts),
      con__conversations__members,
    );
  }

  /**
   * @description Get information about a conversation, calls the conversations.info method from the Slack API, WebClient {@link client.conversations.info}
   * @see https://api.slack.com/methods/conversations.info
   * @param token The Slack token
   * @param opts The options for the conversation
   * @returns The response from the Slack API
   */
  getConversationInfo(token: string, opts: ConversationsInfoArguments) {
    const client = new WebApiClient(token);

    return this.slackRateLimiterService.schedule(
      () => client.conversations.info(opts),
      con__conversations__info,
    );
  }

  /**
   * @description Get the replies to a message, calls the conversations.replies method from the Slack API, WebClient {@link client.conversations.replies}
   * @see https://api.slack.com/methods/conversations.replies
   * @param token The Slack token
   * @param opts The options for the conversation
   * @returns The response from the Slack API
   */
  getConversationReplies(token: string, opts: ConversationsRepliesArguments) {
    const client = new WebApiClient(token);

    return this.slackRateLimiterService.schedule(
      () => client.conversations.replies(opts),
      con__conversations__replies,
    );
  }

  /**
   * @description Get the history of a conversation, calls the conversations.history method from the Slack API, WebClient {@link client.conversations.history}
   * @see https://api.slack.com/methods/conversations.history
   * @param token The Slack token
   * @param opts The options for the conversation
   * @returns The response from the Slack API
   */
  getConversationHistory(token: string, opts: ConversationsHistoryArguments) {
    const client = new WebApiClient(token);

    return this.slackRateLimiterService.schedule(
      () => client.conversations.history(opts),
      con__conversations__history,
    );
  }

  /**
   * @description Invite a user to a conversation, calls the conversations.invite method from the Slack API, WebClient {@link client.conversations.invite}
   * @see https://api.slack.com/methods/conversations.invite
   */
  inviteUserToConversation(token: string, opts: ConversationsInviteArguments) {
    const client = new WebApiClient(token);

    return this.slackRateLimiterService.schedule(
      () => client.conversations.invite(opts),
      con__conversations__invite,
    );
  }

  /**
   * @description Kick a user from a conversation, calls the conversations.kick method from the Slack API, WebClient {@link client.conversations.kick}
   * @see https://api.slack.com/methods/conversations.kick
   */
  kickUserFromConversation(token: string, opts: ConversationsKickArguments) {
    const client = new WebApiClient(token);

    return this.slackRateLimiterService.schedule(
      () => client.conversations.kick(opts),
      con__conversations__kick,
    );
  }

  /**
   * @description Add a reaction to a message, calls the reactions.add method from the Slack API, WebClient {@link client.reactions.add}
   * @see https://api.slack.com/methods/reactions.add
   * @param token The Slack token
   * @param opts The options for the reaction
   * @returns The response from the Slack API
   */
  addReactionToMessage(token: string, opts: ReactionsAddArguments) {
    const client = new WebApiClient(token);

    return this.slackRateLimiterService.schedule(
      () => client.reactions.add(opts),
      r__reactions__add,
    );
  }

  /**
   * @description Remove a reaction from a message, calls the reactions.remove method from the Slack API, WebClient {@link client.reactions.remove}
   * @see https://api.slack.com/methods/reactions.remove
   * @param token The Slack token
   * @param opts The options for the reaction
   * @returns The response from the Slack API
   */
  removeReactionFromMessage(token: string, opts: ReactionsRemoveArguments) {
    const client = new WebApiClient(token);

    return this.slackRateLimiterService.schedule(
      () => client.reactions.remove(opts),
      r__reactions__remove,
    );
  }

  /**
   * @description Open a modal view, calls the views.open method from the Slack API, WebClient {@link client.views.open}
   * @see https://api.slack.com/methods/views.open
   * @param token The Slack token
   * @param opts The options for the view
   * @returns The response from the Slack API
   */
  openView(token: string, opts: ViewsOpenArguments) {
    const client = new WebApiClient(token);

    return this.slackRateLimiterService.schedule(
      () => client.views.open(opts),
      v__views__open,
    );
  }

  /**
   * @description Update a modal view, calls the views.update method from the Slack API, WebClient {@link client.views.update}
   * @see https://api.slack.com/methods/views.update
   * @param token The Slack token
   * @param opts The options for the view
   * @returns The response from the Slack API
   */
  updateView(token: string, opts: ViewsUpdateArguments) {
    const client = new WebApiClient(token);

    return this.slackRateLimiterService.schedule(
      () => client.views.update(opts),
      v__views__update,
    );
  }

  /**
   * @description Post a message to a channel, calls the chat.postMessage method from the Slack API, WebClient {@link client.chat.postMessage}
   * @see https://api.slack.com/methods/chat.postMessage
   * @param token The Slack token
   * @param opts The options for the message
   * @returns The response from the Slack API
   */
  postMessage(token: string, opts: ChatPostMessageArguments) {
    return this.sendMessage(token, opts);
  }
}
