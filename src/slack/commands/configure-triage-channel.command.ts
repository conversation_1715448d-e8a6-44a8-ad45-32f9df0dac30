import { Inject, Injectable } from '@nestjs/common';
import { ChannelsRepository } from '../../database/entities/channels/repositories/channels.repository';
import { CUSTOM_LOGGER_TOKEN, type ILogger } from '../../utils/logger';
import { ConfigureTriageChannelComposite } from '../blocks/components/composite/channels/configure-triage-channel.composite';
import { SlackCommand } from '../decorators/slack-command.decorator';
import { EnrichedSlackArgsContext } from '../services/slack-action-discovery.service';

@Injectable()
@SlackCommand('/configure-triage')
export class ConfigureTriageChannelCommand {
  constructor(
    @Inject(CUSTOM_LOGGER_TOKEN)
    private readonly logger: ILogger,

    private readonly triageChannelBlock: ConfigureTriageChannelComposite,
    private readonly channelsRepository: ChannelsRepository,
  ) {}

  async handle(args: EnrichedSlackArgsContext) {
    const { command, client, body } = args;

    const { channel_id, channel_name, user_id, user_name } = command;

    this.logger.log(
      `Team config command received for channel: ${channel_name} [${channel_id}], by ${user_name} [${user_id}]`,
    );

    // Check if we have a trigger_id to open a modal
    if (body.trigger_id) {
      // Show the configuration form
      await client.views.open({
        trigger_id: body.trigger_id,
        view: {
          type: 'modal',
          private_metadata: JSON.stringify({
            channelId: channel_id,
          }),
          callback_id: 'configure_triage_channel_submit',
          title: {
            type: 'plain_text',
            text: 'Configure Triage Channel',
            emoji: true,
          },
          blocks: this.triageChannelBlock.build().blocks,
          submit: {
            type: 'plain_text',
            text: 'Save',
          },
          close: {
            type: 'plain_text',
            text: 'Cancel',
          },
        },
      });
    } else {
      // If we don't have a trigger_id, send a persistent message with a configuration button
      await client.chat.postMessage({
        channel: channel_id,
        blocks: [
          {
            type: 'section',
            text: {
              type: 'mrkdwn',
              text: 'This channel needs to be configured before it can be used.',
            },
          },
          {
            type: 'actions',
            elements: [
              {
                type: 'button',
                text: {
                  type: 'plain_text',
                  text: 'Configure channel',
                  emoji: true,
                },
                style: 'primary',
                action_id: 'configure_channel_action',
                value: JSON.stringify({
                  channelId: channel_id,
                  channelName: channel_name,
                }),
              },
            ],
          },
        ],
        text: 'This channel needs to be configured before it can be used. Please click the Configure Channel button to get started.',
      });
    }
  }
}
