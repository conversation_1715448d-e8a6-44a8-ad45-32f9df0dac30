import { Inject, Injectable } from '@nestjs/common';
import { TeamRelationshipType } from '../../database/entities/mappings';
import { TeamChannelMapsRepository } from '../../database/entities/mappings/repositories/team-channel-maps.repository';
import { CUSTOM_LOGGER_TOKEN, type ILogger } from '../../utils/logger';
import { CreateTicketsBlocksComposite } from '../blocks/components/composite/tickets/create-tickets-blocks.composite';
import { SettingsCore } from '../core';
import { SlackCommand } from '../decorators/slack-command.decorator';
import { DecoratedSlackCommandMiddlewareArgs } from '../event-handlers';

@Injectable()
@SlackCommand('/ticket')
export class CreateTicketCommand {
  static readonly CREATE_TICKET_VIEW_CALLBACK_ID = 'create_ticket_modal_submit';

  constructor(
    @Inject(CUSTOM_LOGGER_TOKEN) private readonly logger: ILogger,

    // Database Repositories
    private readonly teamChannelMapsRepo: TeamChannelMapsRepository,

    // Blocks
    private readonly createTicketBlock: CreateTicketsBlocksComposite,
    private readonly settingsCore: SettingsCore,
  ) {}

  async handle(args: DecoratedSlackCommandMiddlewareArgs) {
    const { command, context, body } = args;
    const { client, installation, organization } = context;

    const { channel_id, channel_name, user_id, user_name } = command;

    try {
      this.logger.log(
        `Create ticket command received for channel: ${channel_name} [${channel_id}], by ${user_name} [${user_id}]`,
      );

      // Get the platform team linked with the channel
      const teamChannelMapping = await this.teamChannelMapsRepo.findByCondition(
        {
          where: {
            channel: { channelId: channel_id },
            relationshipType: TeamRelationshipType.PRIMARY,
            installation: { id: installation.id },
            organization: { id: organization.id },
          },
          relations: { platformTeam: true },
        },
      );

      // If no primary team channel mapping is found, show the configuration form
      if (!teamChannelMapping) {
        this.logger.debug(
          `No primary team channel mapping found for channel: ${channel_name} [${channel_id}]`,
        );
      }

      // Get the platform team linked with the channel
      const platformTeam = teamChannelMapping?.platformTeam;
      if (platformTeam) {
        // Check if the ticket command is enabled for the platform team
        const ticketCommandEnabled = await this.settingsCore.getValue(
          'ticket_command',
          {
            platformTeam,
            workspace: installation,
          },
        );

        // If the ticket command is not enabled, show a message to the user
        if (!ticketCommandEnabled) {
          this.logger.error(
            `Ticket command is not enabled for platform team: ${platformTeam.uid}`,
          );

          // Send a message to the user to configure the channel
          await client.chat.postMessage({
            channel: body.user_id,
            text: `Ticket command is not enabled for platform team: ${platformTeam.uid} which is linked to channel <#${channel_id}>. Please enable the ticket command first.`,
          });

          return;
        }
      }

      // Show the configuration form
      await client.views.open({
        token: installation.botToken,
        trigger_id: body.trigger_id,
        view: {
          type: 'modal',
          private_metadata: JSON.stringify({
            channelId: channel_id,
            platformTeamId: platformTeam?.uid,
          }),
          callback_id: CreateTicketCommand.CREATE_TICKET_VIEW_CALLBACK_ID,
          title: {
            type: 'plain_text',
            text: 'Create ticket',
            emoji: true,
          },
          blocks: this.createTicketBlock.build(platformTeam?.uid, channel_id)
            .blocks,
          submit: {
            type: 'plain_text',
            text: 'Save',
          },
          close: {
            type: 'plain_text',
            text: 'Cancel',
          },
        },
      });
    } catch (error) {
      if (error instanceof Error) {
        this.logger.error(
          `Error handling create ticket command for channel: ${channel_name} [${channel_id}] by ${user_name} [${user_id}]`,
          error.stack,
        );
      } else {
        console.error(
          `Error handling create ticket command for channel: ${channel_name} [${channel_id}] by ${user_name} [${user_id}]`,
          error,
        );
      }

      try {
        // Send a message to the user to configure the channel
        await client.chat.postMessage({
          channel: body.user_id,
          text: 'An error occurred while handling the create ticket command. Please try again later or if the issue persists, contact support.',
        });
      } catch (chatPostMessageError) {
        if (chatPostMessageError instanceof Error) {
          this.logger.error(
            `Error sending message to user to configure the channel: ${channel_name} [${channel_id}] by ${user_name} [${user_id}]`,
            chatPostMessageError.stack,
          );
        } else {
          console.error(
            `Error sending message to user to configure the channel: ${channel_name} [${channel_id}] by ${user_name} [${user_id}]`,
            chatPostMessageError,
          );
        }
      }
    }
  }
}
