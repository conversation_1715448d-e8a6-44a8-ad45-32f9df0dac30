import { Inject, Injectable } from '@nestjs/common';
import { CUSTOM_LOGGER_TOKEN, type ILogger } from '../../utils/logger';
import { SelectChannelTeamComposite } from '../blocks/components/composite/channels/select-channel-team.composite';
import { SlackCommand } from '../decorators/slack-command.decorator';
import { EnrichedSlackArgsContext } from '../services/slack-action-discovery.service';

@Injectable()
@SlackCommand('/configure-team')
export class TeamConfigCommand {
  constructor(
    @Inject(CUSTOM_LOGGER_TOKEN)
    private readonly logger: ILogger,

    private readonly teamConfigBlock: SelectChannelTeamComposite,
  ) {}

  async handle(args: EnrichedSlackArgsContext) {
    const { command, client, body } = args;

    const { channel_id, channel_name, user_id, user_name } = command;

    this.logger.log(
      `Team config command received for channel: ${channel_name} [${channel_id}], by ${user_name} [${user_id}]`,
    );

    // Show the configuration form
    await client.views.open({
      trigger_id: body.trigger_id,
      view: {
        type: 'modal',
        private_metadata: JSON.stringify({
          channelId: channel_id,
          ephemeralResponseUrl: body.response_url,
        }),
        callback_id: 'team_config_modal_submit',
        title: {
          type: 'plain_text',
          text: 'Configure Team',
          emoji: true,
        },
        blocks: this.teamConfigBlock.build().blocks,
        submit: {
          type: 'plain_text',
          text: 'Save',
        },
        close: {
          type: 'plain_text',
          text: 'Cancel',
        },
      },
    });
  }
}
