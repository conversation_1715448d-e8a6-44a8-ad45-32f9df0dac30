import { BullModule } from '@nestjs/bullmq';
import { CacheModule } from '@nestjs/cache-manager';
import { Module } from '@nestjs/common';
import { DiscoveryModule } from '@nestjs/core';
import { APP_FILTER } from '@nestjs/core';
import { TypeOrmModule } from '@nestjs/typeorm';
import { SentryModule } from '@sentry/nestjs/setup';
import { AiModule } from './ai/ai.module';
import { AppController } from './app.controller';
import { AppService } from './app.service';
import { AuthModule } from './auth/auth.module';
import { CommonModule } from './common/common.module';
import { ApiExceptionFilter } from './common/filters/api-exception.filter';
import { createRedisModule } from './common/redis/redis.module';
import { ConfigModule } from './config/config.module';
import { ConfigKeys, ConfigService } from './config/config.service';
import { entities } from './database/entities';
import { SeedsManager } from './database/seeds/manager';
import { SeedsModule } from './database/seeds/seeds.module';
import { PlatformModule } from './platform/platform.module';
import { SlackModule } from './slack/slack.module';
import { getSlackDatabaseOptions } from './utils/db';

@Module({
  imports: [
    // Register the sentry module
    SentryModule.forRoot(),

    ConfigModule,
    DiscoveryModule,

    createRedisModule(),
    CommonModule,

    // Register the cache module
    CacheModule.register({ isGlobal: true }),

    // Register the bullmq module with proper configuration
    BullModule.forRootAsync({
      imports: [ConfigModule],
      inject: [ConfigService],
      useFactory: (configService: ConfigService) => ({
        connection: {
          host: configService.get(ConfigKeys.REDIS_HOST),
          port: Number.parseInt(configService.get(ConfigKeys.REDIS_PORT)),
          username: configService.get(ConfigKeys.REDIS_USERNAME),
          password: configService.get(ConfigKeys.REDIS_PASSWORD),
          enableReadyCheck: false,
          lazyConnect: true,
          maxRetriesPerRequest: null,
          prefix: 'slack-bull',
        },
        defaultJobOptions: {
          removeOnComplete: {
            age: 24 * 3600, // keep completed jobs for 24 hours
            count: 1000, // keep last 1000 completed jobs
          },
          removeOnFail: {
            age: 24 * 3600, // keep failed jobs for 24 hours
          },
          attempts: 3, // Add retry attempts
          backoff: {
            type: 'exponential',
            delay: 1000,
          },
          timeout: 30000, // Add job timeout
          stalledInterval: 30000, // Check for stalled jobs every 30 seconds
          lockDuration: 30000, // Lock job for 30 seconds
        },
      }),
    }),

    // Register the typeorm module
    TypeOrmModule.forRootAsync({
      imports: [ConfigModule],
      inject: [ConfigService],
      useFactory: async (configService: ConfigService) => {
        const moreSlackOptions = await getSlackDatabaseOptions(configService);

        return {
          ...moreSlackOptions,

          // Overrides
          type: 'postgres',
          host: configService.get(ConfigKeys.DB_HOST),
          port: Number.parseInt(configService.get(ConfigKeys.DB_PORT)),
          username: configService.get(ConfigKeys.DB_USERNAME),
          password: configService.get(ConfigKeys.DB_PASSWORD),
          database: configService.get(ConfigKeys.DB_NAME),
          entities,
          schema: 'public',
        };
      },
    }),

    SlackModule,
    PlatformModule,
    AuthModule,
    SeedsModule,
    AiModule.forRootAsync({
      imports: [ConfigModule],
      useFactory: async (configService: ConfigService) => {
        return {
          providers: [
            {
              name: 'openai',
              provider: 'openai',
              config: {
                apiKey: configService.get(ConfigKeys.OPENAI_API_KEY),
              },
            },
            {
              name: 'claude',
              provider: 'claude',
              config: {
                apiKey: configService.get(ConfigKeys.CLAUDE_API_KEY),
              },
            },
          ],
          defaultProvider: 'openai',
        };
      },
      inject: [ConfigService],
    }),
  ],
  controllers: [AppController],
  providers: [
    AppService,
    SeedsManager,
    {
      provide: APP_FILTER,
      useClass: ApiExceptionFilter,
    },
  ],
})
export class AppModule {}
