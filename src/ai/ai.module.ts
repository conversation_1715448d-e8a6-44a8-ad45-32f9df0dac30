import { CACHE_MANAGER } from '@nestjs/cache-manager';
import { DynamicModule, Module, Provider } from '@nestjs/common';
import { TypeOrmModule } from '@nestjs/typeorm';
import { Cache } from 'cache-manager';
import { CommonModule } from '../common/common.module';
import { ConfigModule } from '../config/config.module';
import { Installations } from '../database/entities';
import { Prompts } from '../database/entities/prompts/prompts.entity';
import { PlatformTeams } from '../database/entities/teams/teams.entity';
import { CUSTOM_LOGGER_TOKEN, ILogger } from '../utils';
import { AiController } from './ai.controller';
import { AiService } from './ai.service';
import { AI_PROVIDER } from './constants/provider.token';
import {
  AiModuleAsyncOptions,
  AiModuleOptions,
  AiModuleOptionsFactory,
} from './interfaces/ai-module-options.interface';
import {
  I<PERSON>iProvider,
  IAiProviderConfig,
} from './interfaces/ai-provider.interface';
import { AiProviderRegistry } from './providers/ai-provider-registry';
import { ClaudeAiProvider } from './providers/claude-ai.provider';
import { OpenAiProvider } from './providers/open-ai.provider';

export interface ProviderConfig {
  name: string;
  provider: 'grok' | 'openai' | 'deepseek' | 'claude' | 'gemini';
  config: IAiProviderConfig;
}

const AI_ENTITIES = [Prompts, Installations, PlatformTeams];

@Module({
  imports: [ConfigModule, CommonModule, TypeOrmModule],
  exports: [CommonModule, TypeOrmModule],
  controllers: [AiController],
})
// biome-ignore lint/complexity/noStaticOnlyClass: <explanation>
export class AiModule {
  static forRoot(options: AiModuleOptions): DynamicModule {
    return {
      module: AiModule,
      imports: [CommonModule, TypeOrmModule.forFeature(AI_ENTITIES)],
      providers: [
        {
          // Registry is supplied via the AI_PROVIDER factory
          provide: AI_PROVIDER,
          useFactory: async (logger: ILogger) => {
            return AiModule.createProviderRegistry(options, logger);
          },
          inject: [CUSTOM_LOGGER_TOKEN],
        },
        AiService,
      ],
      exports: [AiService, TypeOrmModule],
      global: true,
    };
  }

  static forRootAsync(options: AiModuleAsyncOptions): DynamicModule {
    return {
      module: AiModule,
      imports: [
        ...(options.imports || []),
        CommonModule,
        // Import TypeOrmModule for Prompts entity
        TypeOrmModule.forFeature(AI_ENTITIES),
      ],
      providers: [
        // Individual providers are instantiated by createProviderRegistry
        AiProviderRegistry,
        ...AiModule.createAsyncProviders(options),
        {
          provide: AI_PROVIDER,
          useFactory: async (
            aiModuleOptions: AiModuleOptions,
            logger: ILogger,
          ) => {
            return AiModule.createProviderRegistry(aiModuleOptions, logger);
          },
          inject: [AI_MODULE_OPTIONS, CUSTOM_LOGGER_TOKEN],
        },
        {
          provide: AiService,
          useFactory: (
            logger: ILogger,
            aiProvider: IAiProvider,
            cacheManager: Cache,
            promptsRepo,
          ) => {
            return new AiService(logger, aiProvider, cacheManager, promptsRepo);
          },
          inject: [
            CUSTOM_LOGGER_TOKEN,
            AI_PROVIDER,
            CACHE_MANAGER,
            'PromptsRepository',
          ],
        },
      ],
      exports: [AiService, TypeOrmModule],
      global: true,
    };
  }

  private static createAsyncProviders(
    options: AiModuleAsyncOptions,
  ): Provider[] {
    if (options.useExisting || options.useFactory) {
      return [AiModule.createAsyncOptionsProvider(options)];
    }

    return [
      // biome-ignore lint/complexity/noThisInStatic: <explanation>
      this.createAsyncOptionsProvider(options),
      {
        provide: options.useClass,
        useClass: options.useClass,
      },
    ];
  }

  private static createAsyncOptionsProvider(
    options: AiModuleAsyncOptions,
  ): Provider {
    if (options.useFactory) {
      return {
        provide: AI_MODULE_OPTIONS,
        useFactory: options.useFactory,
        inject: options.inject || [],
      };
    }

    return {
      provide: AI_MODULE_OPTIONS,
      useFactory: async (optionsFactory: AiModuleOptionsFactory) =>
        await optionsFactory.createAiModuleOptions(),
      inject: [options.useExisting || options.useClass],
    };
  }

  private static async createProviderRegistry(
    options: AiModuleOptions,
    logger: ILogger,
  ): Promise<IAiProvider> {
    const registry = new AiProviderRegistry();

    // Initialize each provider
    await Promise.all(
      options.providers.map(async (providerConfig) => {
        const provider = AiModule.instantiateProvider(providerConfig, logger);
        if (!provider) {
          // Deprecated / unsupported provider – already logged, just ignore
          return;
        }
        await provider.initialize(providerConfig.config);
        registry.registerProvider(providerConfig.name, provider);
      }),
    );

    // Set the default provider if specified
    if (options.defaultProvider) {
      if (options.providers.some((p) => p.name === options.defaultProvider)) {
        registry.setActiveProvider(options.defaultProvider);
      } else {
        throw new Error(
          `Default provider "${options.defaultProvider}" not found in provider list`,
        );
      }
    }

    return registry;
  }

  private static instantiateProvider(
    providerConfig: ProviderConfig,
    logger: ILogger,
  ): IAiProvider {
    switch (providerConfig.provider) {
      case 'grok': {
        logger.warn(
          'Grok provider requested but no longer supported – skipping.',
          AiModule.name,
        );
        return null;
      }
      case 'claude': {
        return new ClaudeAiProvider(logger);
      }
      case 'openai': {
        return new OpenAiProvider(logger);
      }
      case 'deepseek': {
        throw new Error('DeepSeek provider not implemented yet');
      }
      case 'gemini': {
        throw new Error('Gemini provider not implemented yet');
      }
      default: {
        throw new Error(`Unknown AI provider: ${providerConfig.provider}`);
      }
    }
  }
}

export const AI_MODULE_OPTIONS = 'AI_MODULE_OPTIONS';
