import { ModuleMetadata, Type } from '@nestjs/common';
import { ProviderConfig } from '../ai.module';

export interface AiModuleOptions {
  providers: ProviderConfig[];
  defaultProvider?: string;
}

export interface AiModuleAsyncOptions extends Pick<ModuleMetadata, 'imports'> {
  useExisting?: Type<AiModuleOptionsFactory>;
  useClass?: Type<AiModuleOptionsFactory>;
  useFactory?: (...args: any[]) => Promise<AiModuleOptions> | AiModuleOptions;
  inject?: any[];
}

export interface AiModuleOptionsFactory {
  createAiModuleOptions(): Promise<AiModuleOptions> | AiModuleOptions;
}
