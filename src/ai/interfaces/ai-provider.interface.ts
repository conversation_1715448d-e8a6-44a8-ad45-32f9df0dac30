import { PromptType } from '../constants/prompt-types.enum';
import { TeamPrompts } from './team-prompts.interface';

export interface IAiModelConfig {
  modelId: string;
  temperature?: number;
  maxTokens?: number;
  topP?: number;
  frequencyPenalty?: number;
  presencePenalty?: number;
  additionalParams?: Record<string, any>;
}

export interface IAiProviderConfig {
  apiKey: string;
  organizationId?: string;
  baseUrl?: string;
  defaultModel?: string;
  models?: Record<string, IAiModelConfig>;
}

export interface IAiPromptRequest {
  prompt: string;
  conversation?: string;
  maxTokens?: number;
  temperature?: number;
}

export interface IAiPromptResponse<T> {
  success: boolean;
  data?: T;
  error?: string;
  rawResponse?: any;
}

export interface IAiTicketDetectionResponse {
  requiresSupportTicket: boolean;
}

export interface IAiSentimentAnalysisResponse {
  sentiment: 'positive' | 'negative' | 'neutral';
  confidence: number;
  urgency: 'low' | 'medium' | 'high';
  frustrationLevel: 'low' | 'medium' | 'high';
  satisfactionLevel: 'low' | 'medium' | 'high';
}

export interface IAiUrgencyDetectionResponse {
  urgency: string;
  confidence: number;
  businessImpact: 'low' | 'medium' | 'high';
  timeSensitivity: 'low' | 'medium' | 'high';
  affectedUsersCount: number | 'unknown';
  severityLevel: 'low' | 'medium' | 'high';
}

export interface IAiTeamRoutingResponse {
  recommendedTeam: string;
  confidence: number;
  technicalComplexity: 'low' | 'medium' | 'high';
  domainExpertise: string[];
  products: string[];
}

export interface IAiCustomFieldsResponse {
  fields: Record<string, string>;
  confidence: Record<string, number>;
}

/**
 * Parameters for AI completion requests
 */
export interface IAiCompletionParams {
  prompt: string;
  temperature?: number;
  maxTokens?: number;
  enabledExtendedThinking?: boolean;
}

export interface IAiProvider {
  // Configuration
  initialize(config: IAiProviderConfig): Promise<void>;

  // Core prompting functionality
  detectTicket(
    conversation: string,
    teamId?: string,
  ): Promise<IAiPromptResponse<IAiTicketDetectionResponse>>;
  analyzeSentiment(
    conversation: string,
    teamId?: string,
  ): Promise<IAiPromptResponse<IAiSentimentAnalysisResponse>>;
  detectUrgency(
    conversation: string,
    teamId?: string,
    urgencyLevels?: string[],
  ): Promise<IAiPromptResponse<IAiUrgencyDetectionResponse>>;
  routeToTeam(
    conversation: string,
    teamId?: string,
  ): Promise<IAiPromptResponse<IAiTeamRoutingResponse>>;
  extractCustomFields(
    conversation: string,
    fields?: string[],
    teamId?: string,
  ): Promise<IAiPromptResponse<IAiCustomFieldsResponse>>;

  // Raw prompt execution
  executePrompt<T>(request: IAiPromptRequest): Promise<IAiPromptResponse<T>>;

  // Set a custom prompt
  setPrompt(type: PromptType, prompt: string): void;

  // Model management
  getAvailableModels(): string[];
  setActiveModel(modelId: string): void;
  getActiveModel(): string;
  addModel(modelId: string, config: IAiModelConfig): void;

  // New methods
  setTeamPrompts(teamId: string, prompts: TeamPrompts): void;

  getPrompt(promptType: PromptType, teamId?: string): string;

  /**
   * Completes a prompt with the AI provider
   * @param params Parameters for completion including prompt, temperature, maxTokens and extended thinking flag
   * @returns The complete response data object
   */
  complete(params: IAiCompletionParams): Promise<any>;

  /**
   * Generates a title for a conversation
   */
  generateTitle(
    conversation: string,
    teamId?: string,
  ): Promise<IAiPromptResponse<IAiTitleGenerationResponse>>;

  /**
   * Generates a description for a conversation
   */
  generateDescription(
    conversation: string,
    teamId?: string,
  ): Promise<IAiPromptResponse<IAiDescriptionGenerationResponse>>;
}

export interface IAiTitleGenerationResponse {
  title: string;
}

export interface IAiDescriptionGenerationResponse {
  description: string;
}
