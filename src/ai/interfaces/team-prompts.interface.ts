import { PromptType } from '../constants/prompt-types.enum';

/**
 * Interface for team prompts to improve type safety
 */
export interface TeamPrompts {
  [PromptType.TICKET_DETECTION]?: string;
  [PromptType.SENTIMENT_ANALYSIS]?: string;
  [PromptType.URGENCY_DETECTION]?: string;
  [PromptType.CUSTOM_FIELDS]?: string;
  [PromptType.TITLE_GENERATION]?: string;
  [PromptType.DESCRIPTION_GENERATION]?: string;
}

/**
 * Options for urgency detection
 */
export interface UrgencyDetectionOptions {
  defaultUrgency?: string;
  enableExtendedThinking?: boolean;
  teamId?: string;
  installationId?: string;
  organizationId?: string;
}

/**
 * Cache entry interface
 */
export interface TeamPromptsCache {
  prompts: TeamPrompts;
  timestamp: number;
}
