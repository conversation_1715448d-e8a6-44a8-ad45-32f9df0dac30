import { Injectable } from '@nestjs/common';
import { ConfigService } from '@nestjs/config';
import { ConfigKeys } from '../../config/config.service';
import { MODEL_CONFIGS } from '../constants/ai-providers.constants';
import {
  AiModuleOptions,
  AiModuleOptionsFactory,
} from '../interfaces/ai-module-options.interface';
import { IAiProviderConfig } from '../interfaces/ai-provider.interface';

@Injectable()
export class AiConfigService implements AiModuleOptionsFactory {
  constructor(private configService: ConfigService) {}

  async createAiModuleOptions(): Promise<AiModuleOptions> {
    return {
      providers: [
        {
          name: 'openai',
          provider: 'openai',
          config: this.getOpenAIConfig(),
        },
        {
          name: 'claude',
          provider: 'claude',
          config: this.getClaudeConfig(),
        },
      ],
      defaultProvider: 'openai',
    };
  }

  private getClaudeConfig(): IAiProviderConfig {
    // Find default Claude model from MODEL_CONFIGS
    const defaultClaudeModel =
      Object.keys(MODEL_CONFIGS)
        .filter((key) => key.startsWith('claude-'))
        .find((key) => (MODEL_CONFIGS[key] as any).isDefault) ||
      Object.keys(MODEL_CONFIGS).find((key) => key.startsWith('claude-'));

    return {
      apiKey: this.configService.get<string>(ConfigKeys.CLAUDE_API_KEY),
      baseUrl: this.configService.get<string>('CLAUDE_API_URL'),
      defaultModel:
        this.configService.get<string>('CLAUDE_DEFAULT_MODEL') ||
        defaultClaudeModel ||
        'claude-3-7-sonnet-20250219', // Fallback if no Claude models in constants
    };
  }

  private getOpenAIConfig(): IAiProviderConfig {
    // Find default OpenAI model from MODEL_CONFIGS
    const openAIModelKeys = Object.keys(MODEL_CONFIGS).filter(
      (key) => key.startsWith('gpt-') || key.startsWith('o3-'),
    );

    const defaultOpenAIModel =
      openAIModelKeys.find((key) => MODEL_CONFIGS[key].isDefault) ||
      openAIModelKeys[0];

    return {
      apiKey: this.configService.get<string>(ConfigKeys.OPENAI_API_KEY),
      baseUrl: this.configService.get<string>('OPENAI_API_URL'),
      defaultModel:
        this.configService.get<string>('OPENAI_DEFAULT_MODEL') ||
        defaultOpenAIModel ||
        'o3-mini-2025-01-31', // Fallback if no OpenAI models in constants
    };
  }
}
