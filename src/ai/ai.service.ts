import { CACHE_MANAGER } from '@nestjs/cache-manager';
import { Inject, Injectable } from '@nestjs/common';
import { InjectRepository } from '@nestjs/typeorm';
import { Cache } from 'cache-manager';
import { Repository } from 'typeorm';
import { Prompts } from '../database/entities/prompts/prompts.entity';
import { CUSTOM_LOGGER_TOKEN, ILogger } from '../utils';
import {
  MODEL_CONFIGS,
  ModelConfig,
  ModelId,
  PROVIDER_CONFIGS,
  ProviderId,
} from './constants/ai-providers.constants';
import { PromptType } from './constants/prompt-types.enum';
import { AI_PROVIDER } from './constants/provider.token';
import {
  IAiCustomFieldsResponse,
  IAiModelConfig,
  IAiPromptRequest,
  IAiPromptResponse,
  IAiProvider,
  IAiSentimentAnalysisResponse,
  IAiTeamRoutingResponse,
} from './interfaces/ai-provider.interface';
import {
  TeamPrompts,
  UrgencyDetectionOptions,
} from './interfaces/team-prompts.interface';
import { AiProviderRegistry } from './providers/ai-provider-registry';

@Injectable()
export class AiService {
  // Configuration constants
  private readonly AI_OPERATION_TIMEOUT_MS = 45000;
  private readonly CACHE_TTL_MS = 10 * 1000; // 10 seconds. This is just to prevent multiple repeated call in the same message detection

  constructor(
    @Inject(CUSTOM_LOGGER_TOKEN) private readonly logger: ILogger,
    @Inject(AI_PROVIDER) private readonly aiProvider: IAiProvider,
    @Inject(CACHE_MANAGER) private readonly cacheManager: Cache,

    @InjectRepository(Prompts)
    private readonly promptsRepository: Repository<Prompts>,
  ) {}

  /**
   * Utility method to extract error message
   */
  private getErrorMessage(error: unknown): string {
    if (error instanceof Error) {
      return error.message;
    }
    return String(error);
  }

  /**
   * Safely executes an AI operation with timeout and error handling
   */
  private async safeAiOperation<T>(
    operation: () => Promise<T>,
    fallback: T,
    operationName = 'AI operation',
  ): Promise<T> {
    try {
      // Set a timeout for the entire operation
      let timer: ReturnType<typeof setTimeout>;
      const timeoutPromise = new Promise<never>((_, reject) => {
        timer = setTimeout(
          () =>
            reject(
              new Error(
                `${operationName} timed out after ${this.AI_OPERATION_TIMEOUT_MS}ms`,
              ),
            ),
          this.AI_OPERATION_TIMEOUT_MS,
        );
      });

      const opPromise = Promise.resolve().then(operation);

      // Race the operation against the timeout
      const result = await Promise.race([opPromise, timeoutPromise]).finally(
        () => clearTimeout(timer),
      );
      return result;
    } catch (error) {
      this.logger.error(
        `${operationName} failed: ${this.getErrorMessage(error)}`,
        error instanceof Error ? error.stack : undefined,
      );
      return fallback;
    }
  }

  /**
   * Higher-order function to handle team prompts loading
   */
  private withTeamPrompts<T>(
    operation: () => Promise<T>,
    teamId?: string,
    installationId?: string,
    organizationId?: string,
  ): Promise<T> {
    return (async () => {
      if (teamId && installationId && organizationId) {
        await this.loadTeamPrompts(teamId, installationId, organizationId);
      }
      return operation();
    })();
  }

  /**
   * Load the prompts for a specific team with caching
   * @param teamId The team ID to load prompts for
   * @param installationId The installation ID
   * @param organizationId The organization ID
   * @returns true if prompts were found and loaded, false otherwise
   */
  async loadTeamPrompts(
    teamId: string,
    installationId?: string,
    organizationId?: string,
  ): Promise<boolean> {
    const cacheKey = `team-prompts-${teamId}`;
    const cachedPrompts = await this.cacheManager.get<TeamPrompts>(cacheKey);

    // Return cached prompts if available
    if (cachedPrompts) {
      this.logger.debug(`Using cached prompts for team ${teamId}`);
      if (
        this.aiProvider &&
        typeof this.aiProvider.setTeamPrompts === 'function'
      ) {
        this.aiProvider.setTeamPrompts(teamId, cachedPrompts);
        return true;
      }
    }

    try {
      this.logger.log(`Loading prompts for team ${teamId}`);

      // Find the default prompt for this team
      const prompt = await this.promptsRepository.findOne({
        where: {
          platformTeam: { id: teamId },
          installation: { id: installationId },
          organization: { id: organizationId },
          isDefault: true,
          isEnabled: true,
        },
      });

      if (!prompt) {
        this.logger.warn(`No default prompt found for team ${teamId}`);
        return false;
      }

      this.logger.debug(
        `Found prompts for team ${teamId}: ${JSON.stringify(prompt.prompts)}`,
      );

      // Set the prompts for this team
      if (
        this.aiProvider &&
        typeof this.aiProvider.setTeamPrompts === 'function'
      ) {
        const teamPrompts: TeamPrompts = {
          [PromptType.TICKET_DETECTION]: prompt.prompts.ticket_detection,
          [PromptType.SENTIMENT_ANALYSIS]: prompt.prompts.sentiment_analysis,
          [PromptType.URGENCY_DETECTION]: prompt.prompts.urgency_detection,
          [PromptType.CUSTOM_FIELDS]: prompt.prompts.custom_fields,
          [PromptType.TITLE_GENERATION]: prompt.prompts.title_generation,
          [PromptType.DESCRIPTION_GENERATION]:
            prompt.prompts.description_generation,
        };

        // Cache the prompts
        await this.cacheManager.set(cacheKey, teamPrompts, this.CACHE_TTL_MS);

        this.aiProvider.setTeamPrompts(teamId, teamPrompts);
        this.logger.log(`Set prompts for team ${teamId}`);
        return true;
      }

      return false;
    } catch (error) {
      this.logger.error(
        `Error loading team prompts for team ${teamId}: ${this.getErrorMessage(error)}`,
      );

      return false;
    }
  }

  /**
   * Determines if a conversation should be converted into a support ticket
   */
  async isValidTicket(
    conversation: string,
    teamId?: string,
    installationId?: string,
    organizationId?: string,
  ): Promise<boolean> {
    return this.safeAiOperation(
      () =>
        this.withTeamPrompts(
          async () => {
            this.logger.log('Checking if the conversation is a valid ticket');

            const result = await this.aiProvider.detectTicket(
              conversation,
              teamId,
            );
            this.logger.debug(
              `Ticket detection outcome: success=${result.success}, requiresTicket=${result.data?.requiresSupportTicket}`,
            );

            if (!result.success || !result.data) {
              this.logger.warn(
                'Failed to detect if conversation is a valid ticket',
                result.error,
              );
              return true;
            }

            return result.data.requiresSupportTicket;
          },
          teamId,
          installationId,
          organizationId,
        ),
      true,
      'Ticket validation', // Default to true if there's an error
    );
  }

  /**
   * Analyzes the sentiment of a conversation
   */
  async analyzeSentiment(
    conversation: string,
    teamId?: string,
    installationId?: string,
    organizationId?: string,
  ): Promise<IAiPromptResponse<IAiSentimentAnalysisResponse>> {
    return this.safeAiOperation(
      () =>
        this.withTeamPrompts(
          async () => {
            return this.aiProvider.analyzeSentiment(conversation, teamId);
          },
          teamId,
          installationId,
          organizationId,
        ),
      { success: false, error: 'Failed to analyze sentiment' },
      'Sentiment analysis',
    );
  }

  /**
   * Detects the urgency level of a ticket based on its content
   * @param content The content to analyze
   * @param urgencies Array of urgency objects with name and description
   * @param options Optional parameters for urgency detection
   * @returns The detected urgency name or default urgency if none detected
   */
  public async detectUrgency(
    content: string,
    urgencies: Array<{ name: string; description: string }>,
    options: UrgencyDetectionOptions = {},
  ): Promise<string> {
    const {
      defaultUrgency = 'Medium',
      enableExtendedThinking,
      teamId,
      installationId,
      organizationId,
    } = options;

    return this.safeAiOperation(
      () =>
        this.withTeamPrompts(
          async () => {
            if (!content || !urgencies || urgencies.length === 0) {
              return defaultUrgency;
            }

            // Build the prompt with the urgencies structure
            const urgenciesPrompt = urgencies
              .map((u) => `- "${u.name}": ${u.description}`)
              .join('\n');

            const prompt = `
            You are an AI assistant helping to determine the urgency level of a support ticket.
            
            Based on the content below, determine the most appropriate urgency level from the following options:
            ${urgenciesPrompt}
            
            If you're unsure, choose "${defaultUrgency}".
            
            Ticket content:
            """
            ${content}
            """
            
            Return only the name of the urgency level without any explanation or additional text.
            Return the result in a JSON with a property called 'urgency_level' which has the values containing one of the urgency levels provided above. THIS IS A MUST AND SHOULD ALWAYS BE FOLLOWED.
          `;

            // Call the AI provider with extended thinking if requested
            const raw = await this.aiProvider.complete({
              prompt,
              temperature: 0.1,
              maxTokens: 4000,
              enabledExtendedThinking: enableExtendedThinking,
            });

            let detectedUrgency: string | undefined;

            try {
              const parsed = JSON.parse(
                typeof raw === 'string' ? raw : String(raw),
              );
              detectedUrgency = parsed.urgency_level;
            } catch {
              /* ignore - fallback below */
            }

            if (!detectedUrgency) {
              return defaultUrgency;
            }

            // Verify that the detected urgency is in the list of valid urgencies
            const isValidUrgency = urgencies.some(
              (u) => u.name.toLowerCase() === detectedUrgency.toLowerCase(),
            );

            return isValidUrgency ? detectedUrgency : defaultUrgency;
          },
          teamId,
          installationId,
          organizationId,
        ),
      defaultUrgency,
      'Urgency detection',
    );
  }

  /**
   * Recommends a team to route the ticket to
   */
  async routeToTeam(
    conversation: string,
    teamId?: string,
    installationId?: string,
    organizationId?: string,
  ): Promise<IAiPromptResponse<IAiTeamRoutingResponse>> {
    return this.safeAiOperation(
      () =>
        this.withTeamPrompts(
          async () => {
            return this.aiProvider.routeToTeam(conversation, teamId);
          },
          teamId,
          installationId,
          organizationId,
        ),
      { success: false, error: 'Failed to route to team' },
      'Team routing',
    );
  }

  /**
   * Extracts custom fields from a conversation
   */
  async extractCustomFields(
    conversation: string,
    fields?: string[],
    teamId?: string,
    installationId?: string,
    organizationId?: string,
  ): Promise<IAiPromptResponse<IAiCustomFieldsResponse>> {
    return this.safeAiOperation(
      () =>
        this.withTeamPrompts(
          async () => {
            return this.aiProvider.extractCustomFields(
              conversation,
              fields,
              teamId,
            );
          },
          teamId,
          installationId,
          organizationId,
        ),
      { success: false, error: 'Failed to extract custom fields' },
      'Custom fields extraction',
    );
  }

  /**
   * Executes a custom prompt
   */
  async executePrompt<T extends Record<string, unknown>>(
    request: IAiPromptRequest & {
      teamId?: string;
      installationId?: string;
      organizationId?: string;
    },
  ): Promise<IAiPromptResponse<T>> {
    return this.safeAiOperation(

      () =>
        this.withTeamPrompts(
          async () => {
            return this.aiProvider.executePrompt<T>(request);
          },
          request.teamId,
          request.installationId,
          request.organizationId,
        ),
      {
        success: false,
        error: 'Failed to execute prompt',
      },
      'Prompt execution',
    );
  }

  /**
   * Updates a prompt template
   */
  setPrompt(type: PromptType, prompt: string): void {
    try {
      this.aiProvider.setPrompt(type, prompt);
      this.logger.debug(`Successfully updated ${type} prompt template`);
    } catch (error) {
      this.logger.error(
        `Failed to set ${type} prompt: ${this.getErrorMessage(error)}`,
      );
      throw error;
    }
  }

  /**
   * Set the prompts for a specific team (for prompt loader service)
   */
  async setTeamPrompts(teamId: string, prompts: TeamPrompts): Promise<void> {
    try {
      this.aiProvider.setTeamPrompts(teamId, prompts);
      this.logger.log(`Set prompts for team ${teamId}`);

      // Update cache
      const cacheKey = `team-prompts-${teamId}`;
      await this.cacheManager.set(cacheKey, prompts, this.CACHE_TTL_MS);
    } catch (error) {
      this.logger.error(
        `Failed to set team prompts for ${teamId}: ${this.getErrorMessage(error)}`,
      );
      throw error;
    }
  }

  // Provider management methods
  getRegisteredProviders(): string[] {
    if (this.aiProvider instanceof AiProviderRegistry) {
      return (this.aiProvider as AiProviderRegistry).getRegisteredProviders();
    }
    return ['default'];
  }

  setActiveProvider(name: ProviderId | string): void {
    try {
      if (this.aiProvider instanceof AiProviderRegistry) {
        (this.aiProvider as AiProviderRegistry).setActiveProvider(name);
        this.logger.log(`Set active provider to ${name}`);
      } else {
        throw new Error(
          'Provider switching not available with single provider',
        );
      }
    } catch (error) {
      this.logger.error(
        `Failed to set active provider to ${name}: ${this.getErrorMessage(error)}`,
      );
      throw error;
    }
  }

  getActiveProvider(): string {
    if (this.aiProvider instanceof AiProviderRegistry) {
      return (this.aiProvider as AiProviderRegistry).getActiveProvider();
    }
    return 'default';
  }

  // Model management methods
  getAvailableModels(): string[] {
    return this.aiProvider.getAvailableModels();
  }

  setActiveModel(modelId: ModelId | string): void {
    try {
      this.aiProvider.setActiveModel(modelId);
      this.logger.log(`Set active model to ${modelId}`);
    } catch (error) {
      this.logger.error(
        `Failed to set active model to ${modelId}: ${this.getErrorMessage(error)}`,
      );
      throw error;
    }
  }

  getActiveModel(): string {
    return this.aiProvider.getActiveModel();
  }

  addModel(modelId: string, config: IAiModelConfig): void {
    try {
      this.aiProvider.addModel(modelId, config);
      this.logger.log(`Added model ${modelId}`);
    } catch (error) {
      this.logger.error(
        `Failed to add model ${modelId}: ${this.getErrorMessage(error)}`,
      );
      throw error;
    }
  }

  /**
   * Returns information about all registered AI providers
   * @returns Array of provider information objects
   */
  async getProviders(): Promise<
    Array<{
      id: ProviderId | string;
      name: string;
      description: string;
      isActive: boolean;
    }>
  > {
    try {
      const registeredProviders = this.getRegisteredProviders();
      const activeProvider = this.getActiveProvider();

      // Map provider IDs to more detailed information using config
      return registeredProviders.map((providerId) => {
        const config = PROVIDER_CONFIGS[providerId as ProviderId] || {
          name: providerId.charAt(0).toUpperCase() + providerId.slice(1),
          description: `${providerId.charAt(0).toUpperCase() + providerId.slice(1)} AI provider`,
        };

        return {
          id: providerId,
          name: config.name,
          description: config.description,
          isActive: providerId === activeProvider,
        };
      });
    } catch (error) {
      this.logger.error(
        `Error fetching AI providers: ${this.getErrorMessage(error)}`,
      );
      return [];
    }
  }

  /**
   * Gets available models for a specific provider
   * @param providerId The ID of the provider to get models for
   * @returns Array of model information objects
   */
  async getModels(providerId: ProviderId | string): Promise<
    Array<{
      id: ModelId | string;
      name: string;
      description: string;
      isActive: boolean;
      capabilities: string[];
    }>
  > {
    try {
      // Temporarily store the current active provider
      let originalProvider = null;
      if (this.aiProvider instanceof AiProviderRegistry) {
        originalProvider = (
          this.aiProvider as AiProviderRegistry
        ).getActiveProvider();

        // Set the requested provider as active to get its models
        (this.aiProvider as AiProviderRegistry).setActiveProvider(providerId);
      }

      // Get models from the provider
      const availableModels = this.aiProvider.getAvailableModels();
      const activeModel = this.aiProvider.getActiveModel();

      // Create rich model information using configuration
      const modelInfoList = availableModels.map((modelId) => {
        // Get config from MODEL_CONFIGS or use defaults
        const config = MODEL_CONFIGS[modelId as ModelId] || {
          name: modelId,
          description: `AI model: ${modelId}`,
          capabilities: ['text-completion', 'ticket-detection'],
        };

        // For type safety, use a type assertion or check if property exists
        // We know some configs might not have isActive yet
        const modelConfig = config as Partial<ModelConfig> & {
          name: string;
          description: string;
          capabilities: string[];
        };

        // Determine if model is active - prioritize model config flag if available
        const isActiveFromConfig = !!modelConfig.isActive;
        const isActiveFromRuntime = modelId === activeModel;

        return {
          id: modelId,
          name: config.name,
          description: config.description,
          isActive: isActiveFromConfig || isActiveFromRuntime,
          capabilities: [...config.capabilities],
        };
      });

      // Restore the original provider if we changed it
      if (originalProvider && this.aiProvider instanceof AiProviderRegistry) {
        (this.aiProvider as AiProviderRegistry).setActiveProvider(
          originalProvider,
        );
      }

      return modelInfoList;
    } catch (error) {
      this.logger.error(
        `Error fetching models for provider ${providerId}: ${this.getErrorMessage(error)}`,
      );
      return [];
    }
  }

  /**
   * Generate a title for a ticket based on the conversation
   * @param content The conversation content
   * @param teamId The team ID
   * @returns The generated title
   */
  public async generateTicketTitle(
    content: string,
    teamId?: string,
    installationId?: string,
    organizationId?: string,
  ): Promise<string> {
    return this.safeAiOperation(
      () =>
        this.withTeamPrompts(
          async () => {
            if (!content) {
              return 'New Support Ticket';
            }

            // Call the specific method in the provider
            const result = await this.aiProvider.generateTitle(content, teamId);

            if (!result.success || !result.data?.title) {
              this.logger.error(
                'Failed to generate ticket title using generateTitle method',
                result.error,
              );
              return 'New Support Ticket';
            }

            // Clean up the title
            return result.data.title
              .replace(/^["']|["']$/g, '')
              .replace(/[\n\r]/g, ' ')
              .trim();
          },
          teamId,
          installationId,
          organizationId,
        ),
      'New Support Ticket',
      'Title generation',
    );
  }

  /**
   * Generate a description for a ticket based on the conversation
   * @param content The conversation content
   * @param teamId The team ID
   * @returns The generated description with markdown formatting
   */
  public async generateTicketDescription(
    content: string,
    teamId?: string,
    installationId?: string,
    organizationId?: string,
  ): Promise<string> {
    return this.safeAiOperation(
      () =>
        this.withTeamPrompts(
          async () => {
            if (!content) {
              return 'No description provided.';
            }

            // Call the specific method in the provider
            const result = await this.aiProvider.generateDescription(
              content,
              teamId,
            );

            if (!result.success || !result.data?.description) {
              this.logger.error(
                'Failed to generate ticket description using generateDescription method',
                result.error,
              );
              return 'No description could be generated.';
            }

            // Return the description, trimming any whitespace
            return result.data.description.trim();
          },
          teamId,
          installationId,
          organizationId,
        ),
      'No description could be generated.',
      'Description generation',
    );
  }
}
