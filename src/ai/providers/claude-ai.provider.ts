import Anthropic from '@anthropic-ai/sdk';
import { Injectable } from '@nestjs/common';
import { ILogger } from '../../utils';
import { MODEL_CONFIGS } from '../constants/ai-providers.constants';
import {
  IAiPromptRequest,
  IAiPromptResponse,
  IAiProviderConfig,
} from '../interfaces/ai-provider.interface';
import {
  BaseAiProvider,
  DEFAULT_SYSTEM_PROMPT,
  MethodType,
} from './base-ai.provider';

@Injectable()
export class ClaudeAiProvider extends BaseAiProvider {
  private client: Anthropic;

  constructor(protected readonly logger: ILogger) {
    super(logger);
  }

  async initialize(config: IAiProviderConfig): Promise<void> {
    this.client = new Anthropic({
      apiKey: config.apiKey,
      baseURL: config.baseUrl,
    });

    // Helper function to check if a property exists
    const hasProperty = (obj: any, prop: string) =>
      obj && Object.prototype.hasOwnProperty.call(obj, prop);

    // Set up models
    if (config.models && Object.keys(config.models).length > 0) {
      this.models = config.models;
    } else {
      // Use configuration from constants instead of hardcoding
      this.models = {};

      // Add Claude models from MODEL_CONFIGS
      const claudeModels = Object.entries(MODEL_CONFIGS).filter(([id]) =>
        id.startsWith('claude-'),
      );

      for (const [modelId] of claudeModels) {
        this.models[modelId] = {
          modelId,
          temperature: 0.5,
          maxTokens: 4000,
        };

        // Reset all isActive flags when initializing
        // We'll set the active one later
        const modelKey = modelId as keyof typeof MODEL_CONFIGS;
        if (hasProperty(MODEL_CONFIGS[modelKey], 'isActive')) {
          (MODEL_CONFIGS[modelKey] as any).isActive = false;
        }
      }

      // If no Claude models were found in MODEL_CONFIGS, add a fallback model
      if (Object.keys(this.models).length === 0) {
        this.logger.warn(
          'No Claude models found in MODEL_CONFIGS, using fallback model',
        );
        const fallbackModel =
          config.defaultModel || 'claude-3-7-sonnet-20250219';
        this.models[fallbackModel] = {
          modelId: fallbackModel,
          temperature: 0.5,
          maxTokens: 4000,
        };
      }
    }

    // Set active model
    this.activeModel = config.defaultModel || Object.keys(this.models)[0];

    // Update isActive state in MODEL_CONFIGS for the active model
    const modelKey = this.activeModel as keyof typeof MODEL_CONFIGS;
    if (hasProperty(MODEL_CONFIGS[modelKey], 'isActive')) {
      (MODEL_CONFIGS[modelKey] as any).isActive = true;
    }

    this.initialized = true;
    this.logger.debug('Claude AI provider initialized');
  }

  async executePrompt<T>(
    request: IAiPromptRequest,
    methodType?: MethodType,
  ): Promise<IAiPromptResponse<T>> {
    if (!this.initialized) {
      return {
        success: false,
        error: 'Claude AI provider not initialized',
      };
    }

    try {
      const schema = this.getResponseSchema(methodType);
      const prompt = this.buildPrompt(
        request.prompt,
        request.conversation,
        schema,
      );
      const modelConfig = this.models[this.activeModel];

      // A concise system prompt is enough – the schema lives in the user prompt
      const systemMessage = DEFAULT_SYSTEM_PROMPT;

      const response = await this.client.messages.create({
        model: modelConfig.modelId,
        max_tokens: request.maxTokens ?? modelConfig.maxTokens ?? 4000,
        temperature: request.temperature ?? modelConfig.temperature ?? 0.5,
        ...(modelConfig.additionalParams ?? {}),
        system: systemMessage,
        messages: [{ role: 'user', content: prompt }],
      });

      // Parse the JSON response from the model
      let parsedData: T;
      try {
        // Extract content from Anthropic's response
        const content =
          response?.content?.[0]?.type === 'text'
            ? response.content[0].text
            : '';

        // Try to extract a JSON block if it exists
        const jsonMatch =
          content.match(/```json\n([\s\S]*?)\n```/) ||
          content.match(/```([\s\S]*?)```/) ||
          content.match(/({[\s\S]*})/);

        const jsonString = jsonMatch ? jsonMatch[1] : content;
        parsedData = JSON.parse(jsonString);
      } catch (error) {
        let errorMessage = 'Unknown error';
        if (error instanceof Error) {
          errorMessage = error.message;
        } else {
          errorMessage = String(error);
        }

        throw new Error(`Failed to parse Claude AI response: ${errorMessage}`);
      }

      return {
        success: true,
        data: parsedData,
        rawResponse: response,
      };
    } catch (error) {
      let errorMessage = 'Unknown error';
      if (error instanceof Error) {
        errorMessage = error.message;
      } else {
        errorMessage = String(error);
      }

      this.logger.error(`Claude AI provider error: ${errorMessage}`);
      return {
        success: false,
        error: errorMessage,
      };
    }
  }
}
