import { Inject, Injectable } from '@nestjs/common';
import { CUSTOM_LOGGER_TOKEN, ILogger } from '../../utils';
import { MODEL_CONFIGS } from '../constants/ai-providers.constants';
import { DEFAULT_PROMPTS } from '../constants/default-prompts';
import { PromptType } from '../constants/prompt-types.enum';
import {
  IAiCompletionParams,
  IAiCustomFieldsResponse,
  IAiDescriptionGenerationResponse,
  IAiModelConfig,
  IAiPromptRequest,
  IAiPromptResponse,
  IAiProvider,
  IAiProviderConfig,
  IAiSentimentAnalysisResponse,
  IAiTeamRoutingResponse,
  IAiTicketDetectionResponse,
  IAiTitleGenerationResponse,
  IAiUrgencyDetectionResponse,
} from '../interfaces/ai-provider.interface';
import { TeamPrompts } from '../interfaces/team-prompts.interface';

// Define the method types for schema selection
export type MethodType = PromptType | 'complete';

// Default system prompt used by all AI providers
export const DEFAULT_SYSTEM_PROMPT =
  'You are a helpful assistant that analyzes support conversations.';

// Define response types (adjust if already defined elsewhere)

@Injectable()
export abstract class BaseAiProvider implements IAiProvider {
  protected constructor(
    @Inject(CUSTOM_LOGGER_TOKEN) protected readonly logger: ILogger,
  ) {}

  protected config: IAiProviderConfig;
  protected initialized = false;
  protected models: Record<string, IAiModelConfig> = {};
  protected activeModel: string;

  // JSON schemas for structured responses - shared by all providers
  protected schemas = {
    ticketDetection: {
      type: 'object',
      properties: {
        requiresSupportTicket: {
          type: 'boolean',
          description: 'Whether this conversation requires a support ticket',
        },
      },
      required: ['requiresSupportTicket'],
      additionalProperties: false,
    },
    sentimentAnalysis: {
      type: 'object',
      properties: {
        sentiment: {
          type: 'string',
          enum: ['positive', 'negative', 'neutral'],
          description: 'The overall sentiment of the conversation',
        },
        confidence: {
          type: 'number',
          minimum: 0,
          maximum: 1,
          description: 'Confidence score of the sentiment analysis',
        },
        urgency: {
          type: 'string',
          enum: ['low', 'medium', 'high'],
          description: 'How urgent the customer issue is',
        },
        frustrationLevel: {
          type: 'string',
          enum: ['low', 'medium', 'high'],
          description: 'Level of customer frustration detected',
        },
        satisfactionLevel: {
          type: 'string',
          enum: ['low', 'medium', 'high'],
          description: 'Level of customer satisfaction detected',
        },
      },
      required: [
        'sentiment',
        'confidence',
        'urgency',
        'frustrationLevel',
        'satisfactionLevel',
      ],
      additionalProperties: false,
    },
    urgencyDetection: {
      type: 'object',
      properties: {
        urgency: {
          type: 'string',
          description: 'The urgency level of the request',
        },
        confidence: {
          type: 'number',
          minimum: 0,
          maximum: 1,
          description: 'Confidence score of the urgency detection',
        },
        businessImpact: {
          type: 'string',
          enum: ['low', 'medium', 'high'],
          description: 'Potential business impact of the issue',
        },
        timeSensitivity: {
          type: 'string',
          enum: ['low', 'medium', 'high'],
          description: 'How time-sensitive the issue is',
        },
        affectedUsersCount: {
          oneOf: [{ type: 'number' }, { type: 'string', enum: ['unknown'] }],
          description: 'Number of users affected by the issue or "unknown"',
        },
        severityLevel: {
          type: 'string',
          enum: ['low', 'medium', 'high'],
          description: 'How severe the issue is',
        },
      },
      required: [
        'urgency',
        'confidence',
        'businessImpact',
        'timeSensitivity',
        'severityLevel',
      ],
      additionalProperties: false,
    },
    teamRouting: {
      type: 'object',
      properties: {
        recommendedTeam: {
          type: 'string',
          description: 'The team recommended to handle this request',
        },
        confidence: {
          type: 'number',
          minimum: 0,
          maximum: 1,
          description: 'Confidence score of the team routing decision',
        },
        technicalComplexity: {
          type: 'string',
          enum: ['low', 'medium', 'high'],
          description: 'Technical complexity of the issue',
        },
        domainExpertise: {
          type: 'array',
          items: {
            type: 'string',
          },
          description: 'Domains of expertise needed to resolve this issue',
        },
        products: {
          type: 'array',
          items: {
            type: 'string',
          },
          description: 'Products or services mentioned in the conversation',
        },
      },
      required: ['recommendedTeam', 'confidence', 'technicalComplexity'],
      additionalProperties: false,
    },
    customFields: {
      type: 'object',
      properties: {
        fields: {
          type: 'object',
          additionalProperties: { type: 'string' },
          description: 'Extracted field values',
        },
        confidence: {
          type: 'object',
          additionalProperties: { type: 'number' },
          description: 'Confidence scores for each extracted field',
        },
      },
      required: ['fields', 'confidence'],
      additionalProperties: false,
    },
    titleGeneration: {
      type: 'object',
      properties: {
        title: {
          type: 'string',
          description: 'The generated title for the support ticket',
        },
      },
      required: ['title'],
      additionalProperties: false,
    },
    descriptionGeneration: {
      type: 'object',
      properties: {
        description: {
          type: 'string',
          description: 'The generated description for the support ticket',
        },
      },
      required: ['description'],
      additionalProperties: false,
    },
  };

  // Map of team IDs to their custom prompts
  protected teamPrompts: Map<string, TeamPrompts> = new Map();

  // Default prompts for each method
  protected prompts: Record<PromptType, string> = {
    [PromptType.TICKET_DETECTION]: DEFAULT_PROMPTS[PromptType.TICKET_DETECTION],
    [PromptType.SENTIMENT_ANALYSIS]:
      DEFAULT_PROMPTS[PromptType.SENTIMENT_ANALYSIS],
    [PromptType.URGENCY_DETECTION]:
      DEFAULT_PROMPTS[PromptType.URGENCY_DETECTION],
    [PromptType.TEAM_ROUTING]: DEFAULT_PROMPTS[PromptType.TEAM_ROUTING],
    [PromptType.CUSTOM_FIELDS]: DEFAULT_PROMPTS[PromptType.CUSTOM_FIELDS],
    [PromptType.TITLE_GENERATION]: DEFAULT_PROMPTS[PromptType.TITLE_GENERATION],
    [PromptType.DESCRIPTION_GENERATION]:
      DEFAULT_PROMPTS[PromptType.DESCRIPTION_GENERATION],
  };

  abstract initialize(config: IAiProviderConfig): Promise<void>;

  abstract executePrompt<T>(
    request: IAiPromptRequest,
    methodType?: MethodType,
  ): Promise<IAiPromptResponse<T>>;

  /**
   * Get the appropriate JSON schema for the response type, based on the method type
   */
  protected getResponseSchema(
    methodType?: MethodType,
  ): Record<string, any> | null {
    if (!methodType) {
      return null;
    }
    return (this as any).schemas?.[methodType] ?? null;
  }

  /**
   * Creates a human-readable description of the schema
   */
  protected getSchemaDescription(schema: Record<string, any> | null): string {
    if (!schema) {
      return '';
    }

    let description =
      'Your response should be a JSON object with the following structure:';

    if (schema.properties) {
      for (const [key, prop] of Object.entries(schema.properties)) {
        const p = prop as Record<string, any>;
        const typeLabel = p.type
          ? p.type
          : p.oneOf
            ? p.oneOf.map((o) => o.type || JSON.stringify(o.enum)).join(' | ')
            : 'unknown';
        const allEnums =
          p.enum ??
          (Array.isArray(p.oneOf)
            ? p.oneOf.flatMap((o: any) => o.enum ?? [])
            : undefined);
        const enumValues = allEnums ? ` (one of: ${allEnums.join(', ')})` : '';
        const propDescription = p.description ? ` - ${p.description}` : '';
        description += `\n- "${key}": ${typeLabel}${enumValues}${propDescription}`;
      }
    }

    return description;
  }

  // Implementation of model management methods
  getAvailableModels(): string[] {
    return Object.keys(this.models);
  }

  setActiveModel(modelId: string): void {
    if (!this.models[modelId]) {
      throw new Error(`Model ${modelId} not found`);
    }

    // First, set all models to inactive
    for (const model of Object.keys(this.models)) {
      // Access the model configuration in MODEL_CONFIGS if available
      const modelKey = model as keyof typeof MODEL_CONFIGS;
      if (MODEL_CONFIGS[modelKey] && 'isActive' in MODEL_CONFIGS[modelKey]) {
        (MODEL_CONFIGS[modelKey] as any).isActive = false;
      }
    }

    // Set the new active model
    this.activeModel = modelId;

    // Update isActive state in MODEL_CONFIGS if available
    const modelKey = modelId as keyof typeof MODEL_CONFIGS;
    if (MODEL_CONFIGS[modelKey] && 'isActive' in MODEL_CONFIGS[modelKey]) {
      (MODEL_CONFIGS[modelKey] as any).isActive = true;
    }

    this.logProviderAndModel('setActiveModel');
  }

  getActiveModel(): string {
    return this.activeModel;
  }

  addModel(modelId: string, config: IAiModelConfig): void {
    this.models[modelId] = config;
    this.logger.debug(`Added model: ${modelId}`);
  }

  /**
   * Logs which model and provider is being used for an AI interaction
   * @param method The method being called (e.g., 'detectTicket')
   */
  protected logProviderAndModel(method: string): void {
    const providerName = this.constructor.name;
    const modelId = this.activeModel;
    this.logger.debug(
      `Using model '${modelId}' from provider '${providerName}' for ${method}`,
    );
  }

  /**
   * Set team-specific prompts
   * @param teamId The team ID to set prompts for
   * @param prompts The prompts to set
   */
  setTeamPrompts(teamId: string, prompts: TeamPrompts): void {
    // Clear any previously cached prompts for this team
    if (this.teamPrompts.has(teamId)) {
      this.logger.log(`Clearing cached prompts for team ${teamId}`);
      this.teamPrompts.delete(teamId);
    }

    // Set new prompts
    this.teamPrompts.set(teamId, prompts);

    this.logger.log(`Set prompts for team ${teamId}`);
  }

  /**
   * Get the prompt to use for a specific team
   * @param promptType The type of prompt
   * @param teamId Optional team ID to get team-specific prompt
   * @returns The prompt to use
   */
  getPrompt(promptType: PromptType, teamId?: string): string {
    this.logger.debug(
      `Getting ${promptType} prompt for team ${teamId || 'default'}`,
    );

    // If a team ID is provided, check for team-specific prompts
    if (teamId && this.teamPrompts.has(teamId)) {
      const teamPrompts = this.teamPrompts.get(teamId);
      if (teamPrompts[promptType]) {
        this.logger.debug(
          `Found team-specific ${promptType} prompt for team ${teamId}`,
        );
        return teamPrompts[promptType];
      }

      this.logger.debug(
        `No team-specific ${promptType} prompt found for team ${teamId}, falling back to default`,
      );
    }

    // Fall back to the default prompts
    this.logger.debug(`Returning default ${promptType} prompt`);
    return this.prompts[promptType];
  }

  async detectTicket(
    conversation: string,
    teamId?: string,
  ): Promise<IAiPromptResponse<IAiTicketDetectionResponse>> {
    if (!this.initialized) {
      this.logger.warn(
        'AI provider not initialized when calling detectTicket',
        this.constructor.name,
      );
      return {
        success: false,
        error: 'AI provider not initialized',
      };
    }

    this.logProviderAndModel('detectTicket');
    this.logger.debug(
      `Starting ticket detection for conversation: ${conversation?.slice(0, 100)}...`,
      this.constructor.name,
    );

    try {
      const prompt = this.getPrompt(PromptType.TICKET_DETECTION, teamId);
      this.logger.debug(
        `Using ticketDetection prompt: ${JSON.stringify(prompt)?.slice(0, 200)}...`,
        this.constructor.name,
      );
      const ticketDetectionPrompt = prompt;

      const result = await this.executePrompt<IAiTicketDetectionResponse>(
        {
          prompt: ticketDetectionPrompt,
          conversation,
        },
        PromptType.TICKET_DETECTION,
      );
      this.logger.log(
        `Ticket detection result: ${JSON.stringify(result)?.slice(0, 200)}...`,
        this.constructor.name,
      );
      return result;
    } catch (error) {
      let errorMessage = 'Unknown error';
      if (error instanceof Error) {
        console.error(error);
        errorMessage = error.message;
        this.logger.error(
          `Error detecting ticket: ${errorMessage}`,
          this.constructor.name,
        );
      } else {
        console.error(error);
        this.logger.error(
          `Non-Error thrown in detectTicket: ${JSON.stringify(error)}`,
          this.constructor.name,
        );
      }

      return {
        success: false,
        error: `Failed to detect ticket: ${errorMessage}`,
      };
    }
  }

  async analyzeSentiment(
    conversation: string,
    teamId?: string,
  ): Promise<IAiPromptResponse<IAiSentimentAnalysisResponse>> {
    if (!this.initialized) {
      return {
        success: false,
        error: 'AI provider not initialized',
      };
    }

    this.logProviderAndModel('analyzeSentiment');

    try {
      const prompt = this.getPrompt(PromptType.SENTIMENT_ANALYSIS, teamId);
      const result = await this.executePrompt<IAiSentimentAnalysisResponse>(
        {
          prompt,
          conversation,
        },
        PromptType.SENTIMENT_ANALYSIS,
      );

      return result;
    } catch (error) {
      let errorMessage = 'Unknown error';
      if (error instanceof Error) {
        console.error(error);

        errorMessage = error.message;
        this.logger.error(
          `Error analyzing sentiment: ${errorMessage}`,
          this.constructor.name,
        );
      } else {
        console.error(error);
      }

      return {
        success: false,
        error: `Failed to analyze sentiment: ${errorMessage}`,
      };
    }
  }

  async detectUrgency(
    conversation: string,
    teamId?: string,
    urgencyLevels?: string[],
  ): Promise<IAiPromptResponse<IAiUrgencyDetectionResponse>> {
    if (!this.initialized) {
      return {
        success: false,
        error: 'AI provider not initialized',
      };
    }

    this.logProviderAndModel('detectUrgency');

    try {
      let prompt = this.getPrompt(PromptType.URGENCY_DETECTION, teamId);

      // If urgency levels are provided, add them to the prompt
      if (urgencyLevels && urgencyLevels.length > 0) {
        prompt += ` Please classify the urgency specifically using one of these values: ${urgencyLevels.join(', ')}.`;
      }

      const result = await this.executePrompt<IAiUrgencyDetectionResponse>(
        {
          prompt,
          conversation,
        },
        PromptType.URGENCY_DETECTION,
      );

      return result;
    } catch (error) {
      let errorMessage = 'Unknown error';
      if (error instanceof Error) {
        console.error(error);
        errorMessage = error.message;
        this.logger.error(
          `Error detecting urgency: ${errorMessage}`,
          this.constructor.name,
        );
      } else {
        console.error(error);
      }

      return {
        success: false,
        error: `Failed to detect urgency: ${errorMessage}`,
      };
    }
  }

  async routeToTeam(
    conversation: string,
    teamId?: string,
  ): Promise<IAiPromptResponse<IAiTeamRoutingResponse>> {
    if (!this.initialized) {
      return {
        success: false,
        error: 'AI provider not initialized',
      };
    }

    this.logProviderAndModel('routeToTeam');

    try {
      const prompt = this.getPrompt(PromptType.TEAM_ROUTING, teamId);
      const result = await this.executePrompt<IAiTeamRoutingResponse>(
        {
          prompt,
          conversation,
        },
        PromptType.TEAM_ROUTING,
      );

      return result;
    } catch (error) {
      let errorMessage = 'Unknown error';
      if (error instanceof Error) {
        console.error(error);
        errorMessage = error.message;
        this.logger.error(
          `Error routing to team: ${errorMessage}`,
          this.constructor.name,
        );
      } else {
        console.error(error);
      }

      return {
        success: false,
        error: `Failed to route to team: ${errorMessage}`,
      };
    }
  }

  async extractCustomFields(
    conversation: string,
    fields?: string[],
    teamId?: string,
  ): Promise<IAiPromptResponse<IAiCustomFieldsResponse>> {
    if (!this.initialized) {
      return {
        success: false,
        error: 'AI provider not initialized',
      };
    }

    this.logProviderAndModel('extractCustomFields');

    try {
      const prompt = this.getPrompt(PromptType.CUSTOM_FIELDS, teamId);
      let customFieldsPrompt = prompt;

      if (fields && fields.length > 0) {
        customFieldsPrompt += `Specifically extract the following fields: ${fields.join(', ')}.`;
      }

      const result = await this.executePrompt<IAiCustomFieldsResponse>(
        {
          prompt: customFieldsPrompt,
          conversation,
        },
        PromptType.CUSTOM_FIELDS,
      );

      return result;
    } catch (error) {
      let errorMessage = 'Unknown error';
      if (error instanceof Error) {
        console.error(error);
        errorMessage = error.message;
        this.logger.error(
          `Error extracting custom fields: ${errorMessage}`,
          this.constructor.name,
        );
      } else {
        console.error(error);
      }

      return {
        success: false,
        error: `Failed to extract custom fields: ${errorMessage}`,
      };
    }
  }

  /**
   * Set a custom prompt for a specific method
   * @param type The type of prompt to set
   * @param prompt The prompt text
   */
  setPrompt(type: PromptType, prompt: string): void {
    this.prompts[type] = prompt;
    this.logger.log(`Set custom prompt for ${type}`);
  }

  /**
   * Completes a prompt with the AI provider
   * @param params Parameters for completion including prompt and options
   * @returns The complete response data object
   */
  async complete(params: IAiCompletionParams): Promise<any> {
    // this.logProviderAndModel('complete');

    try {
      const { prompt, temperature, maxTokens, enabledExtendedThinking } =
        params;

      // If extended thinking is enabled and supported by the model,
      // modify the prompt to encourage detailed reasoning
      let finalPrompt = prompt;
      if (enabledExtendedThinking) {
        finalPrompt = this.addExtendedThinkingPrompt(prompt);
      }

      // Basic implementation that uses executePrompt
      const response = await this.executePrompt(
        {
          prompt: finalPrompt,
          temperature: temperature,
          maxTokens: maxTokens,
        },
        'complete',
      );

      if (!response.success || !response.data) {
        throw new Error(response.error || 'Failed to complete prompt');
      }

      // Return the complete data object
      return response.data;
    } catch (error) {
      if (error instanceof Error) {
        this.logger.error(
          `Error completing prompt: ${error.message}`,
          this.constructor.name,
        );
      } else {
        this.logger.error(`Error completing prompt: ${error}`);
      }
      return null;
    }
  }

  /**
   * Modifies a prompt to encourage extended thinking
   * @param originalPrompt The original prompt
   * @returns Modified prompt that encourages extended thinking
   */
  protected addExtendedThinkingPrompt(originalPrompt: string): string {
    return `
I'd like you to engage in extended thinking on this problem.
Take your time to think step by step through all aspects of the question.
Consider different perspectives and approaches before arriving at your conclusion.

${originalPrompt}

Remember to think thoroughly about all relevant factors before providing your response.
`;
  }

  /**
   * Builds a standard prompt with conversation context
   * @param promptTemplate The prompt template to use
   * @param conversation The conversation to analyze
   * @param schema Optional schema description to include
   * @returns The complete prompt
   */
  protected buildPrompt(
    promptTemplate: string,
    conversation: string,
    schema?: Record<string, any>,
  ): string {
    const schemaDescription = schema ? this.getSchemaDescription(schema) : '';
    const schemaInstructions = schemaDescription
      ? `\n\n${schemaDescription}`
      : '';

    // Ensure conversation is handled gracefully if undefined, although generateTitle should prevent this path
    const conversationText =
      typeof conversation === 'string' ? conversation : '';

    return `${promptTemplate}${schemaInstructions}\n\nConversation:\n${conversationText}\n\nProvide your analysis in JSON format. Format your response as a valid JSON object without any explanation text before or after it.`;
  }

  async generateTitle(
    conversation: string,
    teamId?: string,
  ): Promise<IAiPromptResponse<IAiTitleGenerationResponse>> {
    if (!this.initialized) {
      return {
        success: false,
        error: 'AI provider not initialized',
      };
    }

    this.logProviderAndModel('generateTitle');

    try {
      const promptTemplate = this.getPrompt(
        PromptType.TITLE_GENERATION,
        teamId,
      );
      const prompt = this.buildPrompt(promptTemplate, conversation);

      const result = await this.executePrompt<IAiTitleGenerationResponse>(
        { prompt, conversation },
        PromptType.TITLE_GENERATION,
      );

      return result;
    } catch (error) {
      let errorMessage = 'Unknown error';
      if (error instanceof Error) {
        // console.error(error); // Keep console log for debugging if needed
        errorMessage = error.message;
        this.logger.error(
          `Error generating title: ${errorMessage}`,
          this.constructor.name,
        );
      } else {
        // console.error(error); // Keep console log for debugging if needed
        errorMessage = String(error);
        this.logger.error(
          `Error generating title: ${errorMessage}`,
          this.constructor.name,
        );
      }

      return {
        success: false,
        error: `Failed to generate title: ${errorMessage}`,
      };
    }
  }

  async generateDescription(
    conversation: string,
    teamId?: string,
  ): Promise<IAiPromptResponse<IAiDescriptionGenerationResponse>> {
    if (!this.initialized) {
      return {
        success: false,
        error: 'AI provider not initialized',
      };
    }

    this.logProviderAndModel('generateDescription');

    try {
      const promptTemplate = this.getPrompt(
        PromptType.DESCRIPTION_GENERATION,
        teamId,
      );
      const prompt = this.buildPrompt(promptTemplate, conversation);

      const result = await this.executePrompt<IAiDescriptionGenerationResponse>(
        { prompt, conversation },
        PromptType.DESCRIPTION_GENERATION,
      );

      return result;
    } catch (error) {
      let errorMessage = 'Unknown error';
      if (error instanceof Error) {
        // console.error(error); // Keep console log for debugging if needed
        errorMessage = error.message;
        this.logger.error(
          `Error generating description: ${errorMessage}`,
          this.constructor.name,
        );
      } else {
        // console.error(error); // Keep console log for debugging if needed
        errorMessage = String(error);
        this.logger.error(
          `Error generating description: ${errorMessage}`,
          this.constructor.name,
        );
      }

      return {
        success: false,
        error: `Failed to generate description: ${errorMessage}`,
      };
    }
  }
}
