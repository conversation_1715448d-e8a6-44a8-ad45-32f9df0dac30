import { Injectable } from '@nestjs/common';
import OpenAI from 'openai';
import {
  IAiPromptRequest,
  IAiPromptResponse,
  IAiProviderConfig,
} from '../interfaces/ai-provider.interface';
import { BaseAiProvider, MethodType } from './base-ai.provider';

@Injectable()
export class GrokAiProvider extends BaseAiProvider {
  private client: OpenAI;

  async initialize(config: IAiProviderConfig): Promise<void> {
    // Set up the OpenAI client with Grok base URL
    this.client = new OpenAI({
      apiKey: config.apiKey,
      baseURL: config.baseUrl || 'https://api.x.ai/v1',
      timeout: 30000,
      maxRetries: 2,
    });

    // Set up models
    if (config.models && Object.keys(config.models).length > 0) {
      this.models = config.models;
    } else {
      // Default models if none provided
      this.models = {
        'grok-2': {
          modelId: 'grok-2',
          temperature: 0.5,
          maxTokens: 4000,
        },
      };
    }

    // Set active model
    this.activeModel = config.defaultModel || Object.keys(this.models)[0];

    this.initialized = true;
    console.log(`Grok AI provider initialized with model: ${this.activeModel}`);
  }

  async executePrompt<T>(
    request: IAiPromptRequest,
    methodType?: MethodType,
  ): Promise<IAiPromptResponse<T>> {
    if (!this.initialized) {
      return {
        success: false,
        error: 'Grok AI provider not initialized',
      };
    }

    try {
      console.log(
        `Executing prompt with Grok AI using model: ${this.activeModel}`,
      );
      const schema = this.getResponseSchema(methodType);
      const prompt = this.buildPrompt(
        request.prompt,
        request.conversation,
        schema,
      );
      const modelConfig = this.models[this.activeModel];

      // Get the schema description for use in the system message
      const schemaDescription = schema ? this.getSchemaDescription(schema) : '';

      // Use the OpenAI SDK chat completions
      const response = await this.client.chat.completions.create({
        model: modelConfig.modelId,
        messages: [
          {
            role: 'system',
            content: `You are a helpful assistant that analyzes support conversations.
            ${schemaDescription ? `Provide your analysis using this exact schema: ${JSON.stringify(schema, null, 2)}` : ''}`,
          },
          { role: 'user', content: prompt },
        ],
        temperature: request.temperature ?? modelConfig.temperature ?? 0.5,
        max_tokens: request.maxTokens ?? modelConfig.maxTokens ?? 4000,
        response_format: { type: 'json_object' },
        ...(modelConfig.additionalParams ?? {}),
      });

      // Parse the JSON response from the model
      let parsedData: T;
      try {
        const content = response.choices[0]?.message?.content;
        if (!content) {
          throw new Error('No content in response');
        }

        parsedData = JSON.parse(content);
        console.log('Successfully parsed Grok AI response');
      } catch (error) {
        console.error(
          `Failed to parse Grok AI response: ${error instanceof Error ? error.message : String(error)}`,
        );
        throw new Error(
          `Failed to parse AI response: ${error instanceof Error ? error.message : String(error)}`,
        );
      }

      return {
        success: true,
        data: parsedData,
        rawResponse: response,
      };
    } catch (error) {
      // Handle OpenAI SDK specific errors
      if (error instanceof Error && error.message === 'ECONNABORTED') {
        console.error('Grok API request timed out');
        return {
          success: false,
          error: 'Request timed out',
        };
      }

      // Handle rate limiting
      if (error instanceof Error && error.message === '429') {
        console.error('Grok API rate limit exceeded');
        return {
          success: false,
          error: 'Rate limit exceeded. Please try again later.',
        };
      }

      const errorMessage =
        error instanceof Error ? error.message : String(error);
      console.error(`Grok API error: ${errorMessage}`);

      return {
        success: false,
        error: errorMessage,
      };
    }
  }
}
