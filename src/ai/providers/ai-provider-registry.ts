import { Injectable, Logger } from '@nestjs/common';
import { PromptType } from '../constants/prompt-types.enum';
import {
  IAiCompletionParams,
  IAiCustomFieldsResponse,
  IAiDescriptionGenerationResponse,
  IAiModelConfig,
  IAiPromptRequest,
  IAiPromptResponse,
  IAiProvider,
  IAiProviderConfig,
  IAiSentimentAnalysisResponse,
  IAiTeamRoutingResponse,
  IAiTicketDetectionResponse,
  IAiTitleGenerationResponse,
  IAiUrgencyDetectionResponse,
} from '../interfaces/ai-provider.interface';
import { TeamPrompts } from '../interfaces/team-prompts.interface';

@Injectable()
export class AiProviderRegistry implements IAiProvider {
  private readonly logger = new Logger(AiProviderRegistry.name);
  private providers: Map<string, IAiProvider> = new Map();
  private activeProvider: string | null = null;

  // Registry management methods
  registerProvider(name: string, provider: IAiProvider): void {
    this.providers.set(name, provider);
    this.logger.log(`Registered AI provider: ${name}`);

    // Set as active if it's the first one
    if (!this.activeProvider) {
      this.activeProvider = name;
      this.logger.log(`Set ${name} as active provider`);
    }
  }

  setActiveProvider(name: string): void {
    if (!this.providers.has(name)) {
      throw new Error(`Provider ${name} not registered`);
    }
    this.activeProvider = name;
    this.logger.log(`Switched to AI provider: ${name}`);
  }

  getActiveProvider(): string {
    if (!this.activeProvider) {
      throw new Error('No active AI provider');
    }
    return this.activeProvider;
  }

  getRegisteredProviders(): string[] {
    return Array.from(this.providers.keys());
  }

  // IAiProvider implementation - delegates to active provider
  async initialize(config: IAiProviderConfig): Promise<void> {
    if (!this.activeProvider) {
      throw new Error('No active provider to initialize');
    }

    const provider = this.providers.get(this.activeProvider);
    return provider.initialize(config);
  }

  async detectTicket(
    conversation: string,
    teamId?: string,
  ): Promise<IAiPromptResponse<IAiTicketDetectionResponse>> {
    if (!this.activeProvider) {
      return {
        success: false,
        error: 'No active AI provider',
      };
    }

    const provider = this.providers.get(this.activeProvider);
    return provider.detectTicket(conversation, teamId);
  }

  async analyzeSentiment(
    conversation: string,
    teamId?: string,
  ): Promise<IAiPromptResponse<IAiSentimentAnalysisResponse>> {
    if (!this.activeProvider) {
      return {
        success: false,
        error: 'No active AI provider',
      };
    }

    const provider = this.providers.get(this.activeProvider);
    return provider.analyzeSentiment(conversation, teamId);
  }

  async detectUrgency(
    conversation: string,
    teamId?: string,
    urgencyLevels?: string[],
  ): Promise<IAiPromptResponse<IAiUrgencyDetectionResponse>> {
    if (!this.activeProvider) {
      return {
        success: false,
        error: 'No active AI provider',
      };
    }

    const provider = this.providers.get(this.activeProvider);
    return provider.detectUrgency(conversation, teamId, urgencyLevels);
  }

  async routeToTeam(
    conversation: string,
    teamId?: string,
  ): Promise<IAiPromptResponse<IAiTeamRoutingResponse>> {
    if (!this.activeProvider) {
      return {
        success: false,
        error: 'No active AI provider',
      };
    }

    const provider = this.providers.get(this.activeProvider);
    return provider.routeToTeam(conversation, teamId);
  }

  async extractCustomFields(
    conversation: string,
    fields?: string[],
    teamId?: string,
  ): Promise<IAiPromptResponse<IAiCustomFieldsResponse>> {
    if (!this.activeProvider) {
      return {
        success: false,
        error: 'No active AI provider',
      };
    }

    const provider = this.providers.get(this.activeProvider);
    return provider.extractCustomFields(conversation, fields, teamId);
  }

  async executePrompt<T>(
    request: IAiPromptRequest,
  ): Promise<IAiPromptResponse<T>> {
    if (!this.activeProvider) {
      return {
        success: false,
        error: 'No active AI provider',
      };
    }

    const provider = this.providers.get(this.activeProvider);
    return provider.executePrompt<T>(request);
  }

  setPrompt(type: PromptType, prompt: string): void {
    if (!this.activeProvider) {
      throw new Error('No active AI provider');
    }

    const provider = this.providers.get(this.activeProvider);
    provider.setPrompt(type, prompt);
  }

  getAvailableModels(): string[] {
    if (!this.activeProvider) {
      return [];
    }

    const provider = this.providers.get(this.activeProvider);
    return provider.getAvailableModels();
  }

  setActiveModel(modelId: string): void {
    if (!this.activeProvider) {
      throw new Error('No active AI provider');
    }

    const provider = this.providers.get(this.activeProvider);
    provider.setActiveModel(modelId);
  }

  getActiveModel(): string {
    if (!this.activeProvider) {
      throw new Error('No active AI provider');
    }

    const provider = this.providers.get(this.activeProvider);
    return provider.getActiveModel();
  }

  addModel(modelId: string, config: IAiModelConfig): void {
    if (!this.activeProvider) {
      throw new Error('No active AI provider');
    }

    const provider = this.providers.get(this.activeProvider);
    provider.addModel(modelId, config);
  }

  // Implement the new methods
  setTeamPrompts(teamId: string, prompts: TeamPrompts): void {
    // Set prompts for ALL providers, not just the active one
    for (const provider of this.providers.values()) {
      console.log('Setting prompts for provider in registry');
      provider.setTeamPrompts(teamId, prompts);
    }
  }

  getPrompt(promptType: PromptType, teamId?: string): string {
    return (
      this.providers.get(this.activeProvider)?.getPrompt(promptType, teamId) ||
      ''
    );
  }

  /**
   * Completes a prompt with the AI provider
   * @param params Parameters for completion including prompt, temperature, and maxTokens
   * @returns The completion response as string
   */
  async complete(params: IAiCompletionParams): Promise<any> {
    if (!this.activeProvider) {
      throw new Error('No active AI provider');
    }

    return this.providers.get(this.activeProvider)?.complete(params);
  }

  /**
   * Generates a title for a conversation
   * @param conversation The conversation content
   * @param teamId Optional team ID for team-specific prompts
   * @returns Promise with the generated title response
   */
  async generateTitle(
    conversation: string,
    teamId?: string,
  ): Promise<IAiPromptResponse<IAiTitleGenerationResponse>> {
    if (!this.activeProvider) {
      return {
        success: false,
        error: 'No active AI provider',
      };
    }

    const provider = this.providers.get(this.activeProvider);
    return provider.generateTitle(conversation, teamId);
  }

  /**
   * Generates a description for a conversation
   * @param conversation The conversation content
   * @param teamId Optional team ID for team-specific prompts
   * @returns Promise with the generated description response
   */
  async generateDescription(
    conversation: string,
    teamId?: string,
  ): Promise<IAiPromptResponse<IAiDescriptionGenerationResponse>> {
    if (!this.activeProvider) {
      return {
        success: false,
        error: 'No active AI provider',
      };
    }

    const provider = this.providers.get(this.activeProvider);
    return provider.generateDescription(conversation, teamId);
  }
}
