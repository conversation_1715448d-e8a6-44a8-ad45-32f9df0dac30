import { Injectable } from '@nestjs/common';
import OpenAI from 'openai';
import { ILogger } from '../../utils';
import { MODEL_CONFIGS } from '../constants/ai-providers.constants';
import {
  IAiPromptRequest,
  IAiPromptResponse,
  IAiProviderConfig,
} from '../interfaces/ai-provider.interface';
import {
  BaseAiProvider,
  DEFAULT_SYSTEM_PROMPT,
  MethodType,
} from './base-ai.provider';

@Injectable()
export class OpenAiProvider extends BaseAiProvider {
  private client: OpenAI;

  constructor(protected readonly logger: ILogger) {
    super(logger);
  }

  async initialize(config: IAiProviderConfig): Promise<void> {
    this.config = config;
    this.client = new OpenAI({
      apiKey: config.apiKey,
      baseURL: config.baseUrl,
      // 30 s upper bound
      timeout: 30_000,
      maxRetries: 3,
    });

    // Helper function to check if a property exists
    const hasProperty = (obj: any, prop: string) =>
      obj && Object.prototype.hasOwnProperty.call(obj, prop);

    // Set up models
    if (config.models && Object.keys(config.models).length > 0) {
      this.models = config.models;
    } else {
      // Instead of hard-coding models, use configuration from MODEL_CONFIGS
      this.models = {};

      // Add OpenAI models from MODEL_CONFIGS
      const openAiModels = Object.entries(MODEL_CONFIGS).filter(
        ([id]) => id.startsWith('gpt-') || id.startsWith('o3-'),
      );

      for (const [modelId] of openAiModels) {
        this.models[modelId] = {
          modelId,
          temperature: 0.5,
          maxTokens: 4000,
        };

        // Set all OpenAI models as active in MODEL_CONFIGS
        const modelKey = modelId as keyof typeof MODEL_CONFIGS;
        if (hasProperty(MODEL_CONFIGS[modelKey], 'isActive')) {
          (MODEL_CONFIGS[modelKey] as any).isActive = true;
        }
      }

      // If no OpenAI models were found in MODEL_CONFIGS, add default models
      if (Object.keys(this.models).length === 0) {
        this.logger.warn(
          'No OpenAI models found in MODEL_CONFIGS, using default models',
        );
        this.models = {
          'gpt-4o': {
            modelId: 'gpt-4o',
            temperature: 0.5,
            maxTokens: 4000,
          },
          'o3-mini-2025-01-31': {
            modelId: 'o3-mini-2025-01-31',
            temperature: 0.5,
            maxTokens: 4000,
          },
        };
      }
    }

    // Set active model for this provider instance (doesn't affect MODEL_CONFIGS isActive)
    this.activeModel =
      config.defaultModel && this.models[config.defaultModel]
        ? config.defaultModel
        : Object.keys(this.models)[0];

    this.initialized = true;
    this.logger.debug(
      `OpenAI provider initialized with model: ${this.activeModel}`,
    );
    this.logger.debug('All OpenAI models in MODEL_CONFIGS set to active');
  }

  async executePrompt<T>(
    request: IAiPromptRequest,
    methodType?: MethodType,
  ): Promise<IAiPromptResponse<T>> {
    this.logger.debug(
      `[OpenAiProvider] executePrompt called | methodType: ${methodType}, promptLength: ${request.prompt?.length}, conversationLength: ${request.conversation?.length}`,
    );

    if (!this.initialized) {
      this.logger.error('[OpenAiProvider] Not initialized');
      return {
        success: false,
        error: 'OpenAI provider not initialized',
      };
    }

    try {
      const schema = this.getResponseSchema(methodType);
      const prompt = this.buildPrompt(request.prompt, request.conversation);
      const modelConfig = this.models[this.activeModel];

      // Get the schema description for use in the system message
      const schemaDescription = schema ? this.getSchemaDescription(schema) : '';

      this.logger.debug(
        `[OpenAiProvider] Prepared prompt and schema | model: ${modelConfig.modelId}, schemaDescription (first 200 chars): ${schemaDescription?.slice(0, 200)}, promptPreview (first 200 chars): ${prompt?.slice(0, 200)}`,
      );

      // Use chat completions API with structured output
      const response = await this.client.chat.completions.create({
        model: modelConfig.modelId,
        messages: [
          {
            role: 'system',
            content: `${DEFAULT_SYSTEM_PROMPT}
            ${
              schemaDescription
                ? `Provide your analysis using this schema:\n${schemaDescription}`
                : ''
            }`,
          },
          { role: 'user', content: prompt },
        ],
        // Commented out for now as we're using the structured output
        // temperature: request.temperature ?? modelConfig.temperature ?? 0.5,
        // max_tokens: request.maxTokens ?? modelConfig.maxTokens ?? 4000,
        response_format: { type: 'json_object' },

        ...(modelConfig.additionalParams ?? {}),
      });

      this.logger.debug(
        `[OpenAiProvider] Raw response received | snippet: ${JSON.stringify(response)?.slice(0, 500)}`,
      );

      // Parse the JSON response from the model
      let parsedData: T;
      try {
        const content = response.choices[0]?.message?.content ?? '';
        if (!content) {
          this.logger.error(
            `[OpenAiProvider] No content in response | raw response: ${JSON.stringify(response)?.slice(0, 300)}`,
          );
          throw new Error('No content in response');
        }

        // Helper function to strip code fences
        const stripCodeFences = (text: string): string => {
          return text.replace(/^```[\w]*\r?\n?/, '').replace(/\r?\n?```$/, '');
        };

        // Add fallback JSON extraction in case model ignores response_format
        const jsonCandidate =
          (/```json[\s\S]*?```/i.exec(content) ??
            /```[a-z]*[\s\S]*?```/i.exec(content) ??
            /{[\s\S]*}/.exec(content))?.[0] ?? content;

        this.logger.debug(
          `[OpenAiProvider] Extracted JSON candidate | preview (first 300 chars): ${jsonCandidate?.slice(0, 300)}`,
        );

        parsedData = JSON.parse(stripCodeFences(jsonCandidate));
        this.logger.debug(
          '[OpenAiProvider] Successfully parsed OpenAI response',
        );
      } catch (error) {
        let errorMessage = 'Unknown error';
        if (error instanceof Error) {
          errorMessage = error.message;
        } else {
          errorMessage = String(error);
        }
        this.logger.error(
          `[OpenAiProvider] Failed to parse OpenAI response | error: ${errorMessage}`,
        );
        throw new Error(`Failed to parse OpenAI response: ${errorMessage}`);
      }

      this.logger.debug('[OpenAiProvider] executePrompt succeeded');
      return {
        success: true,
        data: parsedData,
        rawResponse: response,
      };
    } catch (error) {
      // Handle OpenAI SDK specific errors
      if ((error as any)?.code === 'ETIMEDOUT') {
        this.logger.error('OpenAI API request timed out');
        return {
          success: false,
          error: 'Request timed out',
        };
      }

      // Handle rate limiting
      if ((error as any)?.status === 429) {
        this.logger.error('OpenAI API rate limit exceeded');
        return {
          success: false,
          error: 'Rate limit exceeded. Please try again later.',
        };
      }

      const errorMessage =
        error instanceof Error ? error.message : String(error);
      this.logger.error(`[OpenAiProvider] OpenAI API error: ${errorMessage}`);

      return {
        success: false,
        error: errorMessage,
      };
    }
  }
}
