export interface ModelConfig {
  readonly name: string;
  readonly description: string;
  readonly capabilities: readonly string[];
  readonly isDefault?: boolean;
  readonly isActive?: boolean;
}

export const PROVIDER_CONFIGS = {
  grok: {
    name: 'Grok',
    description: "xAI's Grok AI model (deprecated)",
  },
  claude: {
    name: '<PERSON>',
    description:
      "Anthropic's Claude AI model for advanced natural language processing",
  },
  openai: {
    name: 'OpenAI',
    description: "OpenAI's GPT models for powerful language understanding",
  },
  gemini: {
    name: 'Gemini',
    description: "Google's Gemini multimodal AI models",
  },
  // Add other providers as needed
} as const satisfies Record<
  string,
  {
    readonly name: string;
    readonly description: string;
  }
>;

export type ProviderId = keyof typeof PROVIDER_CONFIGS;

export const MODEL_CONFIGS = {
  'claude-3-7-sonnet-********': {
    name: 'Claude 3.7 Sonnet',
    description:
      "<PERSON>throp<PERSON>'s advanced reasoning model with state-of-the-art capabilities",
    capabilities: [
      'text-completion',
      'ticket-detection',
      'sentiment-analysis',
      'urgency-detection',
      'team-routing',
      'extended-thinking',
    ],
    isActive: true,
  },
  'gpt-4o': {
    name: 'GPT-4o',
    description:
      "OpenAI's most capable multimodal model optimized for performance",
    capabilities: [
      'text-completion',
      'ticket-detection',
      'sentiment-analysis',
      'urgency-detection',
      'team-routing',
    ],
    isDefault: true,
    isActive: true,
  },
  'o3-mini-2025-01-31': {
    name: 'GPT-o3-mini',
    description:
      "OpenAI's advanced model with strong reasoning capabilities and high efficiency",
    capabilities: [
      'text-completion',
      'ticket-detection',
      'sentiment-analysis',
      'urgency-detection',
      'team-routing',
      'extended-thinking',
    ],
    isActive: true,
  },
  // Add more models as needed
} as const satisfies Record<string, ModelConfig>;

export type ModelId = keyof typeof MODEL_CONFIGS;
