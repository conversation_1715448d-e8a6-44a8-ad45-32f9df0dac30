import { PromptType } from './prompt-types.enum';

export const DEFAULT_PROMPTS = {
  [PromptType.TICKET_DETECTION]:
    'Read the message and determine if it requires action. If yes, mark it as a ticket.',

  [PromptType.SENTIMENT_ANALYSIS]:
    'Analyze the tone and sentiment of this conversation. Consider factors like: urgency, frustration level, satisfaction, and overall mood.',

  [PromptType.URGENCY_DETECTION]:
    'Analyze the conversation to determine the priority level. Consider factors like business impact, time sensitivity, number of affected users, and severity of the problem. If specific urgency levels are provided in the prompt, use one of those. Otherwise, use "low", "medium", or "high".',

  [PromptType.TEAM_ROUTING]:
    'Based on the conversation, determine the most appropriate team and sub-team to handle this ticket. Consider: technical complexity, domain expertise required, and specific product/service mentioned.',

  [PromptType.CUSTOM_FIELDS]:
    'Extract relevant information to populate custom ticket fields. Look for: environment details, affected systems, steps to reproduce, and any specific requirements.',

  [PromptType.TITLE_GENERATION]:
    'Write a short, descriptive title based on the message. Prioritize clarity and make it easy to scan on a dashboard.',

  [PromptType.DESCRIPTION_GENERATION]:
    "Summarize the message so anyone on the team can quickly understand what it's about, what is needed, and any key context",
};
