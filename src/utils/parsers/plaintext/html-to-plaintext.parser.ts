import * as cheerio from 'cheerio';
import { Repository } from 'typeorm';
import { Users } from '../../../database/entities/users/users.entity';

/**
 * Converts HTML to Plain Text
 *
 * @class HtmlToPlainText
 * @example
 * ```javascript
 * const html = `<p>Some HTML content</p>`;
 * const converter = new HtmlToPlainText(html);
 * const plainText = converter.convert();
 * ```
 */
export class HtmlToPlainText {
  private $: cheerio.CheerioAPI;
  private text: string;
  private userRepository: Repository<Users>;

  /**
   * Creates an instance of HtmlToPlinText
   *
   * @param {String} html HTML to convert to plain text
   */
  constructor(html: any, userRepository: Repository<Users>) {
    this.$ = cheerio.load(html);
    this.text = '';
    this.userRepository = userRepository;
  }

  /**
   * Converts HTML to plain text
   *
   * @returns {String} Plain text
   */
  async convert() {
    const children = this.$('body').children();

    // Use a for of loop to iterate over the children
    for (const child of children) {
      await this.processNode(child);
    }

    return this.text.trim();
  }

  /**
   * Processes a node, and adds the appropriate block
   *
   * @param {Object} node Node to process
   */
  async processNode(node) {
    const nodeName = node.name;
    const nodeText = this.$(node).text().trim();

    if (nodeName === 'p' && nodeText) {
      this.text += `${await this.processInlineElements(this.$(node))}\n\n`;
    } else if (nodeName === 'ul' || nodeName === 'ol') {
      this.processListNode(node, nodeName === 'ul');
    } else if (nodeName === 'blockquote') {
      this.text += `> ${await this.processInlineElements(this.$(node))}\n\n`;
    } else if (nodeName === 'pre') {
      this.text += `\`\`\`\n${nodeText}\n\`\`\`\n\n`;
    } else if (
      nodeName === 'h1' ||
      nodeName === 'h2' ||
      nodeName === 'h3' ||
      nodeName === 'h4' ||
      nodeName === 'h5' ||
      nodeName === 'h6'
    ) {
      const headerText = await this.processInlineElements(this.$(node));
      const headerLevel = Number.parseInt(nodeName.charAt(1), 10);
      const underlineChar = headerLevel > 1 ? '-' : '=';
      const underline = underlineChar.repeat(headerText.length);
      this.text += `${headerText}\n${underline}\n\n`;
    } else if (nodeName === 'img') {
      const src = this.$(node).attr('src');
      const title = this.$(node).attr('title');
      const alt = this.$(node).attr('alt');
      if (title) {
        this.text += `[${title}](${src})\n\n`;
      } else if (alt) {
        this.text += `[${alt}](${src})\n\n`;
      } else {
        this.text += `![Image](${src})\n\n`;
      }
    } else if (nodeName === 'a') {
      const href = this.$(node).attr('href');
      const pTag = this.$(node).find('p');
      const pText = pTag.text();
      if (pText && href) {
        this.text += `[${pText}](${href})\n\n`;
      }
    }

    const children = this.$(node).children();

    // Use a for of loop to iterate over the children
    for (const child of children) {
      await this.processNode(child);
    }
  }

  /**
   * Processes a list node, and adds the appropriate text
   *
   * @param {Object} node Node to process
   * @param {Boolean} isBullet Whether the list is a bullet list
   */
  processListNode(node, isBullet) {
    const listItems = this.$(node)
      .find('li')
      .map((_, li) => {
        const prefix = isBullet ? '• ' : `${this.$(li).index() + 1}. `;
        const itemText = this.processInlineElements(this.$(li));
        return `${prefix}${itemText}`;
      })
      .get();

    this.text += `${listItems.join('\n')}\n\n`;
  }

  /**
   * Processes inline elements within a node
   *
   * @param {Object} node Node to process
   * @returns {String} Processed text
   */
  async processInlineElements(node) {
    let text = '';

    // Use a for of loop to iterate over the children
    for (const child of node.contents()) {
      if (child.type === 'text') {
        text += child.data;
      } else if (child.type === 'tag') {
        if (child.name === 'span' && this.$(child).hasClass('mention')) {
          const mentionId = this.$(child).attr('data-id');
          const userEmail = this.$(child).attr('data-email');
          const userLabel = this.$(child).attr('data-label');
          if (mentionId.startsWith('S') && mentionId.length < 15) { // TODO: This is a hack to avoid mentioning the whole sub team. Need to find a better way.
            text += `<!subteam^${mentionId}>`;
          } else if (userEmail) {
            const user = await this.userRepository.findOne({
              where: { slackProfileEmail: userEmail },
            });

            if (!user) {
              text += `@${userLabel}`;
            } else {
              text += `<@${user.slackId}>`;
            }
          } else {
            text += `@${userLabel}`;
          }
        } else if (child.name === 'strong') {
          const strongText = await this.processInlineElements(this.$(child));
          text += `**${strongText}**`;
        } else if (child.name === 'em') {
          const emText = await this.processInlineElements(this.$(child));
          text += `_${emText}_`;
        } else if (child.name === 's') {
          const sText = await this.processInlineElements(this.$(child));
          text += `~${sText}~`;
        } else if (child.name === 'code') {
          const codeText = await this.processInlineElements(this.$(child));
          text += `\`${codeText}\``;
        } else if (child.name === 'a') {
          const linkText = await this.processInlineElements(this.$(child));
          const href = this.$(child).attr('href');
          if (linkText && href) {
            text += `[${linkText}](${href})`;
          }
        } else {
          text += await this.processInlineElements(this.$(child));
        }
      }
    }

    return text;
  }
}
