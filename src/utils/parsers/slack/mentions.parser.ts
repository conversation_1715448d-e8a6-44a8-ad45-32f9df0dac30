import { Repository } from 'typeorm';
import { Installations, Users } from '../../../database/entities';

/**
 * Parses a string containing Slack mentions in the format <@userId|label> and converts them
 * to the appropriate format based on whether the user exists in the database.
 *
 * @param text The text containing Slack mentions to parse
 * @param userRepository The repository to use for looking up users
 * @param installation The installation to use for looking up users
 * @returns The parsed text with mentions converted to the appropriate format
 */
export async function parseWithMentions(
  text: string,
  userRepository: Repository<Users>,
  installation: Installations,
): Promise<string> {
  if (!text) {
    return '';
  }

  // Regular expression to match Slack mentions in the format <@userId|label>
  const mentionRegex = /<@([^|]+)\|([^>]+)>/g;
  let finalText = text;

  // Create arrays to store matches and promises
  const promises: Promise<Users | null>[] = [];
  const matches: Array<{ label: string; fullMatch: string }> = [];

  // Find all matches first
  let match: RegExpExecArray | null = mentionRegex.exec(text);
  while (match !== null) {
    const label = match[2];
    const fullMatch = match[0];

    // Store the match information
    matches.push({ label, fullMatch });

    // Create a promise for looking up the user

    const promise = userRepository
      .findOne({
        where: {
          installation: { id: installation.id },
          slackProfileEmail: label,
        },
      })
      .catch((error) => {
        // Log the error but continue with null to maintain functionality
        console.error(`Error looking up user with email ${label}:`, error);
        return null;
      });
    promises.push(promise);

    // Get next match without assignment in the loop condition
    match = mentionRegex.exec(text);
  }

  // Wait for all user lookups to complete
  const users = await Promise.all(promises);

  // Process the matches in reverse order to avoid changing the indices
  for (let i = matches.length - 1; i >= 0; i--) {
    const { label, fullMatch } = matches[i];
    const user = users[i];

    // Replace the match with the appropriate format
    if (user) {
      finalText = finalText.replace(fullMatch, `<@${user.slackId}>`);
    } else {
      finalText = finalText.replace(fullMatch, `@${label}`);
    }
  }

  return finalText;
}
