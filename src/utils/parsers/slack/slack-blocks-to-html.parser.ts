import {
  Block,
  KnownBlock,
  MrkdwnElement,
  PlainTextElement,
  RichTextBlock,
  RichTextList,
  RichTextSection,
} from '@slack/types';

/**
 * Parses Slack Blocks into HTML
 * @param blocks Slack Blocks to parse
 * @returns HTML string representation of the Slack Blocks
 */
export function slackBlocksToHtml(blocks: (KnownBlock | Block)[]): string {
  if (!blocks || !Array.isArray(blocks) || blocks.length === 0) {
    return '';
  }

  const htmlParts: string[] = [];

  for (const block of blocks) {
    const blockHtml = parseBlock(block);
    if (blockHtml) {
      htmlParts.push(blockHtml);
    }
  }

  return htmlParts.join('');
}

/**
 * Parses a single Slack Block into HTML
 */
function parseBlock(block: KnownBlock | Block): string {
  if (!block || !block.type) {
    return '';
  }

  switch (block.type) {
    case 'section':
      return parseSectionBlock(block);
    case 'rich_text':
      return parseRichTextBlock(block as RichTextBlock);
    case 'header':
      return parseHeaderBlock(block);
    case 'image':
      return parseImageBlock(block);
    case 'divider':
      return '<hr />';
    case 'context':
      return parseContextBlock(block);
    case 'actions':
      return parseActionsBlock(block);
    case 'input':
      return parseInputBlock(block);
    default:
      // For any unhandled block types, try to extract text content
      if ('text' in block) {
        return parseTextElement(block.text);
      }
      return '';
  }
}

/**
 * Parses a section block into HTML
 */
function parseSectionBlock(block: any): string {
  if (!block) {
    return '';
  }

  let html = '<div class="slack-section">';

  // Handle text
  if (block.text) {
    html += parseTextElement(block.text);
  }

  // Handle fields
  if (block.fields && Array.isArray(block.fields) && block.fields.length > 0) {
    html += '<div class="slack-fields">';
    for (const field of block.fields) {
      html += `<div class="slack-field">${parseTextElement(field)}</div>`;
    }
    html += '</div>';
  }

  // Handle accessory (like an image or button)
  if (block.accessory) {
    if (block.accessory.type === 'image') {
      html += `<div class="slack-accessory"><img src="${block.accessory.image_url}" alt="${block.accessory.alt_text || ''}" /></div>`;
    } else if (block.accessory.type === 'button') {
      html += `<div class="slack-accessory"><button>${parseTextElement(block.accessory.text)}</button></div>`;
    }
  }

  html += '</div>';
  return html;
}

/**
 * Parses a Slack text element (mrkdwn or plain_text) into HTML
 */
function parseTextElement(
  textElement: MrkdwnElement | PlainTextElement | any,
): string {
  if (!textElement) {
    return '';
  }

  if (typeof textElement === 'string') {
    return escapeHtml(textElement);
  }

  if (textElement.type === 'mrkdwn') {
    // Convert Slack markdown to HTML
    return convertSlackMarkdownToHtml(textElement.text || '');
  }

  if (textElement.type === 'plain_text') {
    return escapeHtml(textElement.text || '');
  }

  return '';
}

/**
 * Parses a rich text block into HTML
 */
function parseRichTextBlock(block: RichTextBlock): string {
  if (!block || !block.elements) {
    return '';
  }

  let html = '<div class="slack-rich-text">';

  for (const element of block.elements) {
    html += parseRichTextElement(element);
  }

  html += '</div>';
  return html;
}

/**
 * Parses a rich text element into HTML
 */
function parseRichTextElement(element: any): string {
  if (!element || !element.type) {
    return '';
  }

  switch (element) {
    case 'rich_text_section':
      return parseRichTextSection(element);
    case 'rich_text_list':
      return parseRichTextList(element);
    default:
      return '';
  }
}

/**
 * Parses a rich text section into HTML
 */
function parseRichTextSection(section: RichTextSection): string {
  if (!section || !section.elements) {
    return '';
  }

  let html = '<div class="slack-rich-text-section">';

  for (const element of section.elements) {
    if (element.type === 'text') {
      let text = escapeHtml(element.text || '');

      // Apply formatting
      if (element.style?.bold) {
        text = `<strong>${text}</strong>`;
      }
      if (element.style?.italic) {
        text = `<em>${text}</em>`;
      }
      if (element.style?.strike) {
        text = `<del>${text}</del>`;
      }
      if (element.style?.code) {
        text = `<code>${text}</code>`;
      }

      html += text;
    } else if (element.type === 'link') {
      html += `<a href="${element.url || '#'}">${escapeHtml(element.text || element.url || '')}</a>`;
    } else if (element.type === 'user') {
      html += `<span class="slack-mention slack-user-mention">@${element.user_id || 'user'}</span>`;
    } else if (element.type === 'channel') {
      html += `<span class="slack-mention slack-channel-mention">#${element.channel_id || 'channel'}</span>`;
    } else if (element.type === 'emoji') {
      html += `<span class="slack-emoji">:${element.name || ''}:</span>`;
    }
  }

  html += '</div>';
  return html;
}

/**
 * Parses a rich text list into HTML
 */
function parseRichTextList(list: RichTextList): string {
  if (!list || !list.elements) {
    return '';
  }

  const listTag = list.style === 'ordered' ? 'ol' : 'ul';
  let html = `<${listTag} class="slack-list">`;

  for (const element of list.elements) {
    if (element.type === 'rich_text_section') {
      html += `<li>${parseRichTextSection(element as RichTextSection)}</li>`;
    }
  }

  html += `</${listTag}>`;
  return html;
}

/**
 * Parses a header block into HTML
 */
function parseHeaderBlock(block: any): string {
  if (!block || !block.text) {
    return '';
  }

  const headerText = parseTextElement(block.text);
  return `<h3 class="slack-header">${headerText}</h3>`;
}

/**
 * Parses an image block into HTML
 */
function parseImageBlock(block: any): string {
  if (!block || !block.image_url) {
    return '';
  }

  let html = '<div class="slack-image">';
  html += `<img src="${block.image_url}" alt="${block.alt_text || ''}" />`;

  if (block.title) {
    html += `<div class="slack-image-title">${parseTextElement(block.title)}</div>`;
  }

  html += '</div>';
  return html;
}

/**
 * Parses a context block into HTML
 */
function parseContextBlock(block: any): string {
  if (!block || !block.elements) {
    return '';
  }

  let html = '<div class="slack-context">';

  for (const element of block.elements) {
    if (element.type === 'image') {
      html += `<img src="${element.image_url}" alt="${element.alt_text || ''}" class="slack-context-image" />`;
    } else {
      html += `<div class="slack-context-text">${parseTextElement(element)}</div>`;
    }
  }

  html += '</div>';
  return html;
}

/**
 * Parses an actions block into HTML
 */
function parseActionsBlock(block: any): string {
  if (!block || !block.elements) {
    return '';
  }

  let html = '<div class="slack-actions">';

  for (const element of block.elements) {
    if (element.type === 'button') {
      html += `<button class="slack-button">${parseTextElement(element.text)}</button>`;
    } else if (element.type === 'select') {
      html += '<select class="slack-select">';
      if (element.placeholder) {
        html += `<option disabled selected>${parseTextElement(element.placeholder)}</option>`;
      }
      if (element.options) {
        for (const option of element.options) {
          html += `<option value="${option.value}">${parseTextElement(option.text)}</option>`;
        }
      }
      html += '</select>';
    }
  }

  html += '</div>';
  return html;
}

/**
 * Parses an input block into HTML
 */
function parseInputBlock(block: any): string {
  if (!block || !block.element) {
    return '';
  }

  let html = '<div class="slack-input">';

  if (block.label) {
    html += `<label class="slack-input-label">${parseTextElement(block.label)}</label>`;
  }

  if (block.element.type === 'plain_text_input') {
    const placeholder = block.element.placeholder
      ? ` placeholder="${escapeHtml(block.element.placeholder.text)}"`
      : '';
    html += `<input type="text"${placeholder} class="slack-text-input" />`;
  } else if (block.element.type === 'select') {
    html += '<select class="slack-select">';
    if (block.element.placeholder) {
      html += `<option disabled selected>${parseTextElement(block.element.placeholder)}</option>`;
    }
    if (block.element.options) {
      for (const option of block.element.options) {
        html += `<option value="${option.value}">${parseTextElement(option.text)}</option>`;
      }
    }
    html += '</select>';
  }

  html += '</div>';
  return html;
}

/**
 * Converts Slack markdown to HTML
 */
function convertSlackMarkdownToHtml(text: string): string {
  if (!text) {
    return '';
  }

  // Replace new lines with <br>
  let html = text.replace(/\n/g, '<br>');

  // Bold: *text* -> <strong>text</strong>
  html = html.replace(/\*(.*?)\*/g, '<strong>$1</strong>');

  // Italic: _text_ -> <em>text</em>
  html = html.replace(/_(.*?)_/g, '<em>$1</em>');

  // Strikethrough: ~text~ -> <del>text</del>
  html = html.replace(/~(.*?)~/g, '<del>$1</del>');

  // Code: `text` -> <code>text</code>
  html = html.replace(/`([^`]+)`/g, '<code>$1</code>');

  // Preformatted: ```text``` -> <pre>text</pre>
  html = html.replace(/```([\s\S]*?)```/g, '<pre>$1</pre>');

  // Links: <https://example.com|Text> -> <a href="https://example.com">Text</a>
  html = html.replace(/<(https?:\/\/[^|>]+)\|([^>]+)>/g, '<a href="$1">$2</a>');

  // Plain links: <https://example.com> -> <a href="https://example.com">https://example.com</a>
  html = html.replace(/<(https?:\/\/[^>]+)>/g, '<a href="$1">$1</a>');

  // User mentions: <@U123456> -> <span class="mention">@User</span>
  html = html.replace(
    /<@([A-Z0-9]+)>/g,
    '<span class="slack-mention slack-user-mention">@$1</span>',
  );

  // Channel mentions: <#C123456> -> <span class="mention">#channel</span>
  html = html.replace(
    /<#([A-Z0-9]+)>/g,
    '<span class="slack-mention slack-channel-mention">#$1</span>',
  );

  return html;
}

/**
 * Escapes HTML special characters
 */
function escapeHtml(text: string): string {
  return text
    .replace(/&/g, '&amp;')
    .replace(/</g, '&lt;')
    .replace(/>/g, '&gt;')
    .replace(/"/g, '&quot;')
    .replace(/'/g, '&#039;');
}
