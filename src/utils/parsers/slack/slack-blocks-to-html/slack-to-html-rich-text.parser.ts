import { Inject, Injectable } from '@nestjs/common';
import { InjectRepository } from '@nestjs/typeorm';
import { UsersInfoResponse } from '@slack/web-api';
import { encode } from 'html-entities';
import { isEmpty } from 'lodash';
import { Repository } from 'typeorm';
import { Installations, Users } from '../../../../database/entities';
import { ChannelType } from '../../../../database/entities/channels/channels.entity';
import { ChannelsRepository } from '../../../../database/entities/channels/repositories';
import { SlackSubgroupsRepository } from '../../../../database/entities/subgroups/repositories/subgroups.repository';
import { SlackWebAPIService } from '../../../../slack/providers/slack-apis/slack-apis.service';
import { CUSTOM_LOGGER_TOKEN, ILogger } from '../../../logger';
import { isValidNonEmptyString } from '../../../primitives';

interface StylizeArgs {
  element: any;
  installation: Installations;
  raw: boolean;
}

interface ParseRichTextListArgs {
  element: any;
  listItemIndex?: number;
  installation: Installations;
  raw: boolean;
}

interface ParseRichTextPreformattedArgs {
  element: any;
  installation: Installations;
  raw: boolean;
}

interface SlackMessageArgs {
  blocks: any;
  installation: Installations;
  raw: boolean;
}

interface ParseRichTextQuoteArgs {
  element: any;
  installation: Installations;
  raw: boolean;
}

interface ParseRichTextSectionArgs {
  element: any;
  installation: Installations;
  raw: boolean;
}

@Injectable()
export class SlackToHTMLRichTextParser {
  constructor(
    @Inject(CUSTOM_LOGGER_TOKEN) private readonly logger: ILogger,

    // Database Repositories
    @InjectRepository(Users)
    private readonly userRepository: Repository<Users>,
    private readonly slackChannelsRepository: ChannelsRepository,

    // External API Clients
    private readonly slackWebApiService: SlackWebAPIService,
    private readonly slackSubgroupsRepository: SlackSubgroupsRepository,
  ) {
    // Bind all the methods to preserve 'this' context
    this.stylize = this.stylize.bind(this);
    this.parseRichTextPreformatted = this.parseRichTextPreformatted.bind(this);
    this.parseRichTextSection = this.parseRichTextSection.bind(this);
    this.parseRichTextList = this.parseRichTextList.bind(this);
    this.parseRichTextQuote = this.parseRichTextQuote.bind(this);
    this.slackToHTMLParser = this.slackToHTMLParser.bind(this);
  }

  private async stylize({ element, installation, raw }: StylizeArgs) {
    let html = '';

    if (element.type === 'link') {
      const cleanedUrl = encode(element.url);
      const linkText = element.text ? encode(element.text) : cleanedUrl;
      html += `<a target="_blank" rel="noopener noreferrer nofollow" href='${cleanedUrl}'>${linkText.replaceAll(
        /\n/g,
        '<br />',
      )}</a>`;
    } else if (element.type === 'text') {
      let cleanedText = encode(element.text);
      // convert all leading spaces to &nbsp; as browser cant render the leading spaces present in html
      cleanedText = cleanedText.replace(/^(\s+)(?=\S)/gm, (match) =>
        match
          .replace(/ /g, '&nbsp;')
          .replace(/\t/g, '&nbsp;&nbsp;&nbsp;&nbsp;'),
      );
      html += cleanedText.replaceAll(/\n/g, '<br />');
    } else if (element.type === 'emoji') {
      if (isValidNonEmptyString(element.unicode, true)) {
        html += element?.unicode
          ? element.unicode
              .split('-')
              .map((code) => `&#x${code};`)
              .join('')
          : `:${element.name}:`;
      } else if (isValidNonEmptyString(element?.display_url, true) && !raw) {
        html += `<img data-type="custom-emoji" height="20px" width="20px" display="inline" data-name="${element.name}" src="${element.display_url}" />`;
      } else {
        html += element?.unicode
          ? element.unicode
              .split('-')
              .map((code) => `&#x${code};`)
              .join('')
          : `:${element.name}:`;
      }
    } else if (element.type === 'user') {
      const userId = element.user_id;
      const user = await this.userRepository.findOne({
        where: { slackId: userId, installation: { id: installation.id } },
      });

      let slack_user_info: UsersInfoResponse;

      if (!isEmpty(user)) {
        const userName =
          user.slackProfileDisplayName ||
          user.slackProfileRealName ||
          user.name;
        html += `<span class="mention" data-type="mention" data-id="${userId}" data-email="${user.slackProfileEmail}" data-label="${userName}">@${userName}</span>`;
      } else {
        try {
          slack_user_info = await this.slackWebApiService.getUserInfo(
            installation.botToken,
            { user: userId },
          );

          const userName =
            slack_user_info.user.profile.display_name ||
            slack_user_info.user.profile.real_name ||
            slack_user_info.user.name;

          html += `<span class="mention" data-type="mention" data-id="${userId}" data-email="${slack_user_info?.user?.profile.email}" data-label="${userName}">@${userName}</span>`;
        } catch (error) {
          html += `<span class="mention" data-type="mention" data-id="${userId}">@User</span>`;
          if (error instanceof Error) {
            this.logger.error(
              `Error fetching user info from Slack API for user ID ${userId} - error - ${error.message} - error stack - ${error?.stack}`,
            );
          }
        }
      }
    } else if (element.type === 'usergroup') {
      const groupId = element.usergroup_id;
      const userGroup = await this.slackSubgroupsRepository.findByCondition({
        where: {
          slackGroupId: groupId,
          installation: { id: installation.id },
        },
      });

      if (!isEmpty(userGroup)) {
        const groupName = userGroup.slackHandle;
        html += `<span class="mention" data-type="usergroup" data-id="${groupId}" data-label="${groupName}">@${groupName}</span>`;
      } else {
        try {
          const slackUserGroup =
            await this.slackSubgroupsRepository.findByCondition({
              where: {
                installation: { id: installation.id },
                slackGroupId: groupId,
              },
            });

          const groupName = slackUserGroup.slackHandle;

          html += `<span class="mention" data-type="usergroup" data-id="${groupId}" data-label="${groupName}">@${groupName}</span>`;
        } catch (error) {
          // html += `<span class="mention" data-type="usergroup" data-id="${groupId}">@UserGroup</span>`;
          if (error instanceof Error) {
            this.logger.error(
              `Error fetching user group info from Slack API for group ID ${groupId} - error - ${error.message} - error stack - ${error?.stack}`,
            );
          }
        }
      }
    } else if (element.type === 'channel') {
      const channelId = element.channel_id;

      // Fetch the channel info from the database
      let channel = await this.slackChannelsRepository.findByCondition({
        where: {
          channelId,
          installation: { id: installation.id },
        },
      });

      if (isEmpty(channel)) {
        this.logger.warn(
          `[SlackToHTMLParser] Channel not found for channel ID ${channelId}`,
        );

        const conversationsInfo =
          await this.slackWebApiService.getConversationInfo(
            installation.botToken,
            { channel: channelId },
          );

        // Update the channel info once fetched from Slack API
        if (conversationsInfo?.ok) {
          const channelInfo = conversationsInfo?.channel;

          // Update the channel info in the database
          if (!isEmpty(channelInfo)) {
            channel = await this.slackChannelsRepository.save({
              name: channelInfo.name || channelInfo.name_normalized,
              channelDump: channelInfo as Record<string, any>,
              channelId: channelInfo.id,
              slackCreatedAt: channelInfo.created.toString(),
              isBotActive: false,
              isBotJoined: false,
              channelType: ChannelType.NOT_SETUP,
              isArchived: channelInfo.is_archived,
              isPrivate: channelInfo.is_private,
              isShared: channelInfo.is_ext_shared,
              sharedTeamIds: channelInfo.shared_team_ids,
              installation: { id: installation.id },
              organization: { id: installation.organization.id },
            });
          }
        }
      }

      if (isEmpty(channel)) {
        html += `<a href="slack://channel?team=${installation.teamId}&id=${channelId}">#channel</a>`;
      } else if (channel.isPrivate) {
        html += `<a href="slack://channel?team=${installation.teamId}&id=${channel.channelId}">&#128274;private-channel</a>`;
      } else {
        html += `<a href="slack://channel?team=${installation.teamId}&id=${channel.channelId}">#${channel.name}</a>`;
      }
    } else if (element.type === 'color') {
      html += `<span style="display: inline-flex; align-items: center; gap: 2px;">
			<span style="font-family: monospace;">${element.value.toUpperCase()}</span><span style="display: inline-block; width: 13px; height: 13px; background-color: ${
        element.value
      }; border-radius: 3px;"></span>
		</span>`;
    }

    html = element?.style?.code ? `<code>${html}</code>` : html;
    html = element?.style?.strike ? `<s>${html}</s>` : html;
    html = element?.style?.italic ? `<em>${html}</em>` : html;
    html = element?.style?.bold ? `<strong>${html}</strong>` : html;

    return html;
  }

  private async parseRichTextPreformatted({
    element,
    installation,
    raw,
  }: ParseRichTextPreformattedArgs) {
    let html = '';
    html += '<pre><code>';

    // We need to keep the `await` keyword in the loop therefore we can't
    // use `forEach` here, however the `for...of` loop allows us to use `await`
    // eslint-disable-next-line no-restricted-syntax
    for (const el of element.elements) {
      // eslint-disable-next-line no-await-in-loop
      html += await this.stylize({ element: el, installation, raw });
    }

    html += '</code></pre>';

    return element?.border === 0 ? html : `<blockquote>${html}</blockquote>`;
  }

  private async parseRichTextSection({
    element,
    installation,
    raw,
  }: ParseRichTextSectionArgs) {
    let html = '';
    const nestedElements = element.elements;
    html += '<p>';

    // We need to keep the `await` keyword in the loop therefore we can't
    // use `forEach` here, however the `for...of` loop allows us to use `await`
    // eslint-disable-next-line no-restricted-syntax
    for (const el of nestedElements) {
      // eslint-disable-next-line no-await-in-loop
      html += `${await this.stylize({ element: el, installation, raw })}`;
    }

    html += '</p>';

    return html;
  }

  private async parseRichTextList({
    element,
    listItemIndex = 1,
    installation,
    raw,
  }: ParseRichTextListArgs) {
    let html = '';
    const nestedElements = element.elements;
    const indentLevel = element.indent > 20 ? 20 : element.indent;
    const { border } = element;
    const listTag = element.style === 'ordered' ? 'ol' : 'ul';
    const nestedOpenTags = `<${listTag}>`.repeat(indentLevel);
    const nestedCloseTags = `</${listTag}>`.repeat(indentLevel);
    html += `<${listTag}>${nestedOpenTags}`;

    let i = 0;
    // We need to keep the `await` keyword in the loop therefore we can't
    // use `forEach` here, however the `for...of` loop allows us to use `await`
    // eslint-disable-next-line no-restricted-syntax
    for (const el of nestedElements) {
      if (el.type === 'rich_text_section') {
        // eslint-disable-next-line no-await-in-loop
        html += `<li value="${listItemIndex}">${await this.parseRichTextSection({ element: el, installation, raw })}</li>`;
        // eslint-disable-next-line no-param-reassign
        listItemIndex += 1;
      } else if (el.type === 'rich_text_list') {
        if (i !== 0) {
          html += '</li>';
        }

        // eslint-disable-next-line no-await-in-loop
        html += await this.parseRichTextList({
          element: el,
          installation,
          raw,
        });

        if (i !== nestedElements.length - 1) {
          html += '<li>';
        }
      }

      i += 1;
    }
    html += `${nestedCloseTags}</${listTag}>`;
    return border === 0 ? html : `<blockquote>${html}</blockquote>`;
  }

  private async parseRichTextQuote({
    element,
    installation,
    raw,
  }: ParseRichTextQuoteArgs) {
    let html = '';
    const nestedElements = element.elements;
    html += '<blockquote>';

    // We need to keep the `await` keyword in the loop therefore we can't
    // use `forEach` here, however the `for...of` loop allows us to use `await`
    // eslint-disable-next-line no-restricted-syntax
    for (const el of nestedElements) {
      // eslint-disable-next-line no-await-in-loop
      html += `${await this.stylize({ element: el, installation, raw })}`;
    }

    html += '</blockquote>';

    return html;
  }

  async slackToHTMLParser(message: SlackMessageArgs) {
    let html = '<div>';
    const { blocks, installation, raw = false } = message;

    if (!blocks || blocks.length === 0) {
      return `${html}</div>`;
    }

    const parserMap = {
      rich_text_section: this.parseRichTextSection,
      rich_text_list: this.parseRichTextList,
      rich_text_preformatted: this.parseRichTextPreformatted,
      rich_text_quote: this.parseRichTextQuote,
    };

    // We need to keep the `await` keyword in the loop therefore we can't
    // use `forEach` here, however the `for...of` loop allows us to use `await`
    // eslint-disable-next-line no-restricted-syntax
    for (const block of blocks) {
      if (block.type === 'rich_text') {
        const { elements } = block;
        let listItemIndex = {};

        // NOTE: This inner loop is needed since we need to parse one-by-one
        if (elements?.length) {
          // eslint-disable-next-line no-restricted-syntax
          for (const element of elements) {
            if (
              element.type === 'rich_text_list' &&
              (element.style === 'ordered' || element.style === 'bullet')
            ) {
              if (
                !Object.prototype.hasOwnProperty.call(
                  listItemIndex,
                  element.indent,
                )
              ) {
                listItemIndex[element.indent] = 1;
              }

              // Remove all list item indices that are greater than the current element's indent
              for (const key of Object.keys(listItemIndex)) {
                if (Number.parseInt(key, 10) > element.indent) {
                  delete listItemIndex[key];
                }
              }

              // Parse the rich text list
              html += await this.parseRichTextList({
                element,
                installation,
                listItemIndex: listItemIndex[element.indent],
                raw,
              });

              listItemIndex[element.indent] += element.elements.length;
            } else {
              listItemIndex = {};
              html += await parserMap[element.type]?.({
                element,
                installation,
                raw,
              });
            }
          }
        }
      }
    }

    html = html.replaceAll('</blockquote><blockquote>', '');

    return `${html}</div>`;
  }
}
