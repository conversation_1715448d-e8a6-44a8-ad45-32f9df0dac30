import { Injectable, Logger } from '@nestjs/common';
import { InjectRepository } from '@nestjs/typeorm';
import { decode } from 'html-entities';
import { Repository } from 'typeorm';
import { Installations, Users } from '../../../../database/entities';
import { SlackToHTMLRichTextParser } from './slack-to-html-rich-text.parser';

export interface SlackBlocksToHtmlOptions {
  raw?: boolean;
}

@Injectable()
export class BaseSlackBlocksToHtml {
  private blocks: any[];
  private html: string;
  private installation: Installations;
  private raw: boolean;
  private readonly logger = new Logger(BaseSlackBlocksToHtml.name);

  constructor(
    @InjectRepository(Users)
    private readonly usersRepository: Repository<Users>,
    private readonly slackToHTMLRichTextParser: SlackToHTMLRichTextParser,
  ) {}

  initialize(
    blocks: any,
    installation: Installations,
    options: SlackBlocksToHtmlOptions = {},
  ): void {
    this.blocks = blocks;
    this.html = '';
    this.installation = installation;
    this.raw = options?.raw || false;
  }

  async convert(): Promise<string> {
    try {
      let listItems: string[] = [];
      let listType: string | null = null;

      for (const block of this.blocks) {
        switch (block.type) {
          case 'header': {
            await this.processHeaderBlock(block);
            break;
          }

          case 'section': {
            await this.processSectionBlock(block);
            break;
          }

          case 'rich_text': {
            this.html += await this.slackToHTMLRichTextParser.slackToHTMLParser(
              {
                blocks: [block],
                installation: this.installation,
                raw: this.raw,
              },
            );
            break;
          }

          case 'context': {
            await this.processContextBlock(block);
            break;
          }

          case 'divider': {
            this.html += '<hr>';
            break;
          }

          case 'paragraph': {
            const paragraphText = block.text.text;
            if (paragraphText.startsWith('• ')) {
              listItems.push(paragraphText.slice(2));
              listType = 'ul';
            } else if (/^\d+\. /.test(paragraphText)) {
              listItems.push(paragraphText.replace(/^\d+\.\s*/, ''));
              listType = 'ol';
            } else {
              if (listItems.length > 0) {
                this.html += await this.processListItems(listItems, listType);
                listItems = [];
                listType = null;
              }
              this.html += `<p>${paragraphText}</p>`;
            }
            break;
          }

          case 'image': {
            this.html += `<img src='${block.image_url}' alt='${block.alt_text || ''}'>`;
            break;
          }

          case 'video': {
            this.html += `<video controls poster='${block.thumbnail_url}'><source src='${block.video_url}'></video>`;
            break;
          }

          // Add cases for other block types as needed
          default:
            break;
        }
      }

      if (listItems.length > 0) {
        this.html += await this.processListItems(listItems, listType);
      }

      return this.html;
    } catch (error) {
      const errorMsg = error instanceof Error ? error.message : String(error);
      if (errorMsg.includes('channel_not_found')) {
        // Reconstruct the message, replacing channel mentions with 'private_channel'
        const reconstructed = this.reconstructWithChannelPlaceholder(
          this.blocks,
          'private_channel',
        );
        return `<div><p>${reconstructed}</p></div>`;
      }

      // Only log unexpected errors
      this.logger.error(
        `Error converting Slack blocks to HTML: ${error instanceof Error ? error.message : 'Unknown error'}, stack: ${error instanceof Error ? error.stack : ''}`,
      );

      return '';
    }
  }

  // Section and header are used only in broadcasts. Adding <p></p> at the end to replicate spacing we show on broadcast editor and slack.
  private async processHeaderBlock(block: any): Promise<void> {
    const headerText = await this.processMarkdown(block.text.text);
    this.html += `<div><h1><strong>${headerText}</strong></h1> <p></p></div>`;
  }

  // Section and header are used only in broadcasts. Adding <p></p> at the end to replicate spacing we show on broadcast editor and slack.
  private async processSectionBlock(block: any): Promise<void> {
    if (block.text) {
      if (decode(block.text.text).startsWith('>')) {
        // If the context text starts with '>', it's a blockquote
        this.html += await this.processMarkdown(block.text.text);
      } else {
        // Otherwise, wrap the context text in <p> tags
        this.html += `<div><p>${await this.processMarkdown(block.text.text)}</p> <p></p></div>`;
      }
    }

    if (block.fields) {
      const listType = await this.getListType(block.fields);
      if (listType === 'ul') {
        this.html += '<ul>';
      } else if (listType === 'ol') {
        this.html += '<ol>';
      }

      for (const field of block.fields) {
        let fieldText = field.text;

        // Remove the bullet point or number prefix from the field text
        if (listType === 'ul') {
          fieldText = fieldText.replace(/^• /, '');
        } else if (listType === 'ol') {
          fieldText = fieldText.replace(/^\d+\. /, '');
        }

        const processedText = await this.processMarkdown(fieldText);
        this.html += `<li>${processedText}</li>`;
      }

      if (listType === 'ul') {
        this.html += '</ul>';
      } else if (listType === 'ol') {
        this.html += '</ol>';
      }
    }
  }

  private async getListType(fields: any[]): Promise<string | null> {
    const hasBullet = fields.some((field) => field.text.startsWith('• '));
    const hasNumber = fields.some((field) => /^\d+\. /.test(field.text));

    if (hasBullet && !hasNumber) {
      return 'ul';
    }
    if (!hasBullet && hasNumber) {
      return 'ol';
    }

    return null;
  }

  private async processContextBlock(block: any): Promise<void> {
    const contextText = block.elements[0].text;

    if (decode(contextText).startsWith('>')) {
      // If the context text starts with '>', it's a blockquote
      this.html += await this.processMarkdown(contextText);
    } else {
      // Otherwise, wrap the context text in <p> tags
      this.html += `<p>${await this.processMarkdown(contextText)}</p>`;
    }
  }

  private async processListItems(
    items: string[],
    listType: string | null,
  ): Promise<string> {
    let html = '';

    if (listType === 'ul') {
      html += '<ul>';
    } else if (listType === 'ol') {
      html += '<ol>';
    }

    for (const item of items) {
      html += `<li>${await this.processMarkdown(item)}</li>`;
    }

    if (listType === 'ul') {
      html += '</ul>';
    } else if (listType === 'ol') {
      html += '</ol>';
    }

    return html;
  }

  private async processMarkdown(text: string): Promise<string> {
    let t = decode(text);

    // convert all leading spaces to &nbsp; as browser cant render the leading spaces present in html
    t = t.replace(/^(\s+)(?=\S)/gm, (match) => {
      return match
        .replace(/ /g, '&nbsp;')
        .replace(/\t/g, '&nbsp;&nbsp;&nbsp;&nbsp;');
    });

    // const emojiMatches = [...t.matchAll(/:([a-z0-9_]+):/g)];
    // for (const match of emojiMatches) {
    //   const p1 = match[1];
    //   let replacement = '';

    //   if (!this.raw) {
    //     const customEmojis = await this.installationResourcesModel
    //       .findOne({
    //         resource_type: INSTALLATION_RESOURCE_TYPES.CUSTOM_EMOJIS,
    //         installation_id: new Types.ObjectId(this.installation._id),
    //       })
    //       .lean();

    //     if (customEmojis && !isEmpty(customEmojis.resource_data)) {
    //       const emojis = customEmojis.resource_data;
    //       const emojiLink = emojis[p1];

    //       if (isValidString(emojiLink, true)) {
    //         replacement = `<img data-type="custom-emoji" data-name="${p1}" src="${emojiLink}" />`;
    //       }
    //     }
    //   }

    //   if (!replacement) {
    //     const p1WithoutTone = removeSkinToneFromShortcodes(p1);
    //     const emoji = emojiMap[p1WithoutTone];
    //     replacement = emoji
    //       ? `<span data-name="${p1}" data-type="emoji">${emoji}</span>`
    //       : '';
    //   }

    //   t = t.replace(match[0], replacement);
    // }

    // Convert block quotes identified by '>' at the beginning of the line
    t = t.replace(/^>\s*(.*)/gm, (_match, content) => {
      const lines = content.split('\n').map((line) => line.trim());
      const formattedLines = lines.map((line) => `<p>${line}</p>`).join('');
      return `<blockquote>${formattedLines}</blockquote>`;
    });

    t = t.replace(/\*([^*]+)\*/g, '<strong>$1</strong>');
    t = t.replace(/_([^_]+)_/g, '<em>$1</em>');
    t = t.replace(/~([^~]+)~/g, '<s>$1</s>');
    t = t.replace(/```([\s\S]+?)```/g, (_match, code) => {
      // Remove leading and trailing line breaks from the code block
      const trimmedCode = code.replace(/^\n/, '');
      return `<pre><code>${trimmedCode}</code></pre>`;
    });
    t = t.replace(/(?:\r\n|\r|\n)/g, '<br>');
    t = t.replace(/`([^`]+)`/g, '<code>$1</code>');
    // Replace URLs in angle brackets with anchor tags (Pipe Handling included), using display text if provided or defaulting to the URL.
    t = t.replace(/<https?:\/\/[^>]+>/g, (match) => {
      const [url, urlText] = match.slice(1, -1).split('|');
      return `<a href="${url}" target="_blank">${urlText || url}</a>`;
    });

    const userMentionRegex = /<@(.*?)>/g;
    const mentions = t.match(userMentionRegex);

    if (mentions) {
      for (const mention of mentions) {
        const userId = mention.slice(2, -1);
        const user = await this.usersRepository.findOne({
          where: {
            slackId: userId,
            installation: { id: this.installation.id },
            organization: { id: this.installation.organization.id },
          },
        });

        t = t.replace(
          mention,
          `@${user?.slackProfileDisplayName || user?.slackProfileRealName || user?.name || userId}`,
        );
      }
    }

    return t;
  }

  /**
   * Reconstructs the original message from Slack blocks, replacing channel mentions with a placeholder.
   * @param blocks The Slack blocks array
   * @param placeholder The placeholder string to use for missing channels
   */
  private reconstructWithChannelPlaceholder(
    blocks: any[],
    placeholder: string,
  ): string {
    if (!Array.isArray(blocks)) return '';
    let result = '';
    for (const block of blocks) {
      // Only process rich_text blocks for this logic
      if (block.type === 'rich_text' && Array.isArray(block.elements)) {
        for (const el of block.elements) {
          if (el.type === 'rich_text_section' && Array.isArray(el.elements)) {
            for (const sectionEl of el.elements) {
              if (sectionEl.type === 'channel') {
                // Add leading space if not first character, always add trailing space
                if (result.length === 0) {
                  result += placeholder + ' ';
                } else {
                  result += ' ' + placeholder + ' ';
                }
              } else if (sectionEl.type === 'text') {
                result += sectionEl.text;
              }
              // Add more cases as needed for other element types
            }
          }
        }
      }
    }
    return result;
  }
}
