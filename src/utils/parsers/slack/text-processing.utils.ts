import { ILogger } from '../../logger';

/**
 * Default message content to use when no text is provided
 */
export const DEFAULT_MESSAGE_CONTENT = 'No content';

/**
 * Process Slack message text to preserve email addresses and properly format links
 * This function handles various formats of email addresses and links in Slack messages:
 * - mailto links: <mailto:<EMAIL>|<EMAIL>>
 * - links with email addresses: <https://example.com|<EMAIL>>
 * - plain email links: <<EMAIL>>
 *
 * @param text The original Slack message text
 * @param defaultContent Default content to return if text is empty
 * @param logger Optional logger for debugging
 * @returns Processed text with preserved email addresses and formatted links
 */
export function processSlackMessageText(
  text: string,
  defaultContent: string = DEFAULT_MESSAGE_CONTENT,
  logger?: ILogger,
): string {
  const SPAN_ID = 'processSlackMessageText';

  if (!text) {
    if (logger) {
      logger.debug(
        `${SPAN_ID} No text provided, returning default message content`,
      );
    }
    return defaultContent;
  }

  let processedText = text;

  // Handle mailto links in the format <mailto:<EMAIL>|<EMAIL>>
  processedText = processedText.replace(/<mailto:([^|]+)\|([^>]+)>/g, '$2');

  // Handle other links that might contain email addresses <http://example.com|<EMAIL>>
  processedText = processedText.replace(
    /<(?:https?:\/\/)?[^|]+\|([^>]+@[^>]+)>/g,
    '$1',
  );

  // Handle plain email links in the format <<EMAIL>>
  processedText = processedText.replace(/<([^@<>]+@[^@<>]+\.[^@<>]+)>/g, '$1');

  return processedText;
}
