import { ConfigKeys, ConfigService } from '../../config/config.service';
import { getCertificateFromVault } from '../vault/get-certificate.vault';

/**
 * Get the database options for the Slack database
 * @param configService - The config service
 * @returns The database options
 */
export async function getSlackDatabaseOptions(configService: ConfigService) {
  const env = configService.get(ConfigKeys.NODE_ENV);

  // Development
  if (env === 'development') {
    return {
      synchronize: true,
      logging: false,
    };
  }

  // Production
  if (env === 'production') {
    // Get the certificate from the vault
    const vaultCerts = await getCertificateFromVault({
      url: configService.get(ConfigKeys.VAULT_URL),
      token: configService.get(ConfigKeys.VAULT_TOKEN),
      certPath: configService.get(ConfigKeys.CERT_PATH),
    });

    return {
      synchronize: false,
      logging: false,
      ssl: vaultCerts,
    };
  }

  return {};
}
