import type { WebClientOptions } from '@slack/web-api';
import { WebClient } from '@slack/web-api';

/**
 * A client for Slack's Web API
 *
 * This client provides an alias for each {@link https://api.slack.com/methods|Web API method}. Each method is
 * a convenience wrapper for calling the {@link WebClient#apiCall} method using the method name as the first parameter.
 *
 * NOTE: This client will always reject rate limited calls by default, this prevents
 * Slack WebAPI calls from blocking the app
 */
export class Web<PERSON>pi<PERSON>lient extends WebClient {
  constructor(token: string, opts?: WebClientOptions) {
    const options: WebClientOptions = opts ?? {};

    // We'll always reject rate limited calls by default, this prevents
    // Slack WebAPI calls from blocking the app
    if (!opts) {
      options.rejectRateLimitedCalls = true;
    }

    super(token, options);
  }
}
