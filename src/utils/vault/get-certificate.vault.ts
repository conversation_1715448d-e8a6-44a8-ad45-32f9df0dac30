import vault from 'node-vault';

/**
 * Get the certificate from the vault
 * @param data - The data
 * @returns The certificate
 */
export const getCertificateFromVault = async (data: {
  url: string;
  token: string;
  certPath: string;
}) => {
  try {
    const vaultClient = vault({
      apiVersion: 'v1',
      endpoint: data.url,
      token: data.token,
      requestOptions: {
        followRedirect: true,
        followAllRedirects: true,
      },
    });

    const certificate = await vaultClient.read(data.certPath);

    return {
      rejectUnauthorized: true,
      ca: certificate?.data?.data?.production,
    };
  } catch (error) {
    throw new Error(`Failed to get certificate from vault: ${error}`);
  }
};
