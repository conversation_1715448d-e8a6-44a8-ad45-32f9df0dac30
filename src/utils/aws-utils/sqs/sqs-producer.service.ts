import {
  SQSClient,
  type SQSClientConfig,
  SendMessageCommand,
} from '@aws-sdk/client-sqs';
import { Inject, Injectable, type OnModuleInit } from '@nestjs/common';
import type { EnforcedMessageAttributes, SQSConfig } from './sqs.interfaces';

export const SQS_PRODUCER_CONFIG_TOKEN = Symbol('SQS_PRODUCER_CONFIG_TOKEN');

@Injectable()
export class SQSProducerService implements OnModuleInit {
  private sqsClient: SQSClient;

  constructor(
    @Inject(SQS_PRODUCER_CONFIG_TOKEN) private readonly config: SQSConfig,
  ) {}

  onModuleInit() {
    const clientConfig: SQSClientConfig = {
      region: this.config.region,
      credentials: this.config.credentials,
    };

    this.sqsClient = new SQSClient(clientConfig);
  }

  async sendMessage(
    message: string,
    attributes: EnforcedMessageAttributes,
    messageGroupId?: string,
  ): Promise<void> {
    const command = new SendMessageCommand({
      QueueUrl: this.config.queueUrl,
      MessageBody: message,
      MessageAttributes: attributes,
      MessageGroupId: messageGroupId,
    });

    // Validate if event_name and event_id are present in the message attributes.
    const { event_name, event_id } = attributes;
    if (!event_name?.StringValue || !event_id?.StringValue) {
      throw new Error(
        "'event_name' and 'event_id' are required in MessageAttributes",
      );
    }

    await this.sqsClient.send(command);
  }
}
