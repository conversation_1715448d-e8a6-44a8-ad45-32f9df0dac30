export interface SQSConfig {
  queueUrl: string;
  region: string;
  credentials?: {
    accessKeyId: string;
    secretAccessKey: string;
  };
}

/**
 * This type ensures that the message attributes include the required event_name & event_id attributes.
 * example:
 * {
 * 	event_name: {
 * 		DataType: "String",
 * 		StringValue: "CREATE"
 * 	}
 * }
 * {
 * 	event_id: {
 * 		DataType: "String",
 * 		StringValue: "Any unique identifier which can be used to identify the event"
 * 	}
 * }
 * It also allows for additional attributes to be included, such as StringValue, BinaryValue, etc.
 */
export type EnforcedMessageAttributes = {
  event_name: {
    DataType: 'String';
    StringValue: string;
  };
  event_id: {
    DataType: 'String';
    StringValue: string;
  };
} & Record<
  string,
  {
    DataType: 'Number' | 'String' | 'Binary';
    StringValue?: string;
    BinaryValue?: Uint8Array;
  }
>;

export type TransformedMessageAttributes = {
  event_name: string;
  event_id: string;
} & Record<string, unknown>;

export interface SQSMessage {
  id: string;
  message: Record<string, unknown> | string;
  messageAttributes: TransformedMessageAttributes;
}

export type MessageHandler = (message: SQSMessage) => Promise<void>;
