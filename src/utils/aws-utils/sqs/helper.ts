import type { Message } from '@aws-sdk/client-sqs';
import type {
  EnforcedMessageAttributes,
  SQSMessage,
  TransformedMessageAttributes,
} from './sqs.interfaces';

export function transformSQSMessage(message: Message): SQSMessage {
  return {
    id: message.ReceiptHandle,
    message: transformMessageBody(message.Body),
    messageAttributes: transformMessageAttributes(
      message.MessageAttributes as EnforcedMessageAttributes,
    ),
  };
}

function transformMessageBody(body: string): Record<string, unknown> | string {
  try {
    return JSON.parse(body);
    // eslint-disable-next-line @typescript-eslint/no-unused-vars
  } catch (_error) {
    return body;
  }
}

// Function to transform and validate message attributes
function transformMessageAttributes(
  attributes?: EnforcedMessageAttributes,
): TransformedMessageAttributes {
  const transformed: TransformedMessageAttributes = {
    event_name: attributes?.event_name?.StringValue || 'unknown',
    event_id: attributes?.event_id?.StringValue || `${Date.now()}`,
  };

  if (attributes) {
    for (const [key, value] of Object.entries(attributes)) {
      if (key !== 'event_name' && key !== 'event_id') {
        transformed[key] =
          value.DataType === 'String' ? value.StringValue : value.BinaryValue;
      }
    }
  }

  return transformed;
}
