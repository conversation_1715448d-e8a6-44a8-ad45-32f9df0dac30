import { type DynamicModule, Module } from '@nestjs/common';
import { SQSConsumerService } from './sqs-consumer.service';
import { SQSProducerService } from './sqs-producer.service';
import type { SQSConfig } from './sqs.interfaces';

@Module({})
// biome-ignore lint/complexity/noStaticOnlyClass: "This is how module in Nestjs are defined"
export class SQSModule {
  static Producer(config: SQSConfig): DynamicModule {
    return {
      module: SQSModule,
      providers: [
        {
          provide: 'SQSConfig',
          useValue: config,
        },
        SQSProducerService,
      ],
      exports: [SQSProducerService],
    };
  }

  static Consumer(config: SQSConfig): DynamicModule {
    return {
      module: SQSModule,
      providers: [
        {
          provide: 'SQSConfig',
          useValue: config,
        },

        SQSConsumerService,
      ],
      exports: [SQSConsumerService],
    };
  }
}
