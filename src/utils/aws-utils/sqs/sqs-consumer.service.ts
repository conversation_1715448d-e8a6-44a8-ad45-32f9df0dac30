import {
  DeleteMessageCommand,
  ReceiveMessageCommand,
  SQSClient,
  type SQSClientConfig,
} from '@aws-sdk/client-sqs';
import {
  Inject,
  Injectable,
  type OnModuleDestroy,
  type OnModuleInit,
} from '@nestjs/common';
import { transformSQSMessage } from './helper';
import type { <PERSON><PERSON><PERSON><PERSON>, SQSConfig, SQSMessage } from './sqs.interfaces';

export const SQS_CONSUMER_CONFIG_TOKEN = Symbol('SQS_CONSUMER_CONFIG_TOKEN');

@Injectable()
export class SQSConsumerService implements OnModuleInit, OnModuleDestroy {
  private sqsClient: SQSClient;
  private isPolling = false;
  private handler: MessageHandler;

  constructor(
    @Inject(SQS_CONSUMER_CONFIG_TOKEN) private readonly config: SQSConfig,
  ) {
    // Initialize the client in the constructor
    this.initializeSQSClient();
  }

  onModuleInit() {
    // Verify client initialization
    if (!this.sqsClient) {
      this.initializeSQSClient();
    }
  }

  onModuleD<PERSON>roy() {
    this.stopConsumer();
  }

  private initializeSQSClient() {
    const clientConfig: SQSClientConfig = {
      region: this.config.region,
      credentials: this.config.credentials,
    };

    this.sqsClient = new SQSClient(clientConfig);
  }

  async receiveMessages(): Promise<SQSMessage[]> {
    try {
      const command = new ReceiveMessageCommand({
        QueueUrl: this.config.queueUrl,
        MaxNumberOfMessages: 10,
        WaitTimeSeconds: 20,
        MessageAttributeNames: ['All'],
      });

      const response = await this.sqsClient.send(command);
      if (response.Messages && response.Messages.length > 0) {
        return response.Messages.map((message) => transformSQSMessage(message));
      }

      return [];
    } catch (error) {
      console.error(error);
    }
  }

  async deleteMessage(messageId: string): Promise<void> {
    try {
      const command = new DeleteMessageCommand({
        QueueUrl: this.config.queueUrl,
        ReceiptHandle: messageId,
      });

      await this.sqsClient.send(command);
    } catch (error) {
      console.error(error);
    }
  }

  startConsumer(handler: MessageHandler): void {
    this.handler = handler;
    this.isPolling = true;
    this.poll();
  }

  private async poll(): Promise<void> {
    while (this.isPolling) {
      const messages = (await this.receiveMessages()) || [];
      if (messages.length > 0) {
        for (const message of messages) {
          await this.processMessage(message);
        }
      }
    }
  }

  private async processMessage(message: SQSMessage): Promise<void> {
    try {
      await this.handler(message);
      await this.deleteMessage(message.id);
    } catch (error) {
      console.error(error);
    }
  }

  stopConsumer(): void {
    this.isPolling = false;
  }
}
