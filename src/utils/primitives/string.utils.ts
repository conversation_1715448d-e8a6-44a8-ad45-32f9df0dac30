/**
 * Check if a value is a non-empty string
 * @param value The value to check
 * @param trim Whether to trim the value
 * @returns True if the value is a non-empty string, false otherwise
 */
export function isValidNonEmptyString(value: string, trim = false): boolean {
  if (!value || typeof value !== 'string') {
    return false;
  }

  if (trim) {
    return value.trim().length > 0;
  }

  return value.length > 0;
}
