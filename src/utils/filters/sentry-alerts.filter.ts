import { Injectable } from '@nestjs/common';
import * as Sentry from '@sentry/node';
import * as rTracer from 'cls-rtracer';

export const SENTRY_SERVICE_TOKEN = 'Sentry';

@Injectable()
export class SentryService {
  private readonly sentryDsn: string;
  private readonly appEnv: string;
  private readonly appTag: string;
  private readonly serviceTag: string;

  constructor(
    sentryDsn: string,
    appEnv: string,
    appTag: string,
    serviceTag: string,
  ) {
    this.sentryDsn = sentryDsn;
    this.appEnv = appEnv || 'development';
    this.appTag = appTag;
    this.serviceTag = serviceTag;

    Sentry.init({
      dsn: this.sentryDsn,
      environment: this.appEnv,
    });
  }

  captureException(error: unknown, meta: Record<string, any> = {}) {
    try {
      Sentry.withScope((scope) => {
        scope.setLevel('error');
        scope.setTag('app', this.appTag);
        scope.setTag('service', this.serviceTag);
        scope.setTag('deployment', this.appEnv);

        if (meta?.tag) {
          scope.setTransactionName(meta.tag);
          scope.setTag(meta.tag, true);
        }

        if (meta?.errorSubType) {
          scope.setTag('errorSubType', meta.errorSubType);
        }

        const rid = rTracer.id() || '';
        if (rid) {
          scope.setTag('requestId', rid?.toString() || '');
        }

        for (const key of Object.keys(meta)) {
          scope.setExtra(key, meta[key]);
        }

        Sentry.captureException(error);
      });
    } catch (capturedError) {
      if (capturedError instanceof Error) {
        console.error(
          `[Sentry] Failed to capture exception: ${capturedError.message}`,
        );
      }

      throw capturedError;
    }
  }
}
