import { Module } from '@nestjs/common';
import { ConfigModule } from '../config/config.module';
import { ConfigKeys, ConfigService } from '../config/config.service';
import { SentryService } from '../utils/filters/sentry-alerts.filter';
import { CUSTOM_LOGGER_TOKEN, PinoLoggerService } from '../utils/logger';
import { EventDeduplicationService } from './redis/event-deduplication.service';

@Module({
  imports: [ConfigModule],
  providers: [
    {
      provide: CUSTOM_LOGGER_TOKEN,
      useFactory: (configService: ConfigService) => {
        return new PinoLoggerService(
          configService.get(ConfigKeys.APP_TAG),
          configService.get(ConfigKeys.SERVICE_TAG),
        );
      },
      inject: [ConfigService],
    },
    {
      provide: 'Sentry',
      useFactory: (configService: ConfigService) => {
        return new SentryService(
          configService.get(ConfigKeys.SENTRY_DSN),
          configService.get(ConfigKeys.NODE_ENV),
          configService.get(ConfigKeys.APP_TAG),
          configService.get(ConfigKeys.SERVICE_TAG),
        );
      },
      inject: [ConfigService],
    },
    EventDeduplicationService,
  ],
  exports: [CUSTOM_LOGGER_TOKEN, 'Sentry', EventDeduplicationService],
})
export class CommonModule {}
