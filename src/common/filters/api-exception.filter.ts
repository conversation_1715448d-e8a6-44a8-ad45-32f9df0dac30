import {
  ArgumentsHost,
  Catch,
  ExceptionFilter,
  HttpException,
  HttpStatus,
} from '@nestjs/common';
import { Response } from 'express';
import { ApiResponse } from '../interfaces/api-response.interface';

@Catch()
export class ApiExceptionFilter implements ExceptionFilter {
  catch(exception: <PERSON><PERSON><PERSON>, host: ArgumentsHost) {
    const ctx = host.switchToHttp();
    const response = ctx.getResponse<Response>();

    let status = HttpStatus.INTERNAL_SERVER_ERROR;
    let message = 'Internal server error';
    let responseData = null;

    if (exception instanceof HttpException) {
      status = exception.getStatus();
      message = exception.message;
      responseData = exception.getResponse();
    }

    const errorResponse: ApiResponse<null> = {
      ok: false,
      data: null,
      error: message,
      response: responseData,
    };

    response.status(status).json(errorResponse);
  }
}
