import { Injectable, Logger } from '@nestjs/common';
import { ConfigService } from '../../config/config.service';
import { RedisService } from './redis.service';

/**
 * Service for deduplicating events to ensure idempotency
 */
@Injectable()
export class EventDeduplicationService {
  private readonly logger = new Logger(EventDeduplicationService.name);
  private readonly STATUS_PREFIX = 'slack:event:status:';
  private readonly STATUS_TTL = 60 * 60 * 24; // 24 hours

  constructor(
    private readonly redisService: RedisService,
    private readonly configService: ConfigService,
  ) {}

  /**
   * Process an event idempotently
   * @param eventId The unique ID of the event
   * @param eventType The type of event
   * @param processor Function to process the event
   * @param namespace Optional namespace to prevent collisions
   * @returns The result of the processor function, or null if the event has already been processed
   */
  async processIdempotently<T>(
    eventId: string,
    eventType: string,
    processor: () => Promise<T>,
    namespace?: string,
    customPrefix?: string,
  ): Promise<T | null> {
    const prefix = customPrefix || this.STATUS_PREFIX;
    const statusKey = `${prefix}${namespace ? `${namespace}:` : ''}${eventType}:${eventId}`;

    // Check if the event has already been processed
    const status = await this.redisService.get(statusKey);
    if (status === 'completed') {
      this.logger.debug(
        `Event ${eventId} of type ${eventType} has already been processed, skipping...`,
      );
      return null;
    }

    if (status === 'in_progress') {
      this.logger.debug(
        `Event ${eventId} of type ${eventType} is already being processed, skipping...`,
      );
      return null;
    }

    // Try to set the key to 'in_progress' - this will only succeed if the key doesn't exist
    const setResult = await this.redisService.set(
      statusKey,
      'in_progress',
      'EX',
      this.STATUS_TTL,
      'NX',
    );

    // If we couldn't set the key, another instance just started processing it
    if (setResult !== 'OK') {
      this.logger.debug(
        `Event ${eventId} of type ${eventType} is already being processed by another instance, skipping...`,
      );
      return null;
    }

    try {
      // Process the event
      const result = await processor();

      // Mark the event as processed
      await this.redisService.set(
        statusKey,
        'completed',
        'EX',
        this.STATUS_TTL,
      );

      return result;
    } catch (error) {
      // If processing fails, delete the key to allow retries
      await this.redisService.del(statusKey);
      throw error;
    }
  }
}
