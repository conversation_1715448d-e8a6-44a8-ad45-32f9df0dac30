import { EventEmitter } from 'node:events';
import {
  DynamicModule,
  Global,
  Inject,
  Injectable,
  Logger,
  Module,
  OnModuleDestroy,
} from '@nestjs/common';
import Redis, { RedisOptions } from 'ioredis';
import { ConfigModule } from '../../config/config.module';
import { ConfigKeys, ConfigService } from '../../config/config.service';
import { RedisService } from './redis.service';

export const REDIS_CLIENT = 'REDIS_CLIENT';
export const REDIS_SUBSCRIBER = 'REDIS_SUBSCRIBER';
export const REDIS_PUBLISHER = 'REDIS_PUBLISHER';

/**
 * Mock Redis client for development/testing when Redis is not available
 * Implements a subset of Redis commands using an in-memory Map
 */
export class MockRedisClient extends EventEmitter {
  private storage = new Map<string, { value: string; expiry?: number }>();
  private logger = new Logger('MockRedisClient');

  constructor() {
    super();
    this.logger.warn('Using MockRedisClient instead of real Redis');
  }

  /**
   * Get a value from Redis
   * @param key Key to get
   * @returns Value or null if not found
   */
  async get(key: string): Promise<string | null> {
    const item = this.storage.get(key);
    if (!item) {
      return null;
    }

    // Check if expired
    if (item.expiry && item.expiry < Date.now()) {
      this.storage.delete(key);
      return null;
    }

    return item.value;
  }

  /**
   * Set a value in Redis with various options
   * @param key Key to set
   * @param value Value to set
   * @param args Additional arguments like 'EX', ttl, 'NX', etc.
   * @returns OK if successful, null otherwise
   */
  async set(
    key: string,
    value: string,
    ...args: string[]
  ): Promise<'OK' | null> {
    let ttl: number | undefined;
    let nx = false;
    let xx = false;

    // Parse Redis command arguments
    for (let i = 0; i < args.length; i++) {
      const arg = args[i].toUpperCase();
      if (arg === 'EX' && i + 1 < args.length) {
        ttl = Number.parseInt(args[i + 1], 10) * 1000; // Convert seconds to ms
        i++;
      } else if (arg === 'PX' && i + 1 < args.length) {
        ttl = Number.parseInt(args[i + 1], 10); // Already in ms
        i++;
      } else if (arg === 'NX') {
        nx = true;
      } else if (arg === 'XX') {
        xx = true;
      }
    }

    // Handle NX (only set if key does not exist)
    if (nx && this.storage.has(key)) {
      return null;
    }

    // Handle XX (only set if key exists)
    if (xx && !this.storage.has(key)) {
      return null;
    }

    this.storage.set(key, {
      value,
      expiry: ttl ? Date.now() + ttl : undefined,
    });

    return 'OK';
  }

  /**
   * Delete a key from Redis
   * @param key Key or keys to delete
   * @returns Number of keys deleted
   */
  async del(key: string | string[]): Promise<number> {
    if (Array.isArray(key)) {
      let count = 0;
      for (const k of key) {
        if (this.storage.delete(k)) {
          count++;
        }
      }
      return count;
    }

    const existed = this.storage.delete(key);
    return existed ? 1 : 0;
  }

  /**
   * Connection methods
   */
  async ping(): Promise<string> {
    return 'PONG';
  }

  async quit(): Promise<'OK'> {
    return 'OK';
  }

  // For compatibility with Redis client
  duplicate(): MockRedisClient {
    return new MockRedisClient();
  }
}

/**
 * Redis client factory
 * Creates and manages Redis client instances
 */
@Injectable()
export class RedisClientFactory implements OnModuleDestroy {
  private static instances: Map<string, Redis | MockRedisClient> = new Map();
  private readonly logger = new Logger('RedisClientFactory');

  constructor(private readonly configService: ConfigService) {}

  /**
   * Create a Redis client with the given options
   * @param name Unique name for this client instance
   * @param options Redis connection options
   * @returns Redis client instance
   */
  createClient(name: string, options?: RedisOptions): Redis | MockRedisClient {
    // If we already have an instance with this name, return it
    if (RedisClientFactory.instances.has(name)) {
      const instance = RedisClientFactory.instances.get(name);
      if (instance) {
        return instance;
      }
    }

    try {
      // Get default options from config if not provided
      const defaultOptions: RedisOptions = {
        host: this.configService.get(ConfigKeys.REDIS_HOST),
        port: Number.parseInt(this.configService.get(ConfigKeys.REDIS_PORT)),
        username: this.configService.get(ConfigKeys.REDIS_USERNAME),
        password: this.configService.get(ConfigKeys.REDIS_PASSWORD),
        enableReadyCheck: false,
        maxRetriesPerRequest: null,
        retryStrategy: (times) => {
          const delay = Math.min(times * 100, 3000);
          return delay;
        },
        reconnectOnError: (err) => {
          const targetError = 'READONLY';
          if (err.message.includes(targetError)) {
            // Only reconnect when the error contains "READONLY"
            return true;
          }
          return false;
        },
      };

      // Create a new Redis instance with merged options
      const client = new Redis({
        ...defaultOptions,
        ...options,
      });

      // Handle Redis connection events
      client.on('connect', () => {
        this.logger.log(`Redis client '${name}' connected`);
      });

      client.on('ready', () => {
        this.logger.log(`Redis client '${name}' ready`);
      });

      client.on('error', (err: Error & { code?: string }) => {
        this.logger.error(
          `Redis client '${name}' error: ${err.message}`,
          err.stack,
        );

        // If we can't connect to Redis, use the mock client
        if (
          (err.code === 'ECONNREFUSED' || err.code === 'ETIMEDOUT') &&
          !(client instanceof MockRedisClient)
        ) {
          this.logger.warn(`Falling back to MockRedisClient for '${name}'`);
          const mockClient = new MockRedisClient();
          RedisClientFactory.instances.set(name, mockClient);
          return mockClient;
        }
      });

      client.on('close', () => {
        this.logger.warn(`Redis client '${name}' connection closed`);
      });

      client.on('reconnecting', () => {
        this.logger.log(`Redis client '${name}' reconnecting...`);
      });

      // Store the instance
      RedisClientFactory.instances.set(name, client);
      return client;
    } catch (error: unknown) {
      // If Redis is not available, use the mock client
      const errorMessage =
        error instanceof Error ? error.message : String(error);
      this.logger.warn(
        `Failed to create Redis client '${name}': ${errorMessage}. Using MockRedisClient instead.`,
      );
      const mockClient = new MockRedisClient();
      RedisClientFactory.instances.set(name, mockClient);
      return mockClient;
    }
  }

  /**
   * Get a Redis client by name
   * @param name Name of the client to get
   * @returns Redis client instance or null if not found
   */
  getClient(name: string): Redis | MockRedisClient | null {
    return RedisClientFactory.instances.get(name) || null;
  }

  /**
   * Close all Redis connections when the module is destroyed
   */
  async onModuleDestroy() {
    const closePromises: Promise<void>[] = [];

    for (const [name, client] of RedisClientFactory.instances.entries()) {
      this.logger.log(`Closing Redis client '${name}'`);
      closePromises.push(
        new Promise<void>((resolve) => {
          client.quit().finally(() => {
            resolve();
          });
        }),
      );
    }

    await Promise.all(closePromises);
    RedisClientFactory.instances.clear();
  }
}

/**
 * Creates a Redis module with proper configuration
 * @returns DynamicModule for NestJS
 */
export function createRedisModule(): DynamicModule {
  return {
    module: RedisModule,
    imports: [ConfigModule],
    providers: [
      RedisClientFactory,
      {
        provide: REDIS_CLIENT,
        useFactory: (factory: RedisClientFactory) => {
          return factory.createClient('default');
        },
        inject: [RedisClientFactory],
      },
      {
        provide: REDIS_SUBSCRIBER,
        useFactory: (factory: RedisClientFactory) => {
          return factory.createClient('subscriber', {
            // Subscriber clients should not be used for other commands
            enableReadyCheck: false,
            lazyConnect: true,
          });
        },
        inject: [RedisClientFactory],
      },
      {
        provide: REDIS_PUBLISHER,
        useFactory: (factory: RedisClientFactory) => {
          return factory.createClient('publisher');
        },
        inject: [RedisClientFactory],
      },
      RedisService,
    ],
    exports: [
      RedisClientFactory,
      REDIS_CLIENT,
      REDIS_SUBSCRIBER,
      REDIS_PUBLISHER,
      RedisService,
    ],
  };
}

@Global()
@Module({})
export class RedisModule {
  constructor(@Inject(REDIS_CLIENT) private readonly redisClient: Redis) {
    // Inject Redis client to ensure it's initialized
    if (this.redisClient) {
      this.redisClient.ping().catch(() => {
        // Silently catch errors - they're already handled in the factory
      });
    }
  }

  onModuleInit() {
    Logger.log('Redis module initialized', 'RedisModule');
  }
}
