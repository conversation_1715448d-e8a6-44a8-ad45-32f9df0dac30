import { Inject, Injectable, Logger } from '@nestjs/common';
import Redis from 'ioredis';
import {
  REDIS_CLIENT,
  REDIS_PUBLISHER,
  REDIS_SUBSCRIBER,
} from './redis.module';

/**
 * Enhanced Redis service with core methods and improved error handling
 */
@Injectable()
export class RedisService {
  private readonly logger = new Logger(RedisService.name);

  constructor(
    @Inject(REDIS_CLIENT) private readonly redisClient: Redis | any,
    @Inject(REDIS_SUBSCRIBER) private readonly subscriberClient: Redis | any,
    @Inject(REDIS_PUBLISHER) private readonly publisherClient: Redis | any,
  ) {}

  /**
   * Get the main Redis client
   * @returns Redis client instance
   */
  getClient(): Redis {
    return this.redisClient;
  }

  /**
   * Get the subscriber Redis client (dedicated for subscriptions)
   * @returns Redis subscriber client instance
   */
  getSubscriberClient(): Redis {
    return this.subscriberClient;
  }

  /**
   * Get the publisher Redis client (dedicated for publishing)
   * @returns Redis publisher client instance
   */
  getPublisherClient(): Redis {
    return this.publisherClient;
  }

  /**
   * Get a value from Redis
   * @param key Key to get
   * @returns Value or null if not found
   */
  async get(key: string): Promise<string | null> {
    try {
      return await this.redisClient.get(key);
    } catch (error) {
      this.handleError('get', error, key);
      return null;
    }
  }

  /**
   * Set a value in Redis with various options
   * @param key Key to set
   * @param value Value to set
   * @param args Additional arguments like 'EX', ttl, 'NX', etc.
   * @returns OK if successful, null otherwise
   */
  async set(key: string, value: string, ...args: any[]): Promise<'OK' | null> {
    try {
      return await this.redisClient.set(key, value, ...args);
    } catch (error) {
      this.handleError('set', error, key);
      return null;
    }
  }

  /**
   * Set a value in Redis only if the key does not exist
   * @param key Key to set
   * @param value Value to set
   * @param ttlSeconds Optional TTL in seconds
   * @returns OK if successful, null otherwise
   */
  async setNX(
    key: string,
    value: string,
    ttlSeconds?: number,
  ): Promise<'OK' | null> {
    try {
      if (ttlSeconds) {
        return await this.redisClient.set(key, value, 'NX', 'EX', ttlSeconds);
      }
      return await this.redisClient.set(key, value, 'NX');
    } catch (error) {
      this.handleError('setNX', error, key);
      return null;
    }
  }

  /**
   * Delete a key from Redis
   * @param key Key or keys to delete
   * @returns Number of keys deleted
   */
  async del(key: string | string[]): Promise<number> {
    try {
      return await this.redisClient.del(key);
    } catch (error) {
      this.handleError('del', error, key);
      return 0;
    }
  }

  /**
   * Handle Redis errors
   * @param operation Operation that failed
   * @param error Error object
   * @param key Key that was being operated on
   */
  private handleError(
    operation: string,
    error: unknown,
    key: string | any,
  ): void {
    const errorMessage = error instanceof Error ? error.message : String(error);
    this.logger.error(
      `Redis ${operation} operation failed for key "${key}": ${errorMessage}`,
      error instanceof Error ? error.stack : undefined,
    );
  }
}
