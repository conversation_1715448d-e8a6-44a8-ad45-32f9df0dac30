import { BadRequestException, Inject, Injectable } from '@nestjs/common';
import { InjectRepository } from '@nestjs/typeorm';
import { isEmpty } from 'lodash';
import { Repository } from 'typeorm';
import { EventDeduplicationService } from '../../common/redis/event-deduplication.service';
import { Organizations } from '../../database/entities';
import { CUSTOM_LOGGER_TOKEN, ILogger } from '../../utils/logger';
import { AppInstallation } from '../interfaces';
import { PlatformWebhookEvent } from '../type-system';

@Injectable()
export class PlatformService {
  private eventHandlers: Map<string, any> = new Map<string, any>();
  private readonly PLATFORM_STATUS_PREFIX = 'slack:event:status:';

  constructor(
    @Inject(CUSTOM_LOGGER_TOKEN) private readonly logger: ILogger,

    @InjectRepository(Organizations)
    private readonly organizationsRepository: Repository<Organizations>,

    private readonly eventDeduplicationService: EventDeduplicationService,
  ) {}

  async installApp(installation: AppInstallation) {
    try {
      // Find the organization
      let foundOrganization = await this.organizationsRepository.findOne({
        where: { uid: installation.organization_id },
      });

      // If the organization is not found, throw an error
      if (isEmpty(foundOrganization)) {
        this.logger.debug(
          `Organization ${installation.organization_id} was not found! Creating a new organization in Slack Integration App...`,
        );

        // Create a new organization in Slack Integration App
        foundOrganization = await this.organizationsRepository.save({
          uid: installation.organization_id,
          installingUserId: installation.created_by,
        });
      }

      const { bot_token, application_id, installation_id, created_by } =
        installation;

      // Update the organization with the new metadata
      await this.organizationsRepository.update(
        { uid: installation.organization_id },
        {
          apiKey: bot_token, // This is the Platform's Bot API Key which can be used in the Platform API requests
          metadata: {
            ...foundOrganization.metadata,

            // Set metadata keys
            applicationId: application_id, // This is the application's id in Apps Platform
            createdBy: created_by, // The user who added this app, this is platform's user
            installationId: installation_id, // Installation id for this app in Apps Platform
          },
        },
      );

      return { ok: true };
    } catch (error) {
      console.error(error);
      throw error;
    }
  }

  setEventHandlers(handlers: Map<string, any>) {
    this.eventHandlers = handlers;
  }

  async handlePlatformEvent(event: PlatformWebhookEvent) {
    // Check if the event is valid
    if (!event.xWebhookEvent) {
      throw new BadRequestException(
        'This resource is designed for webhooks, please make sure that the payload is correct',
      );
    }

    // Use the eventId directly from the event payload
    // This is crucial for idempotency when handling events from message queues like SQS
    const eventId = event.message.eventId;
    const eventType = event.message.eventType;
    const orgId = event.message.orgId;

    this.logger.debug(
      `Processing platform event: ${eventType} (${eventId}) for org: ${orgId}`,
    );

    // Create a namespace to prevent collisions between different organizations/teams
    const namespace = `platform:${orgId}`;

    // Use the EventDeduplicationService to process the event idempotently
    return this.eventDeduplicationService.processIdempotently(
      eventId,
      eventType,
      async () => {
        // Get the handler for the event
        const handler = this.eventHandlers.get(eventType);
        if (handler?.instance) {
          const result = await handler.instance.handle(event);
          this.logger.debug(
            `Event ${eventId} of type ${eventType} processed successfully`,
          );
          return result;
        }

        return null;
      },
      namespace,
      this.PLATFORM_STATUS_PREFIX,
    );
  }
}
