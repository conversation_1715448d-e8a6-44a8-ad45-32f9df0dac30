import type { Attachment } from '../common';
import { CommentMetadata } from './comments-common';

export type TicketCommentAddedEvent = {
  comment: {
    attachments: Array<Attachment> | [];
    author: Partial<{
      email: string;
      id: string;
      name: string;
      avatarUrl: string;
    }>;
    customerContact: Partial<{
      email: string;
      id: string;
      avatarUrl: string;
    }>;
    parentCommentId: string | null;
    metadata: CommentMetadata;
    commentType: string;
    commentVisibility: string;
    content: string;
    contentJson: string;
    contentHtml: string;
    contentMarkdown: string;
    createdAt: string;
    deletedAt: string | null;
    id: string;
    teamId: string;
    updatedAt: string;
  };
  ticket: {
    id: string;
    title: string;
  };
};
