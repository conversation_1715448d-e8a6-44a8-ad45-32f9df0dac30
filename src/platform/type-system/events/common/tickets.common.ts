export type CommonTicketPayload = {
  id: string;
  title: string;
  ticketId: number;
  description?: string;
  priorityId: string;
  priorityName: string;
  customerContactEmail: string;
  customerContactFirstName: string;
  customerContactLastName: string;
  statusId: string;
  statusName: string;
  source: string;
  teamId: string;
  assignedTo: string;
  requestorEmail: string;
  submitterEmail: string;
  teamIdentifier: string;
  sentimentName: string;
  assignedAgent: {
    id: string;
    email: string;
    name: string;
  };
  customer: {
    id: string;
    name: string;
    email: string;
  };
  tags: Array<string>;
  customFields: Array<{ [key: string]: string }>;
  metadata: Record<string, unknown>;
  createdAt: string;
  isEscalated: boolean;
  isArchived: boolean;
  aiGeneratedTitle: string;
  aiGeneratedSummary: string;
};
