import type {
  TicketCommentAddedEvent,
  TicketCommentDeletedEvent,
  TicketCommentUpdatedEvent,
} from './events/comments';
import type { TicketCreatedEvent, TicketUpdatedEvent } from './events/tickets';

export type TicketEvents = TicketCreatedEvent | TicketUpdatedEvent;
export type CommentEvents =
  | TicketCommentAddedEvent
  | TicketCommentUpdatedEvent
  | TicketCommentDeletedEvent;

type EventTypeMap = {
  // Ticket Events
  'ticket:created': TicketCreatedEvent;
  'ticket:updated': TicketUpdatedEvent;

  // Comment Events
  'ticket:comment:added': TicketCommentAddedEvent;
  'ticket:comment:updated': TicketCommentUpdatedEvent;
  'ticket:comment:deleted': TicketCommentDeletedEvent;
};

// @ts-expect-error - This can be safely ignored since we are using a string literal type
//		              if the event exists it will be returned otherwise we'll return undefined
type KnownEventFromType<T extends string> = EventTypeMap[T];

export type PlatformWebhookEvent<T extends string = any> = {
  message: {
    actor: {
      email: string;
      id: string;
      type: string;
    };
    eventId: string;
    eventType: string;
    orgId: string;
    payload: KnownEventFromType<T>;
    teamId?: string;
    timestamp: string;
  };
  xWebhookEvent: true;
};
