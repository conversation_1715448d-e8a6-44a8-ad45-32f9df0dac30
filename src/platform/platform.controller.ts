import {
  Body,
  Controller,
  Head,
  HttpException,
  InternalServerErrorException,
  Post,
} from '@nestjs/common';
import { AppInstallation } from './interfaces';
import { PlatformService } from './services/platform.service';
import { PlatformWebhookEvent } from './type-system';

@Controller('v1/platform')
export class PlatformController {
  constructor(private readonly platformService: PlatformService) {}

  @Head('/installations')
  checkInstallationsHTTPResource() {
    return { ok: true };
  }

  @Post('/installations')
  async handleInstallation(@Body() body: AppInstallation) {
    await this.platformService.installApp(body);
    return { ok: true };
  }

  @Head('/events')
  checkEventsHTTPResource() {
    return { ok: true };
  }

  @Post('/events')
  async handleEvents(@Body() body: PlatformWebhookEvent) {
    try {
      const result = await this.platformService.handlePlatformEvent(body);
      return { ok: true, data: result };
    } catch (error) {
      if (error instanceof HttpException) {
        throw error;
      }

      throw new InternalServerErrorException('Something went wrong...');
    }
  }
}
