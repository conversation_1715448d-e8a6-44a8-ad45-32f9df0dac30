import { Inject, Injectable } from '@nestjs/common';
import { InjectRepository } from '@nestjs/typeorm';
import { ChatPostMessageArguments, MessageMetadata } from '@slack/web-api';
import { Repository } from 'typeorm';
import {
  CustomerContacts,
  GroupedSlackMessages,
  Installations,
  SlackTriageMessages,
  Users,
} from '../../../database/entities';
import { CommentConversationMapsRepository } from '../../../database/entities/mappings/repositories/comment-conversation-maps.repository';
import { GroupedSlackMessagesRepository } from '../../../database/entities/slack-messages/repositories/grouped-slack-messages.repository';
import { SlackMessagesRepository } from '../../../database/entities/slack-messages/repositories/slack-messages.repository';
import { SlackTriageMessagesRepository } from '../../../database/entities/slack-messages/repositories/slack-triage-messages.repository';
import { SlackMessages } from '../../../database/entities/slack-messages/slack-messages.entity';
import { SlackWebAPIService } from '../../../slack/providers/slack-apis/slack-apis.service';
import { SLACK_SENTRY_TAG } from '../../../utils';
import { appendAttachmentsToSlackBlocks } from '../../../utils/common';
import { SentryService } from '../../../utils/filters/sentry-alerts.filter';
import { CUSTOM_LOGGER_TOKEN, ILogger } from '../../../utils/logger';
import {
  HtmlToPlainText,
  convertTiptapJSONToSlackBlocks,
} from '../../../utils/parsers';
import {
  COMMENT_TYPE,
  COMMENT_VISIBILITY,
} from '../../constants/comments.constants';
import { PlatformEvent } from '../../decorators';
import { PlatformWebhookEvent } from '../../type-system';
import { PlatformHandler } from '../commons/platform-handlers.interface';

interface TicketCommentAdded {
  orgId: string;
  data: PlatformWebhookEvent<'ticket:comment:added'>['message']['payload'];
}

interface GetSlackUserDetails {
  orgId: string;
  author?: Partial<{
    email: string;
    name: string;
  }>;
  customerContact?: Partial<{
    email: string;
    id: string;
  }>;
  message: SlackTriageMessages | SlackMessages;
}

@Injectable()
@PlatformEvent('ticket:comment:added')
export class TicketCommentAddedHandler implements PlatformHandler {
  constructor(
    @Inject(CUSTOM_LOGGER_TOKEN) private readonly logger: ILogger,
    @Inject('Sentry') private readonly sentryService: SentryService,

    // External API Providers
    private readonly slackApiProvider: SlackWebAPIService,

    // Database Repositories
    private readonly slackMessagesRepository: SlackMessagesRepository,
    private readonly triageMessagesRepository: SlackTriageMessagesRepository,
    private readonly groupedMessagesRepository: GroupedSlackMessagesRepository,
    private readonly commentConversationMapsRepository: CommentConversationMapsRepository,

    @InjectRepository(Users)
    private readonly usersRepository: Repository<Users>,

    @InjectRepository(CustomerContacts)
    private readonly customerContactsRepository: Repository<CustomerContacts>,

    @InjectRepository(Installations)
    private readonly installationsRepository: Repository<Installations>,
  ) {}

  async handle(event: PlatformWebhookEvent<'ticket:comment:added'>) {
    try {
      this.logger.log(
        `Processing ticket comment added on: ${event.message?.payload?.ticket.id}`,
      );
      // Add detailed console log of the entire payload
      console.log(
        'Comment Added Payload:',
        JSON.stringify(event.message.payload, null, 2),
      );

      const { payload, orgId } = event.message;
      const { comment } = payload;
      const { metadata, commentType, commentVisibility } = comment;

      // Check if the comment is from the bot
      const ignoreSelf = metadata?.external_sinks?.slack?.ignoreSelf;
      if (ignoreSelf) {
        this.logger.debug('Comment is from the bot, skipping...');
        return;
      }

      // Handle comments
      if (commentType === COMMENT_TYPE.COMMENT) {
        if (commentVisibility === COMMENT_VISIBILITY.PUBLIC) {
          await this.handlePublicComment({ data: payload, orgId });
        } else if (commentVisibility === COMMENT_VISIBILITY.PRIVATE) {
          await this.handlePrivateComment({ data: payload, orgId });
        }
      }
    } catch (error) {
      if (error instanceof Error) {
        this.logger.error(
          `Error processing ticket comment added on: ${event.message?.payload?.ticket.id}`,
          error.message,
        );
      } else {
        console.error(
          `Error processing ticket comment added on: ${event.message?.payload?.ticket.id}`,
          error,
        );
      }

      // Capture the error
      this.sentryService.captureException(error, {
        name: '🚨 Error processing ticket comment added!',
        tag: SLACK_SENTRY_TAG,
      });

      // Propagate the error up
      throw error;
    }
  }

  /**
   * Handles a public comment on a ticket
   * @param payload - The payload of the event
   */
  private async handlePublicComment(payload: TicketCommentAdded) {
    try {
      const { data, orgId } = payload;
      const { author, parentCommentId, attachments } = data.comment;

      // Find the slack message associated with this ticket
      const slackMessage = await this.slackMessagesRepository.findByCondition({
        where: {
          platformTicketId: data.ticket.id,
          organization: { uid: orgId },
        },
        relations: ['installation', 'channel', 'organization'],
      });

      this.logger.debug(
        'Data for checking slack message \n',
        JSON.stringify(slackMessage),
      );
      // If no slack message is found, log an error and return
      if (!slackMessage) {
        this.logger.error(
          `No slack message found for ticket ${data.ticket.id}`,
        );
        return;
      }

      // If the slack message is found and there is no parent comment, send the message to slack
      if (slackMessage && !parentCommentId) {
        this.logger.debug(
          `Ignore message for ticket ${data.ticket.id} with parent comment id as this comment does not belong to any possible slack thread!`,
        );
        return;
      }

      let groupedMessage: GroupedSlackMessages | null = null;
      if (parentCommentId) {
        groupedMessage = await this.groupedMessagesRepository.findByCondition({
          where: {
            parentCommentId,
            organization: { uid: orgId },
          },
        });
      }

      // Find the slack user associated with the comment
      const { userDetails, slackUser } = await this.getSlackUserDetails({
        orgId,
        author,
        message: slackMessage,
      });

      const { installation } = slackMessage;

      // Convert the comment content to slack blocks
      let blocks = await convertTiptapJSONToSlackBlocks(
        JSON.parse(data.comment.contentJson),
        this.usersRepository,
        installation,
        this.customerContactsRepository,
        this.installationsRepository,
      );

      // If there are attachments, append them to the blocks
      if (attachments.length > 0) {
        blocks = appendAttachmentsToSlackBlocks(blocks, attachments);
      }

      // Convert the comment content to plain text
      const plainTextConverter = new HtmlToPlainText(
        data.comment.contentHtml,
        this.usersRepository,
      );

      const plainText = await plainTextConverter.convert();

      const metadata: MessageMetadata = {
        event_type: 'message',
        event_payload: {},
      };

      // If the slack user is found, set the ignore self flag
      if (slackUser?.slackAccessToken) {
        metadata.event_payload.ignoreSelf = true;
      }

      // Create the chat post message arguments
      const chatPostMessageArgs: ChatPostMessageArguments = {
        text: plainText,
        channel: slackMessage.channel.channelId,
        thread_ts:
          groupedMessage?.slackMessageTs ??
          slackMessage.slackMessageThreadTs ??
          slackMessage.slackMessageTs,
        username: author.name ?? userDetails.userName,
        icon_url: author.avatarUrl ?? userDetails.iconUrl,
        blocks,
        token: slackUser?.slackAccessToken,
        unfurl_links: true,
        unfurl_media: true,
        metadata,
      };

      // Send the message to slack
      const response = await this.sendMessageToSlack(
        installation,
        chatPostMessageArgs,
      );

      // If the message was sent successfully, save or update the record
      if (response.ok) {
        // Save the comment conversation map
        // Added it here as well. Need to understand if this is needed or not.
        await this.commentConversationMapsRepository.save({
          platformCommentId: data.comment.id,
          slackTs: response.message.ts,
          slackThreadTs: response.message.thread_ts,
          channel: { id: slackMessage.channel.id },
          installation: { id: slackMessage.installation.id },
          organization: { id: slackMessage.organization.id },
        });

        const existingRecord =
          await this.slackMessagesRepository.findByCondition({
            where: {
              slackMessageTs: response.message.ts,
              channel: { id: slackMessage.channel.id },
            },
          });

        if (existingRecord) {
          // Update existing record
          await this.slackMessagesRepository.update(
            { id: existingRecord.id },
            { platformCommentId: data.comment.id },
          );
        } else {
          // Create new record
          await this.slackMessagesRepository.save({
            slackMessageTs: response.message.ts,
            slackMessageThreadTs: slackMessage.slackMessageThreadTs,
            platformCommentId: data.comment.id,
            platformTicketId: data.ticket.id,
            channel: { id: slackMessage.channel.id },
            installation: { id: slackMessage.installation.id },
            organization: { id: slackMessage.organization.id },
          });
        }
      }

      return response;
    } catch (error) {
      this.logger.error(
        `Error handling public comment on ticket ${payload.data.ticket.id}: ${error instanceof Error ? error.message : String(error)}`,
      );

      this.sentryService.captureException(error, {
        name: '🚨 Error handling public comment!',
        tag: SLACK_SENTRY_TAG,
        ticketId: payload.data.ticket.id,
        commentId: payload.data.comment.id,
      });

      throw error;
    }
  }

  /**
   * Handles a private comment on a ticket
   * @param payload - The payload of the event
   */
  private async handlePrivateComment(payload: TicketCommentAdded) {
    const spanId = `[handlePrivateComment] [${payload?.data?.comment?.id}]`;
    try {
      const { data, orgId } = payload;
      const { ticket, comment } = data;
      const { author, attachments } = comment;

      // Find the slack message associated with this ticket
      const triageMessage = await this.triageMessagesRepository.findByCondition(
        {
          where: {
            platformThreadId: comment.parentCommentId ?? comment.id,
            organization: { uid: orgId },
            slackRequestMessage: { platformTicketId: ticket.id },
          },
          relations: { installation: true, channel: true, organization: true },
        },
      );

      // If no slack message is found, log an error and return
      if (!triageMessage) {
        this.logger.error(
          `${spanId} No slack message found for ticket ${data.ticket.id}`,
        );
        return;
      }

      // Get the slack user details
      const { userDetails, slackUser } = await this.getSlackUserDetails({
        orgId,
        author,
        message: triageMessage,
      });

      const { installation } = triageMessage;

      // Convert the comment content to slack blocks
      let blocks = await convertTiptapJSONToSlackBlocks(
        JSON.parse(comment.contentJson),
        this.usersRepository,
        installation,
        this.customerContactsRepository,
        this.installationsRepository,
      );

      // If there are attachments, append them to the blocks
      if (attachments.length > 0) {
        blocks = appendAttachmentsToSlackBlocks(blocks, attachments);
      }

      // Convert the comment content to plain text
      const plainTextConverter = new HtmlToPlainText(
        comment.content,
        this.usersRepository,
      );
      const plainText = await plainTextConverter.convert();

      const metadata: MessageMetadata = {
        event_type: 'message',
        event_payload: {},
      };

      // If the slack user is found, set the ignore self flag
      if (slackUser?.slackAccessToken) {
        metadata.event_payload.ignoreSelf = true;
      }

      // Create the chat post message arguments
      const chatPostMessageArgs: ChatPostMessageArguments = {
        text: plainText,
        channel: triageMessage.channel.channelId,
        thread_ts: triageMessage.slackMessageTs,
        username: comment.author?.name ?? userDetails.userName,
        icon_url: comment.author?.avatarUrl ?? userDetails.iconUrl,
        blocks,
        token: slackUser?.slackAccessToken,
        unfurl_links: true,
        unfurl_media: true,
        metadata,
      };

      this.logger.debug(
        `${spanId} Sending private comment to slack: ${JSON.stringify(chatPostMessageArgs)}`,
      );

      // Send the message to slack
      const response = await this.sendMessageToSlack(
        installation,
        chatPostMessageArgs,
      );

      // If the message was sent successfully, save the comment conversation map
      if (response.ok) {
        // Save the comment conversation map
        await this.commentConversationMapsRepository.save({
          platformCommentId: comment.id,
          slackTs: response.message.ts,
          slackThreadTs: response.message.thread_ts,
          channel: { id: triageMessage.channel.id },
          installation: { id: triageMessage.installation.id },
          organization: { id: triageMessage.organization.id },
        });

        //create a new record in slackMessagesRepository
        await this.slackMessagesRepository.save({
          slackMessageTs: response.message.ts,
          slackMessageThreadTs: response.message.thread_ts,
          platformCommentId: comment.id,
          platformTicketId: ticket.id,
          channel: { id: triageMessage.channel.id },
          installation: { id: triageMessage.installation.id },
          organization: { id: triageMessage.organization.id },
        });
      }

      return response;
    } catch (error) {
      this.logger.error(
        `${spanId} Error handling private comment: ${error instanceof Error ? error.message : String(error)}`,
      );

      this.sentryService.captureException(error, {
        name: '🚨 Error handling private comment!',
        tag: SLACK_SENTRY_TAG,
        ticketId: payload.data.ticket.id,
        commentId: payload.data.comment.id,
      });

      throw error;
    }
  }

  /**
   * Gets the slack user details
   * @param data - The data of the event
   */
  private async getSlackUserDetails(data: GetSlackUserDetails) {
    try {
      const { orgId, author, message } = data;

      // Find the slack user associated with the comment
      const slackUser = await this.usersRepository.findOne({
        where: {
          organization: { uid: orgId },
          slackProfileEmail: author.email,
          installation: { id: message.installation.id },
        },
      });

      let userName =
        slackUser?.realName ?? slackUser?.displayName ?? slackUser?.name;
      let iconUrl =
        slackUser?.images?.image_original ?? slackUser?.images?.image_72;

      if (!slackUser) {
        this.logger.warn(
          `No slack user found for comment ${message.id} by ${author.email}`,
        );

        userName = author.name;
        iconUrl = '';
      }

      return { userDetails: { userName, iconUrl }, slackUser };
    } catch (error) {
      this.logger.error(
        `Error getting slack user details: ${error instanceof Error ? error.message : String(error)}`,
      );

      this.sentryService.captureException(error, {
        name: '🚨 Error getting slack user details!',
        tag: SLACK_SENTRY_TAG,
      });

      throw error;
    }
  }

  /**
   * Sends a message to slack
   * @param installation The installation to send the message to
   * @param chatPostMessageArgs The chat post message arguments
   */
  private async sendMessageToSlack(
    installation: Installations,
    chatPostMessageArgs: ChatPostMessageArguments,
  ) {
    const spanId = `[sendMessageToSlack] [${installation?.id}]`;
    this.logger.debug(
      `${spanId} Sending message to slack: ${JSON.stringify(chatPostMessageArgs)}`,
    );
    try {
      // Post a message on the thread
      const response = await this.slackApiProvider.sendMessage(
        installation.botToken,
        chatPostMessageArgs,
      );

      return response;
    } catch (slackError) {
      // Check if error is "not_in_channel"
      if (
        slackError instanceof Error &&
        slackError.message.includes('not_in_channel')
      ) {
        this.logger.warn(`${spanId} User not in channel, retrying as bot`);

        // Create a new args object without the user token
        const { token, ...botPostMessageArgs } = chatPostMessageArgs; // Remove user token to post as bot

        // Retry with bot token only
        return this.slackApiProvider.sendMessage(
          installation.botToken,
          botPostMessageArgs,
        );
      }

      // Handle other errors
      if (slackError instanceof Error) {
        this.logger.error(
          `${spanId} Error posting message to slack: ${slackError.message}`,
        );
      } else {
        console.error(slackError);
      }

      // Capture the error
      this.sentryService.captureException(slackError, {
        name: '🚨 Error posting message to slack!',
        tag: SLACK_SENTRY_TAG,
      });

      // Throw the error instead of returning null
      throw slackError;
    }
  }
}
