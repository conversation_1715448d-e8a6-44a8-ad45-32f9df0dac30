import { Inject, Injectable } from '@nestjs/common';
import { InjectRepository } from '@nestjs/typeorm';
import { ChatDeleteArguments } from '@slack/web-api';
import { Repository } from 'typeorm';
import {
  GroupedSlackMessages,
  Installations,
  SlackTriageMessages,
  Users,
} from '../../../database/entities';
import { SlackMessages } from '../../../database/entities';
import { CommentConversationMapsRepository } from '../../../database/entities/mappings/repositories/comment-conversation-maps.repository';
import { GroupedSlackMessagesRepository } from '../../../database/entities/slack-messages/repositories/grouped-slack-messages.repository';
import { SlackMessagesRepository } from '../../../database/entities/slack-messages/repositories/slack-messages.repository';
import { SlackTriageMessagesRepository } from '../../../database/entities/slack-messages/repositories/slack-triage-messages.repository';
import { SlackWebAPIService } from '../../../slack/providers/slack-apis/slack-apis.service';
import { CUSTOM_LOGGER_TOKEN, ILogger, SLACK_SENTRY_TAG } from '../../../utils';
import { SentryService } from '../../../utils/filters/sentry-alerts.filter';
import {
  COMMENT_TYPE,
  COMMENT_VISIBILITY,
} from '../../constants/comments.constants';
import { PlatformEvent } from '../../decorators';
import { PlatformWebhookEvent } from '../../type-system';
import { PlatformHandler } from '../commons/platform-handlers.interface';

interface TicketCommentDeleted {
  orgId: string;
  data: PlatformWebhookEvent<'ticket:comment:deleted'>['message']['payload'];
}

interface GetSlackUserDetails {
  orgId: string;
  author?: Partial<{
    email: string;
    name: string;
  }>;
  customerContact?: Partial<{
    email: string;
    id: string;
  }>;
  message: SlackTriageMessages | SlackMessages;
}

@Injectable()
@PlatformEvent('ticket:comment:deleted')
export class TicketCommentDeletedHandler implements PlatformHandler {
  constructor(
    @Inject(CUSTOM_LOGGER_TOKEN) private readonly logger: ILogger,
    @Inject('Sentry') private readonly sentryService: SentryService,

    // External API Providers
    private readonly slackApiProvider: SlackWebAPIService,

    // Database Repositories
    @InjectRepository(Users)
    private readonly usersRepository: Repository<Users>,
    private readonly slackMessagesRepository: SlackMessagesRepository,
    private readonly groupedMessagesRepository: GroupedSlackMessagesRepository,
    private readonly commentConversationMapsRepository: CommentConversationMapsRepository,
    private readonly triageMessagesRepository: SlackTriageMessagesRepository,
  ) {}

  async handle(event: PlatformWebhookEvent<'ticket:comment:deleted'>) {
    try {
      this.logger.log(
        `Processing ticket comment deleted on: ${event.message?.payload?.ticket?.id}`,
      );

      const { payload, orgId } = event.message;
      const { comment } = payload;
      const { commentVisibility, commentType } = comment;

      // Handle comments
      if (commentType === COMMENT_TYPE.COMMENT) {
        if (commentVisibility === COMMENT_VISIBILITY.PUBLIC) {
          await this.handlePublicComment({ data: payload, orgId });
        } else if (commentVisibility === COMMENT_VISIBILITY.PRIVATE) {
          await this.handlePrivateComment({ data: payload, orgId });
        }
      }
    } catch (error) {
      if (error instanceof Error) {
        this.logger.error(
          `Error processing ticket comment deleted on: ${event.message?.payload?.ticket?.id}`,
          error.message,
        );
      } else {
        console.error(
          `Error processing ticket comment deleted on: ${event.message?.payload?.ticket?.id}`,
          error,
        );
      }
      // Capture the error
      this.sentryService.captureException(error, {
        name: '🚨 Error processing ticket comment deleted!',
        tag: SLACK_SENTRY_TAG,
      });

      // Propagate the error up
      throw error;
    }
  }

  /**
   * Handles a private comment on a ticket
   * @param payload - The payload of the event
   */
  private async handlePrivateComment(payload: TicketCommentDeleted) {
    try {
      const { data, orgId } = payload;
      const { comment } = data;

      // Get the private triage message for the ticket
      const { triageMessage } = await this.getPrivateTriageMessage({
        orgId,
        data,
      });

      // Get the slack user details for the comment
      const { slackUser } = await this.getSlackUserDetails({
        orgId,
        author: comment.author,
        message: triageMessage,
      });

      const { installation } = triageMessage;

      // Get the comment conversation map for the comment
      const commentToDelete =
        await this.commentConversationMapsRepository.findByCondition({
          where: {
            platformCommentId: comment.id,
            channel: { id: triageMessage.channel.id },
            installation: { id: installation.id },
            organization: { uid: orgId },
          },
        });

      // If no comment conversation map is found, log an error and return
      if (!commentToDelete) {
        this.logger.error(
          `No comment conversation map found for comment ${comment.id}`,
        );

        throw new Error(
          `No comment conversation map found for comment ${comment.id}`,
        );
      }

      // Create the chat delete message arguments
      const chatDeleteMessageArgs: ChatDeleteArguments = {
        channel: triageMessage.channel.channelId,
        ts: commentToDelete.slackTs,
        token: slackUser?.slackAccessToken,
      };

      // Delete the message on slack
      const response = await this.deleteMessageOnSlack(
        installation,
        chatDeleteMessageArgs,
      );

      return response;
    } catch (error) {
      this.logger.error(
        `Error handling private comment deletion: ${error instanceof Error ? error.message : String(error)}`,
      );

      this.sentryService.captureException(error, {
        name: '🚨 Error handling private comment deletion!',
        tag: SLACK_SENTRY_TAG,
        commentId: payload.data.comment.id,
      });

      throw error;
    }
  }

  /**
   * Handles a public comment on a ticket
   * @param payload - The payload of the event
   */
  private async handlePublicComment(payload: TicketCommentDeleted) {
    try {
      const { data, orgId } = payload;
      const { comment } = data;

      // Get the slack message for the ticket
      const { slackMessage } = await this.getPublicSlackMessage({
        orgId,
        data,
      });

      // Get the slack user details for the comment
      const { slackUser } = await this.getSlackUserDetails({
        orgId,
        author: comment.author,
        message: slackMessage,
      });

      const { installation } = slackMessage;

      // Get the comment conversation map for the comment
      const commentToDelete =
        await this.commentConversationMapsRepository.findByCondition({
          where: {
            platformCommentId: comment.id,
            channel: { id: slackMessage.channel.id },
            installation: { id: installation.id },
            organization: { uid: orgId },
          },
        });

      // If no comment conversation map is found, log an error and return
      if (!commentToDelete) {
        this.logger.error(
          `No comment conversation map found for comment ${comment.id}`,
        );

        throw new Error(
          `No comment conversation map found for comment ${comment.id}`,
        );
      }

      // Create the chat delete message arguments
      const chatDeleteMessageArgs: ChatDeleteArguments = {
        channel: slackMessage.channel.channelId,
        ts: commentToDelete.slackTs,
        token: slackUser?.slackAccessToken,
      };

      // Delete the message on slack
      const response = await this.deleteMessageOnSlack(
        installation,
        chatDeleteMessageArgs,
      );

      // If the message was deleted successfully, remove the comment conversation map
      if (response.ok) {
        await this.commentConversationMapsRepository.remove(commentToDelete);
      }

      return response;
    } catch (error) {
      this.logger.error(
        `Error handling public comment deletion: ${error instanceof Error ? error.message : String(error)}`,
      );

      this.sentryService.captureException(error, {
        name: '🚨 Error handling public comment deletion!',
        tag: SLACK_SENTRY_TAG,
        commentId: payload.data.comment.id,
        ticketId: payload.data.ticket.id,
      });

      throw error;
    }
  }

  /**
   * Deletes the message on slack
   * @param installation - The installation to delete the message on
   * @param chatDeleteArgs - The chat delete arguments
   */
  private async deleteMessageOnSlack(
    installation: Installations,
    chatDeleteArgs: ChatDeleteArguments,
  ) {
    const spanId = `[deleteMessageOnSlack] [${installation?.id}]`;
    try {
      // Delete the message on slack
      const response = await this.slackApiProvider.deleteMessage(
        installation.botToken,
        chatDeleteArgs,
      );

      return response;
    } catch (slackError) {
      // Check if error is "not_in_channel"
      if (
        slackError instanceof Error &&
        slackError.message.includes('not_in_channel')
      ) {
        this.logger.warn(`${spanId} User not in channel, retrying as bot`);

        // Create a new args object without the user token
        const { token, ...botDeleteArgs } = chatDeleteArgs; // Remove user token to post as bot

        // Retry with bot token only
        return this.slackApiProvider.deleteMessage(
          installation.botToken,
          botDeleteArgs,
        );
      }

      // Handle other errors
      if (slackError instanceof Error) {
        this.logger.error(
          `${spanId} Error deleting message from slack: ${slackError.message}`,
        );
      } else {
        console.error(slackError);
      }

      // Capture the error
      this.sentryService.captureException(slackError, {
        name: '🚨 Error deleting message from slack!',
        tag: SLACK_SENTRY_TAG,
      });

      // Throw the error instead of returning null
      throw slackError;
    }
  }

  /**
   * Gets the slack user details for the comment
   * @param data - The data of the event
   */
  private async getSlackUserDetails(data: GetSlackUserDetails) {
    try {
      const { orgId, author, message } = data;

      // Find the slack user associated with the comment
      const slackUser = await this.usersRepository.findOne({
        where: {
          slackProfileEmail: author.email,
          organization: { uid: orgId },
          installation: { id: message.installation.id },
        },
      });

      let userName =
        slackUser?.realName ?? slackUser?.displayName ?? slackUser?.name;
      let iconUrl =
        slackUser?.images?.image_original ?? slackUser?.images?.image_72;

      if (!slackUser) {
        this.logger.warn(
          `No slack user found for comment ${message.id} by ${author.email}`,
        );

        userName = author.name;
        iconUrl = '';
      }

      return { userDetails: { userName, iconUrl }, slackUser };
    } catch (error) {
      this.logger.error(
        `Error getting slack user details: ${error instanceof Error ? error.message : String(error)}`,
      );

      this.sentryService.captureException(error, {
        name: '🚨 Error getting slack user details!',
        tag: SLACK_SENTRY_TAG,
      });

      throw error;
    }
  }

  /**
   * Gets the private triage message for the ticket
   * @param payload - The payload of the event
   * @returns The private triage message for the ticket
   */
  private async getPrivateTriageMessage(payload: TicketCommentDeleted) {
    try {
      const { orgId, data } = payload;
      const { comment } = data;

      // Find the triage message for the ticket
      const triageMessage = await this.triageMessagesRepository.findByCondition(
        {
          where: {
            platformThreadId: comment.parentCommentId,
            organization: { uid: orgId },
          },
          relations: { installation: true, channel: true },
        },
      );

      // If no triage message is found, log an error and return
      if (!triageMessage) {
        const errorMessage = `No triage message found for comment ${comment.parentCommentId}`;
        this.logger.error(errorMessage);
        throw new Error(errorMessage);
      }

      this.logger.log(
        `Found triage message for comment ${comment.parentCommentId} with id ${triageMessage.id}`,
      );

      return { triageMessage };
    } catch (error) {
      this.logger.error(
        `Error getting private triage message: ${error instanceof Error ? error.message : String(error)}`,
      );

      this.sentryService.captureException(error, {
        name: '🚨 Error getting private triage message!',
        tag: SLACK_SENTRY_TAG,
        commentId: payload.data.comment.parentCommentId,
      });

      throw error;
    }
  }

  /**
   * Gets the slack message for the ticket
   * @param payload - The payload of the event
   * @returns The slack message for the ticket
   */
  private async getPublicSlackMessage(payload: TicketCommentDeleted) {
    try {
      const { orgId, data } = payload;
      const { comment } = data;
      const { parentCommentId } = comment;

      // Find the slack message for the ticket
      const slackMessage = await this.slackMessagesRepository.findByCondition({
        where: {
          platformTicketId: data.ticket.id,
          organization: { uid: orgId },
        },
        relations: { installation: true, channel: true },
      });

      // If no slack message is found, log an error and return
      if (!slackMessage) {
        const errorMessage = `No slack message found for ticket ${data.ticket.id}`;
        this.logger.error(errorMessage);
        throw new Error(errorMessage);
      }

      this.logger.log(
        `Found slack message for ticket ${data.ticket.id} with id ${slackMessage.id}`,
      );

      let groupedMessage: GroupedSlackMessages | null = null;
      if (parentCommentId) {
        groupedMessage = await this.groupedMessagesRepository.findByCondition({
          where: { parentCommentId, organization: { uid: orgId } },
        });
      }

      this.logger.log(
        `Found grouped message for ticket ${data.ticket.id} with id ${groupedMessage?.id}`,
      );

      return { slackMessage, groupedMessage };
    } catch (error) {
      this.logger.error(
        `Error getting public slack message: ${error instanceof Error ? error.message : String(error)}`,
      );

      this.sentryService.captureException(error, {
        name: '🚨 Error getting public slack message!',
        tag: SLACK_SENTRY_TAG,
        ticketId: payload.data.ticket.id,
      });

      throw error;
    }
  }
}
