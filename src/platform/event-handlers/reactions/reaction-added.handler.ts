import { Inject, Injectable } from '@nestjs/common';
import { InjectRepository } from '@nestjs/typeorm';
import { Repository } from 'typeorm';
import { SlackMessagesRepository } from '../../../database/entities/slack-messages/repositories/slack-messages.repository';
import { CommentConversationMapsRepository } from '../../../database/entities/mappings/repositories/comment-conversation-maps.repository';
import { SlackWebAPIService } from '../../../slack/providers/slack-apis/slack-apis.service';
import { SLACK_SENTRY_TAG } from '../../../utils';
import { SentryService } from '../../../utils/filters/sentry-alerts.filter';
import { CUSTOM_LOGGER_TOKEN, ILogger } from '../../../utils/logger';
import { PlatformEvent } from '../../decorators';
import { PlatformWebhookEvent } from '../../type-system';
import { <PERSON><PERSON><PERSON><PERSON> } from '../commons/platform-handlers.interface';
import { ThenaPlatformApiProvider } from '../../../external/provider/thena-platform-api.provider';
import { Users } from '../../../database/entities';

interface CommentReactionAddedPayload {
  commentId: string;
  reactionName: string;
  userId: string;
}

interface PlatformReactionEvent {
  commentId: string;
  reactionName: string;
  userId: string;
}

@Injectable()
@PlatformEvent('ticket:comment:reaction:added')
export class CommentReactionAddedHandler implements PlatformHandler {
  constructor(
    @Inject(CUSTOM_LOGGER_TOKEN) private readonly logger: ILogger,
    @Inject('Sentry') private readonly sentryService: SentryService,

    // External API Providers
    private readonly slackApiProvider: SlackWebAPIService,
    private readonly thenaPlatformApiProvider: ThenaPlatformApiProvider,

    // Database Repositories
    private readonly slackMessagesRepository: SlackMessagesRepository,
    private readonly commentConversationMapsRepository: CommentConversationMapsRepository,

    @InjectRepository(Users)
    private readonly usersRepository: Repository<Users>,
  ) {}

  async handle(event: PlatformWebhookEvent) {
    try {
      this.logger.log(
        `Processing comment reaction added on: ${event.message?.payload?.comment?.id}`,
      );

      const { payload, orgId } = event.message;
      // Type casting the payload to ensure TypeScript recognizes the properties
      if (payload.reaction?.metadata?.ignoreSelf) {
        this.logger.log(`Reaction ${payload.reaction.name} ignored because it was added from slack itself`);
        return;
      }

      const commentId = payload.comment.id;
      const reactionName = payload.reaction.name;
      const userEmail = payload.reaction?.metadata?.impersonatedUserEmail ?? payload.reaction.author.email;

      // Find the slack message associated with this comment
      const slackMessage = await this.slackMessagesRepository.findByCondition({
        where: { 
          platformCommentId: commentId, 
          organization: { uid: orgId } 
        },
        relations: { installation: true, channel: true, organization: true },
      });
      
      // If no slack message is found, try to find it in the comment conversation maps
      if (!slackMessage) {
        const commentConversationMap = await this.commentConversationMapsRepository.findByCondition({
          where: { platformCommentId: commentId, organization: { uid: orgId } },
          relations: { installation: true, channel: true, organization: true },
        });

        if (!commentConversationMap) {
          this.logger.error(`No slack message found for comment ${commentId}`);
          return;
        }

        // Try to find the authorized user who reacted
        await this.addReactionWithUserToken(
          commentConversationMap.installation,
          commentConversationMap.channel.channelId,
          commentConversationMap.slackTs,
          reactionName,
          userEmail,
          orgId
        );
      } else {
        // Try to find the authorized user who reacted
        await this.addReactionWithUserToken(
          slackMessage.installation,
          slackMessage.channel.channelId,
          slackMessage.slackMessageTs,
          reactionName,
          userEmail,
          orgId
        );
      }

      this.logger.log(
        `Successfully processed reaction ${reactionName} added to comment ${commentId}`,
      );
    } catch (error) {
      if (error instanceof Error) {
        this.logger.error(
          `Error processing comment reaction added: ${error.message}`,
          error.stack,
        );
      } else {
        console.error(`Error processing comment reaction added:`, error);
      }

      // Capture the error
      this.sentryService.captureException(error, {
        name: '🚨 Error processing comment reaction added!',
        tag: SLACK_SENTRY_TAG,
      });
    }
  }

  /**
   * Adds a reaction to a message using user token if available, falls back to bot token
   * @param installation The installation data
   * @param channel The channel ID
   * @param messageTs The message timestamp
   * @param reactionName The reaction name
   * @param userEmail The email of the user who reacted
   * @param orgId The organization ID
   */
  private async addReactionWithUserToken(
    installation: any,
    channel: string,
    messageTs: string,
    reactionName: string,
    userEmail: string,
    orgId: string
  ) {
    try {
      // Try to find the user who reacted in our database by email
      const user = await this.usersRepository.findOneBy({
        slackProfileEmail: userEmail,
        installation: { id: installation.id },
        organization: { uid: orgId },
      });

      // Check if user exists and has a slack access token (is authorized)
      if (user && user.slackAccessToken) {
        this.logger.log(`Using user token for reaction ${reactionName} from user ${user.id}`);
        
        try {
          // Add reaction using the user's token
          await this.slackApiProvider.addReactionToMessage(user.slackAccessToken, {
            channel,
            timestamp: messageTs,
            name: reactionName,
          });
          
          // If we successfully added the reaction with the user token, return early
          // This prevents the reaction from being added twice
          return;
        } catch (userTokenError) {
          if (userTokenError instanceof Error) {
            const slackError = userTokenError as any;
            const reactionAlreadyAddedErr =
            slackError?.data?.error === 'already_reacted';

            if (reactionAlreadyAddedErr) {
              this.logger.log(`Reaction ${reactionName} already added to message ${messageTs} in channel ${channel} by user ${userEmail}`);
              return;
            }
          }
          // If there's an error with the user token, log it and continue to use bot token
          this.logger.error(`Error using user token, falling back to bot token: ${userTokenError instanceof Error ? userTokenError.message : 'Unknown error'}`);
        }
      }

      
      // Only reach this point if:
      // 1. User doesn't exist
      // 2. User has no token
      // 3. Adding reaction with user token failed
      this.logger.log(`Using bot token for reaction ${reactionName} - user not authorized or token failed`);
      await this.addReactionToMessage(
        installation.botToken,
        channel,
        messageTs,
        reactionName,
      );
    } catch (error) {
      // If there's an error with user token (e.g., token expired), fall back to bot token
      this.logger.error(`Error using user token, falling back to bot token: ${error instanceof Error ? error.message : 'Unknown error'}`);
      await this.addReactionToMessage(
        installation.botToken,
        channel,
        messageTs,
        reactionName,
      );
    }
  }

  /**
   * Adds a reaction to a message using bot token
   * @param token The bot token
   * @param channel The channel ID
   * @param messageTs The message timestamp
   * @param reactionName The reaction name
   */
  private async addReactionToMessage(
    token: string,
    channel: string,
    messageTs: string,
    reactionName: string,
  ) {
    try {
      await this.slackApiProvider.addReactionToMessage(token, {
        channel,
        timestamp: messageTs,
        name: reactionName,
      });
    } catch (slackError) {
      if (slackError instanceof Error) {
        this.logger.error(
          `Error adding reaction to slack message: ${slackError.message}`,
        );
      } else {
        console.error(slackError);
      }

      // Capture the error
      this.sentryService.captureException(slackError, {
        name: '🚨 Error adding reaction to slack message!',
        tag: SLACK_SENTRY_TAG,
      });
    }
  }
} 