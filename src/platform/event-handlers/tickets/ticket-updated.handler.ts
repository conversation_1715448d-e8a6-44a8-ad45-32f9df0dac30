import { Inject, Injectable } from '@nestjs/common';
import { InjectRepository } from '@nestjs/typeorm';
import { Repository } from 'typeorm';
import { SlackMessages } from '../../../database/entities';
import { CoreTriageService } from '../../../slack/core/messages';
import { CUSTOM_LOGGER_TOKEN, ILogger } from '../../../utils/logger';
import { PlatformEvent } from '../../decorators';
import { PlatformWebhookEvent } from '../../type-system';
import { PlatformHandler } from '../commons/platform-handlers.interface';
import { SentryService } from '../../../utils/filters/sentry-alerts.filter';
import { SLACK_SENTRY_TAG } from '../../../utils';

type TicketUpdatedPayload =
  PlatformWebhookEvent<'ticket:updated'>['message']['payload'];

@Injectable()
@PlatformEvent('ticket:updated')
export class TicketUpdatedHandler implements PlatformHandler {
  constructor(
    @Inject(CUSTOM_LOGGER_TOKEN) private readonly logger: ILogger,
    @Inject('Sentry') private readonly sentryService: SentryService,

    // Core utilities
    private readonly coreTriageService: CoreTriageService,

    // Database Repositories
    @InjectRepository(SlackMessages)
    private readonly slackMessagesRepository: Repository<SlackMessages>,
  ) {}

  async handle(event: PlatformWebhookEvent<'ticket:updated'>) {
    const spanId = `[TicketUpdatedHandler] [${event.message.eventId}]`;
    this.logger.log(
      `${spanId} Processing ticket updated: ${event.message.payload.ticket.id}`,
    );

    const { message } = event;
    const { payload, actor, eventId, eventType, orgId, timestamp } = message;
    const { ticket } = payload;

    try {
      // [SANITY CHECK] If the event type is not ticket:updated, skip the event
      if (eventType !== 'ticket:updated') {
        this.logger.debug(
          `${spanId} Skipping ticket updated event processor for event ${eventId} for org ${orgId} at ${timestamp}; event id: ${eventId}, by ${actor.email} id: ${actor.id}, expected event type: ticket:updated got ${eventType}`,
        );

        return;
      }

      this.logger.log(
        `${spanId} Ticket id ${ticket.id} was updated for org ${orgId} at ${timestamp}; event id: ${eventId}, by ${actor.email} id: ${actor.id}`,
      );

      // Find the slack message for the ticket
      const slackMessage = await this.slackMessagesRepository.findOne({
        where: { platformTicketId: ticket.id, organization: { uid: orgId } },
        relations: ['installation', 'organization'],
      });

      // Return if the slack message is not found
      if (!slackMessage) {
        this.logger.debug(
          `${spanId} No slack message found for ticket ${ticket.id} in org ${orgId}`,
        );

        return;
      }

      // Check if the ticket was updated
      const wasUpdated = this.wasTicketUpdated(slackMessage, payload);
      if (!wasUpdated) {
        this.logger.debug(
          `${spanId} Ticket ${ticket.id} in org ${orgId} was not updated, our local metadata is up to date and valid for this ticket`,
        );

        return;
      }

      const installation = {
        ...slackMessage.installation,
        organization: slackMessage.organization,
      };

      // Update the triage messages for the slack message
      try {
        await this.coreTriageService.updateTriageMessagesForSlackMessage(
          installation,
          slackMessage,
        );
      } catch (updateError) {
        this.logger.error(
          `${spanId} Error updating triage messages for ticket ${ticket.id} in org ${orgId}`,
          updateError instanceof Error ? updateError.stack : undefined,
        );

        // Report to Sentry
        this.sentryService.captureException(updateError, {
          tag: SLACK_SENTRY_TAG,
          name: '🚨 Error updating triage messages',
          ticketId: ticket.id,
          orgId: orgId,
          eventId: eventId,
        });
      }
    } catch (error) {
      if (error instanceof Error) {
        this.logger.error(
          `${spanId} Error processing ticket updated event ${event.message.eventId} for org ${orgId} at ${timestamp}; event id: ${eventId}, by ${actor.email} id: ${actor.id}`,
          error.stack,
        );

        // Report to Sentry
        this.sentryService.captureException(error, {
          tag: SLACK_SENTRY_TAG,
          name: '🚨 Error processing ticket updated event',
          ticketId: ticket.id,
          orgId: orgId,
          eventId: eventId,
          actorEmail: actor.email,
        });
      } else {
        console.error(
          `${spanId} Error processing ticket updated event ${event.message.eventId} for org ${orgId} at ${timestamp}; event id: ${eventId}, by ${actor.email} id: ${actor.id}`,
          error,
        );

        // Report to Sentry for non-Error objects
        this.sentryService.captureException(new Error(`Non-Error thrown in ticket updated handler: ${JSON.stringify(error)}`), {
          tag: SLACK_SENTRY_TAG,
          name: '🚨 Non-Error thrown in ticket updated handler',
          ticketId: ticket.id,
          orgId: orgId,
          eventId: eventId,
          actorEmail: actor.email,
        });
      }
    }
  }

  /**
   * Checks if the ticket was updated
   *
   * @param slackMessage Slack message
   * @param payload Ticket updated payload
   * @returns True if the ticket was updated, false otherwise
   */
  private wasTicketUpdated(
    slackMessage: SlackMessages,
    payload: TicketUpdatedPayload,
  ) {
    const spanId = `[wasTicketUpdated] [${slackMessage?.id}]`;
    try {
      const { ticket, previousTicket } = payload;
      const { metadata } = slackMessage;
      const { ticket_details: ticketDetails } = metadata || {};

      let ticketUpdated = false;

      // Check if the status was updated
      if (ticket?.statusId !== previousTicket?.statusId) {
        ticketUpdated = true;
      }

      // Check if the local metadata is up to date
      if (ticketDetails?.statusId !== ticket?.statusId) {
        ticketUpdated = true;
      }

      // Check if the priority was updated
      if (ticket?.priorityId !== previousTicket?.priorityId) {
        ticketUpdated = true;
      }

      // Check if the local metadata is up to date
      if (ticketDetails?.priorityId !== ticket?.priorityId) {
        ticketUpdated = true;
      }

      return ticketUpdated;
    } catch (error) {
      this.logger.error(
        `${spanId} Error checking if ticket was updated`,
        error instanceof Error ? error.stack : undefined,
      );
      // Default to true so we update the ticket anyway

       // Capture the error
       this.sentryService.captureException(error, {
        name: `🚨 Error updating ticket ${spanId}`,
        tag: SLACK_SENTRY_TAG,
      });

      return true;
    }
  }
}
