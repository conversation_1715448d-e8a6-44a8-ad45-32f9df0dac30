import { MigrationInterface, QueryRunner } from 'typeorm';

export class UserSlackToken1741729929123 implements MigrationInterface {
  name = 'UserSlackToken1741729929123';

  public async up(queryRunner: QueryRunner): Promise<void> {
    await queryRunner.query(
      `ALTER TABLE "users" ADD "slack_access_token" character varying`,
    );
  }

  public async down(queryRunner: QueryRunner): Promise<void> {
    await queryRunner.query(
      `ALTER TABLE "users" DROP COLUMN "slack_access_token"`,
    );
  }
}
