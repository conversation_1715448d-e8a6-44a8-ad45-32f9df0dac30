import { MigrationInterface, QueryRunner } from 'typeorm';

export class AlterSlackAuditLogs1744210279339 implements MigrationInterface {
  name = 'AlterSlackAuditLogs1744210279339';

  public async up(queryRunner: QueryRunner): Promise<void> {
    await queryRunner.query(
      `ALTER TABLE "slack_audit_logs" DROP CONSTRAINT "FK_7225b4e24e845eb4a3539c1a85d"`,
    );
    await queryRunner.query(
      `DROP INDEX "public"."IDX_adc2b28d42cbf5448c913f27df"`,
    );
    await queryRunner.query(
      `DROP INDEX "public"."IDX_4e85da6a01e7aadc54a0c7a26a"`,
    );
    await queryRunner.query(
      `DROP INDEX "public"."IDX_7225b4e24e845eb4a3539c1a85"`,
    );
    await queryRunner.query(
      `DROP INDEX "public"."IDX_56553e929a0d96d3f129a46696"`,
    );
    await queryRunner.query(
      `ALTER TABLE "slack_audit_logs" DROP COLUMN "entity_id"`,
    );
    await queryRunner.query(
      `ALTER TABLE "slack_audit_logs" DROP COLUMN "entity_uid"`,
    );
    await queryRunner.query(
      `ALTER TABLE "slack_audit_logs" DROP COLUMN "source"`,
    );
    await queryRunner.query(
      `ALTER TABLE "slack_audit_logs" DROP COLUMN "activity_performed_by"`,
    );
    await queryRunner.query(
      `ALTER TABLE "slack_audit_logs" ADD "externalId" character varying(255)`,
    );
    await queryRunner.query(
      `ALTER TABLE "slack_audit_logs" ADD "slackTs" character varying(255)`,
    );
    await queryRunner.query(
      `ALTER TABLE "slack_audit_logs" ADD "threadTs" character varying(255)`,
    );
    await queryRunner.query(
      `ALTER TABLE "slack_audit_logs" ADD "eventTs" character varying(255) NOT NULL`,
    );
    await queryRunner.query(
      `ALTER TABLE "slack_audit_logs" ADD "activityPerformedBy" character varying(255)`,
    );
    await queryRunner.query(
      `ALTER TABLE "slack_audit_logs" ADD "installation_id" bigint`,
    );
    await queryRunner.query(
      `ALTER TYPE "public"."slack_audit_logs_entity_type_enum" RENAME TO "slack_audit_logs_entity_type_enum_old"`,
    );
    await queryRunner.query(
      `CREATE TYPE "public"."slack_audit_logs_entity_type_enum" AS ENUM('slack_message', 'channel')`,
    );
    await queryRunner.query(
      `ALTER TABLE "slack_audit_logs" ALTER COLUMN "entity_type" TYPE "public"."slack_audit_logs_entity_type_enum" USING "entity_type"::"text"::"public"."slack_audit_logs_entity_type_enum"`,
    );
    await queryRunner.query(
      `DROP TYPE "public"."slack_audit_logs_entity_type_enum_old"`,
    );
    await queryRunner.query(
      `ALTER TYPE "public"."slack_audit_logs_operation_enum" RENAME TO "slack_audit_logs_operation_enum_old"`,
    );
    await queryRunner.query(
      `CREATE TYPE "public"."slack_audit_logs_operation_enum" AS ENUM('info', 'error', 'created', 'updated', 'deleted', 'archived', 'restored')`,
    );
    await queryRunner.query(
      `ALTER TABLE "slack_audit_logs" ALTER COLUMN "operation" TYPE "public"."slack_audit_logs_operation_enum" USING "operation"::"text"::"public"."slack_audit_logs_operation_enum"`,
    );
    await queryRunner.query(
      `DROP TYPE "public"."slack_audit_logs_operation_enum_old"`,
    );
    await queryRunner.query(
      `CREATE INDEX "IDX_eb2d7a564dfdd51223c7894e13" ON "slack_audit_logs" ("installation_id") `,
    );
    await queryRunner.query(
      `CREATE INDEX "IDX_c813aa748055359ccd980fb98b" ON "slack_audit_logs" ("externalId") `,
    );
    await queryRunner.query(
      `CREATE INDEX "IDX_c65931fd4520b5b8d7fabe3dd3" ON "slack_audit_logs" ("slackTs") `,
    );
    await queryRunner.query(
      `CREATE INDEX "IDX_e9e0b9c31f42dbffcb276d5f25" ON "slack_audit_logs" ("threadTs") `,
    );
    await queryRunner.query(
      `CREATE INDEX "IDX_d189598a19b8117190cfa4fdf6" ON "slack_audit_logs" ("eventTs") `,
    );
    await queryRunner.query(
      `CREATE INDEX "IDX_ce42c80f1e2b5c8278fa9d2dc8" ON "slack_audit_logs" ("activityPerformedBy") `,
    );
    await queryRunner.query(
      `CREATE INDEX "IDX_0a3c2d00dd04dfd58095e71483" ON "slack_messages" ("platform_ticket_id") `,
    );
    await queryRunner.query(
      `ALTER TABLE "slack_audit_logs" ADD CONSTRAINT "FK_eb2d7a564dfdd51223c7894e139" FOREIGN KEY ("installation_id") REFERENCES "installations"("id") ON DELETE CASCADE ON UPDATE NO ACTION`,
    );
  }

  public async down(queryRunner: QueryRunner): Promise<void> {
    await queryRunner.query(
      `ALTER TABLE "slack_audit_logs" DROP CONSTRAINT "FK_eb2d7a564dfdd51223c7894e139"`,
    );
    await queryRunner.query(
      `DROP INDEX "public"."IDX_0a3c2d00dd04dfd58095e71483"`,
    );
    await queryRunner.query(
      `DROP INDEX "public"."IDX_ce42c80f1e2b5c8278fa9d2dc8"`,
    );
    await queryRunner.query(
      `DROP INDEX "public"."IDX_d189598a19b8117190cfa4fdf6"`,
    );
    await queryRunner.query(
      `DROP INDEX "public"."IDX_e9e0b9c31f42dbffcb276d5f25"`,
    );
    await queryRunner.query(
      `DROP INDEX "public"."IDX_c65931fd4520b5b8d7fabe3dd3"`,
    );
    await queryRunner.query(
      `DROP INDEX "public"."IDX_c813aa748055359ccd980fb98b"`,
    );
    await queryRunner.query(
      `DROP INDEX "public"."IDX_eb2d7a564dfdd51223c7894e13"`,
    );
    await queryRunner.query(
      `CREATE TYPE "public"."slack_audit_logs_operation_enum_old" AS ENUM('archived', 'created', 'deleted', 'info', 'restored', 'updated')`,
    );
    await queryRunner.query(
      `ALTER TABLE "slack_audit_logs" ALTER COLUMN "operation" TYPE "public"."slack_audit_logs_operation_enum_old" USING "operation"::"text"::"public"."slack_audit_logs_operation_enum_old"`,
    );
    await queryRunner.query(
      `DROP TYPE "public"."slack_audit_logs_operation_enum"`,
    );
    await queryRunner.query(
      `ALTER TYPE "public"."slack_audit_logs_operation_enum_old" RENAME TO "slack_audit_logs_operation_enum"`,
    );
    await queryRunner.query(
      `CREATE TYPE "public"."slack_audit_logs_entity_type_enum_old" AS ENUM('slack_message')`,
    );
    await queryRunner.query(
      `ALTER TABLE "slack_audit_logs" ALTER COLUMN "entity_type" TYPE "public"."slack_audit_logs_entity_type_enum_old" USING "entity_type"::"text"::"public"."slack_audit_logs_entity_type_enum_old"`,
    );
    await queryRunner.query(
      `DROP TYPE "public"."slack_audit_logs_entity_type_enum"`,
    );
    await queryRunner.query(
      `ALTER TYPE "public"."slack_audit_logs_entity_type_enum_old" RENAME TO "slack_audit_logs_entity_type_enum"`,
    );
    await queryRunner.query(
      `ALTER TABLE "slack_audit_logs" DROP COLUMN "installation_id"`,
    );
    await queryRunner.query(
      `ALTER TABLE "slack_audit_logs" DROP COLUMN "activityPerformedBy"`,
    );
    await queryRunner.query(
      `ALTER TABLE "slack_audit_logs" DROP COLUMN "eventTs"`,
    );
    await queryRunner.query(
      `ALTER TABLE "slack_audit_logs" DROP COLUMN "threadTs"`,
    );
    await queryRunner.query(
      `ALTER TABLE "slack_audit_logs" DROP COLUMN "slackTs"`,
    );
    await queryRunner.query(
      `ALTER TABLE "slack_audit_logs" DROP COLUMN "externalId"`,
    );
    await queryRunner.query(
      `ALTER TABLE "slack_audit_logs" ADD "activity_performed_by" bigint`,
    );
    await queryRunner.query(
      `ALTER TABLE "slack_audit_logs" ADD "source" character varying(255)`,
    );
    await queryRunner.query(
      `ALTER TABLE "slack_audit_logs" ADD "entity_uid" character varying(255)`,
    );
    await queryRunner.query(
      `ALTER TABLE "slack_audit_logs" ADD "entity_id" bigint`,
    );
    await queryRunner.query(
      `CREATE INDEX "IDX_56553e929a0d96d3f129a46696" ON "slack_audit_logs" ("source") `,
    );
    await queryRunner.query(
      `CREATE INDEX "IDX_7225b4e24e845eb4a3539c1a85" ON "slack_audit_logs" ("activity_performed_by") `,
    );
    await queryRunner.query(
      `CREATE INDEX "IDX_4e85da6a01e7aadc54a0c7a26a" ON "slack_audit_logs" ("entity_uid") `,
    );
    await queryRunner.query(
      `CREATE INDEX "IDX_adc2b28d42cbf5448c913f27df" ON "slack_audit_logs" ("entity_id") `,
    );
    await queryRunner.query(
      `ALTER TABLE "slack_audit_logs" ADD CONSTRAINT "FK_7225b4e24e845eb4a3539c1a85d" FOREIGN KEY ("activity_performed_by") REFERENCES "users"("id") ON DELETE NO ACTION ON UPDATE NO ACTION`,
    );
  }
}
