import { MigrationInterface, QueryRunner } from 'typeorm';

export class UpdateCascadesOnFks1741184232563 implements MigrationInterface {
  name = 'UpdateCascadesOnFks1741184232563';

  public async up(queryRunner: QueryRunner): Promise<void> {
    await queryRunner.query(
      `ALTER TABLE "users" DROP CONSTRAINT "fk_slack_user_installation_id"`,
    );
    await queryRunner.query(
      `ALTER TABLE "users" DROP CONSTRAINT "fk_slack_user_organization_id"`,
    );
    await queryRunner.query(
      `ALTER TABLE "slack_subgroups" DROP CONSTRAINT "fk_slack_subgroup_installation_id"`,
    );
    await queryRunner.query(
      `ALTER TABLE "slack_subgroups" DROP CONSTRAINT "fk_slack_subgroup_organization_id"`,
    );
    await queryRunner.query(
      `ALTER TABLE "channel_triage_mappings" DROP CONSTRAINT "FK_4bd3b2c93c91af38396780afde9"`,
    );
    await queryRunner.query(
      `ALTER TABLE "channel_triage_mappings" DROP CONSTRAINT "FK_62bd22600ff78d4d5f2ceb7ac91"`,
    );
    await queryRunner.query(
      `ALTER TABLE "comment_thread_mappings" DROP CONSTRAINT "FK_51c78c6562e97e94e0a8d279b4a"`,
    );
    await queryRunner.query(
      `ALTER TABLE "comment_thread_mappings" DROP CONSTRAINT "FK_c03f8f28e58218bbe4a86ac8fc8"`,
    );
    await queryRunner.query(
      `ALTER TABLE "platform_teams_to_channel_mappings" DROP CONSTRAINT "FK_02cce2fe460c9ca132133e3f519"`,
    );
    await queryRunner.query(
      `ALTER TABLE "platform_teams_to_channel_mappings" DROP CONSTRAINT "FK_ddad5fa6d40658771383caecc10"`,
    );
    await queryRunner.query(
      `ALTER TABLE "sub_team_to_sub_groups_mappings" DROP CONSTRAINT "FK_1ff38192049283472bcd49a7fd8"`,
    );
    await queryRunner.query(
      `ALTER TABLE "sub_team_to_sub_groups_mappings" DROP CONSTRAINT "FK_4ef99086bdfd1f77bd9415fed6c"`,
    );
    await queryRunner.query(
      `ALTER TABLE "sub_team_to_sub_groups_mappings" DROP CONSTRAINT "FK_89494bfd0f0b94db047846907ec"`,
    );
    await queryRunner.query(
      `ALTER TABLE "sub_team_to_sub_groups_mappings" DROP CONSTRAINT "FK_c61da7043aeb3743639200e31b0"`,
    );
    await queryRunner.query(
      `ALTER TABLE "team_triage_rule_mappings" DROP CONSTRAINT "FK_24e554d6e96e640b741ba7383a6"`,
    );
    await queryRunner.query(
      `ALTER TABLE "team_triage_rule_mappings" DROP CONSTRAINT "FK_675f82154edd9b674565ddf93fa"`,
    );
    await queryRunner.query(
      `ALTER TABLE "team_triage_rule_mappings" DROP CONSTRAINT "FK_dbdcdcac6b2f885927eaaadac57"`,
    );
    await queryRunner.query(
      `ALTER TABLE "prompts" DROP CONSTRAINT "FK_17ed6c0f33e2720e182e29b80d0"`,
    );
    await queryRunner.query(
      `ALTER TABLE "prompts" DROP CONSTRAINT "FK_1cfe0d970d6b4ed274211b6687d"`,
    );
    await queryRunner.query(
      `ALTER TABLE "prompts" DROP CONSTRAINT "FK_5e0c3fe9886e556ac72000d49cd"`,
    );
    await queryRunner.query(
      `ALTER TABLE "settings" DROP CONSTRAINT "FK_6c36c1705dcf1bef7edb9ab33aa"`,
    );
    await queryRunner.query(
      `ALTER TABLE "settings" DROP CONSTRAINT "FK_7919a3396d20a588a7036035564"`,
    );
    await queryRunner.query(
      `ALTER TABLE "settings" DROP CONSTRAINT "FK_d5bc871126b26b4fb1b9c743825"`,
    );
    await queryRunner.query(
      `ALTER TABLE "slack_messages" DROP CONSTRAINT "FK_5e1fe4fd9640abb971b42986b0c"`,
    );
    await queryRunner.query(
      `ALTER TABLE "slack_messages" DROP CONSTRAINT "fk_request_installation_id"`,
    );
    await queryRunner.query(
      `ALTER TABLE "slack_triage_messages" DROP CONSTRAINT "fk_request_organization_id"`,
    );
    await queryRunner.query(
      `ALTER TABLE "grouped_slack_messages" DROP CONSTRAINT "fk_request_organization_id"`,
    );
    await queryRunner.query(
      `ALTER TABLE "slack_messages" ADD "platform_comment_id" character varying`,
    );
    await queryRunner.query(
      `ALTER TABLE "sub_team_to_sub_groups_mappings" ALTER COLUMN "organization_id" DROP NOT NULL`,
    );
    await queryRunner.query(
      `DROP INDEX "public"."unique_default_triage_channel"`,
    );
    await queryRunner.query(
      `ALTER TABLE "team_triage_rule_mappings" ALTER COLUMN "organization_id" DROP NOT NULL`,
    );
    await queryRunner.query(`DROP INDEX "public"."idx_uniq_settings_for_team"`);
    await queryRunner.query(
      `ALTER TABLE "settings" ALTER COLUMN "platform_team_id" DROP NOT NULL`,
    );
    await queryRunner.query(
      `ALTER TABLE "settings" ALTER COLUMN "installation_id" DROP NOT NULL`,
    );
    await queryRunner.query(
      `ALTER TABLE "settings" ALTER COLUMN "organization_id" DROP NOT NULL`,
    );
    await queryRunner.query(
      `CREATE UNIQUE INDEX "unique_default_triage_channel" ON "team_triage_rule_mappings" ("platform_team_id", "installation_id", "organization_id") WHERE deleted_at IS NULL AND is_default = true`,
    );
    await queryRunner.query(
      `CREATE UNIQUE INDEX "idx_uniq_settings_for_team" ON "settings" ("platform_team_id", "installation_id", "organization_id") `,
    );
    await queryRunner.query(
      `CREATE INDEX "idx_slack_message_platform_comment_id" ON "slack_messages" ("platform_comment_id") `,
    );
    await queryRunner.query(
      `ALTER TABLE "users" ADD CONSTRAINT "fk_slack_user_installation_id" FOREIGN KEY ("installation_id") REFERENCES "installations"("id") ON DELETE CASCADE ON UPDATE CASCADE`,
    );
    await queryRunner.query(
      `ALTER TABLE "users" ADD CONSTRAINT "fk_slack_user_organization_id" FOREIGN KEY ("organization_id") REFERENCES "organizations"("id") ON DELETE CASCADE ON UPDATE CASCADE`,
    );
    await queryRunner.query(
      `ALTER TABLE "slack_subgroups" ADD CONSTRAINT "fk_slack_subgroup_installation_id" FOREIGN KEY ("installation_id") REFERENCES "installations"("id") ON DELETE CASCADE ON UPDATE CASCADE`,
    );
    await queryRunner.query(
      `ALTER TABLE "slack_subgroups" ADD CONSTRAINT "fk_slack_subgroup_organization_id" FOREIGN KEY ("organization_id") REFERENCES "organizations"("id") ON DELETE CASCADE ON UPDATE CASCADE`,
    );
    await queryRunner.query(
      `ALTER TABLE "channel_triage_mappings" ADD CONSTRAINT "FK_4bd3b2c93c91af38396780afde9" FOREIGN KEY ("installation_id") REFERENCES "installations"("id") ON DELETE CASCADE ON UPDATE CASCADE`,
    );
    await queryRunner.query(
      `ALTER TABLE "channel_triage_mappings" ADD CONSTRAINT "FK_62bd22600ff78d4d5f2ceb7ac91" FOREIGN KEY ("organization_id") REFERENCES "organizations"("id") ON DELETE CASCADE ON UPDATE CASCADE`,
    );
    await queryRunner.query(
      `ALTER TABLE "comment_thread_mappings" ADD CONSTRAINT "FK_c03f8f28e58218bbe4a86ac8fc8" FOREIGN KEY ("installation_id") REFERENCES "installations"("id") ON DELETE CASCADE ON UPDATE CASCADE`,
    );
    await queryRunner.query(
      `ALTER TABLE "comment_thread_mappings" ADD CONSTRAINT "FK_51c78c6562e97e94e0a8d279b4a" FOREIGN KEY ("organization_id") REFERENCES "organizations"("id") ON DELETE CASCADE ON UPDATE CASCADE`,
    );
    await queryRunner.query(
      `ALTER TABLE "platform_teams_to_channel_mappings" ADD CONSTRAINT "FK_ddad5fa6d40658771383caecc10" FOREIGN KEY ("installation_id") REFERENCES "installations"("id") ON DELETE CASCADE ON UPDATE CASCADE`,
    );
    await queryRunner.query(
      `ALTER TABLE "platform_teams_to_channel_mappings" ADD CONSTRAINT "FK_02cce2fe460c9ca132133e3f519" FOREIGN KEY ("organization_id") REFERENCES "organizations"("id") ON DELETE CASCADE ON UPDATE CASCADE`,
    );
    await queryRunner.query(
      `ALTER TABLE "sub_team_to_sub_groups_mappings" ADD CONSTRAINT "FK_1ff38192049283472bcd49a7fd8" FOREIGN KEY ("sub_group_id") REFERENCES "slack_subgroups"("id") ON DELETE CASCADE ON UPDATE CASCADE`,
    );
    await queryRunner.query(
      `ALTER TABLE "sub_team_to_sub_groups_mappings" ADD CONSTRAINT "FK_4ef99086bdfd1f77bd9415fed6c" FOREIGN KEY ("platform_team_id") REFERENCES "platform_teams"("id") ON DELETE CASCADE ON UPDATE CASCADE`,
    );
    await queryRunner.query(
      `ALTER TABLE "sub_team_to_sub_groups_mappings" ADD CONSTRAINT "FK_89494bfd0f0b94db047846907ec" FOREIGN KEY ("installation_id") REFERENCES "installations"("id") ON DELETE CASCADE ON UPDATE CASCADE`,
    );
    await queryRunner.query(
      `ALTER TABLE "sub_team_to_sub_groups_mappings" ADD CONSTRAINT "FK_c61da7043aeb3743639200e31b0" FOREIGN KEY ("organization_id") REFERENCES "organizations"("id") ON DELETE CASCADE ON UPDATE CASCADE`,
    );
    await queryRunner.query(
      `ALTER TABLE "team_triage_rule_mappings" ADD CONSTRAINT "FK_24e554d6e96e640b741ba7383a6" FOREIGN KEY ("platform_team_id") REFERENCES "platform_teams"("id") ON DELETE CASCADE ON UPDATE CASCADE`,
    );
    await queryRunner.query(
      `ALTER TABLE "team_triage_rule_mappings" ADD CONSTRAINT "FK_675f82154edd9b674565ddf93fa" FOREIGN KEY ("installation_id") REFERENCES "installations"("id") ON DELETE CASCADE ON UPDATE CASCADE`,
    );
    await queryRunner.query(
      `ALTER TABLE "team_triage_rule_mappings" ADD CONSTRAINT "FK_dbdcdcac6b2f885927eaaadac57" FOREIGN KEY ("organization_id") REFERENCES "organizations"("id") ON DELETE CASCADE ON UPDATE CASCADE`,
    );
    await queryRunner.query(
      `ALTER TABLE "prompts" ADD CONSTRAINT "FK_5e0c3fe9886e556ac72000d49cd" FOREIGN KEY ("platform_team_id") REFERENCES "platform_teams"("id") ON DELETE CASCADE ON UPDATE CASCADE`,
    );
    await queryRunner.query(
      `ALTER TABLE "prompts" ADD CONSTRAINT "FK_1cfe0d970d6b4ed274211b6687d" FOREIGN KEY ("installation_id") REFERENCES "installations"("id") ON DELETE CASCADE ON UPDATE CASCADE`,
    );
    await queryRunner.query(
      `ALTER TABLE "prompts" ADD CONSTRAINT "FK_17ed6c0f33e2720e182e29b80d0" FOREIGN KEY ("organization_id") REFERENCES "organizations"("id") ON DELETE CASCADE ON UPDATE CASCADE`,
    );
    await queryRunner.query(
      `ALTER TABLE "settings" ADD CONSTRAINT "FK_6c36c1705dcf1bef7edb9ab33aa" FOREIGN KEY ("platform_team_id") REFERENCES "platform_teams"("id") ON DELETE CASCADE ON UPDATE CASCADE`,
    );
    await queryRunner.query(
      `ALTER TABLE "settings" ADD CONSTRAINT "FK_7919a3396d20a588a7036035564" FOREIGN KEY ("installation_id") REFERENCES "installations"("id") ON DELETE CASCADE ON UPDATE CASCADE`,
    );
    await queryRunner.query(
      `ALTER TABLE "settings" ADD CONSTRAINT "FK_d5bc871126b26b4fb1b9c743825" FOREIGN KEY ("organization_id") REFERENCES "organizations"("id") ON DELETE CASCADE ON UPDATE CASCADE`,
    );
    await queryRunner.query(
      `ALTER TABLE "slack_messages" ADD CONSTRAINT "FK_5e1fe4fd9640abb971b42986b0c" FOREIGN KEY ("channel_id") REFERENCES "channels"("id") ON DELETE NO ACTION ON UPDATE NO ACTION`,
    );
    await queryRunner.query(
      `ALTER TABLE "slack_messages" ADD CONSTRAINT "fk_request_installation_id" FOREIGN KEY ("installation_id") REFERENCES "installations"("id") ON DELETE NO ACTION ON UPDATE NO ACTION`,
    );
    await queryRunner.query(
      `ALTER TABLE "slack_triage_messages" ADD CONSTRAINT "fk_request_organization_id" FOREIGN KEY ("organization_id") REFERENCES "organizations"("id") ON DELETE CASCADE ON UPDATE CASCADE`,
    );
    await queryRunner.query(
      `ALTER TABLE "grouped_slack_messages" ADD CONSTRAINT "fk_request_organization_id" FOREIGN KEY ("organization_id") REFERENCES "organizations"("id") ON DELETE CASCADE ON UPDATE CASCADE`,
    );
  }

  public async down(queryRunner: QueryRunner): Promise<void> {
    await queryRunner.query(
      `ALTER TABLE "grouped_slack_messages" DROP CONSTRAINT "fk_request_organization_id"`,
    );
    await queryRunner.query(
      `ALTER TABLE "slack_triage_messages" DROP CONSTRAINT "fk_request_organization_id"`,
    );
    await queryRunner.query(
      `ALTER TABLE "slack_messages" DROP CONSTRAINT "fk_request_installation_id"`,
    );
    await queryRunner.query(
      `ALTER TABLE "slack_messages" DROP CONSTRAINT "FK_5e1fe4fd9640abb971b42986b0c"`,
    );
    await queryRunner.query(
      `ALTER TABLE "settings" DROP CONSTRAINT "FK_d5bc871126b26b4fb1b9c743825"`,
    );
    await queryRunner.query(
      `ALTER TABLE "settings" DROP CONSTRAINT "FK_7919a3396d20a588a7036035564"`,
    );
    await queryRunner.query(
      `ALTER TABLE "settings" DROP CONSTRAINT "FK_6c36c1705dcf1bef7edb9ab33aa"`,
    );
    await queryRunner.query(
      `ALTER TABLE "prompts" DROP CONSTRAINT "FK_17ed6c0f33e2720e182e29b80d0"`,
    );
    await queryRunner.query(
      `ALTER TABLE "prompts" DROP CONSTRAINT "FK_1cfe0d970d6b4ed274211b6687d"`,
    );
    await queryRunner.query(
      `ALTER TABLE "prompts" DROP CONSTRAINT "FK_5e0c3fe9886e556ac72000d49cd"`,
    );
    await queryRunner.query(
      `ALTER TABLE "team_triage_rule_mappings" DROP CONSTRAINT "FK_dbdcdcac6b2f885927eaaadac57"`,
    );
    await queryRunner.query(
      `ALTER TABLE "team_triage_rule_mappings" DROP CONSTRAINT "FK_675f82154edd9b674565ddf93fa"`,
    );
    await queryRunner.query(
      `ALTER TABLE "team_triage_rule_mappings" DROP CONSTRAINT "FK_24e554d6e96e640b741ba7383a6"`,
    );
    await queryRunner.query(
      `ALTER TABLE "sub_team_to_sub_groups_mappings" DROP CONSTRAINT "FK_c61da7043aeb3743639200e31b0"`,
    );
    await queryRunner.query(
      `ALTER TABLE "sub_team_to_sub_groups_mappings" DROP CONSTRAINT "FK_89494bfd0f0b94db047846907ec"`,
    );
    await queryRunner.query(
      `ALTER TABLE "sub_team_to_sub_groups_mappings" DROP CONSTRAINT "FK_4ef99086bdfd1f77bd9415fed6c"`,
    );
    await queryRunner.query(
      `ALTER TABLE "sub_team_to_sub_groups_mappings" DROP CONSTRAINT "FK_1ff38192049283472bcd49a7fd8"`,
    );
    await queryRunner.query(
      `ALTER TABLE "platform_teams_to_channel_mappings" DROP CONSTRAINT "FK_02cce2fe460c9ca132133e3f519"`,
    );
    await queryRunner.query(
      `ALTER TABLE "platform_teams_to_channel_mappings" DROP CONSTRAINT "FK_ddad5fa6d40658771383caecc10"`,
    );
    await queryRunner.query(
      `ALTER TABLE "comment_thread_mappings" DROP CONSTRAINT "FK_51c78c6562e97e94e0a8d279b4a"`,
    );
    await queryRunner.query(
      `ALTER TABLE "comment_thread_mappings" DROP CONSTRAINT "FK_c03f8f28e58218bbe4a86ac8fc8"`,
    );
    await queryRunner.query(
      `ALTER TABLE "channel_triage_mappings" DROP CONSTRAINT "FK_62bd22600ff78d4d5f2ceb7ac91"`,
    );
    await queryRunner.query(
      `ALTER TABLE "channel_triage_mappings" DROP CONSTRAINT "FK_4bd3b2c93c91af38396780afde9"`,
    );
    await queryRunner.query(
      `ALTER TABLE "slack_subgroups" DROP CONSTRAINT "fk_slack_subgroup_organization_id"`,
    );
    await queryRunner.query(
      `ALTER TABLE "slack_subgroups" DROP CONSTRAINT "fk_slack_subgroup_installation_id"`,
    );
    await queryRunner.query(
      `ALTER TABLE "users" DROP CONSTRAINT "fk_slack_user_organization_id"`,
    );
    await queryRunner.query(
      `ALTER TABLE "users" DROP CONSTRAINT "fk_slack_user_installation_id"`,
    );
    await queryRunner.query(
      `DROP INDEX "public"."idx_slack_message_platform_comment_id"`,
    );
    await queryRunner.query(`DROP INDEX "public"."idx_uniq_settings_for_team"`);
    await queryRunner.query(
      `DROP INDEX "public"."unique_default_triage_channel"`,
    );
    await queryRunner.query(
      `ALTER TABLE "settings" ALTER COLUMN "organization_id" SET NOT NULL`,
    );
    await queryRunner.query(
      `ALTER TABLE "settings" ALTER COLUMN "installation_id" SET NOT NULL`,
    );
    await queryRunner.query(
      `ALTER TABLE "settings" ALTER COLUMN "platform_team_id" SET NOT NULL`,
    );
    await queryRunner.query(
      `CREATE UNIQUE INDEX "idx_uniq_settings_for_team" ON "settings" ("platform_team_id", "installation_id", "organization_id") `,
    );
    await queryRunner.query(
      `ALTER TABLE "team_triage_rule_mappings" ALTER COLUMN "organization_id" SET NOT NULL`,
    );
    await queryRunner.query(
      `CREATE UNIQUE INDEX "unique_default_triage_channel" ON "team_triage_rule_mappings" ("platform_team_id", "installation_id", "organization_id") WHERE ((deleted_at IS NULL) AND (is_default = true))`,
    );
    await queryRunner.query(
      `ALTER TABLE "sub_team_to_sub_groups_mappings" ALTER COLUMN "organization_id" SET NOT NULL`,
    );
    await queryRunner.query(
      `ALTER TABLE "slack_messages" DROP COLUMN "platform_comment_id"`,
    );
    await queryRunner.query(
      `ALTER TABLE "grouped_slack_messages" ADD CONSTRAINT "fk_request_organization_id" FOREIGN KEY ("organization_id", "organization_id", "organization_id") REFERENCES "organizations"("id","id","id") ON DELETE NO ACTION ON UPDATE NO ACTION`,
    );
    await queryRunner.query(
      `ALTER TABLE "slack_triage_messages" ADD CONSTRAINT "fk_request_organization_id" FOREIGN KEY ("organization_id", "organization_id", "organization_id") REFERENCES "organizations"("id","id","id") ON DELETE NO ACTION ON UPDATE NO ACTION`,
    );
    await queryRunner.query(
      `ALTER TABLE "slack_messages" ADD CONSTRAINT "fk_request_installation_id" FOREIGN KEY ("installation_id", "installation_id", "installation_id") REFERENCES "installations"("id","id","id") ON DELETE CASCADE ON UPDATE CASCADE`,
    );
    await queryRunner.query(
      `ALTER TABLE "slack_messages" ADD CONSTRAINT "FK_5e1fe4fd9640abb971b42986b0c" FOREIGN KEY ("channel_id") REFERENCES "channels"("id") ON DELETE CASCADE ON UPDATE CASCADE`,
    );
    await queryRunner.query(
      `ALTER TABLE "settings" ADD CONSTRAINT "FK_d5bc871126b26b4fb1b9c743825" FOREIGN KEY ("organization_id") REFERENCES "organizations"("id") ON DELETE NO ACTION ON UPDATE NO ACTION`,
    );
    await queryRunner.query(
      `ALTER TABLE "settings" ADD CONSTRAINT "FK_7919a3396d20a588a7036035564" FOREIGN KEY ("installation_id") REFERENCES "installations"("id") ON DELETE NO ACTION ON UPDATE NO ACTION`,
    );
    await queryRunner.query(
      `ALTER TABLE "settings" ADD CONSTRAINT "FK_6c36c1705dcf1bef7edb9ab33aa" FOREIGN KEY ("platform_team_id") REFERENCES "platform_teams"("id") ON DELETE NO ACTION ON UPDATE NO ACTION`,
    );
    await queryRunner.query(
      `ALTER TABLE "prompts" ADD CONSTRAINT "FK_5e0c3fe9886e556ac72000d49cd" FOREIGN KEY ("platform_team_id") REFERENCES "platform_teams"("id") ON DELETE NO ACTION ON UPDATE NO ACTION`,
    );
    await queryRunner.query(
      `ALTER TABLE "prompts" ADD CONSTRAINT "FK_1cfe0d970d6b4ed274211b6687d" FOREIGN KEY ("installation_id") REFERENCES "installations"("id") ON DELETE NO ACTION ON UPDATE NO ACTION`,
    );
    await queryRunner.query(
      `ALTER TABLE "prompts" ADD CONSTRAINT "FK_17ed6c0f33e2720e182e29b80d0" FOREIGN KEY ("organization_id") REFERENCES "organizations"("id") ON DELETE NO ACTION ON UPDATE NO ACTION`,
    );
    await queryRunner.query(
      `ALTER TABLE "team_triage_rule_mappings" ADD CONSTRAINT "FK_dbdcdcac6b2f885927eaaadac57" FOREIGN KEY ("organization_id") REFERENCES "organizations"("id") ON DELETE NO ACTION ON UPDATE NO ACTION`,
    );
    await queryRunner.query(
      `ALTER TABLE "team_triage_rule_mappings" ADD CONSTRAINT "FK_675f82154edd9b674565ddf93fa" FOREIGN KEY ("installation_id") REFERENCES "installations"("id") ON DELETE CASCADE ON UPDATE NO ACTION`,
    );
    await queryRunner.query(
      `ALTER TABLE "team_triage_rule_mappings" ADD CONSTRAINT "FK_24e554d6e96e640b741ba7383a6" FOREIGN KEY ("platform_team_id") REFERENCES "platform_teams"("id") ON DELETE CASCADE ON UPDATE NO ACTION`,
    );
    await queryRunner.query(
      `ALTER TABLE "sub_team_to_sub_groups_mappings" ADD CONSTRAINT "FK_c61da7043aeb3743639200e31b0" FOREIGN KEY ("organization_id") REFERENCES "organizations"("id") ON DELETE NO ACTION ON UPDATE NO ACTION`,
    );
    await queryRunner.query(
      `ALTER TABLE "sub_team_to_sub_groups_mappings" ADD CONSTRAINT "FK_89494bfd0f0b94db047846907ec" FOREIGN KEY ("installation_id") REFERENCES "installations"("id") ON DELETE CASCADE ON UPDATE NO ACTION`,
    );
    await queryRunner.query(
      `ALTER TABLE "sub_team_to_sub_groups_mappings" ADD CONSTRAINT "FK_4ef99086bdfd1f77bd9415fed6c" FOREIGN KEY ("platform_team_id") REFERENCES "platform_teams"("id") ON DELETE CASCADE ON UPDATE NO ACTION`,
    );
    await queryRunner.query(
      `ALTER TABLE "sub_team_to_sub_groups_mappings" ADD CONSTRAINT "FK_1ff38192049283472bcd49a7fd8" FOREIGN KEY ("sub_group_id") REFERENCES "slack_subgroups"("id") ON DELETE CASCADE ON UPDATE NO ACTION`,
    );
    await queryRunner.query(
      `ALTER TABLE "platform_teams_to_channel_mappings" ADD CONSTRAINT "FK_ddad5fa6d40658771383caecc10" FOREIGN KEY ("installation_id") REFERENCES "installations"("id") ON DELETE NO ACTION ON UPDATE NO ACTION`,
    );
    await queryRunner.query(
      `ALTER TABLE "platform_teams_to_channel_mappings" ADD CONSTRAINT "FK_02cce2fe460c9ca132133e3f519" FOREIGN KEY ("organization_id") REFERENCES "organizations"("id") ON DELETE NO ACTION ON UPDATE NO ACTION`,
    );
    await queryRunner.query(
      `ALTER TABLE "comment_thread_mappings" ADD CONSTRAINT "FK_c03f8f28e58218bbe4a86ac8fc8" FOREIGN KEY ("installation_id") REFERENCES "installations"("id") ON DELETE NO ACTION ON UPDATE NO ACTION`,
    );
    await queryRunner.query(
      `ALTER TABLE "comment_thread_mappings" ADD CONSTRAINT "FK_51c78c6562e97e94e0a8d279b4a" FOREIGN KEY ("organization_id") REFERENCES "organizations"("id") ON DELETE NO ACTION ON UPDATE NO ACTION`,
    );
    await queryRunner.query(
      `ALTER TABLE "channel_triage_mappings" ADD CONSTRAINT "FK_62bd22600ff78d4d5f2ceb7ac91" FOREIGN KEY ("organization_id") REFERENCES "organizations"("id") ON DELETE NO ACTION ON UPDATE NO ACTION`,
    );
    await queryRunner.query(
      `ALTER TABLE "channel_triage_mappings" ADD CONSTRAINT "FK_4bd3b2c93c91af38396780afde9" FOREIGN KEY ("installation_id") REFERENCES "installations"("id") ON DELETE NO ACTION ON UPDATE NO ACTION`,
    );
    await queryRunner.query(
      `ALTER TABLE "slack_subgroups" ADD CONSTRAINT "fk_slack_subgroup_organization_id" FOREIGN KEY ("organization_id") REFERENCES "organizations"("id") ON DELETE NO ACTION ON UPDATE NO ACTION`,
    );
    await queryRunner.query(
      `ALTER TABLE "slack_subgroups" ADD CONSTRAINT "fk_slack_subgroup_installation_id" FOREIGN KEY ("installation_id") REFERENCES "installations"("id") ON DELETE NO ACTION ON UPDATE NO ACTION`,
    );
    await queryRunner.query(
      `ALTER TABLE "users" ADD CONSTRAINT "fk_slack_user_organization_id" FOREIGN KEY ("organization_id", "organization_id") REFERENCES "organizations"("id","id") ON DELETE NO ACTION ON UPDATE NO ACTION`,
    );
    await queryRunner.query(
      `ALTER TABLE "users" ADD CONSTRAINT "fk_slack_user_installation_id" FOREIGN KEY ("installation_id", "installation_id") REFERENCES "installations"("id","id") ON DELETE NO ACTION ON UPDATE NO ACTION`,
    );
  }
}
