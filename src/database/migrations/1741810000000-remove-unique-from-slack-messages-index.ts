import { MigrationInterface, QueryRunner } from 'typeorm';

export class RemoveUniqueFromSlackMessagesIndex1741810000000
  implements MigrationInterface
{
  name = 'RemoveUniqueFromSlackMessagesIndex1741810000000';

  public async up(queryRunner: QueryRunner): Promise<void> {
    // Drop existing unique index
    await queryRunner.query(
      `DROP INDEX "public"."idx_slack_message_platform_ticket_id"`,
    );

    // Create non-unique index with the same name and columns
    await queryRunner.query(
      `CREATE INDEX "idx_slack_message_platform_ticket_id" ON "slack_messages" ("platform_ticket_id", "organization_id")`,
    );
  }

  public async down(queryRunner: QueryRunner): Promise<void> {
    // Drop non-unique index
    await queryRunner.query(
      `DROP INDEX "public"."idx_slack_message_platform_ticket_id"`,
    );

    // Recreate the unique index
    await queryRunner.query(
      `CREATE UNIQUE INDEX "idx_slack_message_platform_ticket_id" ON "slack_messages" ("platform_ticket_id", "organization_id")`,
    );
  }
}
