import { MigrationInterface, QueryRunner } from 'typeorm';

export class CreateSlackEmojiActions1742249182699
  implements MigrationInterface
{
  name = 'CreateSlackEmojiActions1742249182699';

  public async up(queryRunner: QueryRunner): Promise<void> {
    await queryRunner.query(
      `CREATE TABLE "emoji_actions" ("id" BIGSERIAL NOT NULL, "action" character varying NOT NULL, "emoji" character varying NOT NULL, "created_at" TIMESTAMP WITH TIME ZONE NOT NULL DEFAULT now(), "updated_at" TIMESTAMP WITH TIME ZONE NOT NULL DEFAULT now(), "platform_team_id" bigint, "organization_id" bigint, "installation_id" bigint, CONSTRAINT "PK_f5d4a419db92af76b8527991adc" PRIMARY KEY ("id"))`,
    );
    await queryRunner.query(
      `CREATE UNIQUE INDEX "idx_uniq_emoji_action_mapping" ON "emoji_actions" ("emoji", "platform_team_id", "installation_id") `,
    );
    await queryRunner.query(
      `ALTER TABLE "emoji_actions" ADD CONSTRAINT "FK_3e053b7cd562959a9c74beeba0a" FOREIGN KEY ("platform_team_id") REFERENCES "platform_teams"("id") ON DELETE CASCADE ON UPDATE CASCADE`,
    );
    await queryRunner.query(
      `ALTER TABLE "emoji_actions" ADD CONSTRAINT "FK_0c67073a1f65ceedd446f56675c" FOREIGN KEY ("organization_id") REFERENCES "organizations"("id") ON DELETE CASCADE ON UPDATE CASCADE`,
    );
    await queryRunner.query(
      `ALTER TABLE "emoji_actions" ADD CONSTRAINT "FK_aa2c30d2a936dec07fe775d0845" FOREIGN KEY ("installation_id") REFERENCES "installations"("id") ON DELETE CASCADE ON UPDATE CASCADE`,
    );
  }

  public async down(queryRunner: QueryRunner): Promise<void> {
    await queryRunner.query(
      `ALTER TABLE "emoji_actions" DROP CONSTRAINT "FK_aa2c30d2a936dec07fe775d0845"`,
    );
    await queryRunner.query(
      `ALTER TABLE "emoji_actions" DROP CONSTRAINT "FK_0c67073a1f65ceedd446f56675c"`,
    );
    await queryRunner.query(
      `ALTER TABLE "emoji_actions" DROP CONSTRAINT "FK_3e053b7cd562959a9c74beeba0a"`,
    );
    await queryRunner.query(
      `DROP INDEX "public"."idx_uniq_emoji_action_mapping"`,
    );
    await queryRunner.query(`DROP TABLE "emoji_actions"`);
  }
}
