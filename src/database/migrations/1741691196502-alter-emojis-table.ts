import { MigrationInterface, QueryRunner } from 'typeorm';

export class AlterEmojisTable1741691196502 implements MigrationInterface {
  name = 'AlterEmojisTable1741691196502';

  public async up(queryRunner: QueryRunner): Promise<void> {
    await queryRunner.query(
      `ALTER TABLE "slack_emojis" ALTER COLUMN "unicode" DROP NOT NULL`,
    );
  }

  public async down(queryRunner: QueryRunner): Promise<void> {
    await queryRunner.query(
      `ALTER TABLE "slack_emojis" ALTER COLUMN "unicode" SET NOT NULL`,
    );
  }
}
