import { MigrationInterface, QueryRunner } from 'typeorm';

export class InitialMigrations1740228806871 implements MigrationInterface {
  name = 'InitialMigrations1740228806871';

  public async up(queryRunner: QueryRunner): Promise<void> {
    await queryRunner.query(
      `CREATE TABLE "platform_teams" ("id" BIGSERIAL NOT NULL, "uid" character varying, "installed_by" character varying, "created_at" TIMESTAMP WITH TIME ZONE NOT NULL DEFAULT now(), "updated_at" TIMESTAMP WITH TIME ZONE NOT NULL DEFAULT now(), "deleted_at" TIMESTAMP WITH TIME ZONE, "installation_id" bigint NOT NULL, "organization_id" bigint NOT NULL, CONSTRAINT "PK_da66ebc8d50090c4a9afcb4a541" PRIMARY KEY ("id"))`,
    );
    await queryRunner.query(
      `CREATE INDEX "idx_platform_team_uid" ON "platform_teams" ("uid") `,
    );
    await queryRunner.query(
      `CREATE UNIQUE INDEX "idx_uniq_team_id" ON "platform_teams" ("uid", "installation_id", "organization_id") `,
    );
    await queryRunner.query(
      `CREATE TABLE "users" ("id" BIGSERIAL NOT NULL, "user_dump" jsonb NOT NULL, "slack_id" character varying NOT NULL, "slack_deleted" boolean NOT NULL DEFAULT false, "name" character varying NOT NULL, "real_name" character varying, "display_name" character varying, "tz" character varying, "tz_label" character varying, "is_admin" boolean NOT NULL DEFAULT false, "is_owner" boolean NOT NULL DEFAULT false, "is_restricted" boolean NOT NULL DEFAULT false, "is_ultra_restricted" boolean NOT NULL DEFAULT false, "is_bot" boolean NOT NULL DEFAULT false, "user_title" character varying, "slack_profile_real_name" character varying, "slack_profile_display_name" character varying, "slack_profile_phone" character varying, "slack_status_text" character varying, "slack_status_emoji" character varying, "slack_profile_email" character varying, "images" jsonb, "metadata" jsonb, "installation_id" bigint, "organization_id" bigint, CONSTRAINT "PK_a3ffb1c0c8416b9fc6f907b7433" PRIMARY KEY ("id"))`,
    );
    await queryRunner.query(
      `CREATE INDEX "IDX_5b6c6b1d0a684db1fb5954304a" ON "users" ("slack_id") `,
    );
    await queryRunner.query(
      `CREATE UNIQUE INDEX "idx_unique_slack_user_slack_id" ON "users" ("slack_id", "installation_id") `,
    );
    await queryRunner.query(
      `CREATE TABLE "organizations" ("id" BIGSERIAL NOT NULL, "external_pk" character varying, "name" character varying, "uid" character varying NOT NULL, "api_key" character varying, "installing_user_id" character varying, "metadata" jsonb, CONSTRAINT "UQ_a98b290f129e50b4f0334a5b2ef" UNIQUE ("uid"), CONSTRAINT "PK_6b031fcd0863e3f6b44230163f9" PRIMARY KEY ("id"))`,
    );
    await queryRunner.query(
      `CREATE UNIQUE INDEX "uniq_organization_uid_idx" ON "organizations" ("uid") `,
    );
    await queryRunner.query(
      `CREATE TABLE "channel_triage_mappings" ("id" BIGSERIAL NOT NULL, "created_at" TIMESTAMP WITH TIME ZONE NOT NULL DEFAULT now(), "updated_at" TIMESTAMP WITH TIME ZONE NOT NULL DEFAULT now(), "deleted_at" TIMESTAMP WITH TIME ZONE, "active_channel_id" bigint, "triage_channel_id" bigint, "installation_id" bigint, "organization_id" bigint, CONSTRAINT "PK_4f9c584914cdb2d8b40f544c24b" PRIMARY KEY ("id"))`,
    );
    await queryRunner.query(
      `CREATE INDEX "idx_triage_channel_mappings_active_channel_id" ON "channel_triage_mappings" ("active_channel_id") `,
    );
    await queryRunner.query(
      `CREATE INDEX "idx_triage_channel_mappings_triage_channel_id" ON "channel_triage_mappings" ("triage_channel_id") `,
    );
    await queryRunner.query(
      `CREATE INDEX "idx_triage_channel_mappings_installation_id" ON "channel_triage_mappings" ("installation_id") `,
    );
    await queryRunner.query(
      `CREATE INDEX "idx_triage_channel_mappings_organization_id" ON "channel_triage_mappings" ("organization_id") `,
    );
    await queryRunner.query(
      `CREATE UNIQUE INDEX "idx_uniq_channel_triage_mapping" ON "channel_triage_mappings" ("active_channel_id", "triage_channel_id", "installation_id") `,
    );
    await queryRunner.query(
      `CREATE TABLE "comment_thread_mappings" ("id" BIGSERIAL NOT NULL, "platform_comment_thread_id" character varying NOT NULL, "platform_comment_ticket_id" character varying NOT NULL, "slack_thread_id" character varying NOT NULL, "slack_channel_id" character varying NOT NULL, "created_at" TIMESTAMP WITH TIME ZONE NOT NULL DEFAULT now(), "updated_at" TIMESTAMP WITH TIME ZONE NOT NULL DEFAULT now(), "installation_id" bigint, "organization_id" bigint, CONSTRAINT "PK_1a4849b162710afb45325b3ad22" PRIMARY KEY ("id"))`,
    );
    await queryRunner.query(
      `CREATE INDEX "idx_ctm_platform_comment_thread_id" ON "comment_thread_mappings" ("platform_comment_thread_id") `,
    );
    await queryRunner.query(
      `CREATE INDEX "idx_ctm_platform_comment_ticket_id" ON "comment_thread_mappings" ("platform_comment_ticket_id") `,
    );
    await queryRunner.query(
      `CREATE INDEX "idx_ctm_slack_thread_id" ON "comment_thread_mappings" ("slack_thread_id") `,
    );
    await queryRunner.query(
      `CREATE INDEX "idx_ctm_slack_channel_id" ON "comment_thread_mappings" ("slack_channel_id") `,
    );
    await queryRunner.query(
      `CREATE INDEX "idx_ctm_installation_id" ON "comment_thread_mappings" ("installation_id") `,
    );
    await queryRunner.query(
      `CREATE INDEX "idx_ctm_organization_id" ON "comment_thread_mappings" ("organization_id") `,
    );
    await queryRunner.query(
      `CREATE INDEX "idx_comment_thread_mappings" ON "comment_thread_mappings" ("platform_comment_thread_id", "slack_thread_id", "organization_id") `,
    );
    await queryRunner.query(
      `CREATE TYPE "public"."platform_teams_to_channel_mappings_relationship_type_enum" AS ENUM('primary', 'secondary')`,
    );
    await queryRunner.query(
      `CREATE TABLE "platform_teams_to_channel_mappings" ("id" BIGSERIAL NOT NULL, "relationship_type" "public"."platform_teams_to_channel_mappings_relationship_type_enum" NOT NULL DEFAULT 'secondary', "created_at" TIMESTAMP WITH TIME ZONE NOT NULL DEFAULT now(), "updated_at" TIMESTAMP WITH TIME ZONE NOT NULL DEFAULT now(), "platform_team_id" bigint, "channel_id" bigint, "installation_id" bigint, "organization_id" bigint, CONSTRAINT "PK_3bfd69ad4987b257ecb6310dd56" PRIMARY KEY ("id"))`,
    );
    await queryRunner.query(
      `CREATE INDEX "idx_ptcm_platform_team_ids" ON "platform_teams_to_channel_mappings" ("platform_team_id") `,
    );
    await queryRunner.query(
      `CREATE INDEX "idx_ptcm_channel_id" ON "platform_teams_to_channel_mappings" ("channel_id") `,
    );
    await queryRunner.query(
      `CREATE INDEX "idx_ptcm_installation_id" ON "platform_teams_to_channel_mappings" ("installation_id") `,
    );
    await queryRunner.query(
      `CREATE INDEX "idx_ptcm_organization_id" ON "platform_teams_to_channel_mappings" ("organization_id") `,
    );
    await queryRunner.query(
      `CREATE UNIQUE INDEX "idx_uniq_primary_team_to_channel_mapping" ON "platform_teams_to_channel_mappings" ("platform_team_id", "channel_id", "relationship_type") WHERE relationship_type = 'primary'`,
    );
    await queryRunner.query(
      `CREATE INDEX "idx_platform_slack_channel_organization" ON "platform_teams_to_channel_mappings" ("platform_team_id", "channel_id", "organization_id") `,
    );
    await queryRunner.query(
      `CREATE TYPE "public"."channels_channel_type_enum" AS ENUM('customer_channel', 'internal_helpdesk', 'triage_channel', 'not_setup', 'not_configured')`,
    );
    await queryRunner.query(
      `CREATE TABLE "channels" ("id" BIGSERIAL NOT NULL, "name" character varying NOT NULL, "channel_dump" jsonb NOT NULL, "channel_id" character varying NOT NULL, "slack_created_at" character varying NOT NULL, "is_bot_active" boolean NOT NULL DEFAULT false, "is_bot_joined" boolean NOT NULL DEFAULT false, "channel_type" "public"."channels_channel_type_enum" NOT NULL DEFAULT 'not_setup', "is_archived" boolean NOT NULL, "is_private" boolean NOT NULL, "is_shared" boolean NOT NULL, "guest_are_customers" boolean NOT NULL DEFAULT false, "created_at" TIMESTAMP WITH TIME ZONE NOT NULL DEFAULT now(), "updated_at" TIMESTAMP WITH TIME ZONE NOT NULL DEFAULT now(), "slack_deleted_at" character varying, "last_bot_left_at" character varying, "deleted_at" TIMESTAMP WITH TIME ZONE, "installation_id" bigint NOT NULL, "organization_id" bigint, CONSTRAINT "PK_bc603823f3f741359c2339389f9" PRIMARY KEY ("id"))`,
    );
    await queryRunner.query(
      `CREATE INDEX "idx_channel_type" ON "channels" ("channel_type") `,
    );
    await queryRunner.query(
      `CREATE UNIQUE INDEX "idx_slack_channel_channel_id" ON "channels" ("channel_id", "installation_id") `,
    );
    await queryRunner.query(
      `CREATE TYPE "public"."installations_status_enum" AS ENUM('syncing', 'synced', 'error')`,
    );
    await queryRunner.query(
      `CREATE TABLE "installations" ("id" BIGSERIAL NOT NULL, "name" character varying NOT NULL, "installation_dump" jsonb NOT NULL, "team_id" character varying NOT NULL, "domains" character varying, "team_name" character varying NOT NULL, "enterprise_id" character varying, "team_info" jsonb NOT NULL, "status" "public"."installations_status_enum" NOT NULL DEFAULT 'syncing', "installing_user_id" character varying, "installing_user_slack_id" character varying NOT NULL, "installing_user_name" character varying NOT NULL, "bot_token" character varying NOT NULL, "bot_slack_id" character varying NOT NULL, "bot_slack_user_id" character varying NOT NULL, "slack_app_auth_token" character varying, "disconnected" boolean NOT NULL DEFAULT false, "disconnected_on" TIMESTAMP, "created_at" TIMESTAMP NOT NULL DEFAULT now(), "updated_at" TIMESTAMP NOT NULL DEFAULT now(), "deleted_at" TIMESTAMP, "organization_id" bigint, CONSTRAINT "PK_21c7fb94d81eca7a66e64bd2b7f" PRIMARY KEY ("id"))`,
    );
    await queryRunner.query(
      `CREATE UNIQUE INDEX "uniq_slack_installation_team_id" ON "installations" ("team_id") `,
    );
    await queryRunner.query(
      `CREATE INDEX "idx_slack_installation_team_name" ON "installations" ("team_name") `,
    );
    await queryRunner.query(
      `CREATE INDEX "idx_slack_installation_installing_user_id" ON "installations" ("installing_user_id") `,
    );
    await queryRunner.query(
      `CREATE INDEX "idx_slack_installation_installing_user_slack_id" ON "installations" ("installing_user_slack_id") `,
    );
    await queryRunner.query(
      `CREATE UNIQUE INDEX "idx_slack_installation_slack_app_auth_token" ON "installations" ("slack_app_auth_token") `,
    );
    await queryRunner.query(
      `CREATE INDEX "idx_slack_installation_organization_id" ON "installations" ("organization_id") `,
    );
    await queryRunner.query(
      `CREATE TABLE "bots" ("id" BIGSERIAL NOT NULL, "bot_dump" jsonb NOT NULL, "slack_id" character varying NOT NULL, "slack_deleted" boolean NOT NULL DEFAULT false, "name" character varying NOT NULL, "real_name" character varying, "display_name" character varying, "tz" character varying, "tz_label" character varying, "is_admin" boolean NOT NULL DEFAULT false, "is_owner" boolean NOT NULL DEFAULT false, "is_restricted" boolean NOT NULL DEFAULT false, "is_ultra_restricted" boolean NOT NULL DEFAULT false, "is_bot" boolean NOT NULL DEFAULT false, "bot_title" character varying, "bot_real_name" character varying, "bot_display_name" character varying, "bot_status_text" character varying, "bot_status_emoji" character varying, "images" jsonb, "installation_id" bigint, "organization_id" bigint, CONSTRAINT "PK_8b1b0180229dec2cbfdf5e776e4" PRIMARY KEY ("id"))`,
    );
    await queryRunner.query(
      `CREATE INDEX "IDX_87f683459f386079271f80cd54" ON "bots" ("slack_id") `,
    );
    await queryRunner.query(
      `CREATE UNIQUE INDEX "idx_unique_slack_bot_slack_id" ON "bots" ("slack_id", "installation_id") `,
    );
    await queryRunner.query(
      `CREATE TABLE "slack_customer_contacts" ("id" BIGSERIAL NOT NULL, "user_dump" jsonb NOT NULL, "slack_id" character varying NOT NULL, "slack_deleted" boolean NOT NULL DEFAULT false, "name" character varying NOT NULL, "real_name" character varying, "display_name" character varying, "tz" character varying, "tz_label" character varying, "is_admin" boolean NOT NULL DEFAULT false, "is_owner" boolean NOT NULL DEFAULT false, "is_restricted" boolean NOT NULL DEFAULT false, "is_ultra_restricted" boolean NOT NULL DEFAULT false, "is_bot" boolean NOT NULL DEFAULT false, "user_title" character varying, "slack_profile_real_name" character varying, "slack_profile_display_name" character varying, "slack_profile_phone" character varying, "slack_status_text" character varying, "slack_status_emoji" character varying, "slack_profile_email" character varying, "images" jsonb, "metadata" jsonb, "installation_id" bigint, "organization_id" bigint, CONSTRAINT "PK_2c080071c3cbad9403161e74cfd" PRIMARY KEY ("id"))`,
    );
    await queryRunner.query(
      `CREATE INDEX "IDX_75e5928e340ecbeb4b477fef57" ON "slack_customer_contacts" ("slack_id") `,
    );
    await queryRunner.query(
      `CREATE UNIQUE INDEX "idx_unique_customer_contact_slack_id" ON "slack_customer_contacts" ("slack_id", "installation_id") `,
    );
    await queryRunner.query(
      `CREATE TABLE "settings_schemas" ("id" BIGSERIAL NOT NULL, "setting_key" character varying NOT NULL, "setting_type" character varying NOT NULL, "default_value" character varying, "description" character varying, "created_at" TIMESTAMP WITH TIME ZONE NOT NULL DEFAULT now(), "updated_at" TIMESTAMP WITH TIME ZONE NOT NULL DEFAULT now(), CONSTRAINT "UQ_4df8f3ece73d6ad9846f0a857d6" UNIQUE ("setting_key"), CONSTRAINT "chk_setting_type_is_valid" CHECK (setting_type IN ('string', 'number', 'boolean', 'array', 'array_of_strings', 'array_of_numbers', 'jsonb')), CONSTRAINT "PK_9d46952633895458b2c0fdc728c" PRIMARY KEY ("id"))`,
    );
    await queryRunner.query(
      `CREATE TABLE "settings" ("id" BIGSERIAL NOT NULL, "settings" jsonb NOT NULL, "created_at" TIMESTAMP WITH TIME ZONE NOT NULL DEFAULT now(), "updated_at" TIMESTAMP WITH TIME ZONE NOT NULL DEFAULT now(), "platform_team_id" bigint NOT NULL, "installation_id" bigint NOT NULL, "organization_id" bigint NOT NULL, CONSTRAINT "PK_0669fe20e252eb692bf4d344975" PRIMARY KEY ("id"))`,
    );
    await queryRunner.query(
      `CREATE UNIQUE INDEX "idx_uniq_settings_for_team" ON "settings" ("platform_team_id", "installation_id", "organization_id") `,
    );
    await queryRunner.query(
      `CREATE TABLE "slack_messages" ("id" BIGSERIAL NOT NULL, "platform_ticket_id" character varying, "slack_message_ts" character varying, "slack_message_thread_ts" character varying, "slack_permalink" character varying, "is_independent" boolean NOT NULL DEFAULT false, "metadata" jsonb, "created_at" TIMESTAMP WITH TIME ZONE NOT NULL DEFAULT now(), "slack_event_created_at" TIMESTAMP WITH TIME ZONE, "updated_at" TIMESTAMP WITH TIME ZONE NOT NULL DEFAULT now(), "deleted_at" TIMESTAMP WITH TIME ZONE, "channel_id" bigint, "installation_id" bigint NOT NULL, "organization_id" bigint, CONSTRAINT "CHK_PLATFORM_OR_SLACK_TS_DEFINED" CHECK (platform_ticket_id IS NOT NULL OR slack_message_ts IS NOT NULL), CONSTRAINT "PK_9bebbd00f3dea662f2d642bcf4c" PRIMARY KEY ("id"))`,
    );
    await queryRunner.query(
      `CREATE INDEX "idx_slack_message_platform_ticket_id" ON "slack_messages" ("platform_ticket_id") `,
    );
    await queryRunner.query(
      `CREATE UNIQUE INDEX "idx_uniq_platform_ticket_id_in_org" ON "slack_messages" ("platform_ticket_id", "organization_id") `,
    );
    await queryRunner.query(
      `CREATE UNIQUE INDEX "idx_uniq_platform_ticket_id_in_channel" ON "slack_messages" ("platform_ticket_id", "channel_id") `,
    );
    await queryRunner.query(
      `CREATE TABLE "slack_triage_messages" ("id" BIGSERIAL NOT NULL, "slack_message_ts" character varying NOT NULL, "platform_thread_id" character varying, "metadata" jsonb, "created_at" TIMESTAMP WITH TIME ZONE DEFAULT now(), "updated_at" TIMESTAMP WITH TIME ZONE DEFAULT now(), "deleted_at" TIMESTAMP WITH TIME ZONE, "slack_request_message_id" bigint NOT NULL, "channel_id" bigint NOT NULL, "installation_id" bigint NOT NULL, "organization_id" bigint, CONSTRAINT "PK_e626f417bb2429daf3adf933eb8" PRIMARY KEY ("id"))`,
    );
    await queryRunner.query(
      `CREATE INDEX "idx_tmt_platform_thread_id" ON "slack_triage_messages" ("platform_thread_id") `,
    );
    await queryRunner.query(
      `ALTER TABLE "platform_teams" ADD CONSTRAINT "fk_slack_platform_team_installation_id" FOREIGN KEY ("installation_id") REFERENCES "installations"("id") ON DELETE CASCADE ON UPDATE CASCADE`,
    );
    await queryRunner.query(
      `ALTER TABLE "platform_teams" ADD CONSTRAINT "fk_slack_platform_team_organization_id" FOREIGN KEY ("organization_id") REFERENCES "organizations"("id") ON DELETE CASCADE ON UPDATE CASCADE`,
    );
    await queryRunner.query(
      `ALTER TABLE "users" ADD CONSTRAINT "fk_slack_user_installation_id" FOREIGN KEY ("installation_id") REFERENCES "installations"("id") ON DELETE NO ACTION ON UPDATE NO ACTION`,
    );
    await queryRunner.query(
      `ALTER TABLE "users" ADD CONSTRAINT "fk_slack_user_organization_id" FOREIGN KEY ("organization_id") REFERENCES "organizations"("id") ON DELETE NO ACTION ON UPDATE NO ACTION`,
    );
    await queryRunner.query(
      `ALTER TABLE "channel_triage_mappings" ADD CONSTRAINT "FK_f3fb0b0c22af929b00b106e8e16" FOREIGN KEY ("active_channel_id") REFERENCES "channels"("id") ON DELETE NO ACTION ON UPDATE NO ACTION`,
    );
    await queryRunner.query(
      `ALTER TABLE "channel_triage_mappings" ADD CONSTRAINT "FK_c3644e3763d078c6ee9625d1c83" FOREIGN KEY ("triage_channel_id") REFERENCES "channels"("id") ON DELETE NO ACTION ON UPDATE NO ACTION`,
    );
    await queryRunner.query(
      `ALTER TABLE "channel_triage_mappings" ADD CONSTRAINT "FK_4bd3b2c93c91af38396780afde9" FOREIGN KEY ("installation_id") REFERENCES "installations"("id") ON DELETE NO ACTION ON UPDATE NO ACTION`,
    );
    await queryRunner.query(
      `ALTER TABLE "channel_triage_mappings" ADD CONSTRAINT "FK_62bd22600ff78d4d5f2ceb7ac91" FOREIGN KEY ("organization_id") REFERENCES "organizations"("id") ON DELETE NO ACTION ON UPDATE NO ACTION`,
    );
    await queryRunner.query(
      `ALTER TABLE "comment_thread_mappings" ADD CONSTRAINT "FK_c03f8f28e58218bbe4a86ac8fc8" FOREIGN KEY ("installation_id") REFERENCES "installations"("id") ON DELETE NO ACTION ON UPDATE NO ACTION`,
    );
    await queryRunner.query(
      `ALTER TABLE "comment_thread_mappings" ADD CONSTRAINT "FK_51c78c6562e97e94e0a8d279b4a" FOREIGN KEY ("organization_id") REFERENCES "organizations"("id") ON DELETE NO ACTION ON UPDATE NO ACTION`,
    );
    await queryRunner.query(
      `ALTER TABLE "platform_teams_to_channel_mappings" ADD CONSTRAINT "FK_5933dea08ab5218fc3093a4d12a" FOREIGN KEY ("platform_team_id") REFERENCES "platform_teams"("id") ON DELETE NO ACTION ON UPDATE NO ACTION`,
    );
    await queryRunner.query(
      `ALTER TABLE "platform_teams_to_channel_mappings" ADD CONSTRAINT "FK_8112b7c8ab45ee278398b8a9af0" FOREIGN KEY ("channel_id") REFERENCES "channels"("id") ON DELETE NO ACTION ON UPDATE NO ACTION`,
    );
    await queryRunner.query(
      `ALTER TABLE "platform_teams_to_channel_mappings" ADD CONSTRAINT "FK_ddad5fa6d40658771383caecc10" FOREIGN KEY ("installation_id") REFERENCES "installations"("id") ON DELETE NO ACTION ON UPDATE NO ACTION`,
    );
    await queryRunner.query(
      `ALTER TABLE "platform_teams_to_channel_mappings" ADD CONSTRAINT "FK_02cce2fe460c9ca132133e3f519" FOREIGN KEY ("organization_id") REFERENCES "organizations"("id") ON DELETE NO ACTION ON UPDATE NO ACTION`,
    );
    await queryRunner.query(
      `ALTER TABLE "channels" ADD CONSTRAINT "fk_slack_channel_installation_id" FOREIGN KEY ("installation_id") REFERENCES "installations"("id") ON DELETE CASCADE ON UPDATE CASCADE`,
    );
    await queryRunner.query(
      `ALTER TABLE "channels" ADD CONSTRAINT "fk_slack_channel_organization_id" FOREIGN KEY ("organization_id") REFERENCES "organizations"("id") ON DELETE NO ACTION ON UPDATE NO ACTION`,
    );
    await queryRunner.query(
      `ALTER TABLE "installations" ADD CONSTRAINT "fk_slack_installation_organization_id" FOREIGN KEY ("organization_id") REFERENCES "organizations"("id") ON DELETE NO ACTION ON UPDATE NO ACTION`,
    );
    await queryRunner.query(
      `ALTER TABLE "bots" ADD CONSTRAINT "fk_slack_bot_installation_id" FOREIGN KEY ("installation_id") REFERENCES "installations"("id") ON DELETE NO ACTION ON UPDATE NO ACTION`,
    );
    await queryRunner.query(
      `ALTER TABLE "bots" ADD CONSTRAINT "fk_slack_bot_organization_id" FOREIGN KEY ("organization_id") REFERENCES "organizations"("id") ON DELETE NO ACTION ON UPDATE NO ACTION`,
    );
    await queryRunner.query(
      `ALTER TABLE "slack_customer_contacts" ADD CONSTRAINT "fk_slack_user_installation_id" FOREIGN KEY ("installation_id") REFERENCES "installations"("id") ON DELETE NO ACTION ON UPDATE NO ACTION`,
    );
    await queryRunner.query(
      `ALTER TABLE "slack_customer_contacts" ADD CONSTRAINT "fk_slack_user_organization_id" FOREIGN KEY ("organization_id") REFERENCES "organizations"("id") ON DELETE NO ACTION ON UPDATE NO ACTION`,
    );
    await queryRunner.query(
      `ALTER TABLE "settings" ADD CONSTRAINT "FK_6c36c1705dcf1bef7edb9ab33aa" FOREIGN KEY ("platform_team_id") REFERENCES "platform_teams"("id") ON DELETE NO ACTION ON UPDATE NO ACTION`,
    );
    await queryRunner.query(
      `ALTER TABLE "settings" ADD CONSTRAINT "FK_7919a3396d20a588a7036035564" FOREIGN KEY ("installation_id") REFERENCES "installations"("id") ON DELETE NO ACTION ON UPDATE NO ACTION`,
    );
    await queryRunner.query(
      `ALTER TABLE "settings" ADD CONSTRAINT "FK_d5bc871126b26b4fb1b9c743825" FOREIGN KEY ("organization_id") REFERENCES "organizations"("id") ON DELETE NO ACTION ON UPDATE NO ACTION`,
    );
    await queryRunner.query(
      `ALTER TABLE "slack_messages" ADD CONSTRAINT "FK_5e1fe4fd9640abb971b42986b0c" FOREIGN KEY ("channel_id") REFERENCES "channels"("id") ON DELETE CASCADE ON UPDATE CASCADE`,
    );
    await queryRunner.query(
      `ALTER TABLE "slack_messages" ADD CONSTRAINT "fk_request_installation_id" FOREIGN KEY ("installation_id") REFERENCES "installations"("id") ON DELETE CASCADE ON UPDATE CASCADE`,
    );
    await queryRunner.query(
      `ALTER TABLE "slack_messages" ADD CONSTRAINT "fk_request_organization_id" FOREIGN KEY ("organization_id") REFERENCES "organizations"("id") ON DELETE NO ACTION ON UPDATE NO ACTION`,
    );
    await queryRunner.query(
      `ALTER TABLE "slack_triage_messages" ADD CONSTRAINT "FK_0b3165af928f2d60f4f2d95927c" FOREIGN KEY ("slack_request_message_id") REFERENCES "slack_messages"("id") ON DELETE CASCADE ON UPDATE CASCADE`,
    );
    await queryRunner.query(
      `ALTER TABLE "slack_triage_messages" ADD CONSTRAINT "FK_c8a8e0be3773fb0204fae17ce71" FOREIGN KEY ("channel_id") REFERENCES "channels"("id") ON DELETE CASCADE ON UPDATE CASCADE`,
    );
    await queryRunner.query(
      `ALTER TABLE "slack_triage_messages" ADD CONSTRAINT "fk_request_installation_id" FOREIGN KEY ("installation_id") REFERENCES "installations"("id") ON DELETE CASCADE ON UPDATE CASCADE`,
    );
    await queryRunner.query(
      `ALTER TABLE "slack_triage_messages" ADD CONSTRAINT "fk_request_organization_id" FOREIGN KEY ("organization_id") REFERENCES "organizations"("id") ON DELETE NO ACTION ON UPDATE NO ACTION`,
    );
  }

  public async down(queryRunner: QueryRunner): Promise<void> {
    await queryRunner.query(
      `ALTER TABLE "slack_triage_messages" DROP CONSTRAINT "fk_request_organization_id"`,
    );
    await queryRunner.query(
      `ALTER TABLE "slack_triage_messages" DROP CONSTRAINT "fk_request_installation_id"`,
    );
    await queryRunner.query(
      `ALTER TABLE "slack_triage_messages" DROP CONSTRAINT "FK_c8a8e0be3773fb0204fae17ce71"`,
    );
    await queryRunner.query(
      `ALTER TABLE "slack_triage_messages" DROP CONSTRAINT "FK_0b3165af928f2d60f4f2d95927c"`,
    );
    await queryRunner.query(
      `ALTER TABLE "slack_messages" DROP CONSTRAINT "fk_request_organization_id"`,
    );
    await queryRunner.query(
      `ALTER TABLE "slack_messages" DROP CONSTRAINT "fk_request_installation_id"`,
    );
    await queryRunner.query(
      `ALTER TABLE "slack_messages" DROP CONSTRAINT "FK_5e1fe4fd9640abb971b42986b0c"`,
    );
    await queryRunner.query(
      `ALTER TABLE "settings" DROP CONSTRAINT "FK_d5bc871126b26b4fb1b9c743825"`,
    );
    await queryRunner.query(
      `ALTER TABLE "settings" DROP CONSTRAINT "FK_7919a3396d20a588a7036035564"`,
    );
    await queryRunner.query(
      `ALTER TABLE "settings" DROP CONSTRAINT "FK_6c36c1705dcf1bef7edb9ab33aa"`,
    );
    await queryRunner.query(
      `ALTER TABLE "slack_customer_contacts" DROP CONSTRAINT "fk_slack_user_organization_id"`,
    );
    await queryRunner.query(
      `ALTER TABLE "slack_customer_contacts" DROP CONSTRAINT "fk_slack_user_installation_id"`,
    );
    await queryRunner.query(
      `ALTER TABLE "bots" DROP CONSTRAINT "fk_slack_bot_organization_id"`,
    );
    await queryRunner.query(
      `ALTER TABLE "bots" DROP CONSTRAINT "fk_slack_bot_installation_id"`,
    );
    await queryRunner.query(
      `ALTER TABLE "installations" DROP CONSTRAINT "fk_slack_installation_organization_id"`,
    );
    await queryRunner.query(
      `ALTER TABLE "channels" DROP CONSTRAINT "fk_slack_channel_organization_id"`,
    );
    await queryRunner.query(
      `ALTER TABLE "channels" DROP CONSTRAINT "fk_slack_channel_installation_id"`,
    );
    await queryRunner.query(
      `ALTER TABLE "platform_teams_to_channel_mappings" DROP CONSTRAINT "FK_02cce2fe460c9ca132133e3f519"`,
    );
    await queryRunner.query(
      `ALTER TABLE "platform_teams_to_channel_mappings" DROP CONSTRAINT "FK_ddad5fa6d40658771383caecc10"`,
    );
    await queryRunner.query(
      `ALTER TABLE "platform_teams_to_channel_mappings" DROP CONSTRAINT "FK_8112b7c8ab45ee278398b8a9af0"`,
    );
    await queryRunner.query(
      `ALTER TABLE "platform_teams_to_channel_mappings" DROP CONSTRAINT "FK_5933dea08ab5218fc3093a4d12a"`,
    );
    await queryRunner.query(
      `ALTER TABLE "comment_thread_mappings" DROP CONSTRAINT "FK_51c78c6562e97e94e0a8d279b4a"`,
    );
    await queryRunner.query(
      `ALTER TABLE "comment_thread_mappings" DROP CONSTRAINT "FK_c03f8f28e58218bbe4a86ac8fc8"`,
    );
    await queryRunner.query(
      `ALTER TABLE "channel_triage_mappings" DROP CONSTRAINT "FK_62bd22600ff78d4d5f2ceb7ac91"`,
    );
    await queryRunner.query(
      `ALTER TABLE "channel_triage_mappings" DROP CONSTRAINT "FK_4bd3b2c93c91af38396780afde9"`,
    );
    await queryRunner.query(
      `ALTER TABLE "channel_triage_mappings" DROP CONSTRAINT "FK_c3644e3763d078c6ee9625d1c83"`,
    );
    await queryRunner.query(
      `ALTER TABLE "channel_triage_mappings" DROP CONSTRAINT "FK_f3fb0b0c22af929b00b106e8e16"`,
    );
    await queryRunner.query(
      `ALTER TABLE "users" DROP CONSTRAINT "fk_slack_user_organization_id"`,
    );
    await queryRunner.query(
      `ALTER TABLE "users" DROP CONSTRAINT "fk_slack_user_installation_id"`,
    );
    await queryRunner.query(
      `ALTER TABLE "platform_teams" DROP CONSTRAINT "fk_slack_platform_team_organization_id"`,
    );
    await queryRunner.query(
      `ALTER TABLE "platform_teams" DROP CONSTRAINT "fk_slack_platform_team_installation_id"`,
    );
    await queryRunner.query(`DROP INDEX "public"."idx_tmt_platform_thread_id"`);
    await queryRunner.query(`DROP TABLE "slack_triage_messages"`);
    await queryRunner.query(
      `DROP INDEX "public"."idx_uniq_platform_ticket_id_in_channel"`,
    );
    await queryRunner.query(
      `DROP INDEX "public"."idx_uniq_platform_ticket_id_in_org"`,
    );
    await queryRunner.query(
      `DROP INDEX "public"."idx_slack_message_platform_ticket_id"`,
    );
    await queryRunner.query(`DROP TABLE "slack_messages"`);
    await queryRunner.query(`DROP INDEX "public"."idx_uniq_settings_for_team"`);
    await queryRunner.query(`DROP TABLE "settings"`);
    await queryRunner.query(`DROP TABLE "settings_schemas"`);
    await queryRunner.query(
      `DROP INDEX "public"."idx_unique_customer_contact_slack_id"`,
    );
    await queryRunner.query(
      `DROP INDEX "public"."IDX_75e5928e340ecbeb4b477fef57"`,
    );
    await queryRunner.query(`DROP TABLE "slack_customer_contacts"`);
    await queryRunner.query(
      `DROP INDEX "public"."idx_unique_slack_bot_slack_id"`,
    );
    await queryRunner.query(
      `DROP INDEX "public"."IDX_87f683459f386079271f80cd54"`,
    );
    await queryRunner.query(`DROP TABLE "bots"`);
    await queryRunner.query(
      `DROP INDEX "public"."idx_slack_installation_organization_id"`,
    );
    await queryRunner.query(
      `DROP INDEX "public"."idx_slack_installation_slack_app_auth_token"`,
    );
    await queryRunner.query(
      `DROP INDEX "public"."idx_slack_installation_installing_user_slack_id"`,
    );
    await queryRunner.query(
      `DROP INDEX "public"."idx_slack_installation_installing_user_id"`,
    );
    await queryRunner.query(
      `DROP INDEX "public"."idx_slack_installation_team_name"`,
    );
    await queryRunner.query(
      `DROP INDEX "public"."uniq_slack_installation_team_id"`,
    );
    await queryRunner.query(`DROP TABLE "installations"`);
    await queryRunner.query(`DROP TYPE "public"."installations_status_enum"`);
    await queryRunner.query(
      `DROP INDEX "public"."idx_slack_channel_channel_id"`,
    );
    await queryRunner.query(`DROP INDEX "public"."idx_channel_type"`);
    await queryRunner.query(`DROP TABLE "channels"`);
    await queryRunner.query(`DROP TYPE "public"."channels_channel_type_enum"`);
    await queryRunner.query(
      `DROP INDEX "public"."idx_platform_slack_channel_organization"`,
    );
    await queryRunner.query(
      `DROP INDEX "public"."idx_uniq_primary_team_to_channel_mapping"`,
    );
    await queryRunner.query(`DROP INDEX "public"."idx_ptcm_organization_id"`);
    await queryRunner.query(`DROP INDEX "public"."idx_ptcm_installation_id"`);
    await queryRunner.query(`DROP INDEX "public"."idx_ptcm_channel_id"`);
    await queryRunner.query(`DROP INDEX "public"."idx_ptcm_platform_team_ids"`);
    await queryRunner.query(`DROP TABLE "platform_teams_to_channel_mappings"`);
    await queryRunner.query(
      `DROP TYPE "public"."platform_teams_to_channel_mappings_relationship_type_enum"`,
    );
    await queryRunner.query(
      `DROP INDEX "public"."idx_comment_thread_mappings"`,
    );
    await queryRunner.query(`DROP INDEX "public"."idx_ctm_organization_id"`);
    await queryRunner.query(`DROP INDEX "public"."idx_ctm_installation_id"`);
    await queryRunner.query(`DROP INDEX "public"."idx_ctm_slack_channel_id"`);
    await queryRunner.query(`DROP INDEX "public"."idx_ctm_slack_thread_id"`);
    await queryRunner.query(
      `DROP INDEX "public"."idx_ctm_platform_comment_ticket_id"`,
    );
    await queryRunner.query(
      `DROP INDEX "public"."idx_ctm_platform_comment_thread_id"`,
    );
    await queryRunner.query(`DROP TABLE "comment_thread_mappings"`);
    await queryRunner.query(
      `DROP INDEX "public"."idx_uniq_channel_triage_mapping"`,
    );
    await queryRunner.query(
      `DROP INDEX "public"."idx_triage_channel_mappings_organization_id"`,
    );
    await queryRunner.query(
      `DROP INDEX "public"."idx_triage_channel_mappings_installation_id"`,
    );
    await queryRunner.query(
      `DROP INDEX "public"."idx_triage_channel_mappings_triage_channel_id"`,
    );
    await queryRunner.query(
      `DROP INDEX "public"."idx_triage_channel_mappings_active_channel_id"`,
    );
    await queryRunner.query(`DROP TABLE "channel_triage_mappings"`);
    await queryRunner.query(`DROP INDEX "public"."uniq_organization_uid_idx"`);
    await queryRunner.query(`DROP TABLE "organizations"`);
    await queryRunner.query(
      `DROP INDEX "public"."idx_unique_slack_user_slack_id"`,
    );
    await queryRunner.query(
      `DROP INDEX "public"."IDX_5b6c6b1d0a684db1fb5954304a"`,
    );
    await queryRunner.query(`DROP TABLE "users"`);
    await queryRunner.query(`DROP INDEX "public"."idx_uniq_team_id"`);
    await queryRunner.query(`DROP INDEX "public"."idx_platform_team_uid"`);
    await queryRunner.query(`DROP TABLE "platform_teams"`);
  }
}
