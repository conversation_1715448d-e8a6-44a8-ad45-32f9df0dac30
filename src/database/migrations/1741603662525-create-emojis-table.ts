import { MigrationInterface, QueryRunner } from 'typeorm';

export class CreateEmojisTable1741603662525 implements MigrationInterface {
  name = 'CreateEmojisTable1741603662525';

  public async up(queryRunner: QueryRunner): Promise<void> {
    await queryRunner.query(
      `ALTER TABLE "bots" DROP CONSTRAINT "fk_slack_bot_installation_id"`,
    );
    await queryRunner.query(
      `ALTER TABLE "bots" DROP CONSTRAINT "fk_slack_bot_organization_id"`,
    );
    await queryRunner.query(
      `CREATE TABLE "slack_emojis" ("id" BIGSERIAL NOT NULL, "name" character varying NOT NULL, "unicode" text NOT NULL, "url" text, "aliases" text, "keywords" text array, "created_at" TIMESTAMP WITH TIME ZONE NOT NULL DEFAULT now(), "updated_at" TIMESTAMP WITH TIME ZONE NOT NULL DEFAULT now(), "organization_id" bigint, "installation_id" bigint, CONSTRAINT "UQ_7a2395afc0e1aecd40750e4dfa8" UNIQUE ("name"), CONSTRAINT "PK_21519f242e693dc1135eede697a" PRIMARY KEY ("id"))`,
    );
    await queryRunner.query(
      `CREATE INDEX "IDX_7a2395afc0e1aecd40750e4dfa" ON "slack_emojis" ("name") `,
    );
    await queryRunner.query(
      `CREATE UNIQUE INDEX "idx_unique_emoji_name" ON "slack_emojis" ("name", "installation_id") `,
    );
    await queryRunner.query(
      `ALTER TABLE "bots" ADD CONSTRAINT "fk_slack_bot_installation_id" FOREIGN KEY ("installation_id") REFERENCES "installations"("id") ON DELETE CASCADE ON UPDATE CASCADE`,
    );
    await queryRunner.query(
      `ALTER TABLE "bots" ADD CONSTRAINT "fk_slack_bot_organization_id" FOREIGN KEY ("organization_id") REFERENCES "organizations"("id") ON DELETE CASCADE ON UPDATE CASCADE`,
    );
    await queryRunner.query(
      `ALTER TABLE "slack_emojis" ADD CONSTRAINT "FK_1ec3b0c25f1e7d83ca74a688cfa" FOREIGN KEY ("organization_id") REFERENCES "organizations"("id") ON DELETE NO ACTION ON UPDATE NO ACTION`,
    );
    await queryRunner.query(
      `ALTER TABLE "slack_emojis" ADD CONSTRAINT "FK_7c6de951eb6a99ca962e6c7c927" FOREIGN KEY ("installation_id") REFERENCES "installations"("id") ON DELETE NO ACTION ON UPDATE NO ACTION`,
    );
  }

  public async down(queryRunner: QueryRunner): Promise<void> {
    await queryRunner.query(
      `ALTER TABLE "slack_emojis" DROP CONSTRAINT "FK_7c6de951eb6a99ca962e6c7c927"`,
    );
    await queryRunner.query(
      `ALTER TABLE "slack_emojis" DROP CONSTRAINT "FK_1ec3b0c25f1e7d83ca74a688cfa"`,
    );
    await queryRunner.query(
      `ALTER TABLE "bots" DROP CONSTRAINT "fk_slack_bot_organization_id"`,
    );
    await queryRunner.query(
      `ALTER TABLE "bots" DROP CONSTRAINT "fk_slack_bot_installation_id"`,
    );
    await queryRunner.query(`DROP INDEX "public"."idx_unique_emoji_name"`);
    await queryRunner.query(
      `DROP INDEX "public"."IDX_7a2395afc0e1aecd40750e4dfa"`,
    );
    await queryRunner.query(`DROP TABLE "slack_emojis"`);
    await queryRunner.query(
      `ALTER TABLE "bots" ADD CONSTRAINT "fk_slack_bot_organization_id" FOREIGN KEY ("organization_id") REFERENCES "organizations"("id") ON DELETE NO ACTION ON UPDATE NO ACTION`,
    );
    await queryRunner.query(
      `ALTER TABLE "bots" ADD CONSTRAINT "fk_slack_bot_installation_id" FOREIGN KEY ("installation_id") REFERENCES "installations"("id") ON DELETE NO ACTION ON UPDATE NO ACTION`,
    );
  }
}
