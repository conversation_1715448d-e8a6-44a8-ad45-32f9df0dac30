import { MigrationInterface, QueryRunner } from 'typeorm';

export class AlterCustomerContactsAddChannels1740917589860
  implements MigrationInterface
{
  name = 'AlterCustomerContactsAddChannels1740917589860';

  public async up(queryRunner: QueryRunner): Promise<void> {
    await queryRunner.query(
      `CREATE TABLE "slack_customer_contacts_channels" ("customer_contact_id" bigint NOT NULL, "channel_id" bigint NOT NULL, CONSTRAINT "PK_9a44cbb7de9ce5c6210576fdd52" PRIMARY KEY ("customer_contact_id", "channel_id"))`,
    );
    await queryRunner.query(
      `CREATE INDEX "IDX_b20d9a9f7a9eb3dd5a86a0c14e" ON "slack_customer_contacts_channels" ("customer_contact_id") `,
    );
    await queryRunner.query(
      `CREATE INDEX "IDX_e0cee5298e015aa9a8a485f780" ON "slack_customer_contacts_channels" ("channel_id") `,
    );
    await queryRunner.query(
      `ALTER TABLE "slack_customer_contacts_channels" ADD CONSTRAINT "FK_b20d9a9f7a9eb3dd5a86a0c14ef" FOREIGN KEY ("customer_contact_id") REFERENCES "slack_customer_contacts"("id") ON DELETE CASCADE ON UPDATE CASCADE`,
    );
    await queryRunner.query(
      `ALTER TABLE "slack_customer_contacts_channels" ADD CONSTRAINT "FK_e0cee5298e015aa9a8a485f780d" FOREIGN KEY ("channel_id") REFERENCES "channels"("id") ON DELETE CASCADE ON UPDATE CASCADE`,
    );
  }

  public async down(queryRunner: QueryRunner): Promise<void> {
    await queryRunner.query(
      `ALTER TABLE "slack_customer_contacts_channels" DROP CONSTRAINT "FK_e0cee5298e015aa9a8a485f780d"`,
    );
    await queryRunner.query(
      `ALTER TABLE "slack_customer_contacts_channels" DROP CONSTRAINT "FK_b20d9a9f7a9eb3dd5a86a0c14ef"`,
    );
    await queryRunner.query(
      `DROP INDEX "public"."IDX_e0cee5298e015aa9a8a485f780"`,
    );
    await queryRunner.query(
      `DROP INDEX "public"."IDX_b20d9a9f7a9eb3dd5a86a0c14e"`,
    );
    await queryRunner.query(`DROP TABLE "slack_customer_contacts_channels"`);
  }
}
