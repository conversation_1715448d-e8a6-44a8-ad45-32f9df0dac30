import { MigrationInterface, QueryRunner } from 'typeorm';

export class CreateGroupedMessages1740715608487 implements MigrationInterface {
  name = 'CreateGroupedMessages1740715608487';

  public async up(queryRunner: QueryRunner): Promise<void> {
    await queryRunner.query(
      `CREATE TABLE "grouped_slack_messages" ("id" BIGSERIAL NOT NULL, "platform_ticket_id" character varying NOT NULL, "parent_comment_id" character varying NOT NULL, "slack_message_ts" character varying NOT NULL, "created_at" TIMESTAMP WITH TIME ZONE NOT NULL DEFAULT now(), "updated_at" TIMESTAMP WITH TIME ZONE NOT NULL DEFAULT now(), "deleted_at" TIMESTAMP WITH TIME ZONE, "grouped_into_message_id" bigint, "channel_id" bigint, "installation_id" bigint NOT NULL, "organization_id" bigint, CONSTRAINT "PK_a1ff83a2566ae01cef16e771133" PRIMARY KEY ("id"))`,
    );
    await queryRunner.query(
      `CREATE INDEX "IDX_aa799bb0ba6aa910298fabb757" ON "grouped_slack_messages" ("platform_ticket_id") `,
    );
    await queryRunner.query(
      `CREATE INDEX "IDX_a5d8dac323b912b258ccbfb480" ON "grouped_slack_messages" ("parent_comment_id") `,
    );
    await queryRunner.query(
      `CREATE INDEX "IDX_49c11fc80d988a7480b5def672" ON "grouped_slack_messages" ("slack_message_ts") `,
    );
    await queryRunner.query(
      `ALTER TABLE "grouped_slack_messages" ADD CONSTRAINT "FK_5fe473e5a9148f9e23469c20ba7" FOREIGN KEY ("grouped_into_message_id") REFERENCES "slack_messages"("id") ON DELETE CASCADE ON UPDATE CASCADE`,
    );
    await queryRunner.query(
      `ALTER TABLE "grouped_slack_messages" ADD CONSTRAINT "FK_5108297348460f2d112dfa875e3" FOREIGN KEY ("channel_id") REFERENCES "channels"("id") ON DELETE CASCADE ON UPDATE CASCADE`,
    );
    await queryRunner.query(
      `ALTER TABLE "grouped_slack_messages" ADD CONSTRAINT "fk_request_installation_id" FOREIGN KEY ("installation_id") REFERENCES "installations"("id") ON DELETE CASCADE ON UPDATE CASCADE`,
    );
    await queryRunner.query(
      `ALTER TABLE "grouped_slack_messages" ADD CONSTRAINT "fk_request_organization_id" FOREIGN KEY ("organization_id") REFERENCES "organizations"("id") ON DELETE NO ACTION ON UPDATE NO ACTION`,
    );
  }

  public async down(queryRunner: QueryRunner): Promise<void> {
    await queryRunner.query(
      `ALTER TABLE "grouped_slack_messages" DROP CONSTRAINT "fk_request_organization_id"`,
    );
    await queryRunner.query(
      `ALTER TABLE "grouped_slack_messages" DROP CONSTRAINT "fk_request_installation_id"`,
    );
    await queryRunner.query(
      `ALTER TABLE "grouped_slack_messages" DROP CONSTRAINT "FK_5108297348460f2d112dfa875e3"`,
    );
    await queryRunner.query(
      `ALTER TABLE "grouped_slack_messages" DROP CONSTRAINT "FK_5fe473e5a9148f9e23469c20ba7"`,
    );
    await queryRunner.query(
      `DROP INDEX "public"."IDX_49c11fc80d988a7480b5def672"`,
    );
    await queryRunner.query(
      `DROP INDEX "public"."IDX_a5d8dac323b912b258ccbfb480"`,
    );
    await queryRunner.query(
      `DROP INDEX "public"."IDX_aa799bb0ba6aa910298fabb757"`,
    );
    await queryRunner.query(`DROP TABLE "grouped_slack_messages"`);
  }
}
