import { MigrationInterface, QueryRunner } from 'typeorm';

export class AlterTriageMessageShouldUpdateFlag1741086538046
  implements MigrationInterface
{
  name = 'AlterTriageMessageShouldUpdateFlag1741086538046';

  public async up(queryRunner: QueryRunner): Promise<void> {
    await queryRunner.query(
      `ALTER TABLE "slack_triage_messages" ADD "should_update_thread" boolean NOT NULL DEFAULT true`,
    );
  }

  public async down(queryRunner: QueryRunner): Promise<void> {
    await queryRunner.query(
      `ALTER TABLE "slack_triage_messages" DROP COLUMN "should_update_thread"`,
    );
  }
}
