import { MigrationInterface, QueryRunner } from 'typeorm';

export class AddTriggerOnSettings1740230848527 implements MigrationInterface {
  public async up(queryRunner: QueryRunner): Promise<void> {
    await queryRunner.query(`
      CREATE TRIGGER validate_settings_trigger
      BEFORE INSERT OR UPDATE ON settings
      FOR EACH ROW
      EXECUTE FUNCTION validate_settings();
    `);
  }

  public async down(queryRunner: QueryRunner): Promise<void> {
    await queryRunner.query(`
      DROP TRIGGER validate_settings_trigger ON settings;
    `);
  }
}
