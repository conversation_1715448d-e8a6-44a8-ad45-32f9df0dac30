import { MigrationInterface, QueryRunner } from 'typeorm';

export class CreateCommentConversationMappings1741906049281
  implements MigrationInterface
{
  name = 'CreateCommentConversationMappings1741906049281';

  public async up(queryRunner: QueryRunner): Promise<void> {
    await queryRunner.query(
      `CREATE TABLE "comment_conversation_mappings" ("id" BIGSERIAL NOT NULL, "platform_comment_id" character varying NOT NULL, "slack_ts" character varying NOT NULL, "slack_thread_ts" character varying NOT NULL, "created_at" TIMESTAMP WITH TIME ZONE NOT NULL DEFAULT now(), "updated_at" TIMESTAMP WITH TIME ZONE NOT NULL DEFAULT now(), "channel_id" bigint, "installation_id" bigint, "organization_id" bigint, CONSTRAINT "PK_5beed019218bfc6fecd124897e7" PRIMARY KEY ("id"))`,
    );
    await queryRunner.query(
      `CREATE INDEX "idx_comment_conversations_map" ON "comment_conversation_mappings" ("platform_comment_id", "slack_ts", "slack_thread_ts") `,
    );
    await queryRunner.query(
      `ALTER TABLE "comment_conversation_mappings" ADD CONSTRAINT "FK_c1fe4f4c94e8d820105552dda28" FOREIGN KEY ("channel_id") REFERENCES "channels"("id") ON DELETE CASCADE ON UPDATE CASCADE`,
    );
    await queryRunner.query(
      `ALTER TABLE "comment_conversation_mappings" ADD CONSTRAINT "FK_7a1c978ae97a481d9bfea5fc29f" FOREIGN KEY ("installation_id") REFERENCES "installations"("id") ON DELETE CASCADE ON UPDATE CASCADE`,
    );
    await queryRunner.query(
      `ALTER TABLE "comment_conversation_mappings" ADD CONSTRAINT "FK_ac525afa4b3a970a54f9d9e6443" FOREIGN KEY ("organization_id") REFERENCES "organizations"("id") ON DELETE CASCADE ON UPDATE CASCADE`,
    );
  }

  public async down(queryRunner: QueryRunner): Promise<void> {
    await queryRunner.query(
      `ALTER TABLE "comment_conversation_mappings" DROP CONSTRAINT "FK_ac525afa4b3a970a54f9d9e6443"`,
    );
    await queryRunner.query(
      `ALTER TABLE "comment_conversation_mappings" DROP CONSTRAINT "FK_7a1c978ae97a481d9bfea5fc29f"`,
    );
    await queryRunner.query(
      `ALTER TABLE "comment_conversation_mappings" DROP CONSTRAINT "FK_c1fe4f4c94e8d820105552dda28"`,
    );
    await queryRunner.query(
      `DROP INDEX "public"."idx_comment_conversations_map"`,
    );
    await queryRunner.query(`DROP TABLE "comment_conversation_mappings"`);
  }
}
