import { MigrationInterface, QueryRunner } from 'typeorm';

export class AddProcedureToValidateSettings1740228806882
  implements MigrationInterface
{
  public async up(queryRunner: QueryRunner): Promise<void> {
    await queryRunner.query(
      `
CREATE OR REPLACE FUNCTION validate_settings()
RETURNS TRIGGER AS $$
#variable_conflict error
#variable_conflict use_variable
#variable_conflict use_column
DECLARE
    curr_key TEXT;
    curr_value JSONB;
    schema_type TEXT;
    array_element JSONB;
BEGIN
    -- Loop through each setting in the JSONB
    FOR curr_key, curr_value IN SELECT * FROM jsonb_each(NEW.settings)
    LOOP
        -- Get the expected type from schema
        SELECT s.setting_type INTO schema_type 
        FROM settings_schemas s 
        WHERE s.setting_key = curr_key;
        
        -- Check if setting exists in schema
        IF schema_type IS NULL THEN
            RAISE EXCEPTION 'Unknown setting: %', curr_key;
        END IF;
        
        -- Validate type
        CASE schema_type
            WHEN 'boolean' THEN
                IF jsonb_typeof(curr_value) != 'boolean' THEN
                    RAISE EXCEPTION 'Invalid boolean value for %: %', curr_key, curr_value;
                END IF;

            WHEN 'number' THEN
                IF jsonb_typeof(curr_value) != 'number' THEN
                    RAISE EXCEPTION 'Invalid number value for %: %', curr_key, curr_value;
                END IF;

            WHEN 'string' THEN
                IF jsonb_typeof(curr_value) != 'string' THEN
                    RAISE EXCEPTION 'Invalid string value for %: %', curr_key, curr_value;
                END IF;

            WHEN 'array' THEN
                IF jsonb_typeof(curr_value) != 'array' THEN
                    RAISE EXCEPTION 'Invalid array value for %: %', curr_key, curr_value;
                END IF;

            WHEN 'array_of_strings' THEN
                IF jsonb_typeof(curr_value) != 'array' THEN
                    RAISE EXCEPTION 'Invalid array value for %: %', curr_key, curr_value;
                END IF;
                
                -- Check each element is a string
                FOR array_element IN SELECT * FROM jsonb_array_elements(curr_value)
                LOOP
                    IF jsonb_typeof(array_element) != 'string' THEN
                        RAISE EXCEPTION 'Array % contains non-string element: %', curr_key, array_element;
                    END IF;
                END LOOP;

            WHEN 'array_of_numbers' THEN
                IF jsonb_typeof(curr_value) != 'array' THEN
                    RAISE EXCEPTION 'Invalid array value for %: %', curr_key, curr_value;
                END IF;
                
                -- Check each element is a number
                FOR array_element IN SELECT * FROM jsonb_array_elements(curr_value)
                LOOP
                    IF jsonb_typeof(array_element) != 'number' THEN
                        RAISE EXCEPTION 'Array % contains non-number element: %', curr_key, array_element;
                    END IF;
                END LOOP;

            WHEN 'jsonb' THEN
                -- All JSONB values are valid here
                CONTINUE;

            ELSE
                RAISE EXCEPTION 'Unknown setting type: %', schema_type;
        END CASE;
    END LOOP;
    
    RETURN NEW;
END;
$$ LANGUAGE plpgsql;
      `,
    );
  }

  public async down(queryRunner: QueryRunner): Promise<void> {
    await queryRunner.query(
      `
      DROP FUNCTION validate_settings();
      `,
    );
  }
}
