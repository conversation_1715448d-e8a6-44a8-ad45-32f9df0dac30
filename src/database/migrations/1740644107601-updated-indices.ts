import { MigrationInterface, QueryRunner } from 'typeorm';

export class UpdatedIndices1740644107601 implements MigrationInterface {
  name = 'UpdatedIndices1740644107601';

  public async up(queryRunner: QueryRunner): Promise<void> {
    await queryRunner.query(
      `DROP INDEX "public"."idx_uniq_platform_ticket_id_in_org"`,
    );
    await queryRunner.query(
      `DROP INDEX "public"."idx_uniq_platform_ticket_id_in_channel"`,
    );
  }

  public async down(queryRunner: QueryRunner): Promise<void> {
    await queryRunner.query(
      `CREATE UNIQUE INDEX "idx_uniq_platform_ticket_id_in_channel" ON "slack_messages" ("platform_ticket_id", "channel_id") `,
    );
    await queryRunner.query(
      `CREATE UNIQUE INDEX "idx_uniq_platform_ticket_id_in_org" ON "slack_messages" ("platform_ticket_id", "organization_id") `,
    );
  }
}
