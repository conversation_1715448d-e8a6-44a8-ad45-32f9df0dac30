import { MigrationInterface, QueryRunner } from 'typeorm';

export class CreateIndexOnSettingsData1740499500287
  implements MigrationInterface
{
  public async up(queryRunner: QueryRunner): Promise<void> {
    await queryRunner.query(
      'CREATE INDEX settings_data_gin ON settings USING GIN (settings jsonb_path_ops)',
    );
  }

  public async down(queryRunner: QueryRunner): Promise<void> {
    await queryRunner.query('DROP INDEX settings_data_gin');
  }
}
