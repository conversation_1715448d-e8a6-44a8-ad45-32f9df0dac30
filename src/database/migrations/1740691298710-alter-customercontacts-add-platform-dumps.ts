import { MigrationInterface, QueryRunner } from 'typeorm';

export class AlterCustomercontactsAddPlatformDumps1740691298710
  implements MigrationInterface
{
  name = 'AlterCustomercontactsAddPlatformDumps1740691298710';

  public async up(queryRunner: QueryRunner): Promise<void> {
    await queryRunner.query(
      `ALTER TABLE "slack_customer_contacts" RENAME COLUMN "metadata" TO "platform_dump"`,
    );
  }

  public async down(queryRunner: QueryRunner): Promise<void> {
    await queryRunner.query(
      `ALTER TABLE "slack_customer_contacts" RENAME COLUMN "platform_dump" TO "metadata"`,
    );
  }
}
