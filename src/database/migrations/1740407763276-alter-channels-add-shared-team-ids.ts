import { MigrationInterface, QueryRunner } from 'typeorm';

export class AlterChannelsAddSharedTeamIds1740407763276
  implements MigrationInterface
{
  name = 'AlterChannelsAddSharedTeamIds1740407763276';

  public async up(queryRunner: QueryRunner): Promise<void> {
    await queryRunner.query(
      `ALTER TABLE "channels" ADD "shared_team_ids" character varying array`,
    );
  }

  public async down(queryRunner: QueryRunner): Promise<void> {
    await queryRunner.query(
      `ALTER TABLE "channels" DROP COLUMN "shared_team_ids"`,
    );
  }
}
