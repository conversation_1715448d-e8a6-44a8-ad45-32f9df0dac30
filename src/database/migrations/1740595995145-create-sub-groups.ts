import { MigrationInterface, QueryRunner } from 'typeorm';

export class CreateSubGroups1740595995145 implements MigrationInterface {
  name = 'CreateSubGroups1740595995145';

  public async up(queryRunner: QueryRunner): Promise<void> {
    await queryRunner.query(
      `CREATE TABLE "slack_subgroups" ("id" BIGSERIAL NOT NULL, "slack_group_id" character varying NOT NULL, "slack_group_dump" jsonb NOT NULL, "slack_handle" character varying NOT NULL, "description" character varying NOT NULL, "is_external" boolean NOT NULL DEFAULT false, "users_count" integer NOT NULL DEFAULT '0', "users" character varying array, "created_at" TIMESTAMP WITH TIME ZONE NOT NULL DEFAULT now(), "updated_at" TIMESTAMP WITH TIME ZONE NOT NULL DEFAULT now(), "deleted_at" TIMESTAMP WITH TIME ZONE, "slack_deleted_at" integer, "deleted_by" bigint, "created_by" bigint, "updated_by" bigint, "installation_id" bigint, "organization_id" bigint, CONSTRAINT "PK_33aa30e27183635a73e98106dd3" PRIMARY KEY ("id"))`,
    );
    await queryRunner.query(
      `CREATE UNIQUE INDEX "idx_unique_slack_sub_group_id_per_workspace" ON "slack_subgroups" ("slack_group_id", "installation_id") `,
    );
    await queryRunner.query(
      `CREATE TABLE "sub_team_to_sub_groups_mappings" ("id" BIGSERIAL NOT NULL, "sub_team_id" character varying NOT NULL, "created_at" TIMESTAMP WITH TIME ZONE NOT NULL DEFAULT now(), "updated_at" TIMESTAMP WITH TIME ZONE NOT NULL DEFAULT now(), "deleted_at" TIMESTAMP WITH TIME ZONE, "sub_group_id" bigint, "platform_team_id" bigint, "installation_id" bigint, "organization_id" bigint NOT NULL, CONSTRAINT "PK_10c48432dc17e39d2675c8d1674" PRIMARY KEY ("id"))`,
    );
    await queryRunner.query(
      `CREATE INDEX "IDX_6e3f81f1745a3c7bd068730f32" ON "sub_team_to_sub_groups_mappings" ("sub_team_id") `,
    );
    await queryRunner.query(
      `CREATE INDEX "IDX_c61da7043aeb3743639200e31b" ON "sub_team_to_sub_groups_mappings" ("organization_id") `,
    );
    await queryRunner.query(
      `CREATE UNIQUE INDEX "idx_unique_sub_team_sub_group_per_workspace" ON "sub_team_to_sub_groups_mappings" ("sub_group_id", "sub_team_id", "installation_id") `,
    );
    await queryRunner.query(
      `CREATE TABLE "team_triage_rule_mappings" ("id" BIGSERIAL NOT NULL, "is_enabled" boolean NOT NULL DEFAULT true, "is_default" boolean NOT NULL DEFAULT false, "triage_rules" jsonb, "created_at" TIMESTAMP WITH TIME ZONE NOT NULL DEFAULT now(), "updated_at" TIMESTAMP WITH TIME ZONE NOT NULL DEFAULT now(), "deleted_at" TIMESTAMP WITH TIME ZONE, "platform_team_id" bigint, "installation_id" bigint, "organization_id" bigint NOT NULL, CONSTRAINT "PK_b6ae111280561a600e0fe46d1b8" PRIMARY KEY ("id"))`,
    );
    await queryRunner.query(
      `CREATE INDEX "IDX_80761eb9c4de4b7289a2ed5308" ON "team_triage_rule_mappings" ("is_default") `,
    );
    await queryRunner.query(
      `CREATE INDEX "IDX_dbdcdcac6b2f885927eaaadac5" ON "team_triage_rule_mappings" ("organization_id") `,
    );
    await queryRunner.query(
      `CREATE UNIQUE INDEX "unique_default_triage_channel" ON "team_triage_rule_mappings" ("platform_team_id", "installation_id", "organization_id") WHERE deleted_at IS NULL AND is_default = true`,
    );
    await queryRunner.query(
      `CREATE TABLE "prompts" ("id" BIGSERIAL NOT NULL, "name" character varying(255) NOT NULL, "prompts" jsonb NOT NULL, "selection_rules" jsonb, "is_default" boolean NOT NULL DEFAULT false, "is_enabled" boolean NOT NULL DEFAULT true, "created_at" TIMESTAMP WITH TIME ZONE NOT NULL DEFAULT now(), "updated_at" TIMESTAMP WITH TIME ZONE NOT NULL DEFAULT now(), "deleted_at" TIMESTAMP WITH TIME ZONE, "platform_team_id" bigint, "installation_id" bigint, "organization_id" bigint, CONSTRAINT "PK_21f33798862975179e40b216a1d" PRIMARY KEY ("id"))`,
    );
    await queryRunner.query(
      `CREATE TABLE "team_triage_rule_channels" ("triage_rule_id" bigint NOT NULL, "channel_id" bigint NOT NULL, CONSTRAINT "PK_4a2a3bd3ec08fbf2e3b6fdb757c" PRIMARY KEY ("triage_rule_id", "channel_id"))`,
    );
    await queryRunner.query(
      `CREATE INDEX "IDX_3dee4e942e03b6013dddf70359" ON "team_triage_rule_channels" ("triage_rule_id") `,
    );
    await queryRunner.query(
      `CREATE INDEX "IDX_da02fd62b802ef4ce648cb2125" ON "team_triage_rule_channels" ("channel_id") `,
    );
    await queryRunner.query(
      `ALTER TABLE "slack_messages" ADD "slack_user_id" character varying`,
    );
    await queryRunner.query(
      `ALTER TABLE "slack_subgroups" ADD CONSTRAINT "fk_slack_subgroup_deleted_by" FOREIGN KEY ("deleted_by") REFERENCES "users"("id") ON DELETE NO ACTION ON UPDATE NO ACTION`,
    );
    await queryRunner.query(
      `ALTER TABLE "slack_subgroups" ADD CONSTRAINT "fk_slack_subgroup_created_by" FOREIGN KEY ("created_by") REFERENCES "users"("id") ON DELETE NO ACTION ON UPDATE NO ACTION`,
    );
    await queryRunner.query(
      `ALTER TABLE "slack_subgroups" ADD CONSTRAINT "fk_slack_subgroup_updated_by" FOREIGN KEY ("updated_by") REFERENCES "users"("id") ON DELETE NO ACTION ON UPDATE NO ACTION`,
    );
    await queryRunner.query(
      `ALTER TABLE "slack_subgroups" ADD CONSTRAINT "fk_slack_subgroup_installation_id" FOREIGN KEY ("installation_id") REFERENCES "installations"("id") ON DELETE NO ACTION ON UPDATE NO ACTION`,
    );
    await queryRunner.query(
      `ALTER TABLE "slack_subgroups" ADD CONSTRAINT "fk_slack_subgroup_organization_id" FOREIGN KEY ("organization_id") REFERENCES "organizations"("id") ON DELETE NO ACTION ON UPDATE NO ACTION`,
    );
    await queryRunner.query(
      `ALTER TABLE "sub_team_to_sub_groups_mappings" ADD CONSTRAINT "FK_1ff38192049283472bcd49a7fd8" FOREIGN KEY ("sub_group_id") REFERENCES "slack_subgroups"("id") ON DELETE CASCADE ON UPDATE NO ACTION`,
    );
    await queryRunner.query(
      `ALTER TABLE "sub_team_to_sub_groups_mappings" ADD CONSTRAINT "FK_4ef99086bdfd1f77bd9415fed6c" FOREIGN KEY ("platform_team_id") REFERENCES "platform_teams"("id") ON DELETE CASCADE ON UPDATE NO ACTION`,
    );
    await queryRunner.query(
      `ALTER TABLE "sub_team_to_sub_groups_mappings" ADD CONSTRAINT "FK_89494bfd0f0b94db047846907ec" FOREIGN KEY ("installation_id") REFERENCES "installations"("id") ON DELETE CASCADE ON UPDATE NO ACTION`,
    );
    await queryRunner.query(
      `ALTER TABLE "sub_team_to_sub_groups_mappings" ADD CONSTRAINT "FK_c61da7043aeb3743639200e31b0" FOREIGN KEY ("organization_id") REFERENCES "organizations"("id") ON DELETE NO ACTION ON UPDATE NO ACTION`,
    );
    await queryRunner.query(
      `ALTER TABLE "team_triage_rule_mappings" ADD CONSTRAINT "FK_24e554d6e96e640b741ba7383a6" FOREIGN KEY ("platform_team_id") REFERENCES "platform_teams"("id") ON DELETE CASCADE ON UPDATE NO ACTION`,
    );
    await queryRunner.query(
      `ALTER TABLE "team_triage_rule_mappings" ADD CONSTRAINT "FK_675f82154edd9b674565ddf93fa" FOREIGN KEY ("installation_id") REFERENCES "installations"("id") ON DELETE CASCADE ON UPDATE NO ACTION`,
    );
    await queryRunner.query(
      `ALTER TABLE "team_triage_rule_mappings" ADD CONSTRAINT "FK_dbdcdcac6b2f885927eaaadac57" FOREIGN KEY ("organization_id") REFERENCES "organizations"("id") ON DELETE NO ACTION ON UPDATE NO ACTION`,
    );
    await queryRunner.query(
      `ALTER TABLE "prompts" ADD CONSTRAINT "FK_5e0c3fe9886e556ac72000d49cd" FOREIGN KEY ("platform_team_id") REFERENCES "platform_teams"("id") ON DELETE NO ACTION ON UPDATE NO ACTION`,
    );
    await queryRunner.query(
      `ALTER TABLE "prompts" ADD CONSTRAINT "FK_1cfe0d970d6b4ed274211b6687d" FOREIGN KEY ("installation_id") REFERENCES "installations"("id") ON DELETE NO ACTION ON UPDATE NO ACTION`,
    );
    await queryRunner.query(
      `ALTER TABLE "prompts" ADD CONSTRAINT "FK_17ed6c0f33e2720e182e29b80d0" FOREIGN KEY ("organization_id") REFERENCES "organizations"("id") ON DELETE NO ACTION ON UPDATE NO ACTION`,
    );
    await queryRunner.query(
      `ALTER TABLE "team_triage_rule_channels" ADD CONSTRAINT "FK_3dee4e942e03b6013dddf703599" FOREIGN KEY ("triage_rule_id") REFERENCES "team_triage_rule_mappings"("id") ON DELETE CASCADE ON UPDATE CASCADE`,
    );
    await queryRunner.query(
      `ALTER TABLE "team_triage_rule_channels" ADD CONSTRAINT "FK_da02fd62b802ef4ce648cb2125c" FOREIGN KEY ("channel_id") REFERENCES "channels"("id") ON DELETE CASCADE ON UPDATE CASCADE`,
    );
  }

  public async down(queryRunner: QueryRunner): Promise<void> {
    await queryRunner.query(
      `ALTER TABLE "team_triage_rule_channels" DROP CONSTRAINT "FK_da02fd62b802ef4ce648cb2125c"`,
    );
    await queryRunner.query(
      `ALTER TABLE "team_triage_rule_channels" DROP CONSTRAINT "FK_3dee4e942e03b6013dddf703599"`,
    );
    await queryRunner.query(
      `ALTER TABLE "prompts" DROP CONSTRAINT "FK_17ed6c0f33e2720e182e29b80d0"`,
    );
    await queryRunner.query(
      `ALTER TABLE "prompts" DROP CONSTRAINT "FK_1cfe0d970d6b4ed274211b6687d"`,
    );
    await queryRunner.query(
      `ALTER TABLE "prompts" DROP CONSTRAINT "FK_5e0c3fe9886e556ac72000d49cd"`,
    );
    await queryRunner.query(
      `ALTER TABLE "team_triage_rule_mappings" DROP CONSTRAINT "FK_dbdcdcac6b2f885927eaaadac57"`,
    );
    await queryRunner.query(
      `ALTER TABLE "team_triage_rule_mappings" DROP CONSTRAINT "FK_675f82154edd9b674565ddf93fa"`,
    );
    await queryRunner.query(
      `ALTER TABLE "team_triage_rule_mappings" DROP CONSTRAINT "FK_24e554d6e96e640b741ba7383a6"`,
    );
    await queryRunner.query(
      `ALTER TABLE "sub_team_to_sub_groups_mappings" DROP CONSTRAINT "FK_c61da7043aeb3743639200e31b0"`,
    );
    await queryRunner.query(
      `ALTER TABLE "sub_team_to_sub_groups_mappings" DROP CONSTRAINT "FK_89494bfd0f0b94db047846907ec"`,
    );
    await queryRunner.query(
      `ALTER TABLE "sub_team_to_sub_groups_mappings" DROP CONSTRAINT "FK_4ef99086bdfd1f77bd9415fed6c"`,
    );
    await queryRunner.query(
      `ALTER TABLE "sub_team_to_sub_groups_mappings" DROP CONSTRAINT "FK_1ff38192049283472bcd49a7fd8"`,
    );
    await queryRunner.query(
      `ALTER TABLE "slack_subgroups" DROP CONSTRAINT "fk_slack_subgroup_organization_id"`,
    );
    await queryRunner.query(
      `ALTER TABLE "slack_subgroups" DROP CONSTRAINT "fk_slack_subgroup_installation_id"`,
    );
    await queryRunner.query(
      `ALTER TABLE "slack_subgroups" DROP CONSTRAINT "fk_slack_subgroup_updated_by"`,
    );
    await queryRunner.query(
      `ALTER TABLE "slack_subgroups" DROP CONSTRAINT "fk_slack_subgroup_created_by"`,
    );
    await queryRunner.query(
      `ALTER TABLE "slack_subgroups" DROP CONSTRAINT "fk_slack_subgroup_deleted_by"`,
    );
    await queryRunner.query(
      `ALTER TABLE "slack_messages" DROP COLUMN "slack_user_id"`,
    );
    await queryRunner.query(
      `DROP INDEX "public"."IDX_da02fd62b802ef4ce648cb2125"`,
    );
    await queryRunner.query(
      `DROP INDEX "public"."IDX_3dee4e942e03b6013dddf70359"`,
    );
    await queryRunner.query(`DROP TABLE "team_triage_rule_channels"`);
    await queryRunner.query(`DROP TABLE "prompts"`);
    await queryRunner.query(
      `DROP INDEX "public"."unique_default_triage_channel"`,
    );
    await queryRunner.query(
      `DROP INDEX "public"."IDX_dbdcdcac6b2f885927eaaadac5"`,
    );
    await queryRunner.query(
      `DROP INDEX "public"."IDX_80761eb9c4de4b7289a2ed5308"`,
    );
    await queryRunner.query(`DROP TABLE "team_triage_rule_mappings"`);
    await queryRunner.query(
      `DROP INDEX "public"."idx_unique_sub_team_sub_group_per_workspace"`,
    );
    await queryRunner.query(
      `DROP INDEX "public"."IDX_c61da7043aeb3743639200e31b"`,
    );
    await queryRunner.query(
      `DROP INDEX "public"."IDX_6e3f81f1745a3c7bd068730f32"`,
    );
    await queryRunner.query(`DROP TABLE "sub_team_to_sub_groups_mappings"`);
    await queryRunner.query(
      `DROP INDEX "public"."idx_unique_slack_sub_group_id_per_workspace"`,
    );
    await queryRunner.query(`DROP TABLE "slack_subgroups"`);
  }
}
