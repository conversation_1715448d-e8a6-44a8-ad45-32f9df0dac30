import { MigrationInterface, QueryRunner } from 'typeorm';

export class AddPlatformDumps1740607572362 implements MigrationInterface {
  name = 'AddPlatformDumps1740607572362';

  public async up(queryRunner: QueryRunner): Promise<void> {
    await queryRunner.query(
      `CREATE TYPE "public"."slack_audit_logs_entity_type_enum" AS ENUM('slack_message')`,
    );
    await queryRunner.query(
      `CREATE TYPE "public"."slack_audit_logs_visibility_enum" AS ENUM('system', 'team', 'organization')`,
    );
    await queryRunner.query(
      `CREATE TYPE "public"."slack_audit_logs_operation_enum" AS ENUM('info', 'created', 'updated', 'deleted', 'archived', 'restored')`,
    );
    await queryRunner.query(
      `CREATE TABLE "slack_audit_logs" ("id" BIGSERIAL NOT NULL, "entity_type" "public"."slack_audit_logs_entity_type_enum", "entity_id" bigint, "entity_uid" character varying(255), "activity" character varying(255) NOT NULL, "description" text, "is_automated" boolean NOT NULL DEFAULT false, "source" character varying(255), "visibility" "public"."slack_audit_logs_visibility_enum" NOT NULL DEFAULT 'organization', "operation" "public"."slack_audit_logs_operation_enum" NOT NULL, "metadata" jsonb, "created_at" TIMESTAMP WITH TIME ZONE NOT NULL DEFAULT now(), "updated_at" TIMESTAMP WITH TIME ZONE NOT NULL DEFAULT now(), "deleted_at" TIMESTAMP WITH TIME ZONE, "organization_id" bigint, "team_id" bigint, "activity_performed_by" bigint, CONSTRAINT "PK_b7e9e1bf0686857b605e473e58f" PRIMARY KEY ("id"))`,
    );
    await queryRunner.query(
      `CREATE INDEX "IDX_d23074a9912decd2dd81404f02" ON "slack_audit_logs" ("organization_id") `,
    );
    await queryRunner.query(
      `CREATE INDEX "IDX_0dedaab53c011332d4116c9c0f" ON "slack_audit_logs" ("team_id") `,
    );
    await queryRunner.query(
      `CREATE INDEX "IDX_189dca2f101c7e04409dddc59b" ON "slack_audit_logs" ("entity_type") `,
    );
    await queryRunner.query(
      `CREATE INDEX "IDX_adc2b28d42cbf5448c913f27df" ON "slack_audit_logs" ("entity_id") `,
    );
    await queryRunner.query(
      `CREATE INDEX "IDX_4e85da6a01e7aadc54a0c7a26a" ON "slack_audit_logs" ("entity_uid") `,
    );
    await queryRunner.query(
      `CREATE INDEX "IDX_1c70bc02dc79fd4b59fcd21485" ON "slack_audit_logs" ("activity") `,
    );
    await queryRunner.query(
      `CREATE INDEX "IDX_7225b4e24e845eb4a3539c1a85" ON "slack_audit_logs" ("activity_performed_by") `,
    );
    await queryRunner.query(
      `CREATE INDEX "IDX_56553e929a0d96d3f129a46696" ON "slack_audit_logs" ("source") `,
    );
    await queryRunner.query(
      `CREATE INDEX "IDX_1fb679db340691e3a396be6e33" ON "slack_audit_logs" ("visibility") `,
    );
    await queryRunner.query(
      `CREATE INDEX "IDX_c8d8db4a189e399d0a9fac25cf" ON "slack_audit_logs" ("operation") `,
    );
    await queryRunner.query(
      `CREATE INDEX "IDX_3becfb88f519b1573ce10f5ddc" ON "slack_audit_logs" ("created_at") `,
    );
    await queryRunner.query(
      `CREATE INDEX "partial_slack_audit_log_team_organization" ON "slack_audit_logs" ("team_id", "organization_id") `,
    );
    await queryRunner.query(
      `ALTER TABLE "channels" ADD "platform_dump" jsonb NOT NULL DEFAULT '{"accounts":[]}'`,
    );
    await queryRunner.query(
      `ALTER TABLE "installations" ADD "platform_dump" jsonb`,
    );
    await queryRunner.query(
      `ALTER TABLE "slack_audit_logs" ADD CONSTRAINT "FK_d23074a9912decd2dd81404f020" FOREIGN KEY ("organization_id") REFERENCES "organizations"("id") ON DELETE CASCADE ON UPDATE NO ACTION`,
    );
    await queryRunner.query(
      `ALTER TABLE "slack_audit_logs" ADD CONSTRAINT "FK_0dedaab53c011332d4116c9c0fc" FOREIGN KEY ("team_id") REFERENCES "platform_teams"("id") ON DELETE SET NULL ON UPDATE NO ACTION`,
    );
    await queryRunner.query(
      `ALTER TABLE "slack_audit_logs" ADD CONSTRAINT "FK_7225b4e24e845eb4a3539c1a85d" FOREIGN KEY ("activity_performed_by") REFERENCES "users"("id") ON DELETE NO ACTION ON UPDATE NO ACTION`,
    );
  }

  public async down(queryRunner: QueryRunner): Promise<void> {
    await queryRunner.query(
      `ALTER TABLE "slack_audit_logs" DROP CONSTRAINT "FK_7225b4e24e845eb4a3539c1a85d"`,
    );
    await queryRunner.query(
      `ALTER TABLE "slack_audit_logs" DROP CONSTRAINT "FK_0dedaab53c011332d4116c9c0fc"`,
    );
    await queryRunner.query(
      `ALTER TABLE "slack_audit_logs" DROP CONSTRAINT "FK_d23074a9912decd2dd81404f020"`,
    );
    await queryRunner.query(
      `ALTER TABLE "installations" DROP COLUMN "platform_dump"`,
    );
    await queryRunner.query(
      `ALTER TABLE "channels" DROP COLUMN "platform_dump"`,
    );
    await queryRunner.query(
      `DROP INDEX "public"."partial_slack_audit_log_team_organization"`,
    );
    await queryRunner.query(
      `DROP INDEX "public"."IDX_3becfb88f519b1573ce10f5ddc"`,
    );
    await queryRunner.query(
      `DROP INDEX "public"."IDX_c8d8db4a189e399d0a9fac25cf"`,
    );
    await queryRunner.query(
      `DROP INDEX "public"."IDX_1fb679db340691e3a396be6e33"`,
    );
    await queryRunner.query(
      `DROP INDEX "public"."IDX_56553e929a0d96d3f129a46696"`,
    );
    await queryRunner.query(
      `DROP INDEX "public"."IDX_7225b4e24e845eb4a3539c1a85"`,
    );
    await queryRunner.query(
      `DROP INDEX "public"."IDX_1c70bc02dc79fd4b59fcd21485"`,
    );
    await queryRunner.query(
      `DROP INDEX "public"."IDX_4e85da6a01e7aadc54a0c7a26a"`,
    );
    await queryRunner.query(
      `DROP INDEX "public"."IDX_adc2b28d42cbf5448c913f27df"`,
    );
    await queryRunner.query(
      `DROP INDEX "public"."IDX_189dca2f101c7e04409dddc59b"`,
    );
    await queryRunner.query(
      `DROP INDEX "public"."IDX_0dedaab53c011332d4116c9c0f"`,
    );
    await queryRunner.query(
      `DROP INDEX "public"."IDX_d23074a9912decd2dd81404f02"`,
    );
    await queryRunner.query(`DROP TABLE "slack_audit_logs"`);
    await queryRunner.query(
      `DROP TYPE "public"."slack_audit_logs_operation_enum"`,
    );
    await queryRunner.query(
      `DROP TYPE "public"."slack_audit_logs_visibility_enum"`,
    );
    await queryRunner.query(
      `DROP TYPE "public"."slack_audit_logs_entity_type_enum"`,
    );
  }
}
