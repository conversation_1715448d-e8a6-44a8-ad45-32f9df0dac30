/**
 * The operations that was performed on the entity
 */
export enum AuditLogOp {
  /**
   * The activity is informational and did not perform any operation on the entity
   */
  INFO = 'info',

  /**
   * The activity is an error and did not perform any operation on the entity
   */
  ERROR = 'error',

  /**
   * The entity was created
   */
  CREATED = 'created',

  /**
   * Data or properties of the entity were updated
   */
  UPDATED = 'updated',

  /**
   * The entity was marked deleted
   */
  DELETED = 'deleted',

  /**
   * The entity was marked archived
   */
  ARCHIVED = 'archived',

  /**
   * The entity was restored possibly from a deleted state or archived state
   */
  RESTORED = 'restored',
}

/**
 * The visibility of the audit log
 */
export enum AuditLogVisibility {
  /**
   * The audit logs visible only to us (Thena Development Team) shouldn't be exposed via the API
   */
  SYSTEM = 'system',

  /**
   * The audit logs visible only to the team and its members
   */
  TEAM = 'team',

  /**
   * The audit logs visible to everyone
   */
  ORGANIZATION = 'organization',
}

/**
 * The types of entities that the audit log is about
 */
export enum AuditLogEntityType {
  SLACK_MESSAGE = 'slack_message',
  CHANNEL = 'channel',
}
