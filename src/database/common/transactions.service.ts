import { Injectable } from '@nestjs/common';
import { DataSource } from 'typeorm';
import { TransactionContext } from './transaction-interface';

@Injectable()
export class TransactionService {
  constructor(private readonly dataSource: DataSource) {}

  async runInTransaction<T>(
    callback: (transactionContext: TransactionContext) => Promise<T>,
    existingTransaction?: TransactionContext,
  ): Promise<T> {
    // If an existing transaction is provided, use it
    if (existingTransaction) {
      return callback(existingTransaction);
    }

    // Otherwise, create a new transaction
    const queryRunner = this.dataSource.createQueryRunner();

    try {
      await queryRunner.connect();
      await queryRunner.startTransaction();
    } catch (error) {
      await queryRunner.release();
      throw new Error(
        `Transaction failed: failed to connect to database: ${
          (error as Error)?.message
        }`,
      );
    }

    try {
      const result = await callback({
        queryRunner,
        manager: queryRunner.manager,
      });
      await queryRunner.commitTransaction();
      return result;
    } catch (err) {
      await queryRunner.rollbackTransaction();
      if (err instanceof Error) {
        throw err;
      }

      throw new Error('Transaction failed: unknown error');
    } finally {
      if (!queryRunner.isReleased) {
        await queryRunner.release();
      }
    }
  }
}
