import { Mo<PERSON>le, OnApplicationBootstrap } from '@nestjs/common';
import { ConfigKeys, ConfigService } from '../../config/config.service';
import { SeedsManager } from './manager';
import { RunSeedsCommand } from './seeds.command';

@Module({
  providers: [SeedsManager, RunSeedsCommand],
  exports: [SeedsManager],
})
export class SeedsModule implements OnApplicationBootstrap {
  constructor(
    private readonly seedsManager: SeedsManager,
    private readonly configService: ConfigService,
  ) {}

  async onApplicationBootstrap() {
    // Only run seeds if enabled in config
    const shouldRunSeeds = this.configService.get(
      ConfigKeys.DATABASE_AUTO_RUN_SEEDS,
    );

    if (shouldRunSeeds) {
      try {
        console.log('Running database seeds...');
        await this.seedsManager.runSeeds();
        console.log('Database seeds completed successfully');
      } catch (error) {
        console.error('Failed to run database seeds:', error);
        throw error;
      }
    }
  }
}
