import { Command, CommandRunner } from 'nest-commander';
import { SeedsManager } from './manager';

@Command({ name: 'run-seeds', description: 'Run database seeds' })
export class RunSeedsCommand extends CommandRunner {
  constructor(private readonly seedsManager: SeedsManager) {
    super();
  }

  async run(): Promise<void> {
    try {
      console.log('Running database seeds...');
      await this.seedsManager.runSeeds();
      console.log('Database seeds completed successfully');
    } catch (error) {
      console.error('Failed to run database seeds:', error);
      process.exit(1);
    }
  }
}
