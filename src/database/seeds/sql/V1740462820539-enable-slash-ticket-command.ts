import { QueryRunner } from 'typeorm';
import { Seed } from '../manager';

class AddTicketCommandSettings implements Seed {
  public id = '1740462820539';
  public name = 'AddTicketCommandSettings';
  public timestamp = 1740462820539;

  public async up(queryRunner: QueryRunner): Promise<void> {
    await queryRunner.query(`
      -- Set up some schema definitions
      INSERT INTO settings_schemas (setting_key, setting_type, default_value, description) 
      VALUES ('ticket_command', 'boolean', 'true', 'Enable the "/ticket" command to create tickets.');
    `);
  }

  public async down(queryRunner: QueryRunner): Promise<void> {
    await queryRunner.query(`
      DELETE FROM settings_schemas WHERE setting_key IN ('ticket_command');
    `);
  }
}

export const seed = new AddTicketCommandSettings();
