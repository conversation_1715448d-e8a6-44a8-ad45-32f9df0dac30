import { QueryRunner } from 'typeorm';
import { Seed } from '../manager';

class AddTicketEmojiSetting implements Seed {
  public id = '1741180759291';
  public name = 'AddTicketEmojiSetting';
  public timestamp = 1741180759291;

  public async up(queryRunner: QueryRunner): Promise<void> {
    await queryRunner.query(`
      -- Set up some schema definitions
      INSERT INTO settings_schemas (setting_key, setting_type, default_value, description) 
      VALUES
        ('emoji_to_create_ticket', 'string', 'ticket', 'The emoji to use to create a ticket.');
    `);
  }

  public async down(queryRunner: QueryRunner): Promise<void> {
    await queryRunner.query(`
      DELETE FROM settings_schemas WHERE setting_key = 'emoji_to_create_ticket';
    `);
  }
}

export const seed = new AddTicketEmojiSetting();
