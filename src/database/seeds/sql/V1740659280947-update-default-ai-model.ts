import { QueryRunner } from 'typeorm';
import { Seed } from '../manager';

class UpdateDefaultAiModel implements Seed {
  public id = '1740659533471';
  public name = 'UpdateDefaultAiModel';
  public timestamp = 1740659533471;

  public async up(queryRunner: QueryRunner): Promise<void> {
    await queryRunner.query(`
      -- Set up some schema definitions
      UPDATE settings_schemas SET default_value = 'grok-2' WHERE setting_key = 'ai_model';
    `);
  }

  public async down(queryRunner: QueryRunner): Promise<void> {
    await queryRunner.query(`
      DELETE FROM settings_schemas WHERE setting_key IN ('ai_enable_extended_thinking');
    `);
  }
}

export const seed = new UpdateDefaultAiModel();
