import { QueryRunner } from 'typeorm';
import { Seed } from '../manager';

class AddSelectedFormsSetting implements Seed {
  public id = '1741500000000';
  public name = 'AddSelectedFormsSetting';
  public timestamp = 1741500000000;

  public async up(queryRunner: QueryRunner): Promise<void> {
    await queryRunner.query(`
      -- Set up schema definition for selected_forms
      INSERT INTO settings_schemas (setting_key, setting_type, default_value, description) 
      VALUES
        ('selected_forms', 'array_of_strings', '[]', 'List of form IDs that should be displayed to users in Slack.');
    `);
  }

  public async down(queryRunner: QueryRunner): Promise<void> {
    await queryRunner.query(`
      DELETE FROM settings_schemas WHERE setting_key = 'selected_forms';
    `);
  }
}

export const seed = new AddSelectedFormsSetting();
