import { QueryRunner } from 'typeorm';
import { Seed } from '../manager';

class EnableExtendedThinking implements Seed {
  public id = '1740659280947';
  public name = 'EnableExtendedThinking';
  public timestamp = 1740659280947;

  public async up(queryRunner: QueryRunner): Promise<void> {
    await queryRunner.query(`
      -- Set up some schema definitions
      INSERT INTO settings_schemas (setting_key, setting_type, default_value, description) 
      VALUES
        ('ai_enable_extended_thinking', 'boolean', 'false', 'Enable extended thinking mode for AI models that support it.');
    `);
  }

  public async down(queryRunner: QueryRunner): Promise<void> {
    await queryRunner.query(`
      DELETE FROM settings_schemas WHERE setting_key IN ('ai_enable_extended_thinking');
    `);
  }
}

export const seed = new EnableExtendedThinking();
