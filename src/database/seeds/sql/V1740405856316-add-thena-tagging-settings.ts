import { QueryRunner } from 'typeorm';
import { Seed } from '../manager';

class AddThenaTaggingSettings implements Seed {
  public id = '1740405856316';
  public name = 'AddThenaTaggingSettings';
  public timestamp = 1740405856316;

  public async up(queryRunner: QueryRunner): Promise<void> {
    await queryRunner.query(`
      -- Set up some schema definitions
      INSERT INTO settings_schemas (setting_key, setting_type, default_value, description) 
      VALUES ('thena_bot_tagging_enabled', 'boolean', 'true', 'Allow creating tickets by tagging @Thena');
    `);
  }

  public async down(queryRunner: QueryRunner): Promise<void> {
    await queryRunner.query(`
      DELETE FROM settings_schemas WHERE setting_key IN ('thena_bot_tagging_enabled');
    `);
  }
}

export const seed = new AddThenaTaggingSettings();
