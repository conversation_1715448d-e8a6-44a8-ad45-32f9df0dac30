import { QueryRunner } from 'typeorm';
import { Seed } from '../manager';

class RemoveAiProviders implements Seed {
  public id = '1741029306786';
  public name = 'RemoveAiProviders';
  public timestamp = 1741029306786;

  public async up(queryRunner: QueryRunner): Promise<void> {
    await queryRunner.query(`
      -- Set up some schema definitions
      DELETE FROM settings_schemas WHERE setting_key = 'ai_provider';
    `);
  }

  public async down(queryRunner: QueryRunner): Promise<void> {
    await queryRunner.query(`
      INSERT INTO settings_schemas (setting_key, setting_type, default_value) VALUES ('ai_provider', 'string', 'grok');
    `);
  }
}

export const seed = new RemoveAiProviders();
