import { QueryRunner } from 'typeorm';
import { Seed } from '../manager';

class AiProvidersAndModels implements Seed {
  public id = '1740605068882';
  public name = 'AiProvidersAndModels';
  public timestamp = 1740605068882;

  public async up(queryRunner: QueryRunner): Promise<void> {
    await queryRunner.query(`
      -- Set up some schema definitions
      INSERT INTO settings_schemas (setting_key, setting_type, default_value, description) 
      VALUES
        ('ai_provider', 'string', 'grok', 'The AI provider to use for the app.'),
        ('ai_model', 'string', 'gpt-4o', 'The AI model to use for the app.'),
        ('ai_temperature', 'number', '0.5', 'The temperature to use for the AI model.'),
        ('ai_max_tokens', 'number', '1000', 'The maximum number of tokens to use for the AI model.');
    `);
  }

  public async down(queryRunner: QueryRunner): Promise<void> {
    await queryRunner.query(`
      DELETE FROM settings_schemas WHERE setting_key IN ('ai_provider');
    `);
  }
}

export const seed = new AiProvidersAndModels();
