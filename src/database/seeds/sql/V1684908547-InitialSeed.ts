import { QueryRunner } from 'typeorm';
import { Seed } from '../manager';

class InitialSeed implements Seed {
  public id = '1684908547';
  public name = 'InitialSeed';
  public timestamp = 1684908547000;

  public async up(queryRunner: QueryRunner): Promise<void> {
    await queryRunner.query(`
      -- Set up some schema definitions
      INSERT INTO settings_schemas (setting_key, setting_type, default_value, description) 
      VALUES
        ('conversation_window', 'number', '30', 'Time in minutes within which slack messages will be grouped together into a ticket.'),
        ('automatic_tickets', 'boolean', 'true', 'Automatically create tickets from detected slack conversations.'),
        ('slash_commands', 'boolean', 'true', 'Enable the "/" command to create tickets.'),
        ('require_form', 'boolean', 'false', 'Require users to fill out a form before creating a ticket.');
    `);
  }

  public async down(queryRunner: QueryRunner): Promise<void> {
    await queryRunner.query(`
      DELETE FROM settings_schemas WHERE setting_key IN (
        'conversation_window',
        'automatic_tickets',
        'slash_commands',
        'require_form'
      );
    `);
  }
}

export const seed = new InitialSeed();
