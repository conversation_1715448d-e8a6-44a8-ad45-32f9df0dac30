import { QueryRunner } from 'typeorm';
import { Seed } from '../manager';

class SettingsToEnableTicketCreationViaEmoji implements Seed {
  public id = '1741261803266';
  public name = 'SettingsToEnableTicketCreationViaEmoji';
  public timestamp = 1741261803266;

  public async up(queryRunner: QueryRunner): Promise<void> {
    await queryRunner.query(`
      -- Set up some schema definitions
      INSERT INTO settings_schemas (setting_key, setting_type, default_value, description) 
      VALUES
        ('enable_ticket_creation_via_reaction', 'boolean', 'true', 'Enable ticket creation via reaction.');
    `);
  }

  public async down(queryRunner: QueryRunner): Promise<void> {
    await queryRunner.query(`
      DELETE FROM settings_schemas WHERE setting_key = 'enable_ticket_creation_via_reaction';
    `);
  }
}

export const seed = new SettingsToEnableTicketCreationViaEmoji();
