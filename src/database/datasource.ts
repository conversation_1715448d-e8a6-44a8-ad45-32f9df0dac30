import { DataSource } from 'typeorm';
import { entities } from './entities';

const slackAppDataSource = new DataSource({
  type: 'postgres',
  host: process.env.DB_HOST,
  port: Number.parseInt(process.env.DB_PORT),
  username: process.env.DB_USERNAME,
  password: process.env.DB_PASSWORD,
  database: process.env.DB_NAME,
  entities,
  logging: true,
  synchronize: true,
  migrations: ['dist/database/migrations/*.js'],
  migrationsTransactionMode: 'each',
  migrationsTableName: 'thena_slack_app_migrations',
});

export default slackAppDataSource;
