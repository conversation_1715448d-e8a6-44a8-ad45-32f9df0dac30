interface UserImages {
  image_original: string;
  image_512: string;
  image_192: string;
  image_72: string;
  image_48: string;
  image_32: string;
  image_24: string;
}

/**
 * @description
 * Represents a person in the database
 */
export interface Person {
  /**
   * @description
   * Checks if the person has an email
   * @returns A boolean value
   */
  userHasEmail(): boolean;

  /**
   * @description
   * Gets the avatar of the person
   * @returns The avatar of the person
   */
  getUserAvatar(): string;

  /**
   * @description
   * The Slack profile name of the person
   */
  displayName?: string | null;

  /**
   * @description
   * The real name of the person
   */
  realName?: string | null;

  /**
   * @description
   * The user dump of the person
   */
  userDump?: Record<string, any>;

  /**
   * @description
   * The Slack ID of the person
   */
  slackId: string;

  /**
   * @description
   * The Slack profile email of the person
   */
  slackProfileEmail?: string | null;

  /**
   * @description
   * Whether this person is restricted to being a multi-channel guest in a workspace
   */
  isRestricted: boolean;

  /**
   * @description
   * Whether this person is ultra restricted to being a single-channel guest in a workspace
   */
  isUltraRestricted: boolean;

  /**
   * @description
   * The images of the person
   */
  images?: Partial<UserImages> | null;
}
