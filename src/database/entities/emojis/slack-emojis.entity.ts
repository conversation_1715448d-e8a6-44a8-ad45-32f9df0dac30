import {
  Column,
  CreateDateColumn,
  Entity,
  Index,
  JoinColumn,
  ManyToOne,
  PrimaryGeneratedColumn,
  UpdateDateColumn,
} from 'typeorm';
import { Installations } from '../installations';
import { Organizations } from '../organizations';

@Entity('slack_emojis')
@Index('idx_unique_emoji_name', ['name', 'installation'], {
  unique: true,
})
export class SlackEmojis {
  @PrimaryGeneratedColumn({ type: 'bigint' })
  id: string;

  @ManyToOne(() => Organizations)
  @JoinColumn({ name: 'organization_id' })
  organization: Organizations;

  @ManyToOne(() => Installations)
  @JoinColumn({ name: 'installation_id' })
  installation: Installations;

  @Index()
  @Column({ type: 'varchar', unique: true })
  name: string;

  @Column({ type: 'text', nullable: true })
  unicode: string;

  @Column({ type: 'text', nullable: true })
  url: string;

  @Column({ type: 'text', nullable: true })
  aliases: string;

  @Column({ type: 'text', nullable: true, array: true })
  keywords: string[];

  @CreateDateColumn({ name: 'created_at', type: 'timestamptz' })
  createdAt: string;

  @UpdateDateColumn({ name: 'updated_at', type: 'timestamptz' })
  updatedAt: string;
}
