import { BaseInterfaceRepository } from '../../../common/base-repository-interface';
import { SlackEmojis } from '../slack-emojis.entity';

/**
 * @interface SlackEmojisRepositoryInterface
 * @extends BaseInterfaceRepository<SlackEmojis>
 * @description
 * Interface for the SlackEmojis repository, extending the base repository interface.
 * This interface ensures type safety and consistency for SlackEmojis-specific
 * repository operations.
 *
 * @remarks
 * By extending BaseInterfaceRepository, this interface inherits all the standard
 * CRUD operations defined for general entities and allows for the addition of
 * Channels-specific methods if needed in the future.
 *
 * @see {@link BaseInterfaceRepository} for the base interface being extended
 * @see {@link SlackEmojis} for the entity this interface is designed to work with
 */
export interface SlackEmojisRepositoryInterface
  extends BaseInterfaceRepository<SlackEmojis> {}
