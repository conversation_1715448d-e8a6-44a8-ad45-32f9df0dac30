import { Injectable } from '@nestjs/common';
import { InjectRepository } from '@nestjs/typeorm';
import { Repository } from 'typeorm';
import { BaseAbstractRepository } from '../../../common/base-abstract-repository';
import { SlackEmojis } from '../slack-emojis.entity';
import { SlackEmojisRepositoryInterface } from './slack-emojis.interface';

/**
 * @class SlackEmojisRepository
 * @extends BaseAbstractRepository<SlackEmojis>
 * @implements SlackEmojisRepositoryInterface
 * @description
 * Repository class for managing SlackEmojis entities in the database.
 * This class extends the BaseAbstractRepository to inherit common CRUD operations
 * and implements the SlackEmojisRepositoryInterface for type safety and consistency.
 *
 * @remarks
 * This repository is designed to work with NestJS and TypeORM. It uses the
 * `@Injectable()` decorator to allow dependency injection in NestJS modules.
 *
 * @example
 * ```ts
 * // Inject and use in a service
 * constructor(private slackEmojisRepository: SlackEmojisRepository) {}
 *
 * async findSlackEmojis(id: string): Promise<SlackEmojis> {
 *   return this.slackEmojisRepository.findOneById(id);
 * }
 * ```
 *
 * @see {@link BaseAbstractRepository} for inherited methods
 * @see {@link SlackEmojisRepositoryInterface} for implemented methods
 * @see {@link SlackEmojis} for the entity this repository manages
 */
@Injectable()
export class SlackEmojisRepository
  extends BaseAbstractRepository<SlackEmojis>
  implements SlackEmojisRepositoryInterface
{
  constructor(
    @InjectRepository(SlackEmojis)
    private readonly slackEmojisRepository: Repository<SlackEmojis>,
  ) {
    super(slackEmojisRepository);
  }
}
