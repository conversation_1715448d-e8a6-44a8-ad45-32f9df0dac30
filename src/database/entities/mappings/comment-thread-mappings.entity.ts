import {
  Column,
  CreateDateColumn,
  Entity,
  Index,
  JoinColumn,
  ManyToOne,
  PrimaryGeneratedColumn,
  UpdateDateColumn,
} from 'typeorm';
import { Installations } from '../installations';
import { Organizations } from '../organizations';

@Entity('comment_thread_mappings')
@Index('idx_comment_thread_mappings', [
  'platformCommentThreadId',
  'slackThreadId',
  'organization',
])
export class CommentThreadMappings {
  @PrimaryGeneratedColumn({ type: 'bigint' })
  id: string;

  @Index('idx_ctm_platform_comment_thread_id')
  @Column({ type: 'varchar', name: 'platform_comment_thread_id' })
  platformCommentThreadId: string;

  @Index('idx_ctm_platform_comment_ticket_id')
  @Column({ type: 'varchar', name: 'platform_comment_ticket_id' })
  platformCommentTicketId: string; // A comment thread in platform is ALWAYS associated with a ticket

  @Index('idx_ctm_slack_thread_id')
  @Column({ type: 'varchar', name: 'slack_thread_id' })
  slackThreadId: string;

  @Index('idx_ctm_slack_channel_id')
  @Column({ type: 'varchar', name: 'slack_channel_id' })
  slackChannelId: string; // A slack comment thread is ALWAYS associated with a slack channel

  @Index('idx_ctm_installation_id')
  @ManyToOne(
    () => Installations,
    (installation) => installation.id,
    { onDelete: 'CASCADE', onUpdate: 'CASCADE' },
  )
  @JoinColumn({ name: 'installation_id' })
  installation: Installations;

  @Index('idx_ctm_organization_id')
  @ManyToOne(
    () => Organizations,
    (organization) => organization.id,
    { onDelete: 'CASCADE', onUpdate: 'CASCADE' },
  )
  @JoinColumn({ name: 'organization_id' })
  organization: Organizations;

  @CreateDateColumn({ type: 'timestamptz', name: 'created_at' })
  createdAt: Date;

  @UpdateDateColumn({ type: 'timestamptz', name: 'updated_at' })
  updatedAt: Date;
}
