import {
  CreateDateColumn,
  DeleteDateColumn,
  Entity,
  Index,
  JoinColumn,
  ManyToOne,
  PrimaryGeneratedColumn,
  UpdateDateColumn,
} from 'typeorm';
import { Channels } from '../channels/channels.entity';
import { Installations } from '../installations/installations.entity';
import { Organizations } from '../organizations/organizations.entity';

@Entity('channel_triage_mappings')
@Index(
  'idx_uniq_channel_triage_mapping',
  ['activeChannel', 'triageChannel', 'installation'],
  { unique: true },
)
export class ChannelTriageMappings {
  @PrimaryGeneratedColumn({ type: 'bigint' })
  id: string;

  @Index('idx_triage_channel_mappings_active_channel_id')
  @ManyToOne(
    () => Channels,
    (channel) => channel.id,
    { nullable: true },
  )
  @JoinColumn({ name: 'active_channel_id' })
  activeChannel: Channels;

  @Index('idx_triage_channel_mappings_triage_channel_id')
  @ManyToOne(
    () => Channels,
    (channel) => channel.id,
  )
  @JoinColumn({ name: 'triage_channel_id' })
  triageChannel: Channels;

  @Index('idx_triage_channel_mappings_installation_id')
  @ManyToOne(
    () => Installations,
    (installation) => installation.id,
    { onDelete: 'CASCADE', onUpdate: 'CASCADE' },
  )
  @JoinColumn({ name: 'installation_id' })
  installation: Installations;

  @Index('idx_triage_channel_mappings_organization_id')
  @ManyToOne(
    () => Organizations,
    (organization) => organization.id,
    { onDelete: 'CASCADE', onUpdate: 'CASCADE' },
  )
  @JoinColumn({ name: 'organization_id' })
  organization: Organizations;

  @CreateDateColumn({ type: 'timestamptz', name: 'created_at' })
  createdAt: Date;

  @UpdateDateColumn({ type: 'timestamptz', name: 'updated_at' })
  updatedAt: Date;

  @DeleteDateColumn({ type: 'timestamptz', name: 'deleted_at' })
  deletedAt: Date;
}
