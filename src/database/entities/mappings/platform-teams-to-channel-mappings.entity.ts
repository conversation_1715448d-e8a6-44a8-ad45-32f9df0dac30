import {
  Column,
  CreateDateColumn,
  Entity,
  Index,
  Join<PERSON>olumn,
  ManyToOne,
  PrimaryGeneratedColumn,
  UpdateDateColumn,
} from 'typeorm';
import { Channels } from '../channels/channels.entity';
import { Installations } from '../installations/installations.entity';
import { Organizations } from '../organizations/organizations.entity';
import { PlatformTeams } from '../teams/teams.entity';

export enum TeamRelationshipType {
  PRIMARY = 'primary',
  SECONDARY = 'secondary',
}

export const IDX_UNIQ_PRIMARY_TEAM_TO_CHANNEL_MAPPING =
  'idx_uniq_primary_team_to_channel_mapping';

@Entity('platform_teams_to_channel_mappings')
@Index('idx_platform_slack_channel_organization', [
  'platformTeam',
  'channel',
  'organization',
])
@Index(
  IDX_UNIQ_PRIMARY_TEAM_TO_CHANNEL_MAPPING,
  ['platformTeam', 'channel', 'relationshipType'],
  {
    where: "relationship_type = 'primary'",
    unique: true,
  },
)
export class PlatformTeamsToChannelMappings {
  @PrimaryGeneratedColumn({ type: 'bigint' })
  id: string;

  @Column({
    type: 'enum',
    enum: TeamRelationshipType,
    name: 'relationship_type',
    default: TeamRelationshipType.SECONDARY,
  })
  relationshipType: TeamRelationshipType;

  @Index('idx_ptcm_platform_team_ids')
  @ManyToOne(
    () => PlatformTeams,
    (platformTeam) => platformTeam.id,
  )
  @JoinColumn({ name: 'platform_team_id' })
  platformTeam: PlatformTeams;

  @Index('idx_ptcm_channel_id')
  @ManyToOne(
    () => Channels,
    (channel) => channel.id,
  )
  @JoinColumn({ name: 'channel_id' })
  channel: Channels;

  @Index('idx_ptcm_installation_id')
  @ManyToOne(
    () => Installations,
    (installation) => installation.id,
    { onDelete: 'CASCADE', onUpdate: 'CASCADE' },
  )
  @JoinColumn({ name: 'installation_id' })
  installation: Installations;

  @Index('idx_ptcm_organization_id')
  @ManyToOne(
    () => Organizations,
    (organization) => organization.id,
    { onDelete: 'CASCADE', onUpdate: 'CASCADE' },
  )
  @JoinColumn({ name: 'organization_id' })
  organization: Organizations;

  @CreateDateColumn({ type: 'timestamptz', name: 'created_at' })
  createdAt: Date;

  @UpdateDateColumn({ type: 'timestamptz', name: 'updated_at' })
  updatedAt: Date;
}
