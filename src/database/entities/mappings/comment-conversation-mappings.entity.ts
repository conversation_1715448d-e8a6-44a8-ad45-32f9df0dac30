import {
  Column,
  CreateDateColumn,
  Entity,
  Index,
  JoinColumn,
  ManyToOne,
  PrimaryGeneratedColumn,
  UpdateDateColumn,
} from 'typeorm';
import { Channels } from '../channels/channels.entity';
import { Installations } from '../installations';
import { Organizations } from '../organizations';

@Entity('comment_conversation_mappings')
@Index('idx_comment_conversations_map', [
  'platformCommentId',
  'slackTs',
  'slackThreadTs',
])
export class CommentConversationMappings {
  @PrimaryGeneratedColumn({ type: 'bigint' })
  id: string;

  @Column({ type: 'varchar', name: 'platform_comment_id' })
  platformCommentId: string;

  @Column({ type: 'varchar', name: 'slack_ts' })
  slackTs: string;

  @Column({ type: 'varchar', name: 'slack_thread_ts' })
  slackThreadTs: string;

  @ManyToOne(
    () => Channels,
    (channel) => channel.id,
    { onDelete: 'CASCADE', onUpdate: 'CASCADE' },
  )
  @JoinColumn({ name: 'channel_id' })
  channel: Channels;

  @ManyToOne(
    () => Installations,
    (installation) => installation.id,
    { onDelete: 'CASCADE', onUpdate: 'CASCADE' },
  )
  @JoinColumn({ name: 'installation_id' })
  installation: Installations;

  @ManyToOne(
    () => Organizations,
    (organization) => organization.id,
    { onDelete: 'CASCADE', onUpdate: 'CASCADE' },
  )
  @JoinColumn({ name: 'organization_id' })
  organization: Organizations;

  @CreateDateColumn({ type: 'timestamptz', name: 'created_at' })
  createdAt: Date;

  @UpdateDateColumn({ type: 'timestamptz', name: 'updated_at' })
  updatedAt: Date;
}
