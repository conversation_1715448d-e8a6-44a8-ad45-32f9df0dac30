import { Injectable } from '@nestjs/common';
import { InjectRepository } from '@nestjs/typeorm';
import { Repository } from 'typeorm';
import { BaseAbstractRepository } from '../../../common/base-abstract-repository';
import { CommentConversationMappings } from '../comment-conversation-mappings.entity';
import { CommentConversationMapsRepositoryInterface } from './comment-conversation-maps.interface';

/**
 * @class CommentConversationMapsRepository
 * @extends BaseAbstractRepository<CommentConversationMappings>
 * @implements CommentConversationMapsRepositoryInterface
 * @description
 * Repository class for managing Comment Conversation Maps entities in the database.
 * This class extends the BaseAbstractRepository to inherit common CRUD operations
 * and implements the CommentConversationMapsRepositoryInterface for type safety and consistency.
 *
 * @remarks
 * This repository is designed to work with NestJS and TypeORM. It uses the
 * `@Injectable()` decorator to allow dependency injection in NestJS modules.
 *
 * @example
 * ```ts
 * // Inject and use in a service
 * constructor(private commentConversationMapsRepository: CommentConversationMapsRepository) {}
 *
 * async findCommentConversationMaps(id: string): Promise<CommentConversationMappings> {
 *   return this.commentConversationMapsRepository.findOneById(id);
 * }
 * ```
 *
 * @see {@link BaseAbstractRepository} for inherited methods
 * @see {@link CommentConversationMapsRepositoryInterface} for implemented methods
 * @see {@link CommentConversationMappings} for the entity this repository manages
 */
@Injectable()
export class CommentConversationMapsRepository
  extends BaseAbstractRepository<CommentConversationMappings>
  implements CommentConversationMapsRepositoryInterface
{
  constructor(
    @InjectRepository(CommentConversationMappings)
    private readonly commentConversationMapsRepository: Repository<CommentConversationMappings>,
  ) {
    super(commentConversationMapsRepository);
  }
}
