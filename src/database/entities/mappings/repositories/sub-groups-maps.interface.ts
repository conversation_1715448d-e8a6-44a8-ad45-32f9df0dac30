import { BaseInterfaceRepository } from '../../../common/base-repository-interface';
import { SubTeamToSubGroupsMapping } from '../sub-team-to-sub-groups-mappings.entity';

/**
 * @interface SubGroupsMapsRepositoryInterface
 * @extends BaseInterfaceRepository<SubTeamToSubGroupsMapping>
 * @description
 * Interface for the Sub Groups Maps repository, extending the base repository interface.
 * This interface ensures type safety and consistency for Sub Groups Maps-specific
 * repository operations.
 *
 * @remarks
 * By extending BaseInterfaceRepository, this interface inherits all the standard
 * CRUD operations defined for general entities and allows for the addition of
 * Sub Groups Maps-specific methods if needed in the future.
 *
 * @see {@link BaseInterfaceRepository} for the base interface being extended
 * @see {@link SubTeamToSubGroupsMapping} for the entity this interface is designed to work with
 */
export interface SubGroupsMapsRepositoryInterface
  extends BaseInterfaceRepository<SubTeamToSubGroupsMapping> {}
