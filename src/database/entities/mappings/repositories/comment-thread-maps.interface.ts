import { BaseInterfaceRepository } from '../../../common/base-repository-interface';
import { CommentThreadMappings } from '../comment-thread-mappings.entity';

/**
 * @interface CommentThreadMapsRepositoryInterface
 * @extends BaseInterfaceRepository<CommentThreadMappings>
 * @description
 * Interface for the Comment Thread Maps repository, extending the base repository interface.
 * This interface ensures type safety and consistency for Comment Thread Maps-specific
 * repository operations.
 *
 * @remarks
 * By extending BaseInterfaceRepository, this interface inherits all the standard
 * CRUD operations defined for general entities and allows for the addition of
 * Comment Thread Maps-specific methods if needed in the future.
 *
 * @see {@link BaseInterfaceRepository} for the base interface being extended
 * @see {@link CommentThreadMappings} for the entity this interface is designed to work with
 */
export interface CommentThreadMapsRepositoryInterface
  extends BaseInterfaceRepository<CommentThreadMappings> {}
