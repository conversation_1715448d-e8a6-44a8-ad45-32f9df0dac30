import { Injectable } from '@nestjs/common';
import { DataSource, Repository } from 'typeorm';
import { BotCtx } from '../../../../auth/interfaces/context.interface';
import { Channels } from '../../channels/channels.entity';
import { TeamTriageRuleMapping } from '../teams-triage-mappings.entity';

@Injectable()
export class TeamTriageRuleMappingRepository extends Repository<TeamTriageRuleMapping> {
  constructor(private dataSource: DataSource) {
    super(TeamTriageRuleMapping, dataSource.createEntityManager());
  }

  async setDefaultMapping(
    platformTeamId: string,
    botCtx: BotCtx,
    channel: Channels,
  ): Promise<TeamTriageRuleMapping> {
    return this.dataSource.transaction(async (manager) => {
      // Unset existing defaults
      await manager
        .createQueryBuilder()
        .update(TeamTriageRuleMapping)
        .set({ isDefault: false })
        .where('platformTeam.id = :platformTeamId', { platformTeamId })
        .andWhere('installation.id = :installationId', {
          installationId: botCtx.installation.id,
        })
        .andWhere('organization.id = :organizationId', {
          organizationId: botCtx.organization.id,
        })
        .execute();

      // Create new default mapping with single channel
      const mapping = manager.create(TeamTriageRuleMapping, {
        platformTeam: { id: platformTeamId },
        installation: { id: botCtx.installation.id },
        organization: { id: botCtx.organization.id },
        triageChannels: [{ id: channel.id }],
        isDefault: true,
        isEnabled: true,
      });

      return manager.save(mapping);
    });
  }

  async updateRuleChannels(
    ruleId: string,
    channels: Channels[],
    botCtx: BotCtx,
  ): Promise<TeamTriageRuleMapping> {
    return this.dataSource.transaction(async (manager) => {
      const mapping = await manager.findOne(TeamTriageRuleMapping, {
        where: {
          id: ruleId,
          installation: { id: botCtx.installation.id },
          organization: { id: botCtx.organization.id },
        },
        relations: ['triageChannels'],
      });

      if (!mapping) {
        throw new Error('Rule mapping not found');
      }

      mapping.triageChannels = channels;
      return manager.save(mapping);
    });
  }

  async findTeamMappings(
    platformTeamId: string,
    botCtx: BotCtx,
  ): Promise<TeamTriageRuleMapping[]> {
    return this.find({
      where: {
        platformTeam: { id: platformTeamId },
        installation: { id: botCtx.installation.id },
        organization: { id: botCtx.organization.id },
      },
      relations: ['triageChannels'],
    });
  }

  async findActiveRulesForTeam(
    platformTeamId: string,
    botCtx: BotCtx,
  ): Promise<TeamTriageRuleMapping[]> {
    return this.find({
      where: {
        platformTeam: { id: platformTeamId },
        installation: { id: botCtx.installation.id },
        organization: { id: botCtx.organization.id },
        isEnabled: true,
      },
      relations: ['triageChannels'],
    });
  }
}
