import { Injectable } from '@nestjs/common';
import { InjectRepository } from '@nestjs/typeorm';
import { Repository } from 'typeorm';
import { BaseAbstractRepository } from '../../../common/base-abstract-repository';
import { SubTeamToSubGroupsMapping } from '../sub-team-to-sub-groups-mappings.entity';
import { SubGroupsMapsRepositoryInterface } from './sub-groups-maps.interface';

/**
 * @class SubGroupsMapsRepository
 * @extends BaseAbstractRepository<SubTeamToSubGroupsMapping>
 * @implements SubGroupsMapsRepositoryInterface
 * @description
 * Repository class for managing Sub Groups Maps entities in the database.
 * This class extends the BaseAbstractRepository to inherit common CRUD operations
 * and implements the SubGroupsMapsRepositoryInterface for type safety and consistency.
 *
 * @remarks
 * This repository is designed to work with NestJS and TypeORM. It uses the
 * `@Injectable()` decorator to allow dependency injection in NestJS modules.
 *
 * @example
 * ```ts
 * // Inject and use in a service
 * constructor(private subGroupsMapsRepository: SubGroupsMapsRepository) {}
 *
 * async findSubGroupsMaps(id: string): Promise<SubTeamToSubGroupsMapping> {
 *   return this.subGroupsMapsRepository.findOneById(id);
 * }
 * ```
 *
 * @see {@link BaseAbstractRepository} for inherited methods
 * @see {@link SubGroupsMapsRepositoryInterface} for implemented methods
 * @see {@link SubTeamToSubGroupsMapping} for the entity this repository manages
 */
@Injectable()
export class SubGroupsMapsRepository
  extends BaseAbstractRepository<SubTeamToSubGroupsMapping>
  implements SubGroupsMapsRepositoryInterface
{
  constructor(
    @InjectRepository(SubTeamToSubGroupsMapping)
    private readonly subGroupsMapsRepository: Repository<SubTeamToSubGroupsMapping>,
  ) {
    super(subGroupsMapsRepository);
  }
}
