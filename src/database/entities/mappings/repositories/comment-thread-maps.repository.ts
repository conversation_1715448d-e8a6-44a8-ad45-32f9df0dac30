import { Injectable } from '@nestjs/common';
import { InjectRepository } from '@nestjs/typeorm';
import { Repository } from 'typeorm';
import { BaseAbstractRepository } from '../../../common/base-abstract-repository';
import { CommentThreadMappings } from '../comment-thread-mappings.entity';
import { CommentThreadMapsRepositoryInterface } from './comment-thread-maps.interface';

/**
 * @class CommentThreadMapsRepository
 * @extends BaseAbstractRepository<CommentThreadMappings>
 * @implements CommentThreadMapsRepositoryInterface
 * @description
 * Repository class for managing Comment Thread Maps entities in the database.
 * This class extends the BaseAbstractRepository to inherit common CRUD operations
 * and implements the CommentThreadMapsRepositoryInterface for type safety and consistency.
 *
 * @remarks
 * This repository is designed to work with NestJS and TypeORM. It uses the
 * `@Injectable()` decorator to allow dependency injection in NestJS modules.
 *
 * @example
 * ```ts
 * // Inject and use in a service
 * constructor(private commentThreadMapsRepository: CommentThreadMapsRepository) {}
 *
 * async findCommentThreadMaps(id: string): Promise<CommentThreadMappings> {
 *   return this.commentThreadMapsRepository.findOneById(id);
 * }
 * ```
 *
 * @see {@link BaseAbstractRepository} for inherited methods
 * @see {@link CommentThreadMapsRepositoryInterface} for implemented methods
 * @see {@link CommentThreadMappings} for the entity this repository manages
 */
@Injectable()
export class CommentThreadMapsRepository
  extends BaseAbstractRepository<CommentThreadMappings>
  implements CommentThreadMapsRepositoryInterface
{
  constructor(
    @InjectRepository(CommentThreadMappings)
    private readonly commentThreadMapsRepository: Repository<CommentThreadMappings>,
  ) {
    super(commentThreadMapsRepository);
  }
}
