import { BaseInterfaceRepository } from '../../../common/base-repository-interface';
import { PlatformTeamsToChannelMappings } from '../platform-teams-to-channel-mappings.entity';

/**
 * @interface TeamChannelMapsRepositoryInterface
 * @extends BaseInterfaceRepository<PlatformTeamsToChannelMappings>
 * @description
 * Interface for the Team Channel Maps repository, extending the base repository interface.
 * This interface ensures type safety and consistency for Team Channel Maps-specific
 * repository operations.
 *
 * @remarks
 * By extending BaseInterfaceRepository, this interface inherits all the standard
 * CRUD operations defined for general entities and allows for the addition of
 * Team Channel Maps-specific methods if needed in the future.
 *
 * @see {@link BaseInterfaceRepository} for the base interface being extended
 * @see {@link PlatformTeamsToChannelMappings} for the entity this interface is designed to work with
 */
export interface TeamChannelMapsRepositoryInterface
  extends BaseInterfaceRepository<PlatformTeamsToChannelMappings> {}
