import { BaseInterfaceRepository } from '../../../common/base-repository-interface';
import { CommentConversationMappings } from '../comment-conversation-mappings.entity';

/**
 * @interface CommentConversationMapsRepositoryInterface
 * @extends BaseInterfaceRepository<CommentConversationMappings>
 * @description
 * Interface for the Comment Conversation Maps repository, extending the base repository interface.
 * This interface ensures type safety and consistency for Comment Conversation Maps-specific
 * repository operations.
 *
 * @remarks
 * By extending BaseInterfaceRepository, this interface inherits all the standard
 * CRUD operations defined for general entities and allows for the addition of
 * Comment Conversation Maps-specific methods if needed in the future.
 *
 * @see {@link BaseInterfaceRepository} for the base interface being extended
 * @see {@link CommentConversationMappings} for the entity this interface is designed to work with
 */
export interface CommentConversationMapsRepositoryInterface
  extends BaseInterfaceRepository<CommentConversationMappings> {}
