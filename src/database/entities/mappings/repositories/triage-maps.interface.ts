import { BaseInterfaceRepository } from '../../../common/base-repository-interface';
import { ChannelTriageMappings } from '../channel-triage-mappings.entity';

/**
 * @interface TriageMapsRepositoryInterface
 * @extends BaseInterfaceRepository<ChannelTriageMappings>
 * @description
 * Interface for the Triage Maps repository, extending the base repository interface.
 * This interface ensures type safety and consistency for Triage Maps-specific
 * repository operations.
 *
 * @remarks
 * By extending BaseInterfaceRepository, this interface inherits all the standard
 * CRUD operations defined for general entities and allows for the addition of
 * Triage Maps-specific methods if needed in the future.
 *
 * @see {@link BaseInterfaceRepository} for the base interface being extended
 * @see {@link ChannelTriageMappings} for the entity this interface is designed to work with
 */
export interface TriageMapsRepositoryInterface
  extends BaseInterfaceRepository<ChannelTriageMappings> {}
