import { Injectable } from '@nestjs/common';
import { InjectRepository } from '@nestjs/typeorm';
import { Repository } from 'typeorm';
import { BaseAbstractRepository } from '../../../common/base-abstract-repository';
import { PlatformTeamsToChannelMappings } from '../platform-teams-to-channel-mappings.entity';
import { TeamChannelMapsRepositoryInterface } from './team-channel-maps.interface';

/**
 * @class TeamChannelMapsRepository
 * @extends BaseAbstractRepository<PlatformTeamsToChannelMappings>
 * @implements TeamChannelMapsRepositoryInterface
 * @description
 * Repository class for managing Team Channel Maps entities in the database.
 * This class extends the BaseAbstractRepository to inherit common CRUD operations
 * and implements the TeamChannelMapsRepositoryInterface for type safety and consistency.
 *
 * @remarks
 * This repository is designed to work with NestJS and TypeORM. It uses the
 * `@Injectable()` decorator to allow dependency injection in NestJS modules.
 *
 * @example
 * ```ts
 * // Inject and use in a service
 * constructor(private teamChannelMapsRepository: TeamChannelMapsRepository) {}
 *
 * async findTeamChannelMaps(id: string): Promise<PlatformTeamsToChannelMappings> {
 *   return this.teamChannelMapsRepository.findOneById(id);
 * }
 * ```
 *
 * @see {@link BaseAbstractRepository} for inherited methods
 * @see {@link TeamChannelMapsRepositoryInterface} for implemented methods
 * @see {@link PlatformTeamsToChannelMappings} for the entity this repository manages
 */
@Injectable()
export class TeamChannelMapsRepository
  extends BaseAbstractRepository<PlatformTeamsToChannelMappings>
  implements TeamChannelMapsRepositoryInterface
{
  constructor(
    @InjectRepository(PlatformTeamsToChannelMappings)
    private readonly teamChannelMapsRepository: Repository<PlatformTeamsToChannelMappings>,
  ) {
    super(teamChannelMapsRepository);
  }
}
