import { Injectable } from '@nestjs/common';
import { InjectRepository } from '@nestjs/typeorm';
import { Repository } from 'typeorm';
import { BaseAbstractRepository } from '../../../common/base-abstract-repository';
import { ChannelTriageMappings } from '../channel-triage-mappings.entity';
import { TriageMapsRepositoryInterface } from './triage-maps.interface';

/**
 * @class TriageMapsRepository
 * @extends BaseAbstractRepository<ChannelTriageMappings>
 * @implements TriageMapsRepositoryInterface
 * @description
 * Repository class for managing Triage Maps entities in the database.
 * This class extends the BaseAbstractRepository to inherit common CRUD operations
 * and implements the TriageMapsRepositoryInterface for type safety and consistency.
 *
 * @remarks
 * This repository is designed to work with NestJS and TypeORM. It uses the
 * `@Injectable()` decorator to allow dependency injection in NestJS modules.
 *
 * @example
 * ```ts
 * // Inject and use in a service
 * constructor(private triageMapsRepository: TriageMapsRepository) {}
 *
 * async findTriageMaps(id: string): Promise<ChannelTriageMappings> {
 *   return this.triageMapsRepository.findOneById(id);
 * }
 * ```
 *
 * @see {@link BaseAbstractRepository} for inherited methods
 * @see {@link TriageMapsRepositoryInterface} for implemented methods
 * @see {@link ChannelTriageMappings} for the entity this repository manages
 */
@Injectable()
export class TriageMapsRepository
  extends BaseAbstractRepository<ChannelTriageMappings>
  implements TriageMapsRepositoryInterface
{
  constructor(
    @InjectRepository(ChannelTriageMappings)
    private readonly triageMapsRepository: Repository<ChannelTriageMappings>,
  ) {
    super(triageMapsRepository);
  }
}
