import {
  Column,
  CreateDateColumn,
  DeleteDateColumn,
  Entity,
  Index,
  JoinColumn,
  ManyToOne,
  PrimaryGeneratedColumn,
  UpdateDateColumn,
} from 'typeorm';
import { Installations } from '../installations/installations.entity';
import { Organizations } from '../organizations/organizations.entity';
import { SlackSubgroups } from '../subgroups';
import { PlatformTeams } from '../teams';

@Entity('sub_team_to_sub_groups_mappings')
@Index(
  'idx_unique_sub_team_sub_group_per_workspace',
  ['subGroup', 'platformSubTeam', 'installation'],
  { unique: true },
)
export class SubTeamToSubGroupsMapping {
  @PrimaryGeneratedColumn({ type: 'bigint' })
  id: string;

  @ManyToOne(
    () => SlackSubgroups,
    (subGroup) => subGroup.id,
    { onDelete: 'CASCADE', onUpdate: 'CASCADE' },
  )
  @JoinColumn({ name: 'sub_group_id' })
  subGroup: SlackSubgroups;

  @Index()
  @Column({ type: 'varchar', name: 'sub_team_id' })
  platformSubTeam: string;

  @ManyToOne(
    () => PlatformTeams,
    (platformTeam) => platformTeam.id,
    { onDelete: 'CASCADE', onUpdate: 'CASCADE' },
  )
  @JoinColumn({ name: 'platform_team_id' })
  platformTeam: PlatformTeams;

  @ManyToOne(
    () => Installations,
    (installation) => installation.id,
    { onDelete: 'CASCADE', onUpdate: 'CASCADE' },
  )
  @JoinColumn({ name: 'installation_id' })
  installation: Installations;

  @Index()
  @ManyToOne(
    () => Organizations,
    (organization) => organization.id,
    { onDelete: 'CASCADE', onUpdate: 'CASCADE' },
  )
  @JoinColumn({ name: 'organization_id' })
  organization: Organizations;

  @CreateDateColumn({ type: 'timestamptz', name: 'created_at' })
  createdAt: Date;

  @UpdateDateColumn({ type: 'timestamptz', name: 'updated_at' })
  updatedAt: Date;

  @DeleteDateColumn({ type: 'timestamptz', name: 'deleted_at' })
  deletedAt: Date;
}
