import {
  Column,
  CreateDateColumn,
  DeleteDateColumn,
  Entity,
  Index,
  JoinColumn,
  JoinTable,
  ManyToMany,
  ManyToOne,
  PrimaryGeneratedColumn,
  UpdateDateColumn,
} from 'typeorm';
import { TriageOperator } from '../../../slack/dtos/triage-rule.dto';
import { Channels } from '../channels/channels.entity';
import { Installations } from '../installations/installations.entity';
import { Organizations } from '../organizations/organizations.entity';
import { PlatformTeams } from '../teams/teams.entity';

export type TriageCondition = {
  category: string;
  field: string;
  operator: TriageOperator;
  value: string;
};

export type TriageRules = {
  AND?: TriageCondition[];
  OR?: TriageCondition[];
};

@Entity('team_triage_rule_mappings')
@Index(
  'unique_default_triage_channel',
  ['platformTeam', 'installation', 'organization'],
  {
    unique: true,
    where: 'deleted_at IS NULL AND is_default = true',
  },
)
export class TeamTriageRuleMapping {
  @PrimaryGeneratedColumn({ type: 'bigint' })
  id: string;

  @Column('boolean', { default: true, name: 'is_enabled' })
  isEnabled: boolean;

  @Column('boolean', { default: false, name: 'is_default' })
  @Index()
  isDefault: boolean;

  @Column('jsonb', { nullable: true, name: 'triage_rules' })
  triageRules: TriageRules;

  @ManyToOne(
    () => PlatformTeams,
    (platformTeam) => platformTeam.id,
    { onDelete: 'CASCADE', onUpdate: 'CASCADE' },
  )
  @JoinColumn({ name: 'platform_team_id' })
  platformTeam: PlatformTeams;

  @ManyToOne(
    () => Installations,
    (installation) => installation.id,
    { onDelete: 'CASCADE', onUpdate: 'CASCADE' },
  )
  @JoinColumn({ name: 'installation_id' })
  installation: Installations;

  @ManyToMany(() => Channels)
  @JoinTable({
    name: 'team_triage_rule_channels',
    joinColumn: {
      name: 'triage_rule_id',
      referencedColumnName: 'id',
    },
    inverseJoinColumn: {
      name: 'channel_id',
      referencedColumnName: 'id',
    },
  })
  triageChannels: Channels[];

  @Index()
  @ManyToOne(
    () => Organizations,
    (organization) => organization.id,
    { onDelete: 'CASCADE', onUpdate: 'CASCADE' },
  )
  @JoinColumn({ name: 'organization_id' })
  organization: Organizations;

  @CreateDateColumn({ type: 'timestamptz', name: 'created_at' })
  createdAt: Date;

  @UpdateDateColumn({ type: 'timestamptz', name: 'updated_at' })
  updatedAt: Date;

  @DeleteDateColumn({ type: 'timestamptz', name: 'deleted_at' })
  deletedAt: Date;
}
