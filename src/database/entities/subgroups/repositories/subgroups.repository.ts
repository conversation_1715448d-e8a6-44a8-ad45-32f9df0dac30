import { Injectable } from '@nestjs/common';
import { InjectRepository } from '@nestjs/typeorm';
import { Repository } from 'typeorm';
import { BaseAbstractRepository } from '../../../common/base-abstract-repository';
import { SlackSubgroups } from '../subgroups.entity';
import { SlackSubgroupsRepositoryInterface } from './subgroups-repository.interface';

/**
 * @class SlackSubgroupsRepository
 * @extends BaseAbstractRepository<SlackSubgroups>
 * @implements SlackSubgroupsRepositoryInterface
 * @description
 * Repository class for managing SlackSubgroups entities in the database.
 * This class extends the BaseAbstractRepository to inherit common CRUD operations
 * and implements the SlackSubgroupsRepositoryInterface for type safety and consistency.
 *
 * @remarks
 * This repository is designed to work with NestJS and TypeORM. It uses the
 * `@Injectable()` decorator to allow dependency injection in NestJS modules.
 *
 * @example
 * ```ts
 * // Inject and use in a service
 * constructor(private slackSubgroupsRepository: SlackSubgroupsRepository) {}
 *
 * async findSlackSubgroup(id: string): Promise<SlackSubgroups> {
 *   return this.slackSubgroupsRepository.findOneById(id);
 * }
 * ```
 *
 * @see {@link BaseAbstractRepository} for inherited methods
 * @see {@link SlackSubgroupsRepositoryInterface} for implemented methods
 * @see {@link SlackSubgroups} for the entity this repository manages
 */
@Injectable()
export class SlackSubgroupsRepository
  extends BaseAbstractRepository<SlackSubgroups>
  implements SlackSubgroupsRepositoryInterface
{
  constructor(
    @InjectRepository(SlackSubgroups)
    private readonly slackSubgroupsRepository: Repository<SlackSubgroups>,
  ) {
    super(slackSubgroupsRepository);
  }
}
