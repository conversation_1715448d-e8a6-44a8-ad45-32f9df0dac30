import { BaseInterfaceRepository } from '../../../common/base-repository-interface';
import { SlackSubgroups } from '../subgroups.entity';

/**
 * @interface SlackSubgroupsRepositoryInterface
 * @extends BaseInterfaceRepository<SlackSubgroups>
 * @description
 * Interface for the SlackSubgroups repository, extending the base repository interface.
 * This interface ensures type safety and consistency for SlackSubgroups-specific
 * repository operations.
 *
 * @remarks
 * By extending BaseInterfaceRepository, this interface inherits all the standard
 * CRUD operations defined for general entities and allows for the addition of
 * SlackSubgroups-specific methods if needed in the future.
 *
 * @see {@link BaseInterfaceRepository} for the base interface being extended
 * @see {@link SlackSubgroups} for the entity this interface is designed to work with
 */
export interface SlackSubgroupsRepositoryInterface
  extends BaseInterfaceRepository<SlackSubgroups> {}
