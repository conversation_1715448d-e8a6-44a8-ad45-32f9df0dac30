import {
  Column,
  CreateDateColumn,
  DeleteDateColumn,
  Entity,
  Index,
  JoinColumn,
  ManyToOne,
  PrimaryGeneratedColumn,
  UpdateDateColumn,
} from 'typeorm';
import { Installations } from '../installations/installations.entity';
import { Organizations } from '../organizations/organizations.entity';
import { Users } from '../users/users.entity';

@Entity('slack_subgroups')
@Index(
  'idx_unique_slack_sub_group_id_per_workspace',
  ['slackGroupId', 'installation'],
  { unique: true },
)
export class SlackSubgroups {
  @PrimaryGeneratedColumn({ type: 'bigint' })
  id: string;

  @Column({ type: 'varchar', name: 'slack_group_id' })
  slackGroupId: string;

  @Column({ type: 'jsonb', name: 'slack_group_dump', select: false })
  slackGroupDump: Record<string, any>;

  // The handle of the subgroup, this essentially is the name of the subgroup
  @Column({ type: 'varchar', name: 'slack_handle' })
  slackHandle: string;

  @Column({ type: 'varchar', name: 'description' })
  description: string;

  @Column({ type: 'boolean', name: 'is_external', default: false })
  isExternal: boolean;

  @ManyToOne(
    () => Users,
    (user) => user.subgroups,
    { nullable: true },
  )
  @JoinColumn({
    name: 'deleted_by',
    foreignKeyConstraintName: 'fk_slack_subgroup_deleted_by',
  })
  deletedBy: Users;

  @Column({ type: 'integer', name: 'users_count', default: 0 })
  usersCount: number;

  @ManyToOne(
    () => Users,
    (user) => user.subgroups,
    { nullable: true },
  )
  @JoinColumn([
    {
      name: 'created_by',
      foreignKeyConstraintName: 'fk_slack_subgroup_created_by',
      referencedColumnName: 'id',
    },
    // {
    //   name: 'created_by_slack_id',
    //   foreignKeyConstraintName: 'fk_slack_subgroup_created_by_slack_id',
    //   referencedColumnName: 'slackId',
    // },
  ])
  createdBy: Users;

  @ManyToOne(
    () => Users,
    (user) => user.subgroups,
    { nullable: true },
  )
  @JoinColumn([
    {
      name: 'updated_by',
      foreignKeyConstraintName: 'fk_slack_subgroup_updated_by',
      referencedColumnName: 'id',
    },
    // {
    //   name: 'updated_by_slack_id',
    //   foreignKeyConstraintName: 'fk_slack_subgroup_updated_by_slack_id',
    //   referencedColumnName: 'slackId',
    // },
  ])
  updatedBy: Users;

  @Column({ array: true, type: 'varchar', name: 'users', nullable: true })
  users: string[];

  @CreateDateColumn({ name: 'created_at', type: 'timestamptz' })
  createdAt: Date;

  @UpdateDateColumn({ name: 'updated_at', type: 'timestamptz' })
  updatedAt: Date;

  @DeleteDateColumn({ name: 'deleted_at', type: 'timestamptz' })
  deletedAt: Date;

  @Column({ type: 'integer', name: 'slack_deleted_at', nullable: true })
  slackDeletedAt: number;

  @ManyToOne(
    () => Installations,
    (installation) => installation.subgroups,
    { onDelete: 'CASCADE', onUpdate: 'CASCADE' },
  )
  @JoinColumn({
    name: 'installation_id',
    foreignKeyConstraintName: 'fk_slack_subgroup_installation_id',
  })
  installation: Installations;

  @ManyToOne(
    () => Organizations,
    (organization) => organization.subgroups,
    { onDelete: 'CASCADE', onUpdate: 'CASCADE' },
  )
  @JoinColumn({
    name: 'organization_id',
    foreignKeyConstraintName: 'fk_slack_subgroup_organization_id',
  })
  organization: Organizations;
}
