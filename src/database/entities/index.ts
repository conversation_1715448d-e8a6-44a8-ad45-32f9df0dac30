import { Bots } from './bots';
import { Channels } from './channels/channels.entity';
import { CustomerContacts } from './customer-contacts/customer-contacts.entity';
import { SlackEmojis } from './emojis';
import { Installations } from './installations/installations.entity';
import {
  ChannelTriageMappings,
  CommentThreadMappings,
  PlatformTeamsToChannelMappings,
} from './mappings';
import { CommentConversationMappings } from './mappings/comment-conversation-mappings.entity';
import { SubTeamToSubGroupsMapping } from './mappings/sub-team-to-sub-groups-mappings.entity';
import { TeamTriageRuleMapping } from './mappings/teams-triage-mappings.entity';
import { Organizations } from './organizations/organizations.entity';
import { Prompts } from './prompts/prompts.entity';
import { SettingsSchemas } from './settings/settings-schemas.entity';
import { Settings } from './settings/settings.entity';
import { SlackAuditLog } from './slack-audit-logs/slack-audit-logs.entity';
import { GroupedSlackMessages, SlackTriageMessages } from './slack-messages';
import { SlackMessages } from './slack-messages/slack-messages.entity';
import { SlackSubgroups } from './subgroups/subgroups.entity';
import { PlatformTeams } from './teams/teams.entity';
import { Users } from './users/users.entity';

// Export all entities
export const entities = [
  Users,
  SlackEmojis,
  GroupedSlackMessages,
  SlackAuditLog,
  Bots,
  Installations,
  Channels,
  Organizations,
  CommentThreadMappings,
  ChannelTriageMappings,
  PlatformTeamsToChannelMappings,
  PlatformTeams,
  CustomerContacts,
  SlackMessages,
  SlackTriageMessages,
  Settings,
  SettingsSchemas,
  SlackSubgroups,
  TeamTriageRuleMapping,
  SubTeamToSubGroupsMapping,
  Prompts,
  CommentConversationMappings,
];

// Export all entities as named exports
export {
  Channels,
  CommentThreadMappings,
  CustomerContacts,
  Installations,
  Organizations,
  PlatformTeams,
  PlatformTeamsToChannelMappings,
  Users,
  SlackMessages,
  ChannelTriageMappings,
  Bots,
  SlackTriageMessages,
  Settings,
  SettingsSchemas,
  SlackSubgroups,
  TeamTriageRuleMapping,
  SubTeamToSubGroupsMapping,
  Prompts,
  SlackAuditLog,
  GroupedSlackMessages,
  SlackEmojis,
  CommentConversationMappings,
};
