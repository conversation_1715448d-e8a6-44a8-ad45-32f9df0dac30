import {
  Column,
  CreateDateColumn,
  DeleteDateColumn,
  Entity,
  JoinColumn,
  ManyToOne,
  PrimaryGeneratedColumn,
  UpdateDateColumn,
} from 'typeorm';
import { Installations } from '../installations';
import { TriageRules } from '../mappings/teams-triage-mappings.entity';
import { Organizations } from '../organizations';
import { PlatformTeams } from '../teams';

/**
 * @description This interface defines the prompts that are required for the AI to function
 */
interface AIRequiredPrompts {
  /**
   * @description This prompt is used to detect if a ticket is urgent
   */
  ticket_detection: string;

  /**
   * @description This prompt is used to analyze the sentiment of a ticket
   */
  sentiment_analysis: string;

  /**
   * @description This prompt is used to detect if a ticket is urgent
   */
  urgency_detection: string;

  /**
   * @description This prompt is used to generate custom fields for a ticket
   */
  custom_fields: string;

  /**
   * @description This prompt is used to generate a title for a ticket
   */
  title_generation: string;

  /**
   * @description This prompt is used to generate a description for a ticket
   */
  description_generation: string;
}

@Entity('prompts')
export class Prompts {
  @PrimaryGeneratedColumn({ type: 'bigint' })
  id: string;

  @Column({ type: 'varchar', length: 255 })
  name: string;

  @Column({ type: 'jsonb' })
  prompts: AIRequiredPrompts;

  @Column('jsonb', { nullable: true, name: 'selection_rules' })
  selectionRules: TriageRules;

  @Column('boolean', { default: false, name: 'is_default' })
  isDefault: boolean;

  @ManyToOne(
    () => PlatformTeams,
    (platformTeam) => platformTeam.id,
    { onDelete: 'CASCADE', onUpdate: 'CASCADE' },
  )
  @JoinColumn({ name: 'platform_team_id' })
  platformTeam: PlatformTeams;

  @Column({ type: 'boolean', name: 'is_enabled', default: true })
  isEnabled: boolean;

  @ManyToOne(
    () => Installations,
    (installation) => installation.id,
    { onDelete: 'CASCADE', onUpdate: 'CASCADE' },
  )
  @JoinColumn({ name: 'installation_id' })
  installation: Installations;

  @ManyToOne(
    () => Organizations,
    (organization) => organization.id,
    { onDelete: 'CASCADE', onUpdate: 'CASCADE' },
  )
  @JoinColumn({ name: 'organization_id' })
  organization: Organizations;

  @CreateDateColumn({ type: 'timestamptz', name: 'created_at' })
  createdAt: Date;

  @UpdateDateColumn({ type: 'timestamptz', name: 'updated_at' })
  updatedAt: Date;

  @DeleteDateColumn({ type: 'timestamptz', name: 'deleted_at', nullable: true })
  deletedAt: Date;
}
