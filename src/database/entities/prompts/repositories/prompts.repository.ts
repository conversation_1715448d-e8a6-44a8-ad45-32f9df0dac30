import { Injectable } from '@nestjs/common';
import { InjectRepository } from '@nestjs/typeorm';
import { Repository } from 'typeorm';
import { BaseAbstractRepository } from '../../../common/base-abstract-repository';
import { Prompts } from '../prompts.entity';
import { PromptsRepositoryInterface } from './prompts.interface';

/**
 * @class PromptsRepository
 * @extends BaseAbstractRepository<Prompts>
 * @implements PromptsRepositoryInterface
 * @description
 * Repository class for managing Prompts entities in the database.
 * This class extends the BaseAbstractRepository to inherit common CRUD operations
 * and implements the PromptsRepositoryInterface for type safety and consistency.
 *
 * @remarks
 * This repository is designed to work with NestJS and TypeORM. It uses the
 * `@Injectable()` decorator to allow dependency injection in NestJS modules.
 *
 * @example
 * ```ts
 * // Inject and use in a service
 * constructor(private promptsRepository: PromptsRepository) {}
 *
 * async findPrompts(id: string): Promise<Prompts> {
 *   return this.promptsRepository.findOneById(id);
 * }
 * ```
 *
 * @see {@link BaseAbstractRepository} for inherited methods
 * @see {@link PromptsRepositoryInterface} for implemented methods
 * @see {@link Prompts} for the entity this repository manages
 */
@Injectable()
export class PromptsRepository
  extends BaseAbstractRepository<Prompts>
  implements PromptsRepositoryInterface
{
  constructor(
    @InjectRepository(Prompts)
    private readonly promptsRepository: Repository<Prompts>,
  ) {
    super(promptsRepository);
  }
}
