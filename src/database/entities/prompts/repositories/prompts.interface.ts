import { BaseInterfaceRepository } from '../../../common/base-repository-interface';
import { Prompts } from '../prompts.entity';

/**
 * @interface PromptsRepositoryInterface
 * @extends BaseInterfaceRepository<Prompts>
 * @description
 * Interface for the Prompts repository, extending the base repository interface.
 * This interface ensures type safety and consistency for Prompts-specific
 * repository operations.
 *
 * @remarks
 * By extending BaseInterfaceRepository, this interface inherits all the standard
 * CRUD operations defined for general entities and allows for the addition of
 * Channels-specific methods if needed in the future.
 *
 * @see {@link BaseInterfaceRepository} for the base interface being extended
 * @see {@link Prompts} for the entity this interface is designed to work with
 */
export interface PromptsRepositoryInterface
  extends BaseInterfaceRepository<Prompts> {}
