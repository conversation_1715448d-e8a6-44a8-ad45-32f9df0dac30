import { Injectable } from '@nestjs/common';
import { InjectRepository } from '@nestjs/typeorm';
import { Repository } from 'typeorm';
import { BaseAbstractRepository } from '../../../common/base-abstract-repository';
import { CustomerContacts } from '../customer-contacts.entity';
import { CustomerContactRepositoryInterface } from './customer-contact.interface';

/**
 * @class CustomerContactRepository
 * @extends BaseAbstractRepository<CustomerContacts>
 * @implements CustomerContactRepositoryInterface
 * @description
 * Repository class for managing CustomerContacts entities in the database.
 * This class extends the BaseAbstractRepository to inherit common CRUD operations
 * and implements the CustomerContactRepositoryInterface for type safety and consistency.
 *
 * @remarks
 * This repository is designed to work with NestJS and TypeORM. It uses the
 * `@Injectable()` decorator to allow dependency injection in NestJS modules.
 *
 * @example
 * ```ts
 * // Inject and use in a service
 * constructor(private customerContactRepository: CustomerContactRepository) {}
 *
 * async findCustomerContact(id: string): Promise<CustomerContacts> {
 *   return this.customerContactRepository.findOneById(id);
 * }
 * ```
 *
 * @see {@link BaseAbstractRepository} for inherited methods
 * @see {@link CustomerContactRepositoryInterface} for implemented methods
 * @see {@link CustomerContacts} for the entity this repository manages
 */
@Injectable()
export class CustomerContactRepository
  extends BaseAbstractRepository<CustomerContacts>
  implements CustomerContactRepositoryInterface
{
  constructor(
    @InjectRepository(CustomerContacts)
    private readonly customerContactRepository: Repository<CustomerContacts>,
  ) {
    super(customerContactRepository);
  }
} 
