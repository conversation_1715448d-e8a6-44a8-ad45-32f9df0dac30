import { BaseInterfaceRepository } from '../../../common/base-repository-interface';
import { CustomerContacts } from '../customer-contacts.entity';

/**
 * @interface CustomerContactRepositoryInterface
 * @extends BaseInterfaceRepository<CustomerContacts>
 * @description
 * Interface for the CustomerContact repository, extending the base repository interface.
 * This interface ensures type safety and consistency for CustomerContact-specific
 * repository operations.
 *
 * @remarks
 * By extending BaseInterfaceRepository, this interface inherits all the standard
 * CRUD operations defined for general entities and allows for the addition of
 * CustomerContact-specific methods if needed in the future.
 *
 * @see {@link BaseInterfaceRepository} for the base interface being extended
 * @see {@link CustomerContacts} for the entity this interface is designed to work with
 */
export interface CustomerContactRepositoryInterface
  extends BaseInterfaceRepository<CustomerContacts> {}
