import {
  Column,
  <PERSON>tity,
  Index,
  JoinColumn,
  ManyToMany,
  ManyToOne,
  PrimaryGeneratedColumn,
} from 'typeorm';
import type { Person } from '../../interfaces/person.interface';
import { Channels } from '../channels/channels.entity';
import { Installations } from '../installations/installations.entity';
import { Organizations } from '../organizations/organizations.entity';

interface UserImages {
  image_original: string;
  image_512: string;
  image_192: string;
  image_72: string;
  image_48: string;
  image_32: string;
  image_24: string;
}

interface PlatformDump {
  customerContactId: string;
  customObjectRecordIds: string[];
}

/**
 * NOTE: The Customer-Contact entity is same as the {@link Users} entity, however it is used to store customer contacts
 * for a given installation. The reason why they are kept separate is to maintain not only a separation but also the core idea
 * of them being from a different Slack Workspace. Note that generally the Customer-Contacts are External Customer or Users
 * connected to our Slack Workspace using Slack Connect.
 */

@Entity('slack_customer_contacts')
@Index('idx_unique_customer_contact_slack_id', ['slackId', 'installation'], {
  unique: true,
})
export class CustomerContacts implements Person {
  @PrimaryGeneratedColumn({ type: 'bigint' })
  id: string;

  @Column({ type: 'jsonb', name: 'user_dump', insert: true })
  userDump: Record<string, any>;

  @Index()
  @Column({ type: 'varchar', name: 'slack_id' })
  slackId: string;

  @Column({ type: 'boolean', name: 'slack_deleted', default: false })
  slackDeleted: boolean;

  @Column({ type: 'varchar', name: 'name' })
  name: string;

  @Column({ type: 'varchar', name: 'real_name', nullable: true })
  realName?: string | null;

  @Column({ type: 'varchar', name: 'display_name', nullable: true })
  displayName?: string | null;

  @ManyToOne(
    () => Installations,
    (installation) => installation.users,
  )
  @JoinColumn({
    name: 'installation_id',
    foreignKeyConstraintName: 'fk_slack_user_installation_id',
  })
  installation: Installations;

  @ManyToOne(
    () => Organizations,
    (organization) => organization.users,
  )
  @JoinColumn({
    name: 'organization_id',
    foreignKeyConstraintName: 'fk_slack_user_organization_id',
  })
  organization: Organizations;

  @Column({ type: 'varchar', name: 'tz', nullable: true })
  tz?: string | null;

  @Column({ type: 'varchar', name: 'tz_label', nullable: true })
  tzLabel?: string | null;

  @Column({ type: 'boolean', name: 'is_admin', default: false })
  isAdmin: boolean;

  @Column({ type: 'boolean', name: 'is_owner', default: false })
  isOwner: boolean;

  @Column({ type: 'boolean', name: 'is_restricted', default: false })
  isRestricted: boolean;

  @Column({ type: 'boolean', name: 'is_ultra_restricted', default: false })
  isUltraRestricted: boolean;

  @Column({ type: 'boolean', name: 'is_bot', default: false })
  isBot: boolean;

  @Column({ type: 'varchar', name: 'user_title', nullable: true })
  userTitle?: string | null;

  @Column({ type: 'varchar', name: 'slack_profile_real_name', nullable: true })
  slackProfileRealName?: string | null;

  @Column({
    type: 'varchar',
    name: 'slack_profile_display_name',
    nullable: true,
  })
  slackProfileDisplayName?: string | null;

  @Column({ type: 'varchar', name: 'slack_profile_phone', nullable: true })
  slackProfilePhone?: string | null;

  @Column({ type: 'varchar', name: 'slack_status_text', nullable: true })
  slackStatusText?: string | null;

  @Column({ type: 'varchar', name: 'slack_status_emoji', nullable: true })
  slackStatusEmoji?: string | null;

  @Column({ type: 'varchar', name: 'slack_profile_email', nullable: true })
  slackProfileEmail?: string | null;

  @Column({ type: 'jsonb', name: 'images', nullable: true })
  images?: Partial<UserImages> | null;

  @Column({ type: 'jsonb', name: 'platform_dump', nullable: true })
  platformDump?: PlatformDump | null;

  @ManyToMany(
    () => Channels,
    (channel) => channel.customerContacts,
  )
  channels: Channels[];

  userHasEmail(): boolean {
    return (
      typeof this.slackProfileEmail === 'string' &&
      this.slackProfileEmail.includes('@')
    );
  }

  getUserAvatar(): string {
    return (
      this.images?.image_original ??
      this.images?.image_512 ??
      this.images?.image_192 ??
      this.images?.image_72 ??
      this.images?.image_48 ??
      this.images?.image_32 ??
      this.images?.image_24 ??
      ''
    );
  }
}
