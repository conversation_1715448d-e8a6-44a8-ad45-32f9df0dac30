import {
  Column,
  CreateDateColumn,
  DeleteDateColumn,
  Entity,
  Index,
  JoinColumn,
  ManyToOne,
  PrimaryGeneratedColumn,
  UpdateDateColumn,
} from 'typeorm';
import {
  AuditLogEntityType,
  AuditLogOp,
  AuditLogVisibility,
} from '../../constants';
import { Installations } from '../installations';
import { Organizations } from '../organizations';
import { PlatformTeams } from '../teams';

/**
 * An updated field in the audit log
 */
export interface AuditLogUpdatedField {
  /**
   * The field that was updated
   */
  field?: string;

  /**
   * The previous value of the field
   */
  previousValue?: string;

  /**
   * The new value of the field
   */
  updatedToValue?: string;

  /**
   * The description of the field
   */
  description?: string;
}

/**
 * The metadata for the audit log
 */
export interface AuditLogMetadata {
  /**
   * The fields that were updated
   */
  updatedFields?: Array<AuditLogUpdatedField>;

  /**
   * The dump of the event log
   */
  eventLogDump?: Record<string, unknown>;
}

@Entity('slack_audit_logs')
@Index('partial_slack_audit_log_team_organization', ['team', 'organization'])
export class SlackAuditLog {
  @PrimaryGeneratedColumn({ type: 'bigint' })
  id: string;

  @Index()
  @ManyToOne(() => Organizations, { onDelete: 'CASCADE' })
  @JoinColumn({ name: 'organization_id' })
  organization: Organizations;

  @Index()
  @ManyToOne(() => Installations, { onDelete: 'CASCADE' })
  @JoinColumn({ name: 'installation_id' })
  installation: Installations;

  @Index()
  @ManyToOne(() => PlatformTeams, { nullable: true, onDelete: 'SET NULL' })
  @JoinColumn({ name: 'team_id' })
  team: PlatformTeams;

  @Index()
  @Column({
    type: 'enum',
    nullable: true,
    enum: AuditLogEntityType,
    name: 'entity_type',
  })
  entityType: AuditLogEntityType; // The type of the entity that the audit log is about

  @Index()
  @Column({ type: 'varchar', length: 255, nullable: true })
  externalId?: string;

  @Index()
  @Column({ type: 'varchar', length: 255, nullable: true })
  slackTs: string;

  @Index()
  @Column({ type: 'varchar', length: 255, nullable: true })
  threadTs?: string;

  @Index()
  @Column({ type: 'varchar', length: 255 })
  eventTs: string;

  @Index()
  @Column({ type: 'varchar', length: 255 })
  activity: string;

  @Column({ type: 'text', nullable: true })
  description?: string; // This can include detailed information about the activity

  @Index()
  @Column({ type: 'varchar', length: 255, nullable: true })
  activityPerformedBy?: string;

  @Column({ type: 'boolean', default: false, name: 'is_automated' })
  isAutomated: boolean; // Whether the activity was automated or not

  @Index()
  @Column({
    type: 'enum',
    enum: AuditLogVisibility,
    default: AuditLogVisibility.ORGANIZATION,
  })
  visibility: AuditLogVisibility;

  @Index() // For filtering activities by type
  @Column({
    type: 'enum',
    name: 'operation',
    enum: AuditLogOp,
  })
  op: AuditLogOp;

  @Column({ type: 'jsonb', nullable: true })
  metadata: AuditLogMetadata;

  @Index() // For filtering activities for this ticket
  @CreateDateColumn({ name: 'created_at', type: 'timestamptz' })
  createdAt: Date;

  @UpdateDateColumn({ name: 'updated_at', type: 'timestamptz' })
  updatedAt: Date;

  @DeleteDateColumn({ name: 'deleted_at', type: 'timestamptz', nullable: true })
  deletedAt: Date;
}
