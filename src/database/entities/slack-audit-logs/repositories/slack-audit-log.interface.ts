import { DeepPartial } from 'typeorm';
import { TransactionContext } from '../../../common';
import { BaseInterfaceRepository } from '../../../common/base-repository-interface';
import { SlackAuditLog } from '../slack-audit-logs.entity';

/**
 * @interface SlackAuditLogRepositoryInterface
 * @extends BaseInterfaceRepository<SlackAuditLog>
 * @description
 * Interface for the SlackAuditLog repository, extending the base repository interface.
 * This interface ensures type safety and consistency for SlackAuditLog-specific
 * repository operations.
 *
 * @remarks
 * By extending BaseInterfaceRepository, this interface inherits all the standard
 * CRUD operations defined for general entities and allows for the addition of
 * SlackAuditLog-specific methods if needed in the future.
 *
 * @see {@link BaseInterfaceRepository} for the base interface being extended
 * @see {@link SlackAuditLog} for the entity this interface is designed to work with
 */
export interface SlackAuditLogRepositoryInterface
  extends BaseInterfaceRepository<SlackAuditLog> {
  recordAuditLog(auditLog: DeepPartial<SlackAuditLog>): Promise<void>;
  recordAuditLog(
    auditLog: DeepPartial<SlackAuditLog>,
    txnContext: TransactionContext,
  ): Promise<void>;
}
