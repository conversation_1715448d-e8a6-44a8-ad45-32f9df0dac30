import { Injectable } from '@nestjs/common';
import { InjectRepository } from '@nestjs/typeorm';
import { DeepPartial, Repository } from 'typeorm';
import { TransactionContext } from '../../../common';
import { BaseAbstractRepository } from '../../../common/base-abstract-repository';
import { SlackAuditLog } from '../slack-audit-logs.entity';
import { SlackAuditLogRepositoryInterface } from './slack-audit-log.interface';

/**
 * @class SlackAuditLogRepository
 * @extends BaseAbstractRepository<SlackAuditLog>
 * @implements SlackAuditLogRepositoryInterface
 * @description
 * Repository class for managing SlackAuditLog entities in the database.
 * This class extends the BaseAbstractRepository to inherit common CRUD operations
 * and implements the SlackAuditLogRepositoryInterface for type safety and consistency.
 *
 * @remarks
 * This repository is designed to work with NestJS and TypeORM. It uses the
 * `@Injectable()` decorator to allow dependency injection in NestJS modules.
 *
 * @example
 * ```ts
 * // Inject and use in a service
 * constructor(private slackAuditLogRepository: SlackAuditLogRepository) {}
 *
 * async findSlackAuditLog(id: string): Promise<SlackAuditLog> {
 *   return this.slackAuditLogRepository.findOneById(id);
 * }
 * ```
 *
 * @see {@link BaseAbstractRepository} for inherited methods
 * @see {@link SlackAuditLogRepositoryInterface} for implemented methods
 * @see {@link SlackAuditLog} for the entity this repository manages
 */
@Injectable()
export class SlackAuditLogRepository
  extends BaseAbstractRepository<SlackAuditLog>
  implements SlackAuditLogRepositoryInterface
{
  constructor(
    @InjectRepository(SlackAuditLog)
    private readonly slackAuditLogRepository: Repository<SlackAuditLog>,
  ) {
    super(slackAuditLogRepository);
  }

  async recordAuditLog(
    auditLog: DeepPartial<SlackAuditLog>,
    txnContext?: TransactionContext,
  ): Promise<void> {
    // Record this activity
    try {
      if (txnContext) {
        await this.saveWithTxn(txnContext, {
          eventTs: auditLog.eventTs,
          activityPerformedBy: auditLog.activityPerformedBy,
          activity: auditLog.activity,
          description: auditLog.description,

          // Base
          op: auditLog.op,
          visibility: auditLog.visibility,
          installation: { id: auditLog.installation.id },
          organization: { id: auditLog.organization.id },
        });
      } else {
        await this.slackAuditLogRepository.save({
          eventTs: auditLog.eventTs,
          activityPerformedBy: auditLog.activityPerformedBy,
          activity: auditLog.activity,
          description: auditLog.description,

          // Base
          op: auditLog.op,
          visibility: auditLog.visibility,
          installation: { id: auditLog.installation.id },
          organization: { id: auditLog.organization.id },
        });
      }
    } catch (error) {
      console.error(
        '[SlackAuditLogRepository] Failed to save audit log',
        error,
      );
    }
  }
}
