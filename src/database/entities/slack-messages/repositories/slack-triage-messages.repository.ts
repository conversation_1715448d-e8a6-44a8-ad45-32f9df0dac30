import { Injectable } from '@nestjs/common';
import { InjectRepository } from '@nestjs/typeorm';
import { Repository } from 'typeorm';
import { BaseAbstractRepository } from '../../../common/base-abstract-repository';
import { SlackTriageMessages } from '../slack-triage-messages.entity';
import { SlackTriageMessagesRepositoryInterface } from './slack-triage-messages.interface';

/**
 * @class SlackTriageMessagesRepository
 * @extends BaseAbstractRepository<SlackTriageMessages>
 * @implements SlackTriageMessagesRepositoryInterface
 * @description
 * Repository class for managing SlackTriageMessages entities in the database.
 * This class extends the BaseAbstractRepository to inherit common CRUD operations
 * and implements the SlackTriageMessagesRepositoryInterface for type safety and consistency.
 *
 * @remarks
 * This repository is designed to work with NestJS and TypeORM. It uses the
 * `@Injectable()` decorator to allow dependency injection in NestJS modules.
 *
 * @example
 * ```ts
 * // Inject and use in a service
 * constructor(private slackTriageMessagesRepository: SlackTriageMessagesRepository) {}
 *
 * async findSlackTriageMessages(id: string): Promise<SlackTriageMessages> {
 *   return this.slackTriageMessagesRepository.findOneById(id);
 * }
 * ```
 *
 * @see {@link BaseAbstractRepository} for inherited methods
 * @see {@link SlackTriageMessagesRepositoryInterface} for implemented methods
 * @see {@link SlackTriageMessages} for the entity this repository manages
 */
@Injectable()
export class SlackTriageMessagesRepository
  extends BaseAbstractRepository<SlackTriageMessages>
  implements SlackTriageMessagesRepositoryInterface
{
  constructor(
    @InjectRepository(SlackTriageMessages)
    private readonly slackTriageMessagesRepository: Repository<SlackTriageMessages>,
  ) {
    super(slackTriageMessagesRepository);
  }
}
