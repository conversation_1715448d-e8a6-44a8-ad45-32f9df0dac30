import { BaseInterfaceRepository } from '../../../common/base-repository-interface';
import { SlackTriageMessages } from '../slack-triage-messages.entity';

/**
 * @interface SlackTriageMessagesRepositoryInterface
 * @extends BaseInterfaceRepository<SlackTriageMessages>
 * @description
 * Interface for the SlackTriageMessages repository, extending the base repository interface.
 * This interface ensures type safety and consistency for SlackTriageMessages-specific
 * repository operations.
 *
 * @remarks
 * By extending BaseInterfaceRepository, this interface inherits all the standard
 * CRUD operations defined for general entities and allows for the addition of
 * SlackTriageMessages-specific methods if needed in the future.
 *
 * @see {@link BaseInterfaceRepository} for the base interface being extended
 * @see {@link SlackTriageMessages} for the entity this interface is designed to work with
 */
export interface SlackTriageMessagesRepositoryInterface
  extends BaseInterfaceRepository<SlackTriageMessages> {}
