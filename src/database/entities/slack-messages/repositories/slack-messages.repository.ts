import { Injectable } from '@nestjs/common';
import { InjectRepository } from '@nestjs/typeorm';
import { Repository } from 'typeorm';
import { BaseAbstractRepository } from '../../../common/base-abstract-repository';
import { SlackMessages } from '../slack-messages.entity';
import { SlackMessagesRepositoryInterface } from './slack-messages.interface';

/**
 * @class SlackMessagesRepository
 * @extends BaseAbstractRepository<SlackMessages>
 * @implements SlackMessagesRepositoryInterface
 * @description
 * Repository class for managing SlackMessages entities in the database.
 * This class extends the BaseAbstractRepository to inherit common CRUD operations
 * and implements the SlackMessagesRepositoryInterface for type safety and consistency.
 *
 * @remarks
 * This repository is designed to work with NestJS and TypeORM. It uses the
 * `@Injectable()` decorator to allow dependency injection in NestJS modules.
 *
 * @example
 * ```ts
 * // Inject and use in a service
 * constructor(private slackMessagesRepository: SlackMessagesRepository) {}
 *
 * async findSlackMessages(id: string): Promise<SlackMessages> {
 *   return this.slackMessagesRepository.findOneById(id);
 * }
 * ```
 *
 * @see {@link BaseAbstractRepository} for inherited methods
 * @see {@link SlackMessagesRepositoryInterface} for implemented methods
 * @see {@link SlackMessages} for the entity this repository manages
 */
@Injectable()
export class SlackMessagesRepository
  extends BaseAbstractRepository<SlackMessages>
  implements SlackMessagesRepositoryInterface
{
  constructor(
    @InjectRepository(SlackMessages)
    private readonly slackMessagesRepository: Repository<SlackMessages>,
  ) {
    super(slackMessagesRepository);
  }
}
