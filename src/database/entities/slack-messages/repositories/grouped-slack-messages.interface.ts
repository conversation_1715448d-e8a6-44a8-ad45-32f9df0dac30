import { BaseInterfaceRepository } from '../../../common/base-repository-interface';
import { GroupedSlackMessages } from '../grouped-slack-messages.entity';

/**
 * @interface GroupedSlackMessagesRepositoryInterface
 * @extends BaseInterfaceRepository<GroupedSlackMessages>
 * @description
 * Interface for the GroupedSlackMessages repository, extending the base repository interface.
 * This interface ensures type safety and consistency for GroupedSlackMessages-specific
 * repository operations.
 *
 * @remarks
 * By extending BaseInterfaceRepository, this interface inherits all the standard
 * CRUD operations defined for general entities and allows for the addition of
 * GroupedSlackMessages-specific methods if needed in the future.
 *
 * @see {@link BaseInterfaceRepository} for the base interface being extended
 * @see {@link GroupedSlackMessages} for the entity this interface is designed to work with
 */
export interface GroupedSlackMessagesRepositoryInterface
  extends BaseInterfaceRepository<GroupedSlackMessages> {}
