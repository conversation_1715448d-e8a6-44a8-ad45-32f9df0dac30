import { BaseInterfaceRepository } from '../../../common/base-repository-interface';
import { SlackMessages } from '../slack-messages.entity';

/**
 * @interface SlackMessagesRepositoryInterface
 * @extends BaseInterfaceRepository<SlackMessages>
 * @description
 * Interface for the SlackMessages repository, extending the base repository interface.
 * This interface ensures type safety and consistency for SlackMessages-specific
 * repository operations.
 *
 * @remarks
 * By extending BaseInterfaceRepository, this interface inherits all the standard
 * CRUD operations defined for general entities and allows for the addition of
 * SlackMessages-specific methods if needed in the future.
 *
 * @see {@link BaseInterfaceRepository} for the base interface being extended
 * @see {@link SlackMessages} for the entity this interface is designed to work with
 */
export interface SlackMessagesRepositoryInterface
  extends BaseInterfaceRepository<SlackMessages> {}
