import {
  Column,
  CreateDateColumn,
  DeleteDateColumn,
  Entity,
  Index,
  JoinColumn,
  ManyToOne,
  PrimaryGeneratedColumn,
  UpdateDateColumn,
} from 'typeorm';
import { Channels } from '../channels/channels.entity';
import { Installations } from '../installations/installations.entity';
import { Organizations } from '../organizations/organizations.entity';
import { SlackMessages } from './slack-messages.entity';

@Entity('slack_triage_messages')
export class SlackTriageMessages {
  @PrimaryGeneratedColumn({ type: 'bigint' })
  id: string;

  @Column({ type: 'varchar', name: 'slack_message_ts' })
  slackMessageTs: string;

  @Index('idx_tmt_platform_thread_id')
  @Column({ type: 'varchar', name: 'platform_thread_id', nullable: true })
  platformThreadId: string;

  @Column({ type: 'boolean', name: 'should_update_thread', default: true })
  shouldUpdateThread: boolean;

  @ManyToOne(() => SlackMessages, {
    nullable: false,
    onDelete: 'CASCADE',
    onUpdate: 'CASCADE',
  })
  @JoinColumn({
    name: 'slack_request_message_id',
  })
  slackRequestMessage: SlackMessages;

  @ManyToOne(() => Channels, {
    nullable: false,
    onDelete: 'CASCADE',
    onUpdate: 'CASCADE',
  })
  @JoinColumn({
    name: 'channel_id',
  })
  channel: Channels;

  @ManyToOne(() => Installations, {
    nullable: false,
    onDelete: 'CASCADE',
    onUpdate: 'CASCADE',
  })
  @JoinColumn({
    name: 'installation_id',
    foreignKeyConstraintName: 'fk_request_installation_id',
  })
  installation: Installations;

  @ManyToOne(
    () => Organizations,
    (organization) => organization.channels,
    { onDelete: 'CASCADE', onUpdate: 'CASCADE' },
  )
  @JoinColumn({
    name: 'organization_id',
    foreignKeyConstraintName: 'fk_request_organization_id',
  })
  organization: Organizations;

  @Column({ type: 'jsonb', name: 'metadata', nullable: true })
  metadata?: Record<string, any>;

  @CreateDateColumn({ type: 'timestamptz', name: 'created_at', nullable: true })
  createdAt?: Date;

  @UpdateDateColumn({ type: 'timestamptz', name: 'updated_at', nullable: true })
  updatedAt?: Date;

  @DeleteDateColumn({ type: 'timestamptz', name: 'deleted_at', nullable: true })
  deletedAt?: Date;
}
