import {
  Check,
  Column,
  CreateDateColumn,
  DeleteDateColumn,
  Entity,
  Index,
  JoinColumn,
  ManyToOne,
  PrimaryGeneratedColumn,
  UpdateDateColumn,
} from 'typeorm';
import { Channels } from '../channels/channels.entity';
import { Installations } from '../installations/installations.entity';
import { Organizations } from '../organizations/organizations.entity';

interface SlackMessageMetadata {
  customer: {
    email: string;
    name: string;
  };
  ticket_details: {
    status: string;
    statusId: string;
    priority: string;
    priorityId: string;
  };
  [key: string]: any;
}

export const CHK_PLATFORM_OR_SLACK_TS_DEFINED =
  'CHK_PLATFORM_OR_SLACK_TS_DEFINED';

@Entity('slack_messages')
@Index('idx_slack_message_platform_ticket_id', [
  'platformTicketId',
  'organization',
])
@Check(
  CHK_PLATFORM_OR_SLACK_TS_DEFINED,
  'platform_ticket_id IS NOT NULL OR slack_message_ts IS NOT NULL',
)
export class SlackMessages {
  @PrimaryGeneratedColumn({ type: 'bigint' })
  id: string;

  @Index()
  @Column({ type: 'varchar', name: 'platform_ticket_id', nullable: true })
  platformTicketId: string;

  @Index('idx_slack_message_platform_comment_id')
  @Column({ type: 'varchar', name: 'platform_comment_id', nullable: true })
  platformCommentId: string;

  @Column({ type: 'varchar', name: 'slack_message_ts', nullable: true })
  slackMessageTs: string;

  @Column({ type: 'varchar', name: 'slack_message_thread_ts', nullable: true })
  slackMessageThreadTs: string;

  @Column({ type: 'varchar', name: 'slack_permalink', nullable: true })
  slackPermalink: string;

  // This property indicates this slack message was made directly from the platform
  // this message exists as a placeholder so that triage messages can be created
  // and linked to this message, the `platformTicketId` helps identify this.
  @Column({ type: 'boolean', name: 'is_independent', default: false })
  isIndependent: boolean;

  @ManyToOne(() => Channels, {
    nullable: true,
  })
  @JoinColumn({
    name: 'channel_id',
  })
  channel: Channels;

  @Column({ type: 'varchar', name: 'slack_user_id', nullable: true })
  slackUserId: string;

  @ManyToOne(() => Installations, {
    nullable: false,
  })
  @JoinColumn({
    name: 'installation_id',
    foreignKeyConstraintName: 'fk_request_installation_id',
  })
  installation: Installations;

  @ManyToOne(
    () => Organizations,
    (organization) => organization.channels,
  )
  @JoinColumn({
    name: 'organization_id',
    foreignKeyConstraintName: 'fk_request_organization_id',
  })
  organization: Organizations;

  @Column({ type: 'jsonb', name: 'metadata', nullable: true })
  metadata: SlackMessageMetadata;

  @CreateDateColumn({ type: 'timestamptz', name: 'created_at' })
  createdAt: Date;

  @Column({
    type: 'timestamptz',
    name: 'slack_event_created_at',
    nullable: true,
  })
  slackEventCreatedAt: Date;

  @UpdateDateColumn({ type: 'timestamptz', name: 'updated_at' })
  updatedAt: Date;

  @DeleteDateColumn({ type: 'timestamptz', name: 'deleted_at' })
  deletedAt: Date;
}
