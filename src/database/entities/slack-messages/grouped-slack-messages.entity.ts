import {
  Column,
  CreateDateColumn,
  DeleteDateColumn,
  Entity,
  Index,
  JoinColumn,
  ManyToOne,
  PrimaryGeneratedColumn,
  UpdateDateColumn,
} from 'typeorm';
import { Channels } from '../channels/channels.entity';
import { Installations } from '../installations/installations.entity';
import { Organizations } from '../organizations/organizations.entity';
import { SlackMessages } from './slack-messages.entity';

@Entity('grouped_slack_messages')
export class GroupedSlackMessages {
  @PrimaryGeneratedColumn({ type: 'bigint' })
  id: string;

  @Index()
  @Column({ type: 'varchar', name: 'platform_ticket_id' })
  platformTicketId: string;

  @Index()
  @Column({ type: 'varchar', name: 'parent_comment_id' })
  parentCommentId: string;

  @Index()
  @Column({ type: 'varchar', name: 'slack_message_ts' })
  slackMessageTs: string;

  @ManyToOne(() => SlackMessages, {
    nullable: true,
    onDelete: 'CASCADE',
    onUpdate: 'CASCADE',
  })
  @JoinColumn({
    name: 'grouped_into_message_id',
  })
  groupedIntoMessage: SlackMessages;

  @ManyToOne(() => Channels, {
    nullable: true,
    onDelete: 'CASCADE',
    onUpdate: 'CASCADE',
  })
  @JoinColumn({
    name: 'channel_id',
  })
  channel: Channels;

  @ManyToOne(() => Installations, {
    nullable: false,
    onDelete: 'CASCADE',
    onUpdate: 'CASCADE',
  })
  @JoinColumn({
    name: 'installation_id',
    foreignKeyConstraintName: 'fk_request_installation_id',
  })
  installation: Installations;

  @ManyToOne(
    () => Organizations,
    (organization) => organization.channels,
    { onDelete: 'CASCADE', onUpdate: 'CASCADE' },
  )
  @JoinColumn({
    name: 'organization_id',
    foreignKeyConstraintName: 'fk_request_organization_id',
  })
  organization: Organizations;

  @CreateDateColumn({ type: 'timestamptz', name: 'created_at' })
  createdAt: Date;

  @UpdateDateColumn({ type: 'timestamptz', name: 'updated_at' })
  updatedAt: Date;

  @DeleteDateColumn({ type: 'timestamptz', name: 'deleted_at' })
  deletedAt: Date;
}
