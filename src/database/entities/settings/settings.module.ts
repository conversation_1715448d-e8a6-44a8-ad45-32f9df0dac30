import { Module } from '@nestjs/common';
import { TypeOrmModule } from '@nestjs/typeorm';
import { SettingsSchemasRepository } from './repositories/setting-schemas.repository';
import { SettingsRepository } from './repositories/settings.repository';
import { SettingsSchemas } from './settings-schemas.entity';
import { Settings } from './settings.entity';

@Module({
  imports: [TypeOrmModule.forFeature([Settings, SettingsSchemas])],
  providers: [SettingsRepository, SettingsSchemasRepository],
  exports: [SettingsRepository, SettingsSchemasRepository],
})
export class SettingsModule {}
