// ! This is the schema for how the settings are stored in JSONB column

// Supported types
export enum SettingType {
  STRING = 'string',
  NUMBER = 'number',
  BOOLEAN = 'boolean',
  ARRAY = 'array',
  ARRAY_OF_STRINGS = 'array_of_strings',
  ARRAY_OF_NUMBERS = 'array_of_numbers',
  JSONB = 'jsonb',
}

export interface SlackAppSettings {
  /**
   * Time in minutes within which slack messages will be grouped together into a ticket.
   */
  conversation_window: number;

  /**
   * Automatically create tickets from detected slack conversations.
   */
  automatic_tickets: boolean;

  /**
   * Enable the "/" command to create tickets.
   */
  slash_commands: boolean;

  /**
   * The emoji to use to create a ticket.
   */
  emoji_to_create_ticket: string;

  /**
   * Require users to fill out a form before creating a ticket.
   */
  require_form: boolean;

  /**
   * Create a ticket when the bot is mentioned.
   */
  thena_bot_tagging_enabled: boolean;

  /**
   * Enable the `/ticket` command to create tickets.
   */
  ticket_command: boolean;

  /**
   * Enable ticket creation via reaction.
   */
  enable_ticket_creation_via_reaction: boolean;

  /**
   * Enable emoji-to-action mappings.
   */
  enable_emoji_actions: boolean;

  /**
   * Enable action-to-emoji functionality (when an action is performed, add the emoji).
   */
  enable_action_to_emoji: boolean;

  /**
   * Enable emoji-to-action functionality (when an emoji is added, perform the action).
   */
  enable_emoji_to_action: boolean;

  /**
   * Enable extended thinking mode for AI models that support it.
   */
  ai_enable_extended_thinking: boolean;

  /**
   * The AI model to use for ticket creation.
   */
  ai_model: string;

  /**
   * The temperature to use for the AI model.
   */
  ai_temperature: number;

  /**
   * The maximum number of tokens to use for the AI model.
   */
  ai_max_tokens: number;

  /**
   * The AI provider to use (openai, claude, grok, etc).
   */
  ai_provider: string;

  /**
   * List of form IDs that should be displayed to users in Slack.
   */
  selected_forms: string[];
}
