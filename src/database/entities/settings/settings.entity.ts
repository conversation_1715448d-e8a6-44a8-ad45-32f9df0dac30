import {
  Column,
  CreateDateColumn,
  Entity,
  Index,
  JoinColumn,
  ManyToOne,
  PrimaryGeneratedColumn,
  UpdateDateColumn,
} from 'typeorm';
import { Installations } from '../installations';
import { Organizations } from '../organizations';
import { PlatformTeams } from '../teams';
import { SlackAppSettings } from './interfaces/slack-app-settings.interface';

@Entity('settings')
@Index(
  'idx_uniq_settings_for_team',
  ['platformTeam', 'installation', 'organization'],
  {
    unique: true,
  },
)
@Index('settings_data_gin', { synchronize: false })
export class Settings {
  @PrimaryGeneratedColumn({ type: 'bigint' })
  id: string;

  @ManyToOne(
    () => PlatformTeams,
    (platformTeam) => platformTeam.id,
    { onDelete: 'CASCADE', onUpdate: 'CASCADE' },
  )
  @JoinColumn({ name: 'platform_team_id' })
  platformTeam: PlatformTeams;

  @ManyToOne(
    () => Installations,
    (installation) => installation.id,
    { onDelete: 'CASCADE', onUpdate: 'CASCADE' },
  )
  @JoinColumn({ name: 'installation_id' })
  installation: Installations;

  @ManyToOne(
    () => Organizations,
    (organization) => organization.id,
    { onDelete: 'CASCADE', onUpdate: 'CASCADE' },
  )
  @JoinColumn({ name: 'organization_id' })
  organization: Organizations;

  // Note this column has a GIN Index on it, refer to the migration file (CreateIndexOnSettingsData1740499500287) for more details
  @Column({ type: 'jsonb', name: 'settings', nullable: false })
  settings: SlackAppSettings;

  @CreateDateColumn({ name: 'created_at', type: 'timestamptz' })
  createdAt: Date;

  @UpdateDateColumn({ name: 'updated_at', type: 'timestamptz' })
  updatedAt: Date;
}
