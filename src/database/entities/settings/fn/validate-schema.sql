CREATE OR REPLACE FUNCTION validate_settings()
RETURNS TRIGGER AS $$
DECLARE
    setting_key TEXT;
    setting_value JSONB;
    schema_type TEXT;
    array_element JSONB;
    is_valid BOOLEAN;
BEGIN
    -- Loop through each setting in the JSONB
    FOR setting_key, setting_value IN SELECT * FROM jsonb_each(NEW.settings)
    LOOP
        -- Get the expected type from schema
        SELECT setting_type INTO schema_type 
        FROM setting_schemas 
        WHERE setting_key = setting_key;
        
        -- Check if setting exists in schema
        IF schema_type IS NULL THEN
            RAISE EXCEPTION 'Unknown setting: %', setting_key;
        END IF;
        
        -- Validate type
        CASE schema_type
            WHEN 'boolean' THEN
                IF jsonb_typeof(setting_value) != 'boolean' THEN
                    RAISE EXCEPTION 'Invalid boolean value for %: %', setting_key, setting_value;
                END IF;

            WHEN 'number' THEN
                IF jsonb_typeof(setting_value) != 'number' THEN
                    RAISE EXCEPTION 'Invalid number value for %: %', setting_key, setting_value;
                END IF;

            WHEN 'string' THEN
                IF jsonb_typeof(setting_value) != 'string' THEN
                    RAISE EXCEPTION 'Invalid string value for %: %', setting_key, setting_value;
                END IF;

            WHEN 'array' THEN
                IF jsonb_typeof(setting_value) != 'array' THEN
                    RAISE EXCEPTION 'Invalid array value for %: %', setting_key, setting_value;
                END IF;

            WHEN 'array_of_strings' THEN
                IF jsonb_typeof(setting_value) != 'array' THEN
                    RAISE EXCEPTION 'Invalid array value for %: %', setting_key, setting_value;
                END IF;
                
                -- Check each element is a string
                FOR array_element IN SELECT * FROM jsonb_array_elements(setting_value)
                LOOP
                    IF jsonb_typeof(array_element) != 'string' THEN
                        RAISE EXCEPTION 'Array % contains non-string element: %', setting_key, array_element;
                    END IF;
                END LOOP;

            WHEN 'array_of_numbers' THEN
                IF jsonb_typeof(setting_value) != 'array' THEN
                    RAISE EXCEPTION 'Invalid array value for %: %', setting_key, setting_value;
                END IF;
                
                -- Check each element is a number
                FOR array_element IN SELECT * FROM jsonb_array_elements(setting_value)
                LOOP
                    IF jsonb_typeof(array_element) != 'number' THEN
                        RAISE EXCEPTION 'Array % contains non-number element: %', setting_key, array_element;
                    END IF;
                END LOOP;

            WHEN 'jsonb' THEN
                -- All JSONB values are valid here
                is_valid := TRUE;

            ELSE
                RAISE EXCEPTION 'Unknown setting type: %', schema_type;
        END CASE;
    END LOOP;
    
    RETURN NEW;
END;
$$ LANGUAGE plpgsql;
