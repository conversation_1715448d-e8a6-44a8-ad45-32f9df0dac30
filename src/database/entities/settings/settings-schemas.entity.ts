import {
  Check,
  Column,
  CreateDateColumn,
  <PERSON>tity,
  PrimaryGeneratedColumn,
  UpdateDateColumn,
} from 'typeorm';
import { SettingType } from './interfaces/slack-app-settings.interface';

@Entity('settings_schemas')
@Check(
  'chk_setting_type_is_valid',
  `setting_type IN ('string', 'number', 'boolean', 'array', 'array_of_strings', 'array_of_numbers', 'jsonb')`,
)
export class SettingsSchemas {
  @PrimaryGeneratedColumn({ type: 'bigint' })
  id: string;

  @Column({ type: 'varchar', name: 'setting_key', unique: true })
  settingKey: string;

  @Column({
    type: 'varchar',
    name: 'setting_type',
  })
  settingType: SettingType;

  @Column({ type: 'varchar', name: 'default_value', nullable: true })
  defaultValue: string;

  @Column({ type: 'varchar', name: 'description', nullable: true })
  description: string;

  @CreateDateColumn({ name: 'created_at', type: 'timestamptz' })
  createdAt: Date;

  @UpdateDateColumn({ name: 'updated_at', type: 'timestamptz' })
  updatedAt: Date;
}
