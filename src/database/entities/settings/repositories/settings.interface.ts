import { BaseInterfaceRepository } from '../../../common/base-repository-interface';
import { Settings } from '../settings.entity';

/**
 * @interface SettingsRepositoryInterface
 * @extends BaseInterfaceRepository<Settings>
 * @description
 * Interface for the Settings repository, extending the base repository interface.
 * This interface ensures type safety and consistency for Settings-specific
 * repository operations.
 *
 * @remarks
 * By extending BaseInterfaceRepository, this interface inherits all the standard
 * CRUD operations defined for general entities and allows for the addition of
 * Settings-specific methods if needed in the future.
 *
 * @see {@link BaseInterfaceRepository} for the base interface being extended
 * @see {@link Settings} for the entity this interface is designed to work with
 */
export interface SettingsRepositoryInterface
  extends BaseInterfaceRepository<Settings> {}
