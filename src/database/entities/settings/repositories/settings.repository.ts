import { Injectable } from '@nestjs/common';
import { InjectRepository } from '@nestjs/typeorm';
import { Repository } from 'typeorm';
import { BaseAbstractRepository } from '../../../common/base-abstract-repository';
import { Settings } from '../settings.entity';
import { SettingsRepositoryInterface } from './settings.interface';

/**
 * @class SettingsRepository
 * @extends BaseAbstractRepository<Settings>
 * @implements SettingsRepositoryInterface
 * @description
 * Repository class for managing Settings entities in the database.
 * This class extends the BaseAbstractRepository to inherit common CRUD operations
 * and implements the SettingsRepositoryInterface for type safety and consistency.
 *
 * @remarks
 * This repository is designed to work with NestJS and TypeORM. It uses the
 * `@Injectable()` decorator to allow dependency injection in NestJS modules.
 *
 * @example
 * ```ts
 * // Inject and use in a service
 * constructor(private settingsRepository: SettingsRepository) {}
 *
 * async findSettings(id: string): Promise<Settings> {
 *   return this.settingsRepository.findOneById(id);
 * }
 * ```
 *
 * @see {@link BaseAbstractRepository} for inherited methods
 * @see {@link SettingsRepositoryInterface} for implemented methods
 * @see {@link Settings} for the entity this repository manages
 */
@Injectable()
export class SettingsRepository
  extends BaseAbstractRepository<Settings>
  implements SettingsRepositoryInterface
{
  constructor(
    @InjectRepository(Settings)
    private readonly settingsRepository: Repository<Settings>,
  ) {
    super(settingsRepository);
  }
}
