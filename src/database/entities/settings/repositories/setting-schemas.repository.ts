import { Injectable } from '@nestjs/common';
import { InjectRepository } from '@nestjs/typeorm';
import { Repository } from 'typeorm';
import { BaseAbstractRepository } from '../../../common/base-abstract-repository';
import { SettingsSchemas } from '../settings-schemas.entity';
import { SettingsSchemasRepositoryInterface } from './setting-schemas.interface';

/**
 * @class SettingsSchemasRepository
 * @extends BaseAbstractRepository<SettingsSchemas>
 * @implements SettingsSchemasRepositoryInterface
 * @description
 * Repository class for managing SettingsSchemas entities in the database.
 * This class extends the BaseAbstractRepository to inherit common CRUD operations
 * and implements the SettingsSchemasRepositoryInterface for type safety and consistency.
 *
 * @remarks
 * This repository is designed to work with NestJS and TypeORM. It uses the
 * `@Injectable()` decorator to allow dependency injection in NestJS modules.
 *
 * @example
 * ```ts
 * // Inject and use in a service
 * constructor(private settingSchemasRepository: SettingSchemasRepository) {}
 *
 * async findSettingSchemas(id: string): Promise<SettingSchemas> {
 *   return this.settingSchemasRepository.findOneById(id);
 * }
 * ```
 *
 * @see {@link BaseAbstractRepository} for inherited methods
 * @see {@link SettingSchemasRepositoryInterface} for implemented methods
 * @see {@link SettingSchemas} for the entity this repository manages
 */
@Injectable()
export class SettingsSchemasRepository
  extends BaseAbstractRepository<SettingsSchemas>
  implements SettingsSchemasRepositoryInterface
{
  constructor(
    @InjectRepository(SettingsSchemas)
    private readonly settingSchemasRepository: Repository<SettingsSchemas>,
  ) {
    super(settingSchemasRepository);
  }
}
