import { BaseInterfaceRepository } from '../../../common/base-repository-interface';
import { SettingsSchemas } from '../settings-schemas.entity';

/**
 * @interface SettingsSchemasRepositoryInterface
 * @extends BaseInterfaceRepository<SettingsSchemas>
 * @description
 * Interface for the SettingsSchemas repository, extending the base repository interface.
 * This interface ensures type safety and consistency for SettingsSchemas-specific
 * repository operations.
 *
 * @remarks
 * By extending BaseInterfaceRepository, this interface inherits all the standard
 * CRUD operations defined for general entities and allows for the addition of
 * SettingSchemas-specific methods if needed in the future.
 *
 * @see {@link BaseInterfaceRepository} for the base interface being extended
 * @see {@link SettingsSchemas} for the entity this interface is designed to work with
 */
export interface SettingsSchemasRepositoryInterface
  extends BaseInterfaceRepository<SettingsSchemas> {}
