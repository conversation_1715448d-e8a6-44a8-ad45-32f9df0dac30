import { Injectable } from '@nestjs/common';
import { InjectRepository } from '@nestjs/typeorm';
import { Repository } from 'typeorm';
import { BaseAbstractRepository } from '../../../common/base-abstract-repository';
import { Bots } from '../bots.entity';
import { BotsRepositoryInterface } from './bots.interface';

/**
 * @class BotsRepository
 * @extends BaseAbstractRepository<Bots>
 * @implements BotsRepositoryInterface
 * @description
 * Repository class for managing Bots entities in the database.
 * This class extends the BaseAbstractRepository to inherit common CRUD operations
 * and implements the BotsRepositoryInterface for type safety and consistency.
 *
 * @remarks
 * This repository is designed to work with NestJS and TypeORM. It uses the
 * `@Injectable()` decorator to allow dependency injection in NestJS modules.
 *
 * @example
 * ```ts
 * // Inject and use in a service
 * constructor(private botsRepository: BotsRepository) {}
 *
 * async findBots(id: string): Promise<Bots> {
 *   return this.botsRepository.findOneById(id);
 * }
 * ```
 *
 * @see {@link BaseAbstractRepository} for inherited methods
 * @see {@link BotsRepositoryInterface} for implemented methods
 * @see {@link Bots} for the entity this repository manages
 */
@Injectable()
export class BotsRepository
  extends BaseAbstractRepository<Bots>
  implements BotsRepositoryInterface
{
  constructor(
    @InjectRepository(Bots)
    private readonly botsRepository: Repository<Bots>,
  ) {
    super(botsRepository);
  }
}
