import { BaseInterfaceRepository } from '../../../common/base-repository-interface';
import { Bots } from '../bots.entity';

/**
 * @interface BotsRepositoryInterface
 * @extends BaseInterfaceRepository<Bots>
 * @description
 * Interface for the Bots repository, extending the base repository interface.
 * This interface ensures type safety and consistency for Bots-specific
 * repository operations.
 *
 * @remarks
 * By extending BaseInterfaceRepository, this interface inherits all the standard
 * CRUD operations defined for general entities and allows for the addition of
 * Channels-specific methods if needed in the future.
 *
 * @see {@link BaseInterfaceRepository} for the base interface being extended
 * @see {@link Bots} for the entity this interface is designed to work with
 */
export interface BotsRepositoryInterface
  extends BaseInterfaceRepository<Bots> {}
