import {
  Column,
  Entity,
  Index,
  Join<PERSON><PERSON>umn,
  ManyToOne,
  PrimaryGeneratedColumn,
} from 'typeorm';
import { Installations } from '../installations/installations.entity';
import { Organizations } from '../organizations/organizations.entity';

interface BotImages {
  image_original: string;
  image_512: string;
  image_192: string;
  image_72: string;
  image_48: string;
  image_32: string;
  image_24: string;
}

@Entity('bots')
@Index('idx_unique_slack_bot_slack_id', ['slackId', 'installation'], {
  unique: true,
})
export class Bots {
  @PrimaryGeneratedColumn({ type: 'bigint' })
  id: string;

  @Column({ type: 'jsonb', name: 'bot_dump', select: false, insert: true })
  botDump: Record<string, any>;

  @Index()
  @Column({ type: 'varchar', name: 'slack_id' })
  slackId: string;

  @Column({ type: 'boolean', name: 'slack_deleted', default: false })
  slackDeleted: boolean;

  @Column({ type: 'varchar', name: 'name' })
  name: string;

  @Column({ type: 'varchar', name: 'real_name', nullable: true })
  realName?: string | null;

  @Column({ type: 'varchar', name: 'display_name', nullable: true })
  displayName?: string | null;

  @ManyToOne(
    () => Installations,
    (installation) => installation.bots,
    { onDelete: 'CASCADE', onUpdate: 'CASCADE' },
  )
  @JoinColumn({
    name: 'installation_id',
    foreignKeyConstraintName: 'fk_slack_bot_installation_id',
  })
  installation: Installations;

  @ManyToOne(
    () => Organizations,
    (organization) => organization.bots,
    { onDelete: 'CASCADE', onUpdate: 'CASCADE' },
  )
  @JoinColumn({
    name: 'organization_id',
    foreignKeyConstraintName: 'fk_slack_bot_organization_id',
  })
  organization: Organizations;

  @Column({ type: 'varchar', name: 'tz', nullable: true })
  tz?: string | null;

  @Column({ type: 'varchar', name: 'tz_label', nullable: true })
  tzLabel?: string | null;

  @Column({ type: 'boolean', name: 'is_admin', default: false })
  isAdmin: boolean;

  @Column({ type: 'boolean', name: 'is_owner', default: false })
  isOwner: boolean;

  @Column({ type: 'boolean', name: 'is_restricted', default: false })
  isRestricted: boolean;

  @Column({ type: 'boolean', name: 'is_ultra_restricted', default: false })
  isUltraRestricted: boolean;

  @Column({ type: 'boolean', name: 'is_bot', default: false })
  isBot: boolean;

  @Column({ type: 'varchar', name: 'bot_title', nullable: true })
  botTitle?: string | null;

  @Column({ type: 'varchar', name: 'bot_real_name', nullable: true })
  botRealName?: string | null;

  @Column({
    type: 'varchar',
    name: 'bot_display_name',
    nullable: true,
  })
  botDisplayName?: string | null;

  @Column({ type: 'varchar', name: 'bot_status_text', nullable: true })
  botStatusText?: string | null;

  @Column({ type: 'varchar', name: 'bot_status_emoji', nullable: true })
  botStatusEmoji?: string | null;

  @Column({ type: 'jsonb', name: 'images', nullable: true })
  images?: Partial<BotImages> | null;
}
