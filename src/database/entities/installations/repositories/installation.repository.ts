import { Injectable } from '@nestjs/common';
import { InjectRepository } from '@nestjs/typeorm';
import { Repository } from 'typeorm';
import { BaseAbstractRepository } from '../../../common/base-abstract-repository';
import { Installations } from '../installations.entity';
import { InstallationRepositoryInterface } from './installation.interface';

/**
 * @class InstallationRepository
 * @extends BaseAbstractRepository<Installations>
 * @implements InstallationRepositoryInterface
 * @description
 * Repository class for managing Installations entities in the database.
 * This class extends the BaseAbstractRepository to inherit common CRUD operations
 * and implements the InstallationRepositoryInterface for type safety and consistency.
 *
 * @remarks
 * This repository is designed to work with NestJS and TypeORM. It uses the
 * `@Injectable()` decorator to allow dependency injection in NestJS modules.
 *
 * @example
 * ```ts
 * // Inject and use in a service
 * constructor(private installationRepository: InstallationRepository) {}
 *
 * async findInstallation(id: string): Promise<Installations> {
 *   return this.installationRepository.findOneById(id);
 * }
 * ```
 *
 * @see {@link BaseAbstractRepository} for inherited methods
 * @see {@link InstallationRepositoryInterface} for implemented methods
 * @see {@link Installations} for the entity this repository manages
 */
@Injectable()
export class InstallationRepository
  extends BaseAbstractRepository<Installations>
  implements InstallationRepositoryInterface
{
  constructor(
    @InjectRepository(Installations)
    private readonly installationRepository: Repository<Installations>,
  ) {
    super(installationRepository);
  }
}
