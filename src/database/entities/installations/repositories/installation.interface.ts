import { BaseInterfaceRepository } from '../../../common/base-repository-interface';
import { Installations } from '../installations.entity';

/**
 * @interface InstallationRepositoryInterface
 * @extends BaseInterfaceRepository<Installations>
 * @description
 * Interface for the Installation repository, extending the base repository interface.
 * This interface ensures type safety and consistency for Installation-specific
 * repository operations.
 *
 * @remarks
 * By extending BaseInterfaceRepository, this interface inherits all the standard
 * CRUD operations defined for general entities and allows for the addition of
 * Channels-specific methods if needed in the future.
 *
 * @see {@link BaseInterfaceRepository} for the base interface being extended
 * @see {@link Installations} for the entity this interface is designed to work with
 */
export interface InstallationRepositoryInterface
  extends BaseInterfaceRepository<Installations> {}
