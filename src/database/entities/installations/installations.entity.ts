import { Installation } from '@slack/bolt';
import {
  Column,
  CreateDateColumn,
  DeleteDateColumn,
  Entity,
  Index,
  JoinColumn,
  ManyToOne,
  OneToMany,
  PrimaryGeneratedColumn,
  UpdateDateColumn,
} from 'typeorm';
import { Bots } from '../bots/bots.entity';
import { Channels } from '../channels/channels.entity';
import { Organizations } from '../organizations/organizations.entity';
import { SlackSubgroups } from '../subgroups/subgroups.entity';
import { PlatformTeams } from '../teams/teams.entity';
import { Users } from '../users/users.entity';

export enum InstallationStatus {
  SYNCING = 'syncing',
  SYNCED = 'synced',
  ERROR = 'error',
}

export interface PlatformDump {
  customFields: {
    slackChannelId: string;
    slackTeamId: string;
    slackChannelName: string;
    slackTeamName: string;
    slackUserId: string;
    accountId: string;
    customerContactId: string;
  };
  customObjects: {
    accountCustomObjectId: string;
    contactCustomObjectId: string;
  };
}

@Entity('installations')
export class Installations {
  @PrimaryGeneratedColumn({ type: 'bigint' })
  id: string;

  @Column({ type: 'varchar' })
  name: string;

  @Column({ type: 'jsonb', name: 'installation_dump', select: false })
  installationDump: Installation;

  @Index('uniq_slack_installation_team_id', { unique: true })
  @Column({ type: 'varchar', name: 'team_id' })
  teamId: string;

  @Column({ type: 'varchar', nullable: true })
  domains: string | null;

  @Index('idx_slack_installation_team_name')
  @Column({ type: 'varchar', name: 'team_name' })
  teamName: string;

  @Column({ type: 'varchar', name: 'enterprise_id', nullable: true })
  enterpriseId: string | null;

  @Column({ type: 'jsonb', name: 'team_info' })
  teamInfo: Record<string, any>;

  @Column({
    type: 'enum',
    name: 'status',
    enum: InstallationStatus,
    default: InstallationStatus.SYNCING,
  })
  status: InstallationStatus;

  @Index('idx_slack_installation_installing_user_id')
  @Column({ type: 'varchar', name: 'installing_user_id', nullable: true })
  installingUserId: string;

  @Index('idx_slack_installation_installing_user_slack_id')
  @Column({ type: 'varchar', name: 'installing_user_slack_id' })
  installingUserSlackId: string;

  @Column({ type: 'varchar', name: 'installing_user_name' })
  installingUserName: string;

  @Column({ type: 'varchar', name: 'bot_token' })
  botToken: string;

  @Column({ type: 'varchar', name: 'bot_slack_id' })
  botSlackId: string;

  @Column({ type: 'varchar', name: 'bot_slack_user_id' })
  botSlackUserId: string;

  @Index('idx_slack_installation_slack_app_auth_token', { unique: true })
  @Column({ type: 'varchar', name: 'slack_app_auth_token', nullable: true })
  slackAppAuthToken: string;

  @Column({ type: 'boolean', name: 'disconnected', default: false })
  disconnected: boolean;

  @Column({ type: 'timestamp', name: 'disconnected_on', nullable: true })
  disconnectedOn: Date;

  @Column({ type: 'jsonb', name: 'platform_dump', nullable: true })
  platformDump: PlatformDump;

  @CreateDateColumn({ type: 'timestamp', name: 'created_at' })
  createdAt: Date;

  @UpdateDateColumn({ type: 'timestamp', name: 'updated_at' })
  updatedAt: Date;

  @DeleteDateColumn({ type: 'timestamp', name: 'deleted_at' })
  deletedAt: Date;

  @OneToMany(
    () => Bots,
    (bot) => bot.installation,
  )
  bots: Bots[];

  @Index('idx_slack_installation_organization_id')
  @ManyToOne(
    () => Organizations,
    (organization) => organization.installations,
  )
  @JoinColumn({
    name: 'organization_id',
    foreignKeyConstraintName: 'fk_slack_installation_organization_id',
  })
  organization: Organizations;

  @OneToMany(
    () => Channels,
    (channel) => channel.installation,
  )
  channels: Channels[];

  @OneToMany(
    () => Users,
    (user) => user.installation,
  )
  users: Users[];

  @OneToMany(
    () => PlatformTeams,
    (team) => team.installation,
  )
  teams: PlatformTeams[];

  @OneToMany(
    () => SlackSubgroups,
    (subgroup) => subgroup.installation,
  )
  subgroups: SlackSubgroups[];
}
