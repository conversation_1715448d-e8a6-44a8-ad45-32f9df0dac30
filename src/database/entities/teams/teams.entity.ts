import {
  Column,
  CreateDateColumn,
  DeleteDateColumn,
  Entity,
  Index,
  JoinColumn,
  ManyToOne,
  PrimaryGeneratedColumn,
  UpdateDateColumn,
} from 'typeorm';
import { Installations } from '../installations/installations.entity';
import { Organizations } from '../organizations/organizations.entity';

@Entity('platform_teams')
@Index('idx_uniq_team_id', ['uid', 'installation', 'organization'], {
  unique: true,
})
export class PlatformTeams {
  @PrimaryGeneratedColumn({ type: 'bigint' })
  id: string;

  @Index('idx_platform_team_uid')
  @Column({ type: 'varchar', name: 'uid', nullable: true })
  uid: string;

  @Column({ type: 'varchar', name: 'installed_by', nullable: true })
  installedBy: string;

  @ManyToOne(
    () => Installations,
    (installation) => installation.teams,
    {
      nullable: false,
      onDelete: 'CASCADE',
      onUpdate: 'CASCADE',
    },
  )
  @JoinColumn({
    name: 'installation_id',
    foreignKeyConstraintName: 'fk_slack_platform_team_installation_id',
  })
  installation: Installations;

  @ManyToOne(
    () => Organizations,
    (organization) => organization.teams,
    {
      nullable: false,
      onDelete: 'CASCADE',
      onUpdate: 'CASCADE',
    },
  )
  @JoinColumn({
    name: 'organization_id',
    foreignKeyConstraintName: 'fk_slack_platform_team_organization_id',
  })
  organization: Organizations;

  @CreateDateColumn({ type: 'timestamptz', name: 'created_at' })
  createdAt: Date;

  @UpdateDateColumn({ type: 'timestamptz', name: 'updated_at' })
  updatedAt: Date;

  @DeleteDateColumn({ type: 'timestamptz', name: 'deleted_at' })
  deletedAt: Date;
}
