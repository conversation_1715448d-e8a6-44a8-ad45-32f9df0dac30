import { Injectable } from '@nestjs/common';
import { InjectRepository } from '@nestjs/typeorm';
import { Repository } from 'typeorm';
import { BaseAbstractRepository } from '../../../common/base-abstract-repository';
import { PlatformTeams } from '../teams.entity';
import { TeamsRepositoryInterface } from './teams.interface';

/**
 * @class TeamsRepository
 * @extends BaseAbstractRepository<PlatformTeams>
 * @implements TeamsRepositoryInterface
 * @description
 * Repository class for managing Teams entities in the database.
 * This class extends the BaseAbstractRepository to inherit common CRUD operations
 * and implements the TeamsRepositoryInterface for type safety and consistency.
 *
 * @remarks
 * This repository is designed to work with NestJS and TypeORM. It uses the
 * `@Injectable()` decorator to allow dependency injection in NestJS modules.
 *
 * @example
 * ```ts
 * // Inject and use in a service
 * constructor(private teamsRepository: TeamsRepository) {}
 *
 * async findTeams(id: string): Promise<PlatformTeams> {
 *   return this.teamsRepository.findOneById(id);
 * }
 * ```
 *
 * @see {@link BaseAbstractRepository} for inherited methods
 * @see {@link TeamsRepositoryInterface} for implemented methods
 * @see {@link PlatformTeams} for the entity this repository manages
 */
@Injectable()
export class TeamsRepository
  extends BaseAbstractRepository<PlatformTeams>
  implements TeamsRepositoryInterface
{
  constructor(
    @InjectRepository(PlatformTeams)
    private readonly teamsRepository: Repository<PlatformTeams>,
  ) {
    super(teamsRepository);
  }
}
