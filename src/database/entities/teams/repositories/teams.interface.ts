import { BaseInterfaceRepository } from '../../../common/base-repository-interface';
import { PlatformTeams } from '../teams.entity';

/**
 * @interface TeamsRepositoryInterface
 * @extends BaseInterfaceRepository<PlatformTeams>
 * @description
 * Interface for the Teams repository, extending the base repository interface.
 * This interface ensures type safety and consistency for Teams-specific
 * repository operations.
 *
 * @remarks
 * By extending BaseInterfaceRepository, this interface inherits all the standard
 * CRUD operations defined for general entities and allows for the addition of
 * Channels-specific methods if needed in the future.
 *
 * @see {@link BaseInterfaceRepository} for the base interface being extended
 * @see {@link PlatformTeams} for the entity this interface is designed to work with
 */
export interface TeamsRepositoryInterface
  extends BaseInterfaceRepository<PlatformTeams> {}
