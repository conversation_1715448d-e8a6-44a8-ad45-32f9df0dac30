import {
  Column,
  CreateDateColumn,
  DeleteDateColumn,
  Entity,
  Index,
  JoinColumn,
  JoinTable,
  ManyToMany,
  ManyToOne,
  OneToMany,
  PrimaryGeneratedColumn,
  UpdateDateColumn,
} from 'typeorm';
import { CustomerContacts } from '../customer-contacts';
import { Installations } from '../installations/installations.entity';
import { PlatformTeamsToChannelMappings } from '../mappings';
import { Organizations } from '../organizations/organizations.entity';

export enum ChannelType {
  CUSTOMER_CHANNEL = 'customer_channel',
  INTERNAL_HELPDESK = 'internal_helpdesk',
  TRIAGE_CHANNEL = 'triage_channel',
  NOT_SETUP = 'not_setup',
  NOT_CONFIGURED = 'not_configured',
}

export interface PlatformDump {
  accounts: {
    accountId: string;
    customObjectRecordIds: string[];
  }[];
}

@Entity('channels')
@Index('idx_slack_channel_channel_id', ['channelId', 'installation'], {
  unique: true,
})
export class Channels {
  @PrimaryGeneratedColumn({ type: 'bigint' })
  id: string;

  @Column({ type: 'varchar' })
  name: string;

  @Column({ type: 'jsonb', name: 'channel_dump' })
  channelDump: Record<string, any>;

  @Column({ type: 'varchar', name: 'channel_id' })
  channelId: string;

  @Column({ type: 'varchar', name: 'slack_created_at' })
  slackCreatedAt: string;

  @Column({ type: 'boolean', name: 'is_bot_active', default: false })
  isBotActive: boolean;

  @Column({ type: 'boolean', name: 'is_bot_joined', default: false })
  isBotJoined: boolean;

  @Index('idx_channel_type')
  @Column({
    type: 'enum',
    enum: ChannelType,
    name: 'channel_type',
    default: ChannelType.NOT_SETUP,
  })
  channelType: ChannelType;

  @Column({ type: 'boolean', name: 'is_archived' })
  isArchived: boolean;

  @Column({ type: 'boolean', name: 'is_private' })
  isPrivate: boolean;

  @Column({ type: 'boolean', name: 'is_shared' })
  isShared: boolean;

  @Column({
    type: 'varchar',
    array: true,
    name: 'shared_team_ids',
    nullable: true,
  })
  sharedTeamIds: string[];

  @Column({ type: 'boolean', name: 'guest_are_customers', default: false })
  guestAreCustomers: boolean;

  @OneToMany(
    () => PlatformTeamsToChannelMappings,
    (platformTeamsToChannelMappings) => platformTeamsToChannelMappings.channel,
  )
  platformTeamsToChannelMappings: PlatformTeamsToChannelMappings[];

  @ManyToOne(
    () => Installations,
    (installation) => installation.channels,
    {
      nullable: false,
      onDelete: 'CASCADE',
      onUpdate: 'CASCADE',
    },
  )
  @JoinColumn({
    name: 'installation_id',
    foreignKeyConstraintName: 'fk_slack_channel_installation_id',
  })
  installation: Installations;

  @ManyToOne(
    () => Organizations,
    (organization) => organization.channels,
  )
  @JoinColumn({
    name: 'organization_id',
    foreignKeyConstraintName: 'fk_slack_channel_organization_id',
  })
  organization: Organizations;

  @Column({ type: 'jsonb', name: 'platform_dump', default: { accounts: [] } })
  platformDump: PlatformDump;

  @CreateDateColumn({ type: 'timestamptz', name: 'created_at' })
  createdAt: Date;

  @UpdateDateColumn({ type: 'timestamptz', name: 'updated_at' })
  updatedAt: Date;

  // This IS NOT the exact time when this channel was deleted in Slack, rather when
  // we received the `channel_deleted` event from Slack
  @Column({ type: 'varchar', name: 'slack_deleted_at', nullable: true })
  slackDeletedAt: string;

  @Column({ type: 'varchar', name: 'last_bot_left_at', nullable: true })
  lastBotLeftAt: string;

  @DeleteDateColumn({ type: 'timestamptz', name: 'deleted_at' })
  deletedAt: Date;

  @ManyToMany(
    () => CustomerContacts,
    (customerContact) => customerContact.channels,
  )
  @JoinTable({
    name: 'slack_customer_contacts_channels',
    inverseJoinColumn: {
      name: 'customer_contact_id',
      referencedColumnName: 'id',
    },
    joinColumn: {
      name: 'channel_id',
      referencedColumnName: 'id',
    },
  })
  customerContacts: CustomerContacts[];
}
