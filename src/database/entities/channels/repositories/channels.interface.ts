import { BaseInterfaceRepository } from '../../../common/base-repository-interface';
import { Channels } from '../channels.entity';

/**
 * @interface ChannelsRepositoryInterface
 * @extends BaseInterfaceRepository<Channels>
 * @description
 * Interface for the Channels repository, extending the base repository interface.
 * This interface ensures type safety and consistency for Channels-specific
 * repository operations.
 *
 * @remarks
 * By extending BaseInterfaceRepository, this interface inherits all the standard
 * CRUD operations defined for general entities and allows for the addition of
 * Channels-specific methods if needed in the future.
 *
 * @see {@link BaseInterfaceRepository} for the base interface being extended
 * @see {@link Channels} for the entity this interface is designed to work with
 */
export interface ChannelsRepositoryInterface
  extends BaseInterfaceRepository<Channels> {}
