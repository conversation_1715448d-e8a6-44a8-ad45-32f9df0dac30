import { Injectable } from '@nestjs/common';
import { InjectRepository } from '@nestjs/typeorm';
import { Repository } from 'typeorm';
import { BaseAbstractRepository } from '../../../common/base-abstract-repository';
import { Channels } from '../channels.entity';
import { ChannelsRepositoryInterface } from './channels.interface';

/**
 * @class ChannelsRepository
 * @extends BaseAbstractRepository<Channels>
 * @implements ChannelsRepositoryInterface
 * @description
 * Repository class for managing Channels entities in the database.
 * This class extends the BaseAbstractRepository to inherit common CRUD operations
 * and implements the ChannelsRepositoryInterface for type safety and consistency.
 *
 * @remarks
 * This repository is designed to work with NestJS and TypeORM. It uses the
 * `@Injectable()` decorator to allow dependency injection in NestJS modules.
 *
 * @example
 * ```ts
 * // Inject and use in a service
 * constructor(private channelsRepository: ChannelsRepository) {}
 *
 * async findChannels(id: string): Promise<Channels> {
 *   return this.channelsRepository.findOneById(id);
 * }
 * ```
 *
 * @see {@link BaseAbstractRepository} for inherited methods
 * @see {@link ChannelsRepositoryInterface} for implemented methods
 * @see {@link Channels} for the entity this repository manages
 */
@Injectable()
export class ChannelsRepository
  extends BaseAbstractRepository<Channels>
  implements ChannelsRepositoryInterface
{
  constructor(
    @InjectRepository(Channels)
    private readonly channelsRepository: Repository<Channels>,
  ) {
    super(channelsRepository);
  }
}
