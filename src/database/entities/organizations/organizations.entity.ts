import {
  Column,
  <PERSON>tity,
  Index,
  OneToMany,
  PrimaryGeneratedColumn,
} from 'typeorm';
import { Bots } from '../bots/bots.entity';
import { Channels } from '../channels/channels.entity';
import { Installations } from '../installations/installations.entity';
import { SlackSubgroups } from '../subgroups/subgroups.entity';
import { PlatformTeams } from '../teams/teams.entity';
import { Users } from '../users/users.entity';

interface OrganizationMetadata {
  /**
   * The application's id in Apps Platform
   */
  applicationId: string;

  /**
   * The installation id for this app in Apps Platform
   */
  installationId: string;

  /**
   * The user who added this app, this is platform's user
   */
  createdBy: string;
}

@Entity('organizations')
export class Organizations {
  @PrimaryGeneratedColumn({ type: 'bigint' })
  id: string;

  @Column({ type: 'varchar', name: 'external_pk', nullable: true })
  externalPk: string;

  @Column({ type: 'varchar', name: 'name', nullable: true })
  name: string;

  @Index('uniq_organization_uid_idx', { unique: true })
  @Column({ type: 'varchar', name: 'uid', unique: true })
  uid: string;

  @Column({ type: 'varchar', name: 'api_key', nullable: true })
  apiKey: string;

  @Column({ type: 'varchar', name: 'installing_user_id', nullable: true })
  installingUserId: string;

  @Column({ type: 'jsonb', name: 'metadata', nullable: true })
  metadata: OrganizationMetadata;

  @OneToMany(
    () => Bots,
    (bot) => bot.organization,
  )
  bots: Bots[];

  @OneToMany(
    () => Users,
    (user) => user.organization,
    { onDelete: 'CASCADE', onUpdate: 'CASCADE' },
  )
  users: Users[];

  @OneToMany(
    () => Channels,
    (channel) => channel.organization,
    { onDelete: 'CASCADE', onUpdate: 'CASCADE' },
  )
  channels: Channels[];

  @OneToMany(
    () => Installations,
    (installation) => installation.organization,
    { onDelete: 'CASCADE', onUpdate: 'CASCADE' },
  )
  installations: Installations[];

  @OneToMany(
    () => PlatformTeams,
    (team) => team.organization,
    { onDelete: 'CASCADE', onUpdate: 'CASCADE' },
  )
  teams: PlatformTeams[];

  @OneToMany(
    () => SlackSubgroups,
    (subgroup) => subgroup.organization,
    { onDelete: 'CASCADE', onUpdate: 'CASCADE' },
  )
  subgroups: SlackSubgroups[];
}
