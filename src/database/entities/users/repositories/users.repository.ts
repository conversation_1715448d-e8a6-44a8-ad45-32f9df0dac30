import { Injectable } from '@nestjs/common';
import { InjectRepository } from '@nestjs/typeorm';
import { Repository } from 'typeorm';
import { BaseAbstractRepository } from '../../../common/base-abstract-repository';
import { Users } from '../users.entity';
import { UsersRepositoryInterface } from './users.interface';

/**
 * @class UsersRepository
 * @extends BaseAbstractRepository<Users>
 * @implements UsersRepositoryInterface
 * @description
 * Repository class for managing Users entities in the database.
 * This class extends the BaseAbstractRepository to inherit common CRUD operations
 * and implements the UsersRepositoryInterface for type safety and consistency.
 *
 * @remarks
 * This repository is designed to work with NestJS and TypeORM. It uses the
 * `@Injectable()` decorator to allow dependency injection in NestJS modules.
 *
 * @example
 * ```ts
 * // Inject and use in a service
 * constructor(private usersRepository: UsersRepository) {}
 *
 * async findUsers(id: string): Promise<Users> {
 *   return this.usersRepository.findOneById(id);
 * }
 * ```
 *
 * @see {@link BaseAbstractRepository} for inherited methods
 * @see {@link UsersRepositoryInterface} for implemented methods
 * @see {@link Users} for the entity this repository manages
 */
@Injectable()
export class UsersRepository
  extends BaseAbstractRepository<Users>
  implements UsersRepositoryInterface
{
  constructor(
    @InjectRepository(Users)
    private readonly usersRepository: Repository<Users>,
  ) {
    super(usersRepository);
  }
}
