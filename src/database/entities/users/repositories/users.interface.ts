import { BaseInterfaceRepository } from '../../../common/base-repository-interface';
import { Users } from '../users.entity';

/**
 * @interface UsersRepositoryInterface
 * @extends BaseInterfaceRepository<Users>
 * @description
 * Interface for the Users repository, extending the base repository interface.
 * This interface ensures type safety and consistency for Users-specific
 * repository operations.
 *
 * @remarks
 * By extending BaseInterfaceRepository, this interface inherits all the standard
 * CRUD operations defined for general entities and allows for the addition of
 * Users-specific methods if needed in the future.
 *
 * @see {@link BaseInterfaceRepository} for the base interface being extended
 * @see {@link Users} for the entity this interface is designed to work with
 */
export interface UsersRepositoryInterface
  extends BaseInterfaceRepository<Users> {}
