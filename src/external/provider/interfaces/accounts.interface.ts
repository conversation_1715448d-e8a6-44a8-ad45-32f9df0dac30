import { DeepPartial } from 'typeorm';
import { CustomFieldValue } from './custom-fields.interface';

export interface SlackAccountSink {
  syncStatus: 'failed' | 'success';
  failureReason?: string;
  lastSyncedAt: Date;
  channels: {
    channelId: string;
    channelName: string;
    teamId: string;
    teamName: string;
  }[];
}

export interface AccountMetadata {
  sinks: {
    slack: SlackAccountSink;
  };
}

export interface Account {
  id: string;
  name: string;
  description: string;
  source: string;
  logo: string;
  statusId: string;
  status: string;
  classificationId: string;
  classification: string;
  healthId: string;
  health: string;
  industryId: string;
  industry: string;
  primaryDomain: string;
  secondaryDomain: string;
  accountOwner: string;
  accountOwnerId: string;
  accountOwnerEmail: string;
  accountOwnerAvatarUrl: string;
  annualRevenue: number;
  employees: number;
  website: string;
  billingAddress: string;
  shippingAddress: string;
  customFieldValues: CustomFieldValue[];
  metadata: DeepPartial<AccountMetadata>;
  createdAt: string;
  updatedAt: string;
}

interface CommonAccountFields {
  description?: string;
  source?: string;
  secondaryDomain?: string;
  accountOwnerId?: string;
  logo?: string;
  status?: string;
  classification?: string;
  health?: string;
  industry?: string;
  annualRevenue?: number;
  employees?: number;
  website?: string;
  billingAddress?: string;
  shippingAddress?: string;
  customFieldValues?: CustomFieldValue[];
  metadata?: DeepPartial<AccountMetadata>;
}

export interface CreateAccount extends CommonAccountFields {
  name: string;
  primaryDomain: string;
}

export interface UpdateAccount extends CommonAccountFields {
  name?: string;
  primaryDomain?: string;
}
