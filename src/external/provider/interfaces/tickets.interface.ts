export interface CreateNewTicket {
  title: string;
  text: string;
  description?: string;
  teamId: string;
  urgency?: string;
  sentiment?: string;
  requestorEmail: string;
  subTeamId?: string;
  performRouting?: boolean;
  metadata: {
    slack: { channel: string; ts: string; user: string };
  };
}

export interface UpdateTicketData {
  statusId: string;
  priorityId: string;
  assignedAgentId: string;
}
