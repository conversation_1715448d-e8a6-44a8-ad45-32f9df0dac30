import { CustomFieldValue } from './custom-fields.interface';

export interface CustomObject {
  id: string;
  name: string;
  description: string;
}

export interface CustomObjectField {
  id: string;
  createdOn: string;
  updatedOn: string;
  relationshipType?: string;
  defaultValue?: string;
  childObjectId?: string;
  childEntityId?: string;
}

export interface CustomObjectRecord {
  id: string;
  version: number;
  customFieldValues: CustomFieldValue[];
}

export interface CreateCustomObject {
  name: string;
  description: string;
}

export interface SearchCustomObject {
  name: string;
}

export interface AddCustomObjectField {
  customObjectId: string;
  customFieldId: string;
  relationshipType?: string;
  defaultValue?: string;
}

export interface CreateCustomObjectRecord {
  customObjectId: string;
  customFieldValues: CustomFieldValue[];
}

export interface UpdateCustomObjectRecord {
  customObjectId: string;
  customObjectRecordId: string;
  customFieldValues: CustomFieldValue[];
}
