export interface CustomField {
  id: string;
  name: string;
  description: string;
  teamId?: string;
  version?: number;
  source: string;
  fieldType: string;
  options?: {
    is_disabled: boolean;
    order: number;
    id: string;
    value: string;
  }[];
  defaultValue?: string;
  hintText?: string;
  placeholderText?: string;
  mandatoryOnCreation?: boolean;
  mandatoryOnClose?: boolean;
  visibleToCustomer?: boolean;
  editableByCustomer?: boolean;
  createdAt: string;
  updatedAt: string;
}

export interface CreateCustomField {
  name: string;
  source: string;
  fieldType: string;
  description: string;
  teamId?: string;
  options?: {
    is_disabled: boolean;
    order: number;
    id: string;
    value: string;
  }[];
  defaultValue?: string;
  hintText?: string;
  placeholderText?: string;
  mandatoryOnCreation?: boolean;
  mandatoryOnClose?: boolean;
  visibleToCustomer?: boolean;
  editableByCustomer?: boolean;
}

export interface SearchCustomField {
  name: string;
  teamId?: string;
}

export interface CustomFieldValue {
  customFieldId: string;
  data: {
    value: string;
  }[];
  metadata?: Record<string, unknown>;
}
