import { DeepPartial } from 'typeorm';

export interface SlackCustomerContactSink {
  syncStatus: 'failed' | 'success';
  failureReason?: string;
  lastSyncedAt: Date;
  userId: string;
  channels: {
    channelId: string;
    channelName: string;
    teamId: string;
    teamName: string;
  }[];
}

export interface CustomerContactMetadata {
  sinks: {
    slack: SlackCustomerContactSink;
  };
}

export interface PlatformCustomerContact {
  id: string;
  firstName: string;
  lastName: string;
  email: string;
  phoneNumber: string;
  avatarUrl: string;
  accounts: {
    id: string;
    name: string;
  }[];
  contactTypeId: string;
  contactType: string;
  metadata: DeepPartial<CustomerContactMetadata>;
  createdAt: string;
  updatedAt: string;
}

export interface CreateCustomerContactDTO {
  firstName: string;
  lastName: string;
  email: string;
  phoneNumber?: string;
  avatarUrl?: string;
  accountIds?: string[];
  contactType?: string;
  metadata?: DeepPartial<CustomerContactMetadata>;
}

export interface IngestCustomerContactDTO {
  users: CreateCustomerContactDTO[];
  sinkSource: string;
}

export interface UpdateCustomerContactDTO {
  firstName?: string;
  lastName?: string;
  email?: string;
  phoneNumber?: string;
  avatarUrl?: string;
  contactTypeId?: string;
  contactType?: string;
  metadata?: DeepPartial<CustomerContactMetadata>;
}
