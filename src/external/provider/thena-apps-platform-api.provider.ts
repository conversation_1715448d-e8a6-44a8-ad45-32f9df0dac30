import { Inject, Injectable } from '@nestjs/common';
import { ConfigKeys, ConfigService } from '../../config/config.service';
import { Organizations } from '../../database/entities';
import { CUSTOM_LOGGER_TOKEN } from '../../utils/logger/logger.interface';
import { ILogger } from '../../utils/logger/logger.interface';
import { EmittableSlackEventData } from './constants/platform-events.constants';
import { SENTRY_SERVICE_TOKEN, SentryService } from '../../utils/filters/sentry-alerts.filter';

const LOG_SPAN = '[THENA_APPS_PLATFORM_API_PROVIDER]';

@Injectable()
export class ThenaAppsPlatformApiProvider {
  constructor(
    @Inject(CUSTOM_LOGGER_TOKEN) private readonly logger: ILogger,
    @Inject(SENTRY_SERVICE_TOKEN) private readonly sentryService: SentryService,

    private readonly configService: ConfigService,
  ) {}

  /**
   * Get API key
   * @param organization Organization
   * @returns API key
   */
  private getApiKey(organization: Organizations) {
    return organization.apiKey;
  }

  /**
   * Proxy request
   * @param organization Organization
   * @param method Method
   * @param path Path
   * @param body Body
   * @returns Response
   */
  proxy<T>(
    organization: Organizations,
    method: string,
    path: string,
    body?: T extends object ? T : never,
  ): Promise<Response> {
    // Get API key from organization
    const apiKey = this.getApiKey(organization);
    if (!apiKey) {
      throw new Error('Organization has no API key');
    }

    // Construct the base URL
    const baseUrl = this.configService.get(ConfigKeys.APPS_PLATFORM_API_URL);

    // Construct the payload
    const payload: RequestInit = {
      method,
      headers: {
        'Content-Type': 'application/json',
        'x-org-id': organization.uid,
        'x-bot-key': apiKey,
      },
    };

    // Add body if it exists
    if (body) {
      payload.body = JSON.stringify(body);
    } else if (method !== 'GET') {
      payload.body = JSON.stringify({});
    }

    // Ensure proper URL construction by removing trailing/leading slashes
    const cleanBaseUrl = baseUrl.replace(/\/+$/, '');
    const cleanPath = path.replace(/^\/+/, '');
    const url = `${cleanBaseUrl}/${cleanPath}`;

    // Generate the request
    return fetch(url, payload);
  }

  /**
   * Post events to platform
   * @param organization Organization
   * @param event Event
   * @returns Response
   */
  async postEventsToPlatform(
    organization: Organizations,
    event: EmittableSlackEventData,
  ) {
    try{
    const apiKey = this.getApiKey(organization);
    if (!apiKey) {
      throw new Error('Organization has no API key');
    }

    const response = await this.proxy(
      organization,
      'POST',
      '/incoming-webhook/events',
      {
        event: event.type,
        payload: event,
      },
    );

    if (!response.ok) {
      // If the event was not posted, throw an error
      this.logger.error(`${LOG_SPAN} Failed to post event to apps platform!`);
      const eventJson = await response.json();
      throw new Error(eventJson.message);
      }

      return response.json();
    } catch (error) {
      this.logger.error(`${LOG_SPAN} Failed to post event to apps platform!`);
      this.sentryService.captureException(error, {
        fn: 'postEventsToPlatform',
        organization,
        event,
      });
    }
  }
}
