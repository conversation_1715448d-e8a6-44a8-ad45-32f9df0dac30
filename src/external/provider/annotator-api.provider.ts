import { HttpException, HttpStatus, Inject, Injectable } from '@nestjs/common';
import { ConfigKeys, ConfigService } from '../../config/config.service';
import { Installations } from '../../database/entities';
import { CUSTOM_LOGGER_TOKEN, ILogger } from '../../utils';

export interface EntityField {
  type: string;
  label: string;
  expression: string;
  standard: boolean;
  description?: string;
  supportedOperators: string[];
  fields?: Record<string, EntityField>;
  constraints?: {
    dynamicChoices?: Array<{ label: string; value: string }>;
    options?: Array<{ label: string; value: string }>;
    lookupType?: string;
    relatedEntityType?: string;
    relationshipValue?: string;
  };
}

export interface EntityMetadata {
  fields: Record<string, EntityField>;
}

export interface AnnotatorMetadataResponse {
  data: EntityMetadata;
}

@Injectable()
export class AnnotatorApiProvider {
  private readonly baseUrl: string;

  constructor(
    @Inject(CUSTOM_LOGGER_TOKEN) private readonly logger: ILogger,
    private readonly configService: ConfigService,
  ) {
    this.baseUrl = this.configService.get(ConfigKeys.ANNOTATOR_API_URL);
  }

  async getEntityMetadata(
    installation: Installations,
    entityType: string,
    relations: string[] = [],
  ): Promise<EntityMetadata> {
    try {
      const response = await fetch(`${this.baseUrl}/v1/annotator/metadata`, {
        method: 'POST',
        headers: {
          'X-Org-Id': installation.organization.uid,
          'X-Api-Key': installation.organization.apiKey,
          'Content-Type': 'application/json',
        },
        body: JSON.stringify({
          entityType,
          relations,
        }),
      });

      if (!response.ok) {
        let errorMessage = `Failed to fetch metadata, status: ${response.status}`;
        try {
          const errorData = await response.json();
          errorMessage = errorData.message || errorMessage;
        } catch (e) {
          this.logger.error(
            `Error fetching metadata for ${entityType}: ${
              e instanceof Error ? e.message : 'Unknown error'
            }`,
            e instanceof Error ? e.stack : undefined,
          );
        }
        throw new Error(errorMessage);
      }

      const responseData = (await response.json()) as AnnotatorMetadataResponse;
      return responseData.data;
    } catch (error) {
      this.logger.error(
        `Error fetching entity metadata for ${entityType}: ${
          error instanceof Error ? error.message : 'Unknown error'
        }`,
        error instanceof Error ? error.stack : undefined,
      );
      throw new HttpException(
        `Failed to fetch metadata for entity type: ${entityType}`,
        HttpStatus.INTERNAL_SERVER_ERROR,
      );
    }
  }

  async getFieldTypeMappings(
    installation: Installations,
    entityType: string,
    baseField: string,
  ): Promise<Record<string, string>> {
    const metadata = await this.getEntityMetadata(installation, entityType, [
      baseField,
    ]);

    // Create a mapping of field expressions to their types
    const fieldMappings: Record<string, string> = {};

    for (const [_key, field] of Object.entries(metadata.fields)) {
      fieldMappings[field.expression] = this.mapAnnotatorTypeToTriageType(
        field.type,
      );
    }

    return fieldMappings;
  }

  private mapAnnotatorTypeToTriageType(annotatorType: string): string {
    // Map Annotator types to our triage field types
    switch (annotatorType) {
      case 'string':
      case 'text':
      case 'enum':
      case 'date':
      case 'datetime':
        return 'string';
      case 'number':
      case 'integer':
      case 'float':
        return 'number';
      case 'boolean':
        return 'boolean';
      case 'array':
      case 'select':
      case 'multiselect':
        return 'array';
      default:
        return 'string'; // Default to string for unknown types
    }
  }
}
