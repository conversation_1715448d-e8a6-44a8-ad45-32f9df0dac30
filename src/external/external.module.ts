import { Module } from '@nestjs/common';
import { CommonModule } from '../common/common.module';
import { ConfigModule } from '../config/config.module';
import { ExternalService } from './external.service';
import { ThenaPlatformApiProvider } from './provider/thena-platform-api.provider';

@Module({
  imports: [ConfigModule, CommonModule],
  providers: [ExternalService, ThenaPlatformApiProvider],
  exports: [ThenaPlatformApiProvider],
})
export class ExternalModule {}
