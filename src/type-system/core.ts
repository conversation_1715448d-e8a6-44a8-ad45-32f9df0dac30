import { SlackEventMiddlewareArgs } from '@slack/bolt';

// Extract the event type from the SlackEventMiddlewareArgs
export type ExtractEventType<T> = T extends SlackEventMiddlewareArgs<any>
  ? T['event']
  : never;

// Extract the required properties from the event type
export type RequiredEventProps<T extends Record<string, any>> = {
  [K in keyof T]: T[K] extends undefined ? never : K;
}[keyof T];

// Event Type Guard
export type EventTypeGuard<T> = (event: Record<string, any>) => event is T;

/**
 * @class SlackEventTypeGuard
 * This class contains static methods to create type guards for Slack events
 */
// TODO: Can be a function or simply be deleted
// biome-ignore lint/complexity/noStaticOnlyClass: "We want to keep this class static because of only checks"
export class SlackEventTypeGuard {
  /**
   * Creates a type guard that checks for specific properties
   * @param props Array of required properties
   * @returns Type guard function
   */
  static hasProperties<T extends Record<string, any>>(
    props: Array<RequiredEventProps<T>>,
  ): EventTypeGuard<T> {
    return (event: Record<string, any>): event is T => {
      return props.every((prop) => prop in event);
    };
  }

  /**
   * Creates a type guard that checks for specific properties with type checking
   * @param propsWithTypes Object mapping properties to their type names
   * @returns Type guard function
   */
  static hasPropertiesWithTypes<T extends Record<string, any>>(
    propsWithTypes: {
      [K in keyof T]?: string;
    },
  ): EventTypeGuard<T> {
    return (event: Record<string, any>): event is T => {
      return Object.entries(propsWithTypes).every(([prop, type]) => {
        // biome-ignore lint/suspicious/useValidTypeof: "We want to check if the property is of the correct type"
        return prop in event && typeof event[prop] === type;
      });
    };
  }
}
