import {
  CanActivate,
  ExecutionContext,
  ForbiddenException,
  Inject,
  Injectable,
} from '@nestjs/common';
import { Reflector } from '@nestjs/core';
import { InjectRepository } from '@nestjs/typeorm';
import { Repository } from 'typeorm';
import { Installations } from '../../database/entities';
import { CUSTOM_LOGGER_TOKEN, ILogger } from '../../utils/logger';
import { NO_SLACK_TEAM } from '../decorators/no-slack-team.decorator';

@Injectable()
export class AuthGuard implements CanActivate {
  constructor(
    @Inject(CUSTOM_LOGGER_TOKEN) private readonly logger: ILogger,

    private readonly reflector: Reflector,

    @InjectRepository(Installations)
    private readonly installationRepository: Repository<Installations>,
  ) {}

  async canActivate(context: ExecutionContext): Promise<boolean> {
    const request = context.switchToHttp().getRequest();

    this.logger.debug('AuthGuard canActivate', request?.path);

    // Get the auth token
    const authToken = this.getAuthToken(request);
    if (!authToken) {
      this.logger.debug('No auth token found in the request');
      return false;
    }

    // Get the slack id
    const noSlackTeam = this.reflector.get(NO_SLACK_TEAM, context.getHandler());
    const slackId = this.getSlackId(request);
    if (!noSlackTeam && !slackId) {
      this.logger.debug('No slack id found in the request');
      throw new ForbiddenException('No slack team id found in the request');
    }

    this.logger.debug('Slack id found in the request', slackId);

    // Get the installation and organization
    const data = await this.getSlackTeamAndOrg(authToken, slackId);
    if (!data) {
      this.logger.debug('No installation found in the request');
      return false;
    }

    this.logger.debug('Installation found in the request');

    // Set the installation and organization in the request
    if (Array.isArray(data.installation)) {
      request.installations = data.installation;
    } else {
      request.installation = data.installation;
    }

    // Set the organization in the request
    request.organization = data.organization;

    this.logger.debug(
      `Installation and organization set in the request, ${JSON.stringify({
        organizationId: data.organization.uid,
      })}`,
    );

    return true;
  }

  /**
   * Get the installation and organization from the installation key
   */
  private async getSlackTeamAndOrg(authToken: string, slackId: string) {
    let installation: Installations | Array<Installations>;

    if (slackId) {
      installation = await this.installationRepository.findOne({
        where: { teamId: slackId, organization: { apiKey: authToken } },
        relations: { organization: true },
      });
    } else {
      installation = await this.installationRepository.find({
        where: { organization: { apiKey: authToken } },
        relations: { organization: true },
      });
    }

    // Check if the installation was found
    if (!installation) {
      this.logger.debug(`Installation not found for slack id: ${slackId}`);
      return null;
    }

    const organization = this.getOrganization(installation);
    return { installation, organization };
  }

  /**
   * Get the organization from the installation
   */
  private getOrganization(installation: Installations | Array<Installations>) {
    if (Array.isArray(installation)) {
      return installation[0].organization;
    }

    return installation.organization;
  }

  /**
   * Get the installation key from the request headers
   */
  private getSlackId(request: Request) {
    return request.headers?.['x-slack-id'] ?? null;
  }

  /**
   * Get the auth token from the request headers
   */
  private getAuthToken(request: Request) {
    return request.headers?.['x-auth-token'];
  }
}
