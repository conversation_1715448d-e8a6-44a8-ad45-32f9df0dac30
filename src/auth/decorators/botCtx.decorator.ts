import { ExecutionContext, createParamDecorator } from '@nestjs/common';
import { BotCtx } from '../interfaces';

export const GetBotCtx = createParamDecorator(
  (_data: unknown, ctx: ExecutionContext) => {
    const request = ctx.switchToHttp().getRequest();
    return {
      installations: request.installations,
      installation: request.installation,
      organization: request.organization,
    } as BotCtx;
  },
);
