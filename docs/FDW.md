# Foreign Data Wrapper against Slack

Slack App is connected to the Platform Database exposing the `slack_emojis` table as a foreign table.

## References

- [Postgres FDW](https://www.postgresql.org/docs/current/postgres-fdw.html#POSTGRES-FDW)
- [Supabase FDW Guide](https://supabase.com/docs/guides/database/extensions/postgres_fdw)

## Guide on how we went about creating an FDW Table on Platform

Following are the steps to create a foreign data wrapper and how it was created from Platform, note again that the Foreign table exists in Platform and was created from Platform.

1. First we create a foreign server object that points to the Platform Database.

```sql
CREATE server "curated_thena_slack_app"
FOREIGN DATA WRAPPER postgres_fdw
options (
  host 'host',
  port 'port',
  dbname 'dbname'
);
```

2. Create a user mapping for the server, this generally is our user who is used to access platform database.

```sql
CREATE user MAPPING FOR 'postgres'
SERVER 'curated_thena_slack_app'
options (
  user 'user',
  password 'password'
);
```

3. Create a foreign table and import the tables into a schema, we imported the `slack_emojis` table into the `public` schema.

```sql
import foreign schema "public"
limit to (
  "slack_emojis",
  "organizations"
)
from server "curated_thena_slack_app"
into "external_data";
```
