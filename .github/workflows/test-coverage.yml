name: Test Coverage Workflow

on:
  workflow_dispatch:
  pull_request:
    branches: [ master, production ]
    paths:
      - "apps/**"
      - ".github/workflows/test-coverage.yml"

jobs:
  test-coverage:
    runs-on: ubicloud-standard-4
    permissions:
      contents: write
      pages: write
      id-token: write
    
    # Enable colorful output
    env:
      FORCE_COLOR: 3
    
    steps:
      # 1. First checkout current repository (slack app)
      - name: Checkout current repository
        uses: actions/checkout@v4
        with:
          path: thena-curated-apps-slack
          fetch-depth: 0

      # 2. Then checkout thena-platform repository 
      - name: Checkout platform repository
        uses: actions/checkout@v4
        with:
          repository: 'thena-ai/thena-platform'
          token: ${{ secrets.PAT_GITHUB_TOKEN }}
          path: thena-platform
          fetch-depth: 0
          submodules: 'recursive'
          lfs: true

      - name: Setup pnpm
        uses: pnpm/action-setup@v2
        with:
          version: 10.1.0

      - name: Setup Node.js
        uses: actions/setup-node@v4
        with:
          node-version: '20.17'
          
      - name: Set up Docker Buildx
        uses: docker/setup-buildx-action@v3

      - name: Configure npmrc for TipTap Pro
        run: |
          echo "//registry.npmjs.org/:_authToken=${{ secrets.TIPTAP_PRO_TOKEN }}" >> ~/.npmrc

      # 3. Install dependencies for thena-platform first
      - name: Install platform dependencies
        env:
          TIPTAP_PRO_TOKEN: ${{ secrets.TIPTAP_PRO_TOKEN }}
        run: |
          cd thena-platform
          pnpm install

      - name: Build Platform
        run: |
          cd thena-platform
          pnpm run build --filter=!@thena-backend/thena-web

      - name: Install Supabase CLI
        uses: supabase/setup-cli@v1
        with:
          version: latest

      - name: Initialize Supabase
        run: |
          cd thena-platform
          supabase init
        
      - name: Start Supabase
        run: |
          cd thena-platform
          supabase db start

      - name: Reset test environment
        run: |
          cd thena-platform
          pnpm infra:test:reset

      # 4. Start platform services (platform, apps-platform, auth)
      
      # Auth service setup and start
      - name: Get Auth Vault Secrets
        id: auth_vault
        run: |
          cd thena-platform/apps/auth
          curl -H "X-Vault-Token: ${{ secrets.VAULT_TOKEN }}" \
            https://vault.thena.tools/v1/kv/data/test_coverage/auth | \
            jq -r '.data.data | to_entries | .[] | "export \(.key)=\(.value)"' > env_vars.sh
          chmod +x env_vars.sh
          source env_vars.sh
        continue-on-error: false

      - name: Start Auth Service
        run: |
          cd thena-platform/apps/auth
          pnpm run test &
          echo $! > auth_pid.txt
          sleep 10
      
      # Platform service setup and start
      - name: Get Platform Vault Secrets
        id: platform_vault
        run: |
          cd thena-platform/apps/platform
          curl -H "X-Vault-Token: ${{ secrets.VAULT_TOKEN }}" \
            https://vault.thena.tools/v1/kv/data/test_coverage/platform | \
            jq -r '.data.data | to_entries | .[] | "export \(.key)=\(.value)"' > env_vars.sh
          chmod +x env_vars.sh
          source env_vars.sh
        continue-on-error: false

      - name: Start Platform Service
        run: |
          cd thena-platform/apps/platform
          pnpm run test &
          echo $! > platform_pid.txt
          sleep 10

      # Apps Platform service setup and start
      - name: Get Apps Platform Vault Secrets
        id: apps_platform_vault
        run: |
          cd thena-platform/apps/apps-platform
          curl -H "X-Vault-Token: ${{ secrets.VAULT_TOKEN }}" \
            https://vault.thena.tools/v1/kv/data/test_coverage/apps-platform | \
            jq -r '.data.data | to_entries | .[] | "export \(.key)=\(.value)"' > env_vars.sh
          chmod +x env_vars.sh
          source env_vars.sh
        continue-on-error: false

      - name: Start Apps Platform Service
        run: |
          cd thena-platform/apps/apps-platform
          pnpm run test &
          echo $! > apps_platform_pid.txt
          sleep 10

      # 5. Install dependencies for slack app
      - name: Install slack app dependencies
        env:
          TIPTAP_PRO_TOKEN: ${{ secrets.TIPTAP_PRO_TOKEN }}
        run: |
          cd thena-curated-apps-slack
          pnpm install

      # Get Slack App Vault Secrets
      - name: Get Slack App Vault Secrets
        id: slack_app_vault
        run: |
          cd thena-curated-apps-slack
          curl -H "X-Vault-Token: ${{ secrets.VAULT_TOKEN }}" \
            https://vault.thena.tools/v1/kv/data/test_coverage/slack-app | \
            jq -r '.data.data | to_entries | .[] | "export \(.key)=\(.value)"' > env_vars.sh
          chmod +x env_vars.sh
          source env_vars.sh
        continue-on-error: false

      # 6. Run tests on slack app
      - name: Run Test Coverage
        run: |
          cd thena-curated-apps-slack
          pnpm run test

      # Generate badges if needed
      - name: Generate Badges
        run: |
          cd thena-curated-apps-slack  
          pnpm run make-badges || echo "No badge generation script found"

      # Commit README changes if on main branch
      - name: Commit README changes
        if: github.ref == 'refs/heads/master'
        run: |
          cd thena-curated-apps-slack
          git config user.name "GitHub Actions Bot"
          git config user.email "<EMAIL>"
          git add README.md
          git commit -m "Update coverage badges in README" || echo "No changes to commit"
          git push || echo "Failed to push changes"

      # Upload coverage report as artifact
      - name: Upload Coverage Report
        uses: actions/upload-artifact@v4
        with:
          name: coverage-report
          path: thena-curated-apps-slack/coverage

      # Clean up processes
      - name: Cleanup
        if: always()
        run: |
          if [ -f "thena-platform/apps/auth/auth_pid.txt" ]; then
            kill -9 $(cat thena-platform/apps/auth/auth_pid.txt) || true
          fi
          if [ -f "thena-platform/apps/platform/platform_pid.txt" ]; then
            kill -9 $(cat thena-platform/apps/platform/platform_pid.txt) || true
          fi
          if [ -f "thena-platform/apps/apps-platform/apps_platform_pid.txt" ]; then
            kill -9 $(cat thena-platform/apps/apps-platform/apps_platform_pid.txt) || true
          fi 